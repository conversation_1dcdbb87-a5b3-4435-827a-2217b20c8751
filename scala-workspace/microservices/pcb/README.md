# New PCB server

We're moving slowly to a new architecture for PCB server. It will be a monolith (at least for now) with a REST API.

We decided to extend an existing Wuerth API service step by step, as it see it as a good fit for the new architecture.

## Endpoints

### `POST api/manufacturers/wurth/auth`

Deprecated endpoint: `POST api/wurth`

Checks if `Wurth API` credentials are valid.

#### Input

JSON object with the following fields:

- `username`: Wurth API username
- `password`: Wurth API password

#### Output
HTTP code: Always 200

JSON object with attributes: 

- `is_valid` (type: boolean) - Either `true` or `false`

### `POST api/manufacturers/instant-price-available?lang=[en,de]`

Deprecated endpoint: `POST api/wurth/availability`

Checks if PCB manufacturers could provide instant prices offers for the requested PCB.

#### Query parameters

- `lang`: language of the response (`en` or `de`) 

#### Input

JSON object with attributes:

- `pcb_id` (type: UUID) - ID of the PCB in `PCB` service.
- `quantity` (type: int) - number of PCBs to order

#### Output
HTTP code: 404 or 200

JSON object with attributes:

- `status` (type: string) - Either `success` or `error`
- `data` (type: object). This property is available only when `status=error` 
  - `errors` (type: array). Detailed errors for each failed PCB property. Either this parameter or `message` is available.
    - `name` (type: string) - Name of `PCB` attribute caused a problem. Should be used to identify the attribute.
    - `label` (type: string) - Translated label of `PCB` attribute caused a problem. Should be displayed to the user.
    - `error` (type: string) - Error message explaining why the error happened
  - `message` (type: string). Error description when something went wrong with third-party service. Either this parameter or `errors` is available.

#### Output Examples

Successful response (http code = `200`):
`{
"status": "success"
}`

Error response for a case when PCB validation failed (http code = `200`):

`{
"status": "error",
"data": 
  {
    "errors": [
      {
        "label": "Outer copper thickness", 
        "error": "The value for outer copper thickness is above the supported maximum",
        "name": "outerCopperThickness"
      }]
  }
}`

Error response for a case when third-party service (like Wuerth) responds with error (http code = `200`):

`{
"status": "error",
"data":
    {
        "message": "Response is not serializable"
    }
}`

Error response for a case when PCB is not found (http code = `404`):

`{
"status": "error",
"data":
    {
        "message": "Requested PCB not found"
    }
}`


### `POST api/manufacturers/offers?lang=[en,de]`

Deprecated endpoint: `POST api/offers`

Creates a price requests via PCB manufacturer APIs and publishes the offered prices to LumiQuote via the endpoint call. 
It always returns `201 Created` as it "saves" the request and handles it asynchronously.  

#### Query parameters

- `lang`: language of the response (`en` or `de`)

#### Input

JSON object with attributes:

- `pcb_id` (type: UUID) - ID of the PCB in `PCB` service.
- `quantity` (type: int) - number of PCBs to order
- `lead_times` (type: array) - array of strategies to return a correct price. Could be `empty`.
    - `kind` (type: string) - type of strategy. Possible values are:
        - `best_price` - the best available price
        - `best_price_by` - the best available price before the given date. The `date` is set by a separate attribute
        - `fastest` - the price for the fastest delivery date
    - `date` (type: date in YYYY-mm-DD format) - the date for `best_price_by` strategy. Ignored by other strategies.
- `credentials` (type: object, optional) - credentials for `Wurth API`. If not provided, default credentials are used
    - `username` (type: string) - Wurth API username
    - `password` (type: string) - Wurth API password

#### Output
HTTP code: Always 201.

JSON object with attributes:

- `requestId` (type: string) - Unique request identifier passed to LumiQuote via the endpoint call when the offers are ready

### `GET api/pcb/{pcb_id}/download-pdf`

Downloads a PDF file with a PCB specification.

#### Input

- `projectName` (type: optional string) - Name of the project. Used in the PDF file name.

#### Output
HTTP code: 200 or 404 (when PCB is not found)

PDF file with a PCB specification.

### `GET api/pcb/{pcb_id}/download-zip`

Downloads a ZIP file with a PCB specification and source Gerber files

It can produce specifications for several offers at once. In this case, the ZIP file contains a PDF specification for each offer.

#### Input

- `projectName` (type: optional string) - Name of the project. Used in the ZIP file name.
- `offerSize` (type: optional sequence of int, separated by comma) - Number of PCBs in one or several offers. Example: `10,20,30`
- `manufacturerId` (type: optional sequence of ids separated by comma) - IDs of manufacturers. At this moment, it's either `BetaLayout` or `Wuerth`. Example: `BetaLayout,Wuerth`

#### Output
HTTP code: 200 or 404 (when PCB is not found)

PDF file with a PCB specification.
