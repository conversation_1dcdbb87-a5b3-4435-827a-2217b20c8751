package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters

object InnerTraceWidth {
  val name: String  = "innerTraceWidth"
  val label: String = "pcb.basic.innerTraceWidth"

  def empty: InnerTraceWidth                     = InnerTraceWidth(None)
  def apply(value: Millimeters): InnerTraceWidth = InnerTraceWidth(Some(value))
}

final case class InnerTraceWidth(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = InnerTraceWidth.name
  val label: String     = InnerTraceWidth.label

  override val legacyNames: Option[Seq[String]] = Some(Seq("inner_trace_width"))
}
