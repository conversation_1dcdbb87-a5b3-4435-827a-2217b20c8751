package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.microservices.pcb.model.pcb.props.MinViaDiameter

/** Name of param in BetaLayout API: drill_diameter
  */
sealed trait BetaLayoutDrillDiameter {
  val value: Int
  def toPCB: MinViaDiameter
}

private case object DrillDiameter0_3mm extends BetaLayoutDrillDiameter {
  override val value: Int            = 1
  override def toPCB: MinViaDiameter = MinViaDiameter(0.3)
}

private case object DrillDiameter0_2mm extends BetaLayoutDrillDiameter {
  override val value: Int            = 2
  override def toPCB: MinViaDiameter = MinViaDiameter(0.2)
}

private case object DrillDiameter0_15mm extends BetaLayoutDrillDiameter {
  override val value: Int            = 3
  override def toPCB: MinViaDiameter = MinViaDiameter(0.15)
}

private case object DrillDiameter0_1mm extends BetaLayoutDrillDiameter {
  override val value: Int            = 4
  override def toPCB: MinViaDiameter = MinViaDiameter(0.1)
}

private object BetaLayoutDrillDiameter {

  /** Converts PCB values to BetaLayout values
    */
  def fromPcb(value: MinViaDiameter): Option[BetaLayoutDrillDiameter] = value.value match {
    case Some(v) if v < 0.1  => None
    case Some(v) if v < 0.15 => Some(DrillDiameter0_1mm)
    case Some(v) if v < 0.2  => Some(DrillDiameter0_15mm)
    case Some(v) if v < 0.3  => Some(DrillDiameter0_2mm)
    case Some(v) if v >= 0.3 => Some(DrillDiameter0_3mm)
    case _                   => None
  }
}
