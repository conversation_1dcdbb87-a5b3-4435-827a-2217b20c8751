package de.fellows.microservices.pcb.model.betaLayout

import com.osinka.i18n.{<PERSON>, Messages}
import de.fellows.ems.pcb.api.specification.Side
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.pcb.{Convert, PCB, PropertyError, PropertyErrorKind, Quantity}
import de.fellows.microservices.pcb.model.pcb.props.{BoardHeight, BoardWidth, EdgeMetalization, SoldermaskSide}
import play.api.libs.json.{JsObject, Json, Writes}
import zio.prelude.Validation

@annotation.nowarn("msg=Pattern definition introduces")
object BetaLayoutLeadTime extends Enumeration {
  type BetaLayoutLeadTime = Value

  val _, OneDay, TwoDays, ThreeDays, FourDays, FiveDays, SixDays = Value

  val leadTimes = Seq(OneDay, TwoDays, ThreeDays, FourDays, FiveDays, SixDays)
}

/** Describes a request to BetaLayout API
  *
  * @param leadTime If specified, we send a request to BetaLayout API with this lead time
  * @param orderId If specified, we send a request to BetaLayout API with this order id,
  *                making it a real offer, not a price request. For more information on how we treat order id,
  *                read here: https://www.notion.so/luminovo/Beta-layout-API-technical-documentation-65cb45d8dcc54aae88b130f83aaa5109
  */
final case class BetaLayoutPcbRequest(
    quantity: Quantity,
    numberOfLayers: BetaLayoutNumberOfLayers,
    width: BoardWidth,
    height: BoardHeight,
    panelWidth: BigDecimal,
    panelHeight: BigDecimal,
    pcbsPerPanel: Int,
    depanelization: BetaLayoutDepanelization,
    finalThickness: BetaLayoutThickness,
    surfaceFinish: BetaLayoutSurfaceFinish,
    silkscreenSides: BetaLayoutSilkscreenSides,
    minStructure: BetaLayoutMinStructure,
    drillDiameter: BetaLayoutDrillDiameter,
    edgeMetalization: EdgeMetalization,
    soldermask: SoldermaskSide,
    tg: BetaLayoutTGValue,
    leadTime: Option[BetaLayoutLeadTime.BetaLayoutLeadTime],
    orderId: Option[String],
    calculatedPanelInfo: CalculatedPanelInfo
)

object BetaLayoutPcbRequest {
  implicit val writes: Writes[BetaLayoutPcbRequest] = request => {
    val product = Json.obj(
      "base_material"         -> request.tg.value,
      "quantity"              -> request.quantity.value,
      "layer_count"           -> request.numberOfLayers.value,
      "material_thickness"    -> request.finalThickness.value,
      "surface"               -> request.surfaceFinish.value,
      "silkscreen"            -> request.silkscreenSides.value,
      "track_gap_size"        -> request.minStructure.value,
      "drill_diameter"        -> request.drillDiameter.value,
      "edge_metallisation"    -> (if (request.edgeMetalization.value) 1 else 0),
      "soldermask"            -> (if (request.soldermask.value == Side.Both) 1 else 0),
      "multipanel"            -> 3,
      "multipanel_processing" -> request.depanelization.value,
      "multipanel_edge"       -> "yes",
      "projects" -> Json.arr(
        Json.obj(
          "multipanel_width"  -> request.panelWidth,
          "multipanel_length" -> request.panelHeight,
          // there is no quantity_y for self-configured panels
          "multipanel_quantity_x" -> request.pcbsPerPanel
        )
      )
    )

    val leadTime: JsObject = request.leadTime.map(x => Json.obj("lead_time" -> x.id)).getOrElse(Json.obj())
    val orderId            = request.orderId.map(x => Json.obj("order_id" -> x)).getOrElse(Json.obj())
    Json.obj(
      "language" -> "de", // I tried "en" but the API gave me an SQL error :D
      "product"  -> (product ++ leadTime)
    ) ++ orderId
  }

  /** Converts a [[PCB]] to a [[BetaLayoutPcbRequest]]. If the conversion fails, it returns a message, containing all errors,
    * separated by comma
    *
    * @param pcb         PCB
    * @param quantity    Order quantity
    * @param preferences EMS panel preferences
    */
  def validateAndConvert(
      pcb: PCB,
      calculatedPanelInfo: CalculatedPanelInfo
  )(implicit lang: Lang): Either[Seq[PropertyError], BetaLayoutPcbRequest] =
    BetaLayoutCapability.capability.validate(pcb.properties) match {
      case Nil    => convert(pcb, calculatedPanelInfo)
      case errors => Left(errors)
    }

  protected def convert(
      pcb: PCB,
      calculatedPanelInfo: CalculatedPanelInfo
  )(implicit lang: Lang): Either[Seq[PropertyError], BetaLayoutPcbRequest] = {
    val props = pcb.properties
    val minStructureValidation = BetaLayoutMinStructure.converter(props.layer) match {
      case Some(value) => Validation.succeed(value)
      case _ => Validation.fail(PropertyError(
          props.layer.minOuterLayerStructure,
          Messages("pcb.error.converter.notSupported", "", Messages(props.layer.minOuterLayerStructure.label)),
          PropertyErrorKind.ConversionError(
            value = Some(props.layer.minOuterLayerStructure.value.toString),
            unit = Messages(props.layer.minOuterLayerStructure.label)
          )
        ))
    }
    Validation.validateWith(
      Validation.succeed(pcb),
      Validation.succeed(props.basic.boardWidth),
      Validation.succeed(props.basic.boardHeight),
      Convert.convertRequired(props.layer.numberOfLayers, BetaLayoutNumberOfLayers.fromPcb),
      Convert.convertRequired(props.basic.surfaceFinish, BetaLayoutSurfaceFinish.fromPcb),
      Convert.convertRequired(props.layer.finalThickness, BetaLayoutThickness.converter),
      minStructureValidation,
      Convert.convert(props.mechanical.minViaDiameter, BetaLayoutDrillDiameter.fromPcb),
      Convert.convert(props.layer.tgValue, BetaLayoutTGValue.fromPcb),
      Validation.succeed(calculatedPanelInfo)
    )(createRequest).fold(
      errors => Left(errors),
      Right.apply
    )
  }

  /** Creates a [[BetaLayoutPcbRequest]] from validated parameters
    */
  private def createRequest(
      pcb: PCB,
      width: BoardWidth,
      height: BoardHeight,
      numberOfLayers: BetaLayoutNumberOfLayers,
      surfaceFinish: BetaLayoutSurfaceFinish,
      thickness: BetaLayoutThickness,
      minStructure: BetaLayoutMinStructure,
      drillDiameter: BetaLayoutDrillDiameter,
      tg: BetaLayoutTGValue,
      calculatedPanelInfo: CalculatedPanelInfo
  ): BetaLayoutPcbRequest = {
    val props = pcb.properties

    val (panelWidth, panelHeight, pcbsPerPanel, depanelization) = calculatedPanelInfo match {
      case p: CalculatedPanelInfo.FromPanelDetails =>
        (
          p.panelDistribution.panel.widthInMm,
          p.panelDistribution.panel.heightInMm,
          p.panelDistribution.mesh.size,
          p.depanelization
        )
      case p: CalculatedPanelInfo.FromExisting =>
        (
          p.existing.panelWidth,
          p.existing.panelHeight,
          p.existing.numberOfPcbs,
          p.existing.depanelization
        )
    }
    val betaLayoutDepanelization = BetaLayoutDepanelization.converter(depanelization)

    BetaLayoutPcbRequest(
      quantity = Quantity(calculatedPanelInfo.numberOfPanels.value),
      numberOfLayers = numberOfLayers,
      width = width,
      height = height,
      panelWidth = panelWidth,
      panelHeight = panelHeight,
      pcbsPerPanel = pcbsPerPanel,
      depanelization = betaLayoutDepanelization,
      finalThickness = thickness,
      surfaceFinish = surfaceFinish,
      silkscreenSides = BetaLayoutSilkscreenSides.fromPcb(props.basic.silkscreenSide),
      minStructure = minStructure,
      drillDiameter = drillDiameter,
      edgeMetalization = props.advanced.edgeMetalization,
      soldermask = props.basic.soldermaskSide,
      tg = tg,
      leadTime = None,
      orderId = None,
      calculatedPanelInfo = calculatedPanelInfo
    )
  }
}
