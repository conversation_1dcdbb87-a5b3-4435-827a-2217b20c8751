package de.fellows.microservices.pcb.utils

import de.fellows.microservices.pcb.{ServerError, ServiceError}
import play.api.libs.json.{<PERSON>son, Reads}

object StubLoader {
  def readJsonStubFile[T](fileName: String)(implicit rds: Reads[T]): Either[ServiceError, T] = {
    val stream = getClass.getResourceAsStream(fileName)

    try {
      val parsedJson = Json.parse(stream).validate[T]

      parsedJson.asEither match {
        case Left(errors) => Left(ServerError(s"Error reading $fileName ${errors.mkString(", ")}"))
        case Right(value) => Right(value)
      }
    } finally
      stream.close()
  }
}
