package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.Side
import de.fellows.ems.pcb.api.specification.SilkscreenColor
import de.fellows.ems.pcb.api.specification.Side.{Both, Bottom, Top}
import de.fellows.microservices.pcb.model.pcb.props

/** Name of param in Wurth API: service_print
  */
sealed trait WurthSilkscreenSides {
  val value: Int
  val toPCB: props.SilkscreenSide
}
private case object SilkscreenSidesNone extends WurthSilkscreenSides {
  override val value: Int                  = 1
  override val toPCB: props.SilkscreenSide = props.SilkscreenSide(Side.None)
}
private case object SilkscreenSidesTopBottom extends WurthSilkscreenSides {
  override val value: Int                  = 3
  override val toPCB: props.SilkscreenSide = props.SilkscreenSide(Both)
}
private case object SilkscreenSidesTop extends WurthSilkscreenSides {
  override val value: Int                  = 4
  override val toPCB: props.SilkscreenSide = props.SilkscreenSide(Top)
}
private case object SilkscreenSidesBottom extends WurthSilkscreenSides {
  override val value: Int                  = 5
  override val toPCB: props.SilkscreenSide = props.SilkscreenSide(Bottom)
}

private object WurthSilkscreenSides {

  /** According to this specification, https://www.notion.so/luminovo/PCB-Specification-4493fa95f17e4bbf81fe2a6c59259e82
    * the default value for silkscreen sides is TopBottom
    */
  def apply(sides: props.SilkscreenSide): WurthSilkscreenSides =
    sides.value match {
      case Side.None => SilkscreenSidesNone
      case Top       => SilkscreenSidesTop
      case Bottom    => SilkscreenSidesBottom
      case _         => SilkscreenSidesTopBottom
    }

  def fromWurth(legendPrinting: Option[Int]): WurthSilkscreenSides =
    legendPrinting match {
      case Some(value) => fromWurth(value)
      case None        => apply(props.SilkscreenSide.default)
    }

  def fromWurth(legendPrinting: Int): WurthSilkscreenSides = legendPrinting match {
    case 1 => SilkscreenSidesNone
    case 3 => SilkscreenSidesTopBottom
    case 4 => SilkscreenSidesTop
    case 5 => SilkscreenSidesBottom
    case _ => throw new IllegalArgumentException(s"Unknown legend printing: $legendPrinting")
  }
}

sealed trait WurthSilkscreenColor {
  val value: Int
  val toPCB: props.SilkscreenColor
}
object WurthSilkscreenColor {

  def converter(color: props.SilkscreenColor): Option[WurthSilkscreenColor] =
    color.value match {
      case SilkscreenColor.White  => Some(White)
      case SilkscreenColor.Black  => Some(Black)
      case SilkscreenColor.Yellow => Some(Yellow)
      case SilkscreenColor.Red    => Some(Red)
      case SilkscreenColor.Blue   => Some(Blue)
      case SilkscreenColor.Green  => None
    }

  def fromWurth(silkscreenColor: Option[Int]): WurthSilkscreenColor =
    silkscreenColor.map(fromWurth).getOrElse(White)

  def fromWurth(silkscreenColor: Int): WurthSilkscreenColor = silkscreenColor match {
    case 1 => White
    case 2 => Yellow
    case 3 => Red
    case 4 => Blue
    case 5 => Black
  }

  private[wurth] case object White extends WurthSilkscreenColor {
    override val value: Int                   = 1
    override val toPCB: props.SilkscreenColor = props.SilkscreenColor(SilkscreenColor.White)
  }
  private[wurth] case object Yellow extends WurthSilkscreenColor {
    override val value: Int                   = 2
    override val toPCB: props.SilkscreenColor = props.SilkscreenColor(SilkscreenColor.Yellow)
  }
  private[wurth] case object Red extends WurthSilkscreenColor {
    override val value: Int                   = 3
    override val toPCB: props.SilkscreenColor = props.SilkscreenColor(SilkscreenColor.Red)
  }
  private[wurth] case object Blue extends WurthSilkscreenColor {
    override val value: Int                   = 4
    override val toPCB: props.SilkscreenColor = props.SilkscreenColor(SilkscreenColor.Blue)
  }
  private[wurth] case object Black extends WurthSilkscreenColor {
    override val value: Int                   = 5
    override val toPCB: props.SilkscreenColor = props.SilkscreenColor(SilkscreenColor.Black)
  }
}
