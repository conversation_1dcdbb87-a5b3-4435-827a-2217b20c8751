package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Micrometers
import de.fellows.microservices.pcb.model.pcb.capability.SetValueCapability

trait OuterCopperThickness extends PCBRequiredProperty[Micrometers] {
  val value: Micrometers
  val fieldName: String = OuterCopperThickness.name
  val label: String     = OuterCopperThickness.label
  val unit: String      = "µm"

  override val legacyNames: Option[Seq[String]] = Some(Seq("outercopperheight"))
}

object OuterCopperThickness {
  val name: String  = "outerCopperThickness"
  val label: String = "pcb.layer.outerCopperThickness"

  def apply(value: Micrometers): OuterCopperThickness         = apply(Some(value))
  def apply(value: Option[Micrometers]): OuterCopperThickness = value.map(convert).getOrElse(OuterNone)

  private def convert(value: Micrometers): OuterCopperThickness = value match {
    case s if s <= 5   => Outer5mcr
    case s if s <= 9   => Outer9mcr
    case s if s <= 12  => Outer12mcr
    case s if s <= 18  => Outer18mcr
    case s if s <= 25  => Outer25mcr
    case s if s <= 35  => Outer35mcr
    case s if s <= 43  => Outer43mcr
    case s if s <= 50  => Outer50mcr
    case s if s <= 70  => Outer70mcr
    case s if s <= 105 => Outer105mcr
    case s if s <= 140 => Outer140mcr
    case s if s <= 175 => Outer175mcr
    case s if s <= 210 => Outer210mcr
    case s if s <= 245 => Outer245mcr
    case s if s <= 280 => Outer280mcr
    case s if s <= 315 => Outer315mcr
    case s if s <= 350 => Outer350mcr
    case s if s <= 385 => Outer385mcr
    case s if s <= 400 => Outer400mcr
    case s if s <= 455 => Outer455mcr
    case s if s <= 490 => Outer490mcr
    case s if s <= 525 => Outer525mcr
    case _             => Outer560mcr
  }

  type OuterCopperThicknessCapability = SetValueCapability[OuterCopperThickness]

  val values = Seq(
    Outer5mcr,
    Outer9mcr,
    Outer12mcr,
    Outer18mcr,
    Outer25mcr,
    Outer35mcr,
    Outer43mcr,
    Outer50mcr,
    Outer70mcr,
    Outer105mcr,
    Outer140mcr,
    Outer175mcr,
    Outer210mcr,
    Outer245mcr,
    Outer280mcr,
    Outer315mcr,
    Outer350mcr,
    Outer385mcr,
    Outer400mcr,
    Outer455mcr,
    Outer490mcr,
    Outer525mcr,
    Outer560mcr
  )
}

case object OuterNone extends OuterCopperThickness {
  val value: Micrometers = -1
}
case object Outer5mcr extends OuterCopperThickness {
  val value: Micrometers = 5
}
case object Outer9mcr extends OuterCopperThickness {
  val value: Micrometers = 9
}
case object Outer12mcr extends OuterCopperThickness {
  val value: Micrometers = 12
}
case object Outer18mcr extends OuterCopperThickness {
  val value: Micrometers = 18
}
case object Outer25mcr extends OuterCopperThickness {
  val value: Micrometers = 25
}
case object Outer35mcr extends OuterCopperThickness {
  val value: Micrometers = 35
}
case object Outer43mcr extends OuterCopperThickness {
  val value: Micrometers = 43
}
case object Outer50mcr extends OuterCopperThickness {
  val value: Micrometers = 50
}
case object Outer70mcr extends OuterCopperThickness {
  val value: Micrometers = 70
}
case object Outer105mcr extends OuterCopperThickness {
  val value: Micrometers = 105
}
case object Outer140mcr extends OuterCopperThickness {
  val value: Micrometers = 140
}
case object Outer175mcr extends OuterCopperThickness {
  val value: Micrometers = 175
}
case object Outer210mcr extends OuterCopperThickness {
  val value: Micrometers = 210
}
case object Outer245mcr extends OuterCopperThickness {
  val value: Micrometers = 245
}
case object Outer280mcr extends OuterCopperThickness {
  val value: Micrometers = 280
}
case object Outer315mcr extends OuterCopperThickness {
  val value: Micrometers = 315
}
case object Outer350mcr extends OuterCopperThickness {
  val value: Micrometers = 350
}
case object Outer385mcr extends OuterCopperThickness {
  val value: Micrometers = 385
}
case object Outer400mcr extends OuterCopperThickness {
  val value: Micrometers = 400
}
case object Outer455mcr extends OuterCopperThickness {
  val value: Micrometers = 455
}
case object Outer490mcr extends OuterCopperThickness {
  val value: Micrometers = 490
}
case object Outer525mcr extends OuterCopperThickness {
  val value: Micrometers = 525
}
case object Outer560mcr extends OuterCopperThickness {
  val value: Micrometers = 560
}
