package de.fellows.microservices.pcb.model.lq

import de.fellows.app.assemby.api.FileTypeUpdate
import de.fellows.microservices.pcb.model.FileName
import play.api.libs.json.{Format, Json, Reads}

case class FileTypeUpdateRequest(
    updates: Seq[FileTypeUpdate]
)

object FileTypeUpdateRequest {
  implicit val format: Format[FileTypeUpdateRequest] = Json.format[FileTypeUpdateRequest]
}

final case class DeleteFileRequest(
    files: Seq[FileName]
)

object DeleteFileRequest {
  implicit val reads: Reads[DeleteFileRequest] = Json.reads[DeleteFileRequest]
}
