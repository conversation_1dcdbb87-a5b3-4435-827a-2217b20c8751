package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.{ViaFillingType => PCBViaFilling}
import de.fellows.microservices.pcb.model.pcb.capability.SetCapability

object ViaFillingType {
  val name: String  = "viaFillingType"
  val label: String = "pcb.mechanical.viaFillingType"

  /** According to this specification, https://www.notion.so/luminovo/PCB-Specification-4493fa95f17e4bbf81fe2a6c59259e82
    * the default value for viaFillingType is None
    */
  def default: ViaFillingType                             = ViaFillingType(PCBViaFilling.None)
  def apply(value: Option[PCBViaFilling]): ViaFillingType = value.map(ViaFillingType(_)).getOrElse(default)
  type ViaFillingTypeCapability = SetCapability[ViaFillingType, PCBViaFilling]
}

final case class ViaFillingType(override val value: PCBViaFilling)
    extends PCBRequiredProperty[PCBViaFilling] {
  val fieldName: String = ViaFillingType.name
  val label: String     = ViaFillingType.label
  val unit: String      = ""

  override val legacyNames: Option[Seq[String]] = Some(Seq("via_protection", "viafilling"))
}
