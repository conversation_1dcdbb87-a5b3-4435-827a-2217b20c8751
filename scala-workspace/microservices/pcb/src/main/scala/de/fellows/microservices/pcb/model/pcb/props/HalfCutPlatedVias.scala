package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.YesNoCapability

object HalfCutPlatedVias {
  val name: String  = "halfCutPlatedVias"
  val label: String = "pcb.board.advanced.halfCutPlatedVias"

  val yes: HalfCutPlatedVias = HalfCutPlatedVias(true)
  val no: HalfCutPlatedVias  = HalfCutPlatedVias(false)

  def apply(value: Option[Boolean]): HalfCutPlatedVias =
    value.fold(no)(HalfCutPlatedVias(_))
    
  type HalfCutPlatedViasCapability = YesNoCapability[HalfCutPlatedVias]
}

final case class HalfCutPlatedVias(override val value: Boolean) extends YesNoPCBProperty {
  val fieldName: String = HalfCutPlatedVias.name
  val label: String     = HalfCutPlatedVias.label
  
  override val legacyNames: Option[Seq[String]] = Some(Seq("half_cutted_plated_vias", "half_cut_plated_vias"))
} 