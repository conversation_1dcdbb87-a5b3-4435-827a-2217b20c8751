package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.{
  BaseMaterial => PCBBaseMaterial,
  LayerstackType => SpecLayerstackType,
  Side,
  ULMarkingTypeEnum
}
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.No
import de.fellows.microservices.pcb.model.pcb.capability._
import de.fellows.microservices.pcb.model.pcb.props.BaseMaterial.BaseMaterialCapability
import de.fellows.microservices.pcb.model.pcb.props.FinalThickness.FinalThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.HardGold.HardGoldCapability
import de.fellows.microservices.pcb.model.pcb.props.InnerCopperThickness.InnerCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.LayerstackType.LayerstackTypeCapability
import de.fellows.microservices.pcb.model.pcb.props.MinInnerLayerStructure.MinInnerLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.MinOuterLayerStructure.MinOuterLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.NumberOfLayers.NumberOfLayersCapability
import de.fellows.microservices.pcb.model.pcb.props.OuterCopperThickness.OuterCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.SilkscreenSide.SilkscreenSideCapability
import de.fellows.microservices.pcb.model.pcb.props.ULLayerStack.ULLayerStackCapability
import de.fellows.microservices.pcb.model.pcb.props.ULMarkingType.ULMarkingTypeCapability
import de.fellows.microservices.pcb.model.pcb.props._

object WurthFlexCapability {

  private val thicknessConditions = Seq(
    Condition(
      Rule.Eq(FinalThickness(0.17)),
      new NumberOfLayersCapability(props.OneLayer, props.TwoLayers)
    ),
    Condition(
      Rule.Eq(props.NumberOfLayers(2)),
      new FinalThicknessCapability(FinalThickness(0.17))
    )
  )

  val conditionalCapabilities = thicknessConditions

  val layerStackCapability = LayerStackCapability(
    new LayerstackTypeCapability(SpecLayerstackType.Flex),
    new ULLayerStackCapability(No),
    new NumberOfLayersCapability(props.OneLayer),
    new FinalThicknessCapability(FinalThickness(0.12)),
    new BaseMaterialCapability(PCBBaseMaterial.Polyimide),
    new OuterCopperThicknessCapability(Outer18mcr, Outer35mcr),
    new InnerCopperThicknessCapability(),
    MinOuterLayerStructureCapability(0.100),
    MinInnerLayerStructureCapability(0.250),
    ulMarking = new ULMarkingTypeCapability(ULMarkingTypeEnum.NoMarking)
  )

  val capability = ManufacturerCapability(
    boardBasic = WurthCapability.basicBoardCapability.copy(
      silkscreenSide = new SilkscreenSideCapability(Side.None),
      hardGold = new HardGoldCapability(No)
    ),
    boardAdvanced = WurthCapability.advancedBoardCapability,
    layerStack = layerStackCapability,
    mechanical = WurthRigidFlexCapability.mechanicalCapability,
    alternativeConditions = conditionalCapabilities
  )
}
