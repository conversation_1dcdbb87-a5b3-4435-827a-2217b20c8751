package de.fellows.microservices.pcb.model.pcb.props

object Ecobond {
  val name: String  = "ecobond"
  val label: String = "pcb.board.advanced.ecobond"

  val yes: Ecobond = Ecobond(true)
  val no: Ecobond  = Ecobond(false)

  def apply(value: Option[Boolean]): Ecobond =
    value.fold(no)(Ecobond(_))
}

final case class Ecobond(override val value: Boolean) extends YesNoPCBProperty {
  val fieldName: String = Ecobond.name
  val label: String     = Ecobond.label
} 