package de.fellows.microservices.pcb.model.pcb.props

final case class ImpedanceTolerance(override val value: Option[BigDecimal]) extends DecimalPCBProperty {
  override val legacyNames: Option[Seq[String]] = Some(Seq("impedance_tolerance"))
  override val unit: String                     = ""
  val fieldName: String                         = ImpedanceTolerance.name
  val label: String                             = ImpedanceTolerance.label
}

object ImpedanceTolerance {
  val name: String             = "impedanceTolerance"
  val label: String            = "pcb.board.advanced.impedanceTolerance"
  val none: ImpedanceTolerance = ImpedanceTolerance(None)
}
