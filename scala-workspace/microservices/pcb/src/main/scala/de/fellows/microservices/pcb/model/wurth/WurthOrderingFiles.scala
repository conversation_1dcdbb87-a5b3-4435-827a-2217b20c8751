package de.fellows.microservices.pcb.model.wurth

// TODO: this doesn't sound like a good name
sealed trait WurthOrderingFiles {
  val value: Int
}

object WurthOrderingFiles {
  case object WurthNoOrdering extends WurthOrderingFiles {
    override val value: Int = 0
  }

  case object <PERSON><PERSON>PasteFiles extends WurthOrderingFiles {
    override val value: Int = 1
  }

  case object <PERSON><PERSON>WorkingGerber extends WurthOrderingFiles {
    override val value: Int = 2
  }
}
