package de.fellows.microservices.pcb.model

import com.osinka.i18n.Lang
import de.fellows.microservices.pcb.model.panel.PanelInfo
import de.fellows.luminovo.panel.Depanelization
import de.fellows.microservices.pcb.model.pcb.{Convert, PCB, Quantity}
import de.fellows.microservices.pcb.model.pcb.props._
import de.fellows.microservices.pcb.model.pcb.props.MillimeterPCBPropertyOps._
import de.fellows.ems.pcb.api.specification.{
  Chamfering => ChamferingEnum,
  IPC600Class,
  Side,
  SilkscreenColor => SilkscreenColorEnum,
  SoldermaskColor => SoldermaskColorEnum,
  SurfaceFinish => SurfaceFinishEnum,
  ViaFillingType => ViaFillingTypeEnum
}
import de.fellows.microservices.pcb.PropertyErrors
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.lq.ExistingOffer
import enumeratum._
import play.api.libs.json._
import zio.prelude.Validation

import java.util.UUID

package object gatema {

  final case class AuthenticationRequest(
      UserName: String,
      password: String,
      LanguageId: String,
      DbProfile: String,
      UseWindowsAuthentication: Boolean,
      UseCurrentUserCredentials: Boolean,
      ServerURL: String
  )

  object AuthenticationRequest {
    implicit val writes: Writes[AuthenticationRequest] = Json.writes[AuthenticationRequest]
  }

  final case class AuthenticationResponse(
      success: Boolean,
      statusCode: String,
      errorMessage: String,
      userName: String,
      userId: String
  )

  object AuthenticationResponse {
    implicit val reads: Reads[AuthenticationResponse] = Json.reads[AuthenticationResponse]
  }

  final case class GatemaToken(username: String, userId: String)

  final case class GatemaPcbRequest(
      turnAround: Seq[GatemaTurnAround],
      quantity: Seq[Quantity],
      customerId: String,
      pcbId: String,
      pcbSpecificationUpdate: Boolean,
      priceOfferUpdateReference: Option[String],
      pcbCharacteristics: GatemaPcbCharacteristics,
      pcbSpecification: GatemaPcbSpecification,
      layerStack: GatemaLayerStack,
      panel: Option[GatemaPanel]
  ) {
    def withPanel(calculatedPanelInfo: CalculatedPanelInfo): GatemaPcbRequest =
      copy(
        pcbId = UUID.randomUUID().toString,
        panel = Some(GatemaPanel.fromCalculatedPanelInfo(calculatedPanelInfo))
      )

    def withQuantities(quantities: Seq[PanelInfo]): GatemaPcbRequest =
      copy(
        turnAround = quantities.map(_ => GatemaTurnAround.STD),
        quantity = quantities.map(q => Quantity(q.totalPcbs.value))
      )
  }

  object GatemaPcbRequest {
    def validateAndConvert(
        pcb: PCB,
        quantities: Seq[Quantity],
        existingOffer: Option[ExistingOffer],
        customerId: String
    )(implicit lang: Lang): Either[PropertyErrors, GatemaPcbRequest] = {
      val props          = pcb.properties
      val numberOfLayers = props.layer.numberOfLayers
      Validation.validateWith(
        Validation.succeed(pcb),
        Validation.succeed(quantities),
        Validation.succeed(existingOffer),
        Validation.succeed(customerId),
        Convert.convertRequired(props.layer.numberOfLayers, GatemaNumberOfLayers.apply),
        Convert.convert(props.layer.tgValue, GatemaTgValue.converter),
        Convert.convert(props.basic.outerCopperClearance, GatemaMinOuterCopperClearance.converter),
        Convert.convert(props.basic.innerCopperClearance, GatemaMinInnerCopperClearance.converter(numberOfLayers)),
        Convert.convert(props.basic.outerTraceWidth, GatemaMinOuterTraceWidth.converter),
        Convert.convert(props.basic.innerTraceWidth, GatemaMinInnerTraceWidth.converter(numberOfLayers)),
        Validation.succeed(GatemaPhAnnularRing(200)), // TODO - we don't compute this yet in stackrate
        Convert.convertRequired(props.basic.soldermaskColor, GatemaSoldermaskColor.converter),
        Convert.convertRequired(props.basic.silkscreenColor, GatemaSilkscreenColor.converter),
        Convert.convertRequired(props.basic.surfaceFinish, GatemaSurfaceFinish.converter)
      )(createPCB).fold(
        errors => Left(PropertyErrors(errors)),
        pcb => Right(pcb)
      )
    }

    implicit val quantityWrites: Writes[Quantity] = valueWrites(_.value)
    implicit val writes: Writes[GatemaPcbRequest] = Json.writes[GatemaPcbRequest]
  }

  final case class GatemaPcbResponse(
      pcbId: String,
      priceOfferReference: String,
      turnAround: Seq[GatemaTurnAround],
      quantity: Seq[Int],
      leadTime: Seq[Int],
      unitPrice: Seq[BigDecimal],
      tooling: Seq[BigDecimal]
  )

  object GatemaPcbResponse {
    implicit val reads: Reads[GatemaPcbResponse] = Json.reads[GatemaPcbResponse]
  }

  sealed trait GatemaTurnAround extends EnumEntry
  object GatemaTurnAround extends Enum[GatemaTurnAround] with PlayJsonEnum[GatemaTurnAround] {
    val values: IndexedSeq[GatemaTurnAround] = findValues

    case object STD extends GatemaTurnAround
    case object QTA extends GatemaTurnAround
  }

  private def createPCB(
      pcb: PCB,
      quantities: Seq[Quantity],
      existingOffer: Option[ExistingOffer],
      customerId: String,
      numberOfLayers: GatemaNumberOfLayers,
      tgValue: GatemaTgValue,
      minOuterCopperClearance: GatemaMinOuterCopperClearance,
      minInnerCopperClearance: GatemaMinInnerCopperClearance,
      minOuterTraceWidth: GatemaMinOuterTraceWidth,
      minInnerTraceWidth: GatemaMinInnerTraceWidth,
      phAnnularRing: GatemaPhAnnularRing,
      soldermaskColor: GatemaSoldermaskColor,
      silkscreenColor: GatemaSilkscreenColor,
      surfaceFinish: GatemaSurfaceFinish
  ): GatemaPcbRequest = {
    val props = pcb.properties
    val pcbCharacteristics = GatemaPcbCharacteristics(
      dpSname = pcb.name.getOrElse("pcb"),
      height = props.basic.boardHeight.value.doubleValue,
      width = props.basic.boardWidth.value.doubleValue,
      layerCount = numberOfLayers,
      minOuterCopperClearance = minOuterCopperClearance,
      minInnerCopperClearance = minInnerCopperClearance,
      minOuterTraceWidth = minOuterTraceWidth,
      minInnerTraceWidth = minInnerTraceWidth,
      phAnnularRing = phAnnularRing,
      hasBuriedVias = props.mechanical.buriedVias.value,
      hasBlindVias = GatemaBlindVias.converter(props.mechanical.blindVias)
    )
    val hasChamfering = props.mechanical.chamfering.value != ChamferingEnum.None
    val hasViaFilling = props.mechanical.viaFillingType.value != ViaFillingTypeEnum.None

    val pcbSpecification = GatemaPcbSpecification(
      soldermaskColor = soldermaskColor,
      soldermaskSides = GatemaSoldermaskSides.converter(props.basic.soldermaskSide),
      silkscreenColor = silkscreenColor,
      silkscreenSides = GatemaSilkscreenSides.converter(props.basic.silkscreenSide),
      surfaceFinish = surfaceFinish,
      ipc600Class = GatemaIpc600Class.converter(props.advanced.ipcA600Class),
      pressFit = props.advanced.pressFit.value,
      peelableMask = GatemaPeelableMaskSides.converter(props.advanced.peelableMask),
      chamfering = hasChamfering,
      zAxisMilling = GatemaZAxisMilling(GatemaSides2.None),
      platedThroughZaxisMilling = GatemaPlatedThroughZAxisMilling(GatemaSides2.None), // TODO
      routingEdgeMetalization = props.advanced.edgeMetalization.value,
      pthHoleMetalization = false, // TODO
      hardGold = GatemaHardGold.converter(props.basic.hardGold),
      firstSampleTestReport = false, // TODO
      cocReport = true,              // TODO
      crossSectionReport = true,     // TODO
      carbonPrint = GatemaCarbonPrint.converter(props.advanced.carbonPrint),
      viaFilling = hasViaFilling
    )

    val layerStack = GatemaLayerStack(
      finalThickness = props.layer.finalThickness.value.doubleValue,
      impedanceControl = props.advanced.impedanceTested.value, // TODO
      outerCopperThickness = GatemaOuterCopperThickness.converter(props.layer.outerCopperThickness),
      innerCopperThickness = GatemaInnerCopperThickness.converter(props.layer.innerCopperThickness),
      tgValue = tgValue
    )

    // TODO: if we want to reuse the same pcb
    // val pcbSpecificationUpdate    = existingOffer.isDefined
    // val priceOfferUpdateReference = existingOffer.map(_.offerNumber)

    GatemaPcbRequest(
      turnAround = quantities.map(_ => GatemaTurnAround.STD),
      quantity = quantities,
      customerId = customerId,
      pcbId = UUID.randomUUID().toString,
      pcbSpecificationUpdate = false,
      priceOfferUpdateReference = None,
      pcbCharacteristics = pcbCharacteristics,
      pcbSpecification = pcbSpecification,
      layerStack = layerStack,
      panel = None
    )
  }

  final case class GatemaPcbCharacteristics(
      dpSname: String,
      height: Double,
      width: Double,
      layerCount: GatemaNumberOfLayers,
      minOuterCopperClearance: GatemaMinOuterCopperClearance,
      minInnerCopperClearance: GatemaMinInnerCopperClearance,
      minOuterTraceWidth: GatemaMinOuterTraceWidth,
      minInnerTraceWidth: GatemaMinInnerTraceWidth,
      phAnnularRing: GatemaPhAnnularRing,
      hasBuriedVias: Boolean,
      hasBlindVias: GatemaBlindVias
  )

  object GatemaPcbCharacteristics {
    implicit val writes: Writes[GatemaPcbCharacteristics] = Json.writes[GatemaPcbCharacteristics]
  }

  final case class GatemaPcbSpecification(
      soldermaskColor: GatemaSoldermaskColor,
      soldermaskSides: GatemaSoldermaskSides,
      silkscreenColor: GatemaSilkscreenColor,
      silkscreenSides: GatemaSilkscreenSides,
      surfaceFinish: GatemaSurfaceFinish,
      ipc600Class: Option[GatemaIpc600Class],
      pressFit: Boolean,
      peelableMask: GatemaPeelableMaskSides,
      chamfering: Boolean,
      zAxisMilling: GatemaZAxisMilling,
      platedThroughZaxisMilling: GatemaPlatedThroughZAxisMilling,
      routingEdgeMetalization: Boolean,
      pthHoleMetalization: Boolean,
      hardGold: GatemaHardGold,
      firstSampleTestReport: Boolean,
      cocReport: Boolean,
      crossSectionReport: Boolean,
      carbonPrint: GatemaCarbonPrint,
      viaFilling: Boolean
  )

  object GatemaPcbSpecification {
    implicit val writes: Writes[GatemaPcbSpecification] = Json.writes[GatemaPcbSpecification]
  }

  final case class GatemaLayerStack(
      finalThickness: Double,
      impedanceControl: Boolean,
      outerCopperThickness: GatemaOuterCopperThickness,
      innerCopperThickness: GatemaInnerCopperThickness,
      tgValue: GatemaTgValue
  )

  object GatemaLayerStack {
    implicit val writes: Writes[GatemaLayerStack] = Json.writes[GatemaLayerStack]
  }

  final case class GatemaPanel(
      panelWidth: Double,
      panelHeight: Double,
      panelMultiplicity: Int,
      panelSeparation: GatemaDepanelization
  )

  object GatemaPanel {
    def fromCalculatedPanelInfo(calculatedPanelInfo: CalculatedPanelInfo): GatemaPanel = {
      val (
        panelWidth,
        panelHeight,
        pcbsPerPanel,
        depanelization
      ) =
        calculatedPanelInfo match {
          case p: CalculatedPanelInfo.FromPanelDetails =>
            (
              p.panelDistribution.panel.widthInMm,
              p.panelDistribution.panel.heightInMm,
              p.panelDistribution.mesh.size,
              p.depanelization
            )
          case p: CalculatedPanelInfo.FromExisting =>
            (
              p.existing.panelWidth,
              p.existing.panelHeight,
              p.existing.numberOfPcbs,
              p.existing.depanelization
            )
        }
      GatemaPanel(
        panelWidth = panelWidth.doubleValue,
        panelHeight = panelHeight.doubleValue,
        panelMultiplicity = pcbsPerPanel,
        panelSeparation = GatemaDepanelization.converter(depanelization)
      )
    }

    implicit val writes: Writes[GatemaPanel] = Json.writes[GatemaPanel]
  }

  final case class GatemaNumberOfLayers(value: Int) extends AnyVal
  object GatemaNumberOfLayers {
    def apply(numberOfLayers: NumberOfLayers): Option[GatemaNumberOfLayers] =
      numberOfLayers match {
        case TwoLayers   => Some(GatemaNumberOfLayers(2))
        case FourLayers  => Some(GatemaNumberOfLayers(4))
        case SixLayers   => Some(GatemaNumberOfLayers(6))
        case EightLayers => Some(GatemaNumberOfLayers(8))
        case TenLayers   => Some(GatemaNumberOfLayers(10))
        case _           => None
      }

    implicit val writes: Writes[GatemaNumberOfLayers] = valueWrites(_.value)
  }

  final case class GatemaMinOuterCopperClearance(value: Micrometers)
  object GatemaMinOuterCopperClearance {
    def converter(outerCopperClearance: OuterCopperClearance): Option[GatemaMinOuterCopperClearance] =
      outerCopperClearance.toMicrometers.map(GatemaMinOuterCopperClearance.apply)

    implicit val writes: Writes[GatemaMinOuterCopperClearance] = valueWrites(_.value)
  }

  final case class GatemaMinInnerCopperClearance(value: Int)
  object GatemaMinInnerCopperClearance {
    def converter(numberOfLayers: NumberOfLayers)(
        innerCopperClearance: InnerCopperClearance
    ): Option[GatemaMinInnerCopperClearance] =
      if (numberOfLayers.value > 2)
        innerCopperClearance
          .toMicrometers
          .map(GatemaMinInnerCopperClearance.apply)
      else
        Some(GatemaMinInnerCopperClearance(0))

    implicit val writes: Writes[GatemaMinInnerCopperClearance] = valueWrites(_.value)
  }

  final case class GatemaMinOuterTraceWidth(value: Micrometers)
  object GatemaMinOuterTraceWidth {
    def converter(minOuterTraceWidth: OuterTraceWidth): Option[GatemaMinOuterTraceWidth] =
      minOuterTraceWidth.toMicrometers.map(GatemaMinOuterTraceWidth.apply)

    implicit val writes: Writes[GatemaMinOuterTraceWidth] = valueWrites(_.value)
  }

  final case class GatemaMinInnerTraceWidth(value: Micrometers)
  object GatemaMinInnerTraceWidth {
    def converter(numberOfLayers: NumberOfLayers)(
        minInnerTraceWidth: InnerTraceWidth
    ): Option[GatemaMinInnerTraceWidth] =
      if (numberOfLayers.value > 2)
        minInnerTraceWidth.toMicrometers.map(GatemaMinInnerTraceWidth.apply)
      else
        Some(GatemaMinInnerTraceWidth(0))

    implicit val writes: Writes[GatemaMinInnerTraceWidth] = valueWrites(_.value)
  }

  final case class GatemaPhAnnularRing(value: Micrometers)
  object GatemaPhAnnularRing {
    implicit val writes: Writes[GatemaPhAnnularRing] = valueWrites(_.value)
  }

  sealed abstract class GatemaBlindVias(val value: Int) extends EnumEntry
  object GatemaBlindVias extends Enum[GatemaBlindVias] {

    def converter(blindVias: BlindVias): GatemaBlindVias =
      if (blindVias.value)
        GatemaBlindVias.Both
      else
        GatemaBlindVias.None

    val values: IndexedSeq[GatemaBlindVias] = findValues

    case object Top    extends GatemaBlindVias(1)
    case object Bottom extends GatemaBlindVias(2)
    case object Both   extends GatemaBlindVias(3)
    case object None   extends GatemaBlindVias(4)

    implicit val writes: Writes[GatemaBlindVias] = valueWrites(_.value)
  }

  // 1-green, 2-black, 3-white, 4-red, 5-blue
  sealed abstract class GatemaSoldermaskColor(val value: Int) extends EnumEntry
  object GatemaSoldermaskColor extends Enum[GatemaSoldermaskColor] {
    val values: IndexedSeq[GatemaSoldermaskColor] = findValues

    def converter(soldermaskColor: SoldermaskColor): Option[GatemaSoldermaskColor] =
      soldermaskColor.value match {
        case SoldermaskColorEnum.Green => Some(GatemaSoldermaskColor.Green)
        case SoldermaskColorEnum.Black => Some(GatemaSoldermaskColor.Black)
        case SoldermaskColorEnum.White => Some(GatemaSoldermaskColor.White)
        case SoldermaskColorEnum.Red   => Some(GatemaSoldermaskColor.Red)
        case SoldermaskColorEnum.Blue  => Some(GatemaSoldermaskColor.Blue)
        case _                         => None
      }

    case object Green extends GatemaSoldermaskColor(1)
    case object Black extends GatemaSoldermaskColor(2)
    case object White extends GatemaSoldermaskColor(3)
    case object Red   extends GatemaSoldermaskColor(4)
    case object Blue  extends GatemaSoldermaskColor(5)

    implicit val writes: Writes[GatemaSoldermaskColor] = valueWrites(_.value)
  }

  // 1-set for both sides, 2-set for top only, 3- set for bottom only 4 - nothing / none
  final case class GatemaSoldermaskSides(sides: GatemaSides1) { def value: Int = sides.value }

  object GatemaSoldermaskSides {
    def converter(soldermaskSide: SoldermaskSide): GatemaSoldermaskSides =
      GatemaSoldermaskSides(
        GatemaSides1.converter(soldermaskSide.value)
      )

    implicit val writes: Writes[GatemaSoldermaskSides] = valueWrites(_.value)
  }

  // 1-white, 2-yellow, 3-black
  sealed abstract class GatemaSilkscreenColor(val value: Int) extends EnumEntry
  object GatemaSilkscreenColor extends Enum[GatemaSilkscreenColor] {
    val values: IndexedSeq[GatemaSilkscreenColor] = findValues

    def converter(silkscreenColor: SilkscreenColor): Option[GatemaSilkscreenColor] =
      silkscreenColor.value match {
        case SilkscreenColorEnum.White  => Some(GatemaSilkscreenColor.White)
        case SilkscreenColorEnum.Yellow => Some(GatemaSilkscreenColor.Yellow)
        case SilkscreenColorEnum.Black  => Some(GatemaSilkscreenColor.Black)
        case _                          => None
      }

    case object White  extends GatemaSilkscreenColor(1)
    case object Yellow extends GatemaSilkscreenColor(2)
    case object Black  extends GatemaSilkscreenColor(3)

    implicit val writes: Writes[GatemaSilkscreenColor] = valueWrites(_.value)
  }

  // 1-set for both, 2-set only for top, 3-set only for bottom, 4-nothing / none
  final case class GatemaSilkscreenSides(sides: GatemaSides1) { def value: Int = sides.value }

  object GatemaSilkscreenSides {
    def converter(silkscreenSide: SilkscreenSide): GatemaSilkscreenSides =
      GatemaSilkscreenSides(
        GatemaSides1.converter(silkscreenSide.value)
      )

    implicit val writes: Writes[GatemaSilkscreenSides] = valueWrites(_.value)
  }

  // TODO: rename
  sealed abstract class GatemaSides1(val value: Int) extends EnumEntry
  object GatemaSides1 extends Enum[GatemaSides1] {
    val values: IndexedSeq[GatemaSides1] = findValues

    def converter(side: Side): GatemaSides1 =
      side match {
        case Side.Both   => GatemaSides1.Both
        case Side.Top    => GatemaSides1.Top
        case Side.Bottom => GatemaSides1.Bottom
        case Side.None   => GatemaSides1.None
      }

    case object Both   extends GatemaSides1(1)
    case object Top    extends GatemaSides1(2)
    case object Bottom extends GatemaSides1(3)
    case object None   extends GatemaSides1(4)
  }

  // 1-Immersion gold, 2-Lead Free Hal, 3-Leaded HAL, 4-Immersion tin
  sealed abstract class GatemaSurfaceFinish(val value: Int) extends EnumEntry
  object GatemaSurfaceFinish extends Enum[GatemaSurfaceFinish] {
    val values: IndexedSeq[GatemaSurfaceFinish] = findValues

    def converter(surfaceFinish: SurfaceFinish): Option[GatemaSurfaceFinish] =
      surfaceFinish.value match {
        case SurfaceFinishEnum.Enig      => Some(GatemaSurfaceFinish.ImmersionGold)
        case SurfaceFinishEnum.HalPbFree => Some(GatemaSurfaceFinish.LeadFreeHal)
        case SurfaceFinishEnum.HalPb     => Some(GatemaSurfaceFinish.LeadedHal)
        case SurfaceFinishEnum.Is        => Some(GatemaSurfaceFinish.ImmersionTin)
        case _                           => None
      }

    case object ImmersionGold extends GatemaSurfaceFinish(1)
    case object LeadFreeHal   extends GatemaSurfaceFinish(2)
    case object LeadedHal     extends GatemaSurfaceFinish(3)
    case object ImmersionTin  extends GatemaSurfaceFinish(4)

    implicit val writes: Writes[GatemaSurfaceFinish] = valueWrites(_.value)
  }

  // TODO
  // 1-set nothing, 2-set nothing, 3- in case of IPC CLASS 3 (ADDS 2 DAYS TO LT)
  sealed abstract class GatemaIpc600Class(val value: Int) extends EnumEntry
  object GatemaIpc600Class extends Enum[GatemaIpc600Class] {
    val values: IndexedSeq[GatemaIpc600Class] = findValues

    def converter(ipc600Class: IPCA600Class): Option[GatemaIpc600Class] =
      ipc600Class.value match {
        case IPC600Class.IPCNone  => None
        case IPC600Class.IPC1     => Some(GatemaIpc600Class.`1`)
        case IPC600Class.IPC2     => Some(GatemaIpc600Class.`2`)
        case IPC600Class.IPC2Plus => None
        case IPC600Class.IPC3     => Some(GatemaIpc600Class.`3`)
        case IPC600Class.IPC3a    => None
      }

    case object `1` extends GatemaIpc600Class(1)
    case object `2` extends GatemaIpc600Class(2)
    case object `3` extends GatemaIpc600Class(3)

    implicit val writes: Writes[GatemaIpc600Class] = valueWrites(_.value)
  }

  // 1 top, 2 bottom, 3 top + bottom, 4 none
  final case class GatemaPeelableMaskSides(sides: GatemaSides2) { def value: Int = sides.value }
  object GatemaPeelableMaskSides {
    def converter(peelableMask: PeelableMask): GatemaPeelableMaskSides =
      GatemaPeelableMaskSides(
        GatemaSides2.converter(peelableMask.value)
      )

    implicit val writes: Writes[GatemaPeelableMaskSides] = valueWrites(_.value)
  }

  final case class GatemaZAxisMilling(sides: GatemaSides2) { def value: Int = sides.value }
  object GatemaZAxisMilling {
    implicit val writes: Writes[GatemaZAxisMilling] = valueWrites(_.value)
  }

  final case class GatemaPlatedThroughZAxisMilling(sides: GatemaSides2) { def value: Int = sides.value }

  object GatemaPlatedThroughZAxisMilling {
    implicit val writes: Writes[GatemaPlatedThroughZAxisMilling] = valueWrites(_.value)
  }

  final case class GatemaHardGold(sides: GatemaSides2) { def value: Int = sides.value }
  object GatemaHardGold {
    def converter(hardGold: HardGold): GatemaHardGold =
      GatemaHardGold(
        if (hardGold.value)
          GatemaSides2.Both
        else
          GatemaSides2.None
      )

    implicit val writes: Writes[GatemaHardGold] = valueWrites(_.value)
  }

  final case class GatemaCarbonPrint(sides: GatemaSides2) { def value: Int = sides.value }
  object GatemaCarbonPrint {
    def converter(carbonPrint: CarbonPrint): GatemaCarbonPrint =
      GatemaCarbonPrint(
        GatemaSides2.converter(carbonPrint.value)
      )

    implicit val writes: Writes[GatemaCarbonPrint] = valueWrites(_.value)
  }

  // TODO: rename
  sealed abstract class GatemaSides2(val value: Int) extends EnumEntry
  object GatemaSides2 extends Enum[GatemaSides2] {
    val values: IndexedSeq[GatemaSides2] = findValues

    def converter(side: Side): GatemaSides2 =
      side match {
        case Side.Both   => GatemaSides2.Both
        case Side.Top    => GatemaSides2.Top
        case Side.Bottom => GatemaSides2.Bottom
        case Side.None   => GatemaSides2.None
      }

    case object Top    extends GatemaSides2(1)
    case object Bottom extends GatemaSides2(2)
    case object Both   extends GatemaSides2(3)
    case object None   extends GatemaSides2(4)
  }

  case class GatemaOuterCopperThickness(value: Micrometers)
  object GatemaOuterCopperThickness {
    def converter(outerCopperThickness: OuterCopperThickness): GatemaOuterCopperThickness =
      GatemaOuterCopperThickness(outerCopperThickness.value)

    implicit val writes: Writes[GatemaOuterCopperThickness] = valueWrites(_.value)
  }

  case class GatemaInnerCopperThickness(value: Micrometers)
  object GatemaInnerCopperThickness {
    def converter(innerCopperThickness: InnerCopperThickness): GatemaInnerCopperThickness = {
      val innerCopperThicknessValue = innerCopperThickness match {
        case InnerNone => 0
        case _         => innerCopperThickness.value
      }
      GatemaInnerCopperThickness(innerCopperThicknessValue)
    }

    implicit val writes: Writes[GatemaInnerCopperThickness] = valueWrites(_.value)
  }

  // 135-FR4 (Tg135), 150 - IS400 (Tg150), 180 - PCL370HR (Tg180)
  sealed abstract class GatemaTgValue(val value: Int) extends EnumEntry
  object GatemaTgValue extends Enum[GatemaTgValue] {
    val values: IndexedSeq[GatemaTgValue] = findValues

    def converter(tgValue: TGValue): Option[GatemaTgValue] =
      tgValue.value match {
        case Some(tg) if tg == 135 => Some(GatemaTgValue.Tg135)
        case Some(tg) if tg == 150 => Some(GatemaTgValue.Tg150)
        case Some(tg) if tg == 180 => Some(GatemaTgValue.Tg180)
        case _                     => None
      }

    case object Tg135 extends GatemaTgValue(135)
    case object Tg150 extends GatemaTgValue(150)
    case object Tg180 extends GatemaTgValue(180)

    implicit val writes: Writes[GatemaTgValue] = valueWrites(_.value)
  }

  // 1-  SINGLE PCS, 2- ROUTED ON TABS, 3- SCORING (+ ROUTING)
  sealed abstract class GatemaDepanelization(val value: Int) extends EnumEntry
  object GatemaDepanelization extends Enum[GatemaDepanelization] {
    val values: IndexedSeq[GatemaDepanelization] = findValues

    def converter(depanelization: Depanelization): GatemaDepanelization =
      depanelization match {
        case Depanelization.VCut           => GatemaDepanelization.RoutedOnTabs
        case Depanelization.Milling        => GatemaDepanelization.Scoring
        case Depanelization.MillingAndVCut => GatemaDepanelization.Scoring
      }

    case object SinglePcs    extends GatemaDepanelization(1)
    case object RoutedOnTabs extends GatemaDepanelization(2)
    case object Scoring      extends GatemaDepanelization(3)

    implicit val writes: Writes[GatemaDepanelization] = valueWrites(_.value)
  }

  private def valueWrites[T](f: T => Int): Writes[T] = Writes(value => JsNumber(f(value)))
}
