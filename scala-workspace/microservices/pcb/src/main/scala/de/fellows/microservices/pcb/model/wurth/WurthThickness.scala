package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props.FinalThickness

/** Name of param in Wurth API: thickness
  */
sealed trait WurthThickness {
  val value: Int
  val initialValue: FinalThickness

  override def equals(obj: Any): Boolean = obj match {
    case that: WurthThickness => that.value == value
    case _                    => false
  }
}

case class Mm080(override val initialValue: FinalThickness = FinalThickness(0.80)) extends WurthThickness {
  override val value: Int = 1
}

case class Mm100(override val initialValue: FinalThickness = FinalThickness(1.00)) extends WurthThickness {
  override val value: Int = 2
}

case class Mm155(override val initialValue: FinalThickness = FinalThickness(1.55)) extends WurthThickness {
  override val value: Int = 3
}

case class Mm200(override val initialValue: FinalThickness = FinalThickness(2.00)) extends WurthThickness {
  override val value: Int = 4
}

case class Mm240(override val initialValue: FinalThickness = FinalThickness(2.40)) extends WurthThickness {
  override val value: Int = 5
}

case class Mm320(override val initialValue: FinalThickness = FinalThickness(3.20)) extends WurthThickness {
  override val value: Int = 6
}

case class Mm012(override val initialValue: FinalThickness = FinalThickness(0.12)) extends WurthThickness {
  override val value: Int = 7
}

case class Mm017(override val initialValue: FinalThickness = FinalThickness(0.17)) extends WurthThickness {
  override val value: Int = 8
}

case class Mm050(override val initialValue: FinalThickness = FinalThickness(0.50)) extends WurthThickness {
  override val value: Int = 12
}

case class Mm120(override val initialValue: FinalThickness = FinalThickness(1.20)) extends WurthThickness {
  override val value: Int = 13
}

object WurthThickness {

  /** Converts PCB thickness to Wurth thickness.
    */
  def converter(value: FinalThickness): Option[WurthThickness] = {
    val toleranceFactor = 0.1 // 10% tolerance

    def isWithinUpperTolerance(target: Double, actual: Double): Boolean = {
      val upperBound = target * (1 + toleranceFactor)
      actual <= upperBound
    }

    value.value match {
      case t if isWithinUpperTolerance(0.12, t.toDouble) || t.toDouble <= 0.12 => Some(Mm012(value))
      case t if isWithinUpperTolerance(0.17, t.toDouble) || t.toDouble <= 0.17 => Some(Mm017(value))
      case t if isWithinUpperTolerance(0.50, t.toDouble) || t.toDouble <= 0.50 => Some(Mm050(value))
      case t if isWithinUpperTolerance(0.80, t.toDouble) || t.toDouble <= 0.80 => Some(Mm080(value))
      case t if isWithinUpperTolerance(1.00, t.toDouble) || t.toDouble <= 1.00 => Some(Mm100(value))
      case t if isWithinUpperTolerance(1.20, t.toDouble) || t.toDouble <= 1.20 => Some(Mm120(value))
      case t if isWithinUpperTolerance(1.55, t.toDouble) || t.toDouble <= 1.55 => Some(Mm155(value))
      case t if isWithinUpperTolerance(2.00, t.toDouble) || t.toDouble <= 2.00 => Some(Mm200(value))
      case t if isWithinUpperTolerance(2.40, t.toDouble) || t.toDouble <= 2.40 => Some(Mm240(value))
      case t if isWithinUpperTolerance(3.20, t.toDouble) || t.toDouble <= 3.20 => Some(Mm320(value))
      case _                                                                   => None
    }
  }

  def fromWurth(value: Int): WurthThickness = value match {
    case 1  => Mm080()
    case 2  => Mm100()
    case 3  => Mm155()
    case 4  => Mm200()
    case 5  => Mm240()
    case 6  => Mm320()
    case 7  => Mm012()
    case 8  => Mm017()
    case 12 => Mm050()
    case 13 => Mm120()
    case _  => throw new IllegalArgumentException(s"Value $value is not supported")
  }
}
