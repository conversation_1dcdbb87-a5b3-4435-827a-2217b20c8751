package de.fellows.microservices.pcb.model.ibr.api

import play.api.libs.functional.syntax.toFunctionalBuilderOps
import play.api.libs.json.{JsError, JsSuccess, <PERSON><PERSON>, Reads}
import play.api.libs.json._

import scala.util.{Failure, Success, Try}

case class IbrOffer(
    qty: Int,
    deliverytime: Int,
    origin: String,
    express: Int,
    price: BigDecimal
)

object IbrOffer {
  private implicit val safeIntParser: Reads[Int] = Reads { v =>
    val str = v.toString()
    Try(BigDecimal(str).intValue) match {
      case Failure(exception) => JsError(exception.getMessage)
      case Success(value)     => JsSuccess(value)
    }
  }

  private implicit val safeDecParser: Reads[BigDecimal] = Reads { v =>
    val str = v.toString()

    Try(BigDecimal(str)) match {
      case Failure(exception) => JsError(exception.getMessage)
      case Success(value)     => JsSuccess(value)
    }
  }

  val safe: Reads[IbrOffer] =
    ((__ \ "qty").read[Int] and
      (__ \ "deliverytime").read[Int] and
      (__ \ "origin").read[String] and
      (__ \ "express").read[Int] and
      (__ \ "price").read[BigDecimal])(IbrOffer.apply _)

  implicit val format: Format[IbrOffer] = Json.format[IbrOffer]
}
