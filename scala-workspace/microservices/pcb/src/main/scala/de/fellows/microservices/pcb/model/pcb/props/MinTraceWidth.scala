package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters

/** MinTraceWidth is the minimum of inner_trace_width and outer_trace_width, usually calculate
  * during the analysis of source files
  */
final case class MinTraceWidth(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = MinTraceWidth.name
  val label: String     = MinTraceWidth.label
}

object MinTraceWidth {
  val name: String  = "minTraceWidth"
  val label: String = "pcb.basic.minTraceWidth"

  def empty: MinTraceWidth                     = MinTraceWidth(None)
  def apply(value: Millimeters): MinTraceWidth = MinTraceWidth(Some(value))
}
