package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.Side
import de.fellows.ems.pcb.api.specification.Side.Both
import de.fellows.microservices.pcb.model.pcb.capability.SetCapability

object SilkscreenSide {
  val name: String  = "silkscreenSides"
  val label: String = "pcb.board.basic.silkscreenSides"

  /** According to this specification, https://www.notion.so/luminovo/PCB-Specification-4493fa95f17e4bbf81fe2a6c59259e82
    * the default value for silkscreenSides is Both
    */
  def default: SilkscreenSide = SilkscreenSide(Both)

  def apply(value: Option[Side]): SilkscreenSide = value.map(SilkscreenSide(_)).getOrElse(default)

  type SilkscreenSideCapability = SetCapability[SilkscreenSide, Side]
  val allSidesCapability = new SilkscreenSideCapability(Side.None, Side.Top, Side.Bottom, Side.Both)
}

final case class SilkscreenSide(override val value: Side) extends PCBRequiredProperty[Side] {
  val fieldName: String = SilkscreenSide.name
  val label: String     = SilkscreenSide.label
  val unit: String      = ""

  override val legacyNames: Option[Seq[String]] = Some(Seq("silkscreen_sides"))
}
