package de.fellows.microservices.pcb.model.ibr.api

import de.fellows.microservices.pcb.model.lq.{LeadTime, LeadTimePreference}

import java.time.{LocalDate, OffsetDateTime}
import java.time.temporal.ChronoUnit
import scala.util.Try

object IbrExpress {

  /** The fastest express delivery time in days that is supported by the IBR API.
    */
  val FASTEST_EXPRESS_LEAD_TIME = 3
  val MEDIUM_EXPRESS_LEAD_TIME  = 5

  /** Try to find a sane express delivery time for the given offer request.
    */
  def deliveryTime(leadTimes: Seq[LeadTime]): Option[Int] =
    leadTimes.headOption match {
      case Some(value) =>
        value.preference match {
          case LeadTimePreference.Fastest   => Some(FASTEST_EXPRESS_LEAD_TIME)
          case LeadTimePreference.BestPrice => Some(10)
          case LeadTimePreference.BestPriceByDate =>
            value.date match {
              case Some(value) =>
                Seq(
                  Try(LocalDate.parse(value)).toOption,
                  Try(OffsetDateTime.parse(value)).map(_.toLocalDate).toOption
                )
                  .flatten
                  .headOption
                  .map { date =>
                    val now = LocalDate.now()

                    val leadTime = ChronoUnit.DAYS.between(now, date)

                    Math.max(leadTime, 1).toInt
                  }
              case None =>
                Some(FASTEST_EXPRESS_LEAD_TIME)
            }
        }
      case None => Some(MEDIUM_EXPRESS_LEAD_TIME)
    }
}
