package de.fellows.microservices.pcb.model.wurth

import de.fellows.luminovo.panel.Depanelization

/** Name in Wurth API: multiple_image_contour
  */
sealed trait WurthDepanelization {
  val value: Int
}

private object WurthDepanelization {

  case object Milling extends WurthDepanelization {
    override val value: Int = 1
  }

  case object VCut extends WurthDepanelization {
    override val value: Int = 2
  }

  def fromPcb(depanelization: Depanelization): WurthDepanelization = depanelization match {
    case Depanelization.VCut           => VCut
    case Depanelization.Milling        => Milling
    case Depanelization.MillingAndVCut => Milling
  }

  def fromWurth(value: Int): WurthDepanelization = value match {
    case 2 => VCut
    case _ => Milling
  }
}
