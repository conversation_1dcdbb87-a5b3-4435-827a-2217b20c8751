package de.fellows.microservices.pcb.model.lq

import enumeratum._

sealed trait PriceType extends EnumEntry
object PriceType extends Enum[PriceType] with PlayJsonEnum[PriceType] {
  val values = findValues

  case object ListPrice     extends PriceType
  case object ContractPrice extends PriceType

  case object QuotePrice extends PriceType

  case object InventoryPrice extends PriceType

  case object PurchasePrice extends PriceType

}
