package de.fellows.microservices.pcb.model.pcb

import com.osinka.i18n.{Lang, Messages}
import de.fellows.microservices.pcb.model.pcb.props.{PCBOptionalProperty, PCBRequiredProperty}
import zio.prelude.Validation

object Convert {

  def convert[T <: PCBOptionalProperty[_], K](property: T, converter: T => Option[K])(implicit
      lang: Lang
  ): Validation[PropertyError, K] =
    converter(property) match {
      case Some(value) => Validation.succeed(value)
      case None =>
        val value = property.value match {
          case None    => "(empty)"
          case Some(v) => v.toString
        }
        Validation.fail(PropertyError(
          property,
          Messages("pcb.error.converter.notSupported", value, Messages(property.label)),
          PropertyErrorKind.ConversionError(
            value = Some(value),
            unit = Messages(property.label)
          )
        ))
    }

  def convertRequired[T <: PCBRequiredProperty[_], K](property: T, converter: T => Option[K])(implicit
      lang: Lang
  ): Validation[PropertyError, K] =
    converter(property) match {
      case Some(value) => Validation.succeed(value)
      case None =>
        val value = property.value.toString
        Validation.fail(PropertyError(
          property,
          Messages("pcb.error.converter.notSupported", value, Messages(property.label)),
          PropertyErrorKind.ConversionError(
            value = Some(value),
            unit = Messages(property.label)
          )
        ))
    }
}
