package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Micrometers
import de.fellows.microservices.pcb.model.pcb.capability.SetValueCapability

sealed trait InnerCopperThickness extends PCBRequiredProperty[Micrometers] {
  val value: Micrometers
  val fieldName: String = InnerCopperThickness.name
  val label: String     = InnerCopperThickness.label
  val unit: String      = "µm"

  override val legacyNames: Option[Seq[String]] = Some(Seq("innercopperheight"))
}

object InnerCopperThickness {
  val name: String  = "innerCopperThickness"
  val label: String = "pcb.layer.innerCopperThickness"

  def empty: InnerCopperThickness                              = apply(None)
  def apply(value: Micrometers): InnerCopperThickness          = apply(Some(value))
  def apply(value: Option[Micrometers]): InnerCopperThickness  = value.map(convert).getOrElse(InnerNone)
  def apply(value: InnerCopperThickness): InnerCopperThickness = value

  private def convert(value: Micrometers): InnerCopperThickness = value match {
    case s if s <= 5   => Inner5mcr
    case s if s <= 9   => Inner9mcr
    case s if s <= 12  => Inner12mcr
    case s if s <= 18  => Inner18mcr
    case s if s <= 25  => Inner25mcr
    case s if s <= 35  => Inner35mcr
    case s if s <= 43  => Inner43mcr
    case s if s <= 50  => Inner50mcr
    case s if s <= 70  => Inner70mcr
    case s if s <= 105 => Inner105mcr
    case s if s <= 140 => Inner140mcr
    case s if s <= 175 => Inner175mcr
    case s if s <= 210 => Inner210mcr
    case s if s <= 245 => Inner245mcr
    case s if s <= 280 => Inner280mcr
    case s if s <= 315 => Inner315mcr
    case s if s <= 350 => Inner350mcr
    case s if s <= 385 => Inner385mcr
    case s if s <= 400 => Inner400mcr
    case s if s <= 455 => Inner455mcr
    case s if s <= 490 => Inner490mcr
    case s if s <= 525 => Inner525mcr
    case _             => Inner560mcr
  }

  type InnerCopperThicknessCapability = SetValueCapability[InnerCopperThickness]
}

case object InnerNone extends InnerCopperThickness {
  val value: Micrometers = -1
}
case object Inner5mcr extends InnerCopperThickness {
  val value: Micrometers = 5
}
case object Inner9mcr extends InnerCopperThickness {
  val value: Micrometers = 9
}
case object Inner12mcr extends InnerCopperThickness {
  val value: Micrometers = 12
}
case object Inner18mcr extends InnerCopperThickness {
  val value: Micrometers = 18
}
case object Inner25mcr extends InnerCopperThickness {
  val value: Micrometers = 25
}
case object Inner35mcr extends InnerCopperThickness {
  val value: Micrometers = 35
}
case object Inner43mcr extends InnerCopperThickness {
  val value: Micrometers = 43
}
case object Inner50mcr extends InnerCopperThickness {
  val value: Micrometers = 50
}
case object Inner70mcr extends InnerCopperThickness {
  val value: Micrometers = 70
}
case object Inner105mcr extends InnerCopperThickness {
  val value: Micrometers = 105
}
case object Inner140mcr extends InnerCopperThickness {
  val value: Micrometers = 140
}
case object Inner175mcr extends InnerCopperThickness {
  val value: Micrometers = 175
}
case object Inner210mcr extends InnerCopperThickness {
  val value: Micrometers = 210
}
case object Inner245mcr extends InnerCopperThickness {
  val value: Micrometers = 245
}
case object Inner280mcr extends InnerCopperThickness {
  val value: Micrometers = 280
}
case object Inner315mcr extends InnerCopperThickness {
  val value: Micrometers = 315
}
case object Inner350mcr extends InnerCopperThickness {
  val value: Micrometers = 350
}
case object Inner385mcr extends InnerCopperThickness {
  val value: Micrometers = 385
}
case object Inner400mcr extends InnerCopperThickness {
  val value: Micrometers = 400
}
case object Inner455mcr extends InnerCopperThickness {
  val value: Micrometers = 455
}
case object Inner490mcr extends InnerCopperThickness {
  val value: Micrometers = 490
}
case object Inner525mcr extends InnerCopperThickness {
  val value: Micrometers = 525
}
case object Inner560mcr extends InnerCopperThickness {
  val value: Micrometers = 560
}
