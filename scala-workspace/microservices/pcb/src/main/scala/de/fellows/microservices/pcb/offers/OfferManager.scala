package de.fellows.microservices.pcb.offers

import cats.data.EitherT
import cats.syntax.eq._
import com.google.common.collect.ImmutableMap
import com.osinka.i18n.Lang
import com.segment.analytics.messages.TrackMessage
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.price.api.PriceService
import de.fellows.app.quotation.QuotationService
import de.fellows.app.supplier.SupplierService
import de.fellows.app.user.api.UserService
import de.fellows.ems.panel.api.{CustomerPanel, PanelService}
import de.fellows.luminovo.panel._
import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.microservices.pcb.PcbServer.{AsyncBackend, SyncBackend}
import de.fellows.microservices.pcb.PcbServerError.propertyErrors
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.alba.AlbaApiService
import de.fellows.microservices.pcb.model.apct.ApctApiService
import de.fellows.microservices.pcb.model.betaLayout.BetaLayoutApiService
import de.fellows.microservices.pcb.model.gatema.GatemaApiService
import de.fellows.microservices.pcb.model.ibr.IbrApiService
import de.fellows.microservices.pcb.model.lq._
import de.fellows.microservices.pcb.model.multicb.MultiCbApiService
import de.fellows.microservices.pcb.model.panel._
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.safePcb.SafePcbApiService
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI.Team
import de.fellows.microservices.pcb.model.stackrate.pricing.StackratePricingApiService
import de.fellows.microservices.pcb.model.wurth.WurthApiService
import de.fellows.microservices.pcb.model.{
  ApiService,
  ApiServiceWithManufacturers,
  RequestValidation,
  SingleManufacturerApiService
}
import de.fellows.microservices.pcb.offers.OfferManager.createScenarioRequestsWithPanel
import de.fellows.microservices.pcb.{
  decodeToken,
  ApiNotSetUpError,
  AuthToken,
  CredentialsMissingError,
  CustomStackupError,
  ExceptionError,
  InvalidParams,
  NotFoundError,
  PanelError,
  PcbPanelInfo,
  PcbServerConfig,
  PcbServerError,
  PropertyErrors,
  ScenarioRequestWithPanel,
  ServerError,
  ServiceError,
  StackratePricingBreaks,
  StackratePricingErrors,
  SttpHelper,
  ThirdPartyError,
  Tracing,
  UnauthorizedError,
  UnknownError
}
import de.fellows.utils.CurrencyCode
import de.fellows.utils.model.{PCBId, ShareId}
import de.fellows.utils.telemetry.Telemetry
import io.opentelemetry.api.trace.{StatusCode => SpanStatusCode}
import play.api.Logging
import play.api.libs.json.Json
import sttp.client3.playJson.SttpPlayJsonApi
import sttp.model.StatusCode

import java.time.format.DateTimeFormatter
import java.time.{ZoneId, ZonedDateTime}
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.math.BigDecimal.RoundingMode
import de.fellows.microservices.pcb.client.luminovo.api.SupplierAndStockLocationResponse

/** The manager requests a PCB configuration from PCB service and checks with manufacturers the prices for this PCB.
  */
class OfferManager(
    config: PcbServerConfig,
    stackrateApi: StackRateAPI,
    syncBackend: SyncBackend,
    asyncBackend: AsyncBackend
)(
    implicit
    lang: Lang,
    priceService: PriceService,
    quotationService: QuotationService,
    customerService: CustomerService,
    assemblyService: AssemblyService,
    panelService: PanelService,
    userService: UserService,
    supplierService: SupplierService
) extends SttpPlayJsonApi
    with Logging
    with SttpHelper {

  private lazy val wurthApiService =
    new WurthApiService(
      host = config.getString("wurthapi.host"),
      syncBackend = syncBackend
    )

  private lazy val betaLayoutApiService = new BetaLayoutApiService(
    host = config.getString("betaLayoutApi.host"),
    key = config.getString("betaLayoutApi.key"),
    asyncBackend = asyncBackend
  )

  private lazy val safePcbApiService =
    new SafePcbApiService(asyncBackend)(lang)

  private lazy val ibrRinglerApiService =
    new IbrApiService(
      host = config.getString("ibrApi.host"),
      key = config.getString("ibrApi.key"),
      asyncBackend = asyncBackend
    )

  private lazy val apctApiService      = new ApctApiService(config.getString("apct.host"), asyncBackend)
  private lazy val albaApiService      = new AlbaApiService(config.getString("albaApi.host"), asyncBackend)
  private lazy val gatemaApiService    = GatemaApiService.fromConfig(config, asyncBackend)
  private lazy val multiCbApiService   = new MultiCbApiService(config.getString("multiCbApi.host"), asyncBackend)
  private lazy val stackrateApiService = new StackratePricingApiService

  def checkCredentials(credentials: Credentials, token: AuthToken)(implicit
      ec: ExecutionContext
  ): Future[Either[String, Boolean]] =
    (for {
      service <- EitherT.fromOption[Future](
        apiService(credentials.manufacturer) match {
          case service: SingleManufacturerApiService => Some(service)
          case _                                     => None
        },
        s"Unhandled manufacturer '${credentials.manufacturer}'"
      )

      decodedToken <- EitherT.fromEither[Future](decodeToken(token)).leftMap(e => "Invalid token")

      result <- EitherT(service.checkCredentials(credentials, decodedToken.getTeam))
    } yield result).value

  private[pcb] def apiService(m: ManufacturerApi): ApiService =
    m match {
      case ManufacturerApi.Wuerth     => wurthApiService
      case ManufacturerApi.BetaLayout => betaLayoutApiService
      case ManufacturerApi.SafePcb    => safePcbApiService
      case ManufacturerApi.IBRRingler => ibrRinglerApiService
      case ManufacturerApi.APCT       => apctApiService
      case ManufacturerApi.Gatema     => gatemaApiService
      case ManufacturerApi.Alba       => albaApiService
      case ManufacturerApi.MultiCB    => multiCbApiService
      case ManufacturerApi.Stackrate  => stackrateApiService
    }

  private[pcb] def mapApiService(
      manufacturers: Seq[Manufacturer],
      api: ManufacturerApi
  ): ApiServiceWithManufacturers = {
    val service = apiService(api)
    ApiServiceWithManufacturers(service, api, manufacturers)
  }

  private def mapManufacturers(all: Seq[SupplierAndStockLocationResponse]): Seq[Manufacturer] =
    all
      .groupBy(_.supplier)
      .map {
        case (supplier, locations) =>
          Manufacturer(
            supplier = supplier.id,
            locations = locations.sortBy(_.id).map { s =>
              ManufacturerLocation(
                stockLocation = s.id,
                region = s.stock_location
              )
            },
            name = supplier.name
          )
      }
      .toSeq
      .sortBy(_.supplier)

  private def doCapabilityChecks(
      pcbOwningTeam: String,
      pcb: PCB,
      quantity: Int,
      preferences: EmsPreferences,
      apiServices: Seq[ApiServiceWithManufacturers],
      manufacturers: Seq[Manufacturer]
  )(implicit ec: ExecutionContext): Future[Seq[RequestValidation]] =
    Tracing.withAsyncTrace(s"doCapabilityChecks") { span =>
      val additionalStackrateTenants = Seq(
        Some(Tenants.Central),
        Option.when(apiServices.exists(_.api === ManufacturerApi.Gatema))(Tenants.Gatema)
      ).flatten

      val connectedTenants = (preferences.connectedTenants ++ additionalStackrateTenants).distinct
      span.setAttribute("connected_tenants", connectedTenants.mkString(","))

      for {
        // we check the connected tenants, plus the luminovo tenant for the globally defined capabilities
        stackrateCapabilities <- Future.sequence(connectedTenants.map { connectedTeam =>
          Tracing.withAsyncTrace(s"availability request for team ${connectedTeam}") { _ =>
            ApiService.stackrateCapabilityCheck(
              connectedTeam = connectedTeam,
              pcbOwningTeam = pcbOwningTeam,
              manufacturers = manufacturers,
              pcb = pcb
            )
          }
        }).map(_.flatten)

        manufacturerValidations <- Future.traverse(apiServices) { service =>
          logger.info(f"validate request for ${service.api.api}")
          Tracing.withTrace(s"capability check for manufacturer ${service.api.api}") {
            _ =>
              service.service.validateRequest(
                pcbOwningTeam,
                pcb,
                service.manufacturers,
                quantity,
                preferences
              )
          }
        }.map(_.flatten)
      } yield {
        val diff = manufacturerValidations.flatMap { requestValidation =>
          stackrateCapabilities.find(_.manufacturer == requestValidation.manufacturer).flatMap { fromStackrate =>
            (requestValidation.validation, fromStackrate.validation) match {
              case (Some(validation), Some(stackrateValidation)) => // check if different
                val errors          = propertyErrors(validation)
                val stackrateErrors = propertyErrors(stackrateValidation)

                val properties          = errors.map(_.property.fieldName).toSet
                val stackrateProperties = stackrateErrors.map(_.property.fieldName).toSet

                val missingProperties    = properties.diff(stackrateProperties)
                val additionalProperties = stackrateProperties.diff(properties)

                Option.when(missingProperties.nonEmpty || additionalProperties.nonEmpty) {
                  (requestValidation.manufacturer, errors, stackrateErrors)
                }

              case (Some(validation), None) =>
                Some((requestValidation.manufacturer, propertyErrors(validation), Seq.empty[PropertyError]))

              case (None, Some(validation)) =>
                Some((requestValidation.manufacturer, Seq.empty[PropertyError], propertyErrors(validation)))

              case (None, None) => None
            }
          }
        }

        if (diff.nonEmpty) {
          val json = Json.toJson(
            diff
              .sortBy(_._1.name)
              .map {
                case (manufacturer, hardCoded, stackrateCapabilities) =>
                  Json.obj(
                    "manufacturer" -> manufacturer.name,
                    "hard_coded"   -> hardCoded,
                    "stackrate"    -> stackrateCapabilities
                  )
              }
          )

          logger.error(
            s"Found differences in capabilities ${Json.prettyPrint(json)}"
          )

          span.setStatus(SpanStatusCode.ERROR)
          span.setAttribute("capability_mismatch", true)
          span.setAttribute("capability_errors", json.toString)
        }

        val filteredStackrateCapabilities = stackrateCapabilities.filterNot { stackrateValidation =>
          manufacturerValidations.exists(_.manufacturer == stackrateValidation.manufacturer)
        } ++ manufacturerValidations

        logger.info(s"Capabilities checked successfully: ${filteredStackrateCapabilities}")

        filteredStackrateCapabilities.sortBy(_.manufacturer.name)
      }
    }

  def makeBatchOffers(
      tenant: String,
      request: BatchOfferRequest
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, PcbOfferInput]] = {
    logger.info(f"Handling offer request for PCB ${request.pcbId} with existing offers ${request.existingOffers}")

    val futPcb = stackrateApi.getPcbNoToken(request.pcbId, Team(tenant))

    val byQuantity =
      request.sourcingScenarioRequests.groupMapReduce(_.quantity)(_.leadTimes)((a, b) => (a ++ b).distinct)
    val suppliersAndStockLocations =
      request
        .sourcingScenarioRequests
        .flatMap(_.supplierAndStockLocations)
        .distinctBy(_.id)
    val manufacturers = mapManufacturers(suppliersAndStockLocations)
    val service       = mapApiService(manufacturers, request.api)

    val emsPreferences = EmsPreferences.fromPanelPreferences(
      tenant = tenant,
      panelPreferences = request.panelPreferenceSettings.panelPreferences
    ).copy(
      connectedTenants = request.stackratePricingConnections.map(_.stackrateTenant).distinct
    )

    val res = (for {
      pcb <- EitherT(futPcb)

      customerPanels <- EitherT {
        logger.info(f"get customer panels for pcb ${pcb.id} of team ${tenant}")
        panelService._getCustomerPanels(
          tenant,
          pcb.assemblyId.value,
          pcb.id.value
        ).invoke()
          .map { x =>
            logger.info(f"got customer panels for pcb ${pcb.id} of team ${tenant}: ${x}")

            Right(x)
          }
          .recover {
            case e: Throwable =>
              logger.error(s"Error while getting customer panels for pcb ${pcb.id} of team ${tenant}: ${e}")
              Left(ServerError(
                s"Error while getting customer panels for pcb ${pcb.id} of team ${tenant}: ${e}"
              ))
          }
      }

      requestValidations <- EitherT.liftF {
        doCapabilityChecks(
          pcbOwningTeam = tenant,
          pcb = pcb,
          quantity = 10,
          preferences = emsPreferences,
          apiServices = Seq(service),
          manufacturers = manufacturers
        )
      }

      manufacturersBySourcingScenario =
        request
          .sourcingScenarioRequests
          .groupMapReduce { scenarioRequest =>
            scenarioRequest.sourcingScenarioId
          } { scenarioRequest =>
            mapManufacturers(scenarioRequest.supplierAndStockLocations)
          } {
            case (left, right) => left ++ right
          }

      scenarioRequestsWithPanel = createScenarioRequestsWithPanel(
        request.sourcingScenarioRequests,
        request.pcbId,
        request.panels,
        customerPanels,
        manufacturersBySourcingScenario
      )

      quotes <- EitherT.liftF[Future, ServiceError, Seq[Seq[ManufacturerApiResponse]]](
        makeMultipleQuoteRequests(
          tenant = tenant,
          services = Seq(service),
          emsPreferences = emsPreferences,
          pcb = pcb,
          requestValidations = requestValidations,
          scenarioRequestsWithPanels = scenarioRequestsWithPanel,
          existingOffers = request.existingOffers,
          credentials = request.credentials.toSeq
        )
      )
    } yield {
      val response = buildPcbOffer(
        analyticsId = Some(tenant),
        team = tenant,
        leadTimesByQuantity = byQuantity,
        pcb = pcb,
        responses = quotes.flatten
      )

      logger.info(s"Sending offer to LQ: ${Json.toJson(response)}")

      response
    }).value

    res
  }

  private def buildPcbOffer(
      analyticsId: Option[String],
      team: String,
      leadTimesByQuantity: Map[Int, Seq[LeadTime]],
      pcb: PCB,
      responses: Seq[ManufacturerApiResponse]
  ): PcbOfferInput = {
    val pcbId           = pcb.id
    val ignoreLeadTimes = config.getBoolean("pcb.luminovo.ignoreLeadTimes")

    val manufacturerStatus = responses.map { manufacturerResponse =>
      val api                  = manufacturerResponse.api
      val manufacturer         = manufacturerResponse.manufacturer
      val manufacturerLocation = manufacturerResponse.location
      val offerStatus = manufacturerResponse.response match {
        case Right(quote) =>
          val pcbOffers = quote
            .quoteResponses
            // because we can have multiple responses with the same quoteId (SafePCB request grouping)
            .groupBy(o => o.quoteId)
            .flatMap {
              case (quoteId, responses) =>
                val url = responses.headOption.flatMap(_.url)
                val pricePointsWithOneTimeCosts = responses.flatMap { response =>
                  val leadTimes =
                    leadTimesByQuantity
                      .getOrElse(response.offerResponse.quantity, Seq(LeadTime(LeadTimePreference.Fastest)))

                  val pricePoints =
                    response.offerResponse.toPricePoints(leadTimes, ignoreLeadTimes, response.sourcingScenarioId)

                  analyticsId.foreach { analyticsId =>
                    logPriceOffer(
                      analyticsId = analyticsId,
                      team = team,
                      pcbId = pcbId,
                      quantity = response.offerResponse.quantity,
                      panel = response.calculatedPanelInfo,
                      price = pricePoints,
                      manufacturer = manufacturer
                    )
                  }

                  pricePoints
                }

                // split offers by currency and one time costs
                val calculatedPanelInfos = responses.map(_.calculatedPanelInfo).distinct
                if (calculatedPanelInfos.size > 1) {
                  logger.error(s"Found multiple panel distributions for quote $quoteId: ${calculatedPanelInfos}")
                }

                val calculatedPanelInfo =
                  calculatedPanelInfos
                    .headOption
                    .getOrElse(throw new IllegalStateException("Failed to get panel info"))

                pricePointsWithOneTimeCosts
                  .groupBy(p => (p.point.currency, p.oneTimeCosts, p.priceType, p.sourcingScenarioId)).map {
                    case ((currency, otc, priceType, sourcingScenarioId), pricePoints) =>
                      // TODO: LQ only supports one otc per offer number. for now we just take care in stackrate
                      //  not to mess this up with the pricing
                      OfferManager.prepareLqOffer(
                        offerNumber = quoteId,
                        calculatedPanelInfo = calculatedPanelInfo,
                        pricePoints = pricePoints.map(_.point),
                        currency = currency.getOrElse(CurrencyCode.EUR),
                        priceType = priceType,
                        hashCode = pcb.hash,
                        oneTimeCosts = otc,
                        sourcingScenarioId = sourcingScenarioId,
                        offerUrl = url,
                        sharedPcbId = responses.head.sharedPcbId
                      )
                  }
            }
            .toSeq

          PcbOfferStatus.Success(pcbOffers)

        case Left(e: PropertyErrors)          => PcbOfferStatus.SpecificationUnsupported(e.errors)
        case Left(e: PanelError)              => PcbOfferStatus.PanelError(e.error)
        case Left(e: ThirdPartyError)         => PcbOfferStatus.ApiError(e.statusCode, e.error)
        case Left(e: NotFoundError)           => PcbOfferStatus.ApiError(StatusCode.NotFound, e.error)
        case Left(_: CredentialsMissingError) => PcbOfferStatus.MissingCredentials
        case Left(e: ApiNotSetUpError)        => PcbOfferStatus.ApiNotSetUp(e.kind)
        case Left(_: UnauthorizedError)       => PcbOfferStatus.InvalidCredentials
        case Left(e: InvalidParams)           => PcbOfferStatus.ApiError(StatusCode.BadRequest, e.error)
        case Left(e: ServerError)             => PcbOfferStatus.ApiError(StatusCode.InternalServerError, e.error)
        case Left(e: UnknownError)            => PcbOfferStatus.ApiError(StatusCode.InternalServerError, e.error)
        case Left(e: ExceptionError)          => PcbOfferStatus.ApiError(StatusCode.InternalServerError, e.error)
        case Left(e: StackratePricingErrors)  => PcbOfferStatus.StackratePricingErrors.from(e)
        case Left(e: StackratePricingBreaks)  => PcbOfferStatus.StackratePricingBreaks.from(e)
        case Left(CustomStackupError)         => PcbOfferStatus.CustomStackup
      }

      ManufacturerStatus(api, manufacturer, manufacturerLocation, offerStatus)
    }

    PcbOfferInput(
      pcbId = pcbId,
      pcbHash = pcb.hash,
      responses = manufacturerStatus
    )
  }

  private def makeMultipleQuoteRequests(
      tenant: String,
      services: Seq[ApiServiceWithManufacturers],
      emsPreferences: EmsPreferences,
      pcb: PCB,
      requestValidations: Seq[RequestValidation],
      scenarioRequestsWithPanels: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      credentials: Seq[Credentials]
  )(implicit ec: ExecutionContext): Future[Seq[Seq[ManufacturerApiResponse]]] = {
    val groupedValidations = requestValidations.groupBy(_.manufacturer.supplier)

    Future.traverse(services) { service =>
      service.api match {
        case ManufacturerApi.Stackrate =>
          makeQuoteRequest(
            tenant = tenant,
            service = service,
            emsPreferences = emsPreferences,
            pcb = pcb,
            scenarioRequestsWithPanels = scenarioRequestsWithPanels,
            existingOffers = existingOffers,
            credentials = credentials,
            requestValidations = groupedValidations
          )

        case _ =>
          service
            .manufacturers
            .headOption
            .map { manufacturer =>
              val validationResults = groupedValidations.getOrElse(manufacturer.supplier, Seq.empty)
              val errors            = validationResults.flatMap(_.validation).distinct

              errors.headOption match {
                case Some(error) =>
                  val response = manufacturer.locations.map { location =>
                    ManufacturerApiResponse(
                      ManufacturerApiWithInformation(service.api),
                      manufacturer,
                      location,
                      Left(error)
                    )
                  }

                  Future.successful(response)
                case None =>
                  val filteredScenarios = scenarioRequestsWithPanels.filter { sourcingScenario =>
                    sourcingScenario
                      .manufacturers
                      .exists(s => s.supplier == manufacturer.supplier)
                  }
                  makeQuoteRequest(
                    tenant = tenant,
                    service = service,
                    emsPreferences = emsPreferences,
                    pcb = pcb,
                    scenarioRequestsWithPanels = filteredScenarios,
                    existingOffers = existingOffers,
                    credentials = credentials,
                    requestValidations = groupedValidations
                  )
              }
            }
            .getOrElse(Future.successful(Seq.empty))
      }
    }
  }

  private def makeQuoteRequest(
      tenant: String,
      service: ApiServiceWithManufacturers,
      emsPreferences: EmsPreferences,
      pcb: PCB,
      scenarioRequestsWithPanels: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      credentials: Seq[Credentials],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Seq[ManufacturerApiResponse]] = {
    val srvDescriptionString = service.api.api
    logger.info(s"create quotes for $srvDescriptionString")

    Tracing.withAsyncTrace(s"create quotes for $srvDescriptionString") { span =>
      logger.info(s"request quote from $srvDescriptionString ${service.toString}")
      span.setAttribute("external_api", srvDescriptionString)

      val manufacturerCredentials = credentials.filter(c => service.api === c.manufacturer)
      logger.info(
        s"find credentials for ${service.api} in ${credentials.map(_.manufacturer)} => ${credentials.length}"
      )

      val existingManufacturerOffers = existingOffers.filter(o =>
        service.manufacturers.flatMap(_.locations.map(_.stockLocation)).contains(o.supplierAndStockLocation)
      )

      service
        .service
        .makeQuotes(
          tenant = tenant,
          pcb = pcb,
          manufacturers = service.manufacturers,
          scenarios = scenarioRequestsWithPanels,
          existingOffers = existingManufacturerOffers,
          emsPreferences = emsPreferences,
          credentials = manufacturerCredentials,
          requestValidations = requestValidations
        )
        .recover {
          case e: Throwable =>
            val msg = s"External API ${service.getClass.getSimpleName} $srvDescriptionString failed: ${e}"

            span.setStatus(SpanStatusCode.ERROR, msg)
            span.recordException(e)

            logger.error(msg, e)

            val error = ThirdPartyError(e.getMessage, StatusCode.InternalServerError)
            createErrorResponse(
              service,
              emsPreferences
            )((manufacturer, location) =>
              ManufacturerApiResponse(
                ManufacturerApiWithInformation(service.api),
                manufacturer,
                location,
                Left(error)
              )
            )
        }
    }
  }

  private def createErrorResponse[T](
      service: ApiServiceWithManufacturers,
      preferences: EmsPreferences
  )(
      createStatus: (Manufacturer, ManufacturerLocation) => T
  ): Seq[T] = {
    val manufacturers = service.api match {
      case ManufacturerApi.Stackrate if preferences.connectedTenants.nonEmpty =>
        val set = preferences.connectedTenants.toSet
        service.manufacturers.filter(m => set.contains(m.name))

      case ManufacturerApi.Stackrate => Seq.empty
      case _                         => service.manufacturers
    }

    manufacturers.flatMap { manufacturer =>
      manufacturer.locations.map { location =>
        createStatus(manufacturer, location)
      }
    }
  }

  /** Prepares the offer object which is sent to LQ
    */

  /** Logs price offer request to Segment
    */
  private def logPriceOffer(
      analyticsId: String,
      team: String,
      pcbId: PCBId,
      quantity: Int,
      panel: CalculatedPanelInfo,
      price: Seq[PricePointWithOneTimeCosts],
      manufacturer: Manufacturer
  ): Unit = {

    val (panelWidth, panelHeight, rows, columns, numberOfPcbs) = panel match {
      case p: CalculatedPanelInfo.FromPanelDetails =>
        (
          p.panelDistribution.panel.widthInMm,
          p.panelDistribution.panel.heightInMm,
          p.panelDistribution.mesh.rows.toString,
          p.panelDistribution.mesh.columns.toString,
          p.panelDistribution.mesh.size
        )
      case p: CalculatedPanelInfo.FromExisting =>
        (
          p.existing.panelWidth,
          p.existing.panelHeight,
          "-",
          "-",
          p.existing.numberOfPcbs
        )
    }

    Telemetry.analytics(
      TrackMessage.builder("pcb_create_api_offer")
        .userId(analyticsId)
        .properties(
          ImmutableMap.builder()
            .put("stackrate_team", team)
            .put("pcb_supplier", manufacturer.supplier.toString)
            .put("pcb_id", pcbId.toString)
            .put("quantity", quantity.toString)
            .put("panel_width", panelWidth.setScale(2, RoundingMode.HALF_UP).toString)
            .put("panel_height", panelHeight.setScale(2, RoundingMode.HALF_UP).toString)
            .put("rows", rows)
            .put("columns", columns)
            .put("pcbsPerPanel", numberOfPcbs.toString)
            .put("price", price.headOption.map(_.point.amount).getOrElse(0.0).toString)
            .put(
              "one_time_costs",
              price.headOption.flatMap(_.oneTimeCosts.map(_.map(_.amount))).getOrElse(Seq()).toString
            )
            .build()
        )
    )
  }
}

object OfferManager extends Logging {

  def prepareLqOffer(
      offerNumber: String,
      calculatedPanelInfo: CalculatedPanelInfo,
      pricePoints: Seq[PricePointInput],
      priceType: PriceType,
      hashCode: Option[String],
      currency: CurrencyCode,
      oneTimeCosts: Option[Seq[Cost]],
      sourcingScenarioId: SourcingScenarioId,
      offerUrl: Option[String],
      sharedPcbId: Option[ShareId]
  ): PcbOffer = {
    val currentDate = ZonedDateTime.now(ZoneId.of("UTC"))
    val validUntil  = currentDate.plusWeeks(6).format(DateTimeFormatter.ISO_LOCAL_DATE)

    val (notes, panelSpecification, pcbsPerPanel) = calculatedPanelInfo match {
      case p: CalculatedPanelInfo.FromPanelDetails =>
        val distribution = p.panelDistribution
        val rows         = if (distribution.mesh.rows === 1) "1 row" else s"${distribution.mesh.rows} rows"
        val cols         = if (distribution.mesh.columns === 1) "1 column" else s"${distribution.mesh.columns} columns"
        val width        = f"${distribution.panel.widthInMm.toFloat}%.2f"
        val height       = f"${distribution.panel.heightInMm.toFloat}%.2f"
        val notes        = s"Delivery panel: $rows, $cols (${width}mm x ${height}mm)"
        val panelDetails = PanelDetails(
          id = None,
          rowCount = distribution.mesh.rows,
          columnCount = distribution.mesh.columns,
          horizontalSpacingInMm = distribution.gap.x,
          verticalSpacingInMm = distribution.gap.y,
          minMillingDistanceInMm = distribution.minMillingDistanceInMm,
          padding = LuminovoPadding(
            topInMm = distribution.padding.topInMm,
            rightInMm = distribution.padding.rightInMm,
            bottomInMm = distribution.padding.bottomInMm,
            leftInMm = distribution.padding.leftInMm
          ),
          depanelization = distribution.depanelization,
          pcbIsRotated = distribution.pcbIsRotated
        )
        val panelSpecification = PanelSpecification.PanelDetailsSpecification(panelDetails)

        (notes, panelSpecification, p.panelDistribution.mesh.size)

      case p: CalculatedPanelInfo.FromExisting =>
        val notes =
          s"""Delivery panel (existing panel): ${p.existing.numberOfPcbs}
             |per panel (${p.existing.panelWidth}mm x ${p.existing.panelHeight}mm)""".stripMargin
        val panelSpecification = PanelSpecification.ExistingPanelSpecification(p.existing)

        (notes, panelSpecification, p.existing.numberOfPcbs)
    }

    PcbOffer(
      pricePoints = pricePoints,
      currency = currency,
      validUntil = Some(validUntil),
      offerNumber = offerNumber,
      offerValidity = hashCode.getOrElse("empty hash code from PCB"),
      priceType = priceType,
      piecesPerUnit = pcbsPerPanel,
      notes = notes,
      offerUrl = offerUrl,
      oneTimeCosts = oneTimeCosts,
      sourcingScenarioId = sourcingScenarioId,
      sharedPcbId = sharedPcbId,
      panel = None,
      panelSpecification = Some(panelSpecification)
    )
  }

  def combine(value: Seq[PcbServerError]): Option[PcbServerError] = {
    val (propertyErrors, other) = value.partition(_.isInstanceOf[PropertyErrors])

    if (propertyErrors.nonEmpty) {
      Some(PropertyErrors(propertyErrors.flatMap(_.asInstanceOf[PropertyErrors].errors)))
    } else other.headOption
    // TODO: what if there are multiple errors?
    //  currently that can only happen if there is a stackrate check AND an api check.
    //  Since we want to remove the api checks its not a problem right now

  }

  def combineValidations(validations: Seq[RequestValidation]): Seq[RequestValidation] =
    (validations.groupBy(v => (v.manufacturer, v.location)).map { x =>
      val list: Seq[RequestValidation] = x._2
      val (manufacturer, location)     = x._1
      val api                          = list.headOption.map(_.api).getOrElse(ManufacturerApi.Stackrate)
      RequestValidation(
        api = api,
        manufacturer = manufacturer,
        location = location,
        validation = combine(list.flatMap(_.validation))
      )

    }).toSeq

  def panelForSourcingScenario(
      panels: Seq[GenericPanel],
      pcbId: PCBId,
      mbSourcingScenarioId: Option[SourcingScenarioId]
  ): Option[GenericPanel] =
    mbSourcingScenarioId
      .flatMap { sourcingScenarioId =>
        panels
          .find {
            case p: PerSourcingScenario => p.sourcingScenario == sourcingScenarioId
            case _                      => false
          }
      }
      .orElse {
        panels
          .find {
            case p: PerPcb => p.pcb == pcbId
            case _         => false
          }
      }
      .orElse {
        panels.find {
          case e: ExistingWrapper => e.panel.pcb == pcbId
          case _                  => false
        }
      }

  def createScenarioRequestsWithPanel(
      scenarioRequests: Seq[ScenarioRequest],
      pcbId: PCBId,
      panels: Seq[GenericPanel],
      stackrateCustomerPanels: Seq[CustomerPanel],
      manufacturersBySourcingScenario: Map[SourcingScenarioId, Seq[Manufacturer]]
  ): Seq[ScenarioRequestWithPanel] = {
    logger.info(s"create scenario requests for ${scenarioRequests}")
    scenarioRequests.map { scenarioRequest =>
      logger.info(s"create scenario request for quantity ${scenarioRequest.quantity}")
      val suppliers = manufacturersBySourcingScenario.getOrElse(scenarioRequest.sourcingScenarioId, Seq.empty)

      val (mbPanelId, panelInfo) = panelForSourcingScenario(
        panels = panels,
        pcbId = pcbId,
        mbSourcingScenarioId = Some(scenarioRequest.sourcingScenarioId)
      ) match {
        case Some(panel: ExistingWrapper)     => (panel.panel.id, PcbPanelInfo.Existing(panel.panel))
        case Some(panel: PerSourcingScenario) => (panel.panelDetails.id, PcbPanelInfo.Details(panel.panelDetails))
        case Some(panel: PerPcb)              => (panel.panelDetails.id, PcbPanelInfo.Details(panel.panelDetails))
        case None                             => (None, PcbPanelInfo.NoPanel)
      }

      val stackratePanel = mbPanelId.flatMap { panelId =>
        stackrateCustomerPanels.find(p => p.externalId.nonEmpty && p.externalId.exists(_ === panelId.value.toString))
      }

      logger.info(
        s"create scenario request panel ${stackratePanel.map(_.id)} found from ${stackrateCustomerPanels.flatMap(
            _.externalId
          )}, searched for id ${mbPanelId}"
      )

      ScenarioRequestWithPanel(scenarioRequest, panelInfo, stackratePanel, suppliers)
    }
  }

}
