package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.Side
import de.fellows.ems.pcb.api.specification.Side.None
import de.fellows.microservices.pcb.model.pcb.capability.SetCapability

object CaptonTape {
  val name: String  = "captonTape"
  val label: String = "pcb.board.advanced.captonTape"

  /** The default value for captonTape is None (no capton tape) */
  def default: CaptonTape                    = CaptonTape(None)
  def apply(value: Option[Side]): CaptonTape = value.map(CaptonTape(_)).getOrElse(default)
  val allSidesCapability = new CaptonTapeCapability(Side.None, Side.Top, Side.Bottom, Side.Both)
  type CaptonTapeCapability = SetCapability[CaptonTape, Side]
}

final case class CaptonTape(override val value: Side) extends PCBRequiredProperty[Side] {
  val fieldName: String = CaptonTape.name
  val label: String     = CaptonTape.label
  val unit: String      = ""
} 