package de.fellows.microservices.pcb.client.luminovo

import de.fellows.luminovo.LuminovoJson
import play.api.libs.json._

import java.util.UUID

package object api {

  implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration

  final case class StackratePricingConnection(
      stackrateTenant: String,
      tenant: Option[String],
      supplierAndStockLocation: Option[UUID]
  )

  object StackratePricingConnection {
    implicit val stackratePricingConnectionReads: Reads[StackratePricingConnection] =
      Json.reads[StackratePricingConnection]
  }
}
