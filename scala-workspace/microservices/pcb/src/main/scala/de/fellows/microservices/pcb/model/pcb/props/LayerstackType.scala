package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.{LayerstackType => SpecLayerstackType}
import de.fellows.microservices.pcb.model.pcb.capability.SetCapability

object LayerstackType {
  val name: String  = "LayerstackType"
  val label: String = "pcb.layer.layerstackType"

  val rigid = LayerstackType(SpecLayerstackType.Rigid)

  def apply(value: Option[SpecLayerstackType]): LayerstackType =
    LayerstackType(value.getOrElse(SpecLayerstackType.Rigid))

  type LayerstackTypeCapability = SetCapability[LayerstackType, SpecLayerstackType]
}

final case class LayerstackType(override val value: SpecLayerstackType)
    extends PCBRequiredProperty[SpecLayerstackType] {
  val fieldName: String = LayerstackType.name
  val label: String     = LayerstackType.label
  val unit: String      = ""
}
