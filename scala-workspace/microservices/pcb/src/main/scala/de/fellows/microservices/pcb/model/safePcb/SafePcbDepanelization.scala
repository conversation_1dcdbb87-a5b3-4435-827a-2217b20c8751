package de.fellows.microservices.pcb.model.safePcb

import de.fellows.luminovo.panel.Depanelization

/** Describes how PCB should be depanelized
  */
sealed trait SafePcbDepanelization {
  val value: String
}
case object SafePcbVCut extends SafePcbDepanelization {
  override val value: String = "VCUT"
}

case object SafePcbMilling extends SafePcbDepanelization {
  override val value: String = "MIL"
}

object SafePcbDepanelization {
  def apply(value: Depanelization): SafePcbDepanelization = value match {
    case Depanelization.VCut           => SafePcbVCut
    case Depanelization.Milling        => SafePcbMilling
    case Depanelization.MillingAndVCut => SafePcbMilling
  }
}
