package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters

object SoldermaskClearance {
  val name: String  = "solderMaskClearance"
  val label: String = "pcb.basic.solderMaskClearance"

  def empty: SoldermaskClearance                     = SoldermaskClearance(None)
  def apply(value: Millimeters): SoldermaskClearance = SoldermaskClearance(Some(value))
}

final case class SoldermaskClearance(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = SoldermaskClearance.name
  val label: String     = SoldermaskClearance.label

  override val legacyNames: Option[Seq[String]] = Some(Seq("soldermask_clearance"))
}
