package de.fellows.microservices.pcb.model.alba

import com.osinka.i18n.Lang
import de.fellows.ems.pcb.api.specification.Side
import de.fellows.microservices.pcb.PropertyErrors
import de.fellows.microservices.pcb.model.alba.AlbaPcbRequest.sideToBools
import de.fellows.microservices.pcb.model.panel.NumberOfPanels
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.pcb.props._
import de.fellows.microservices.pcb.model.pcb.{Convert, PCB}
import zio.prelude.Validation

case class AlbaPcbRequest(
    pcbName: String,
    calculatedPanelInfo: CalculatedPanelInfo.FromPanelDetails,
    numberOfPanels: NumberOfPanels,
    soldermaskSide: SoldermaskSide,
    soldermaskColor: AlbaSoldermaskColor,
    silkscreenSide: SilkscreenSide,
    silkscreenColor: AlbaSilkscreenColor,
    baseMaterial: AlbaMaterial,
    peelableMask: PeelableMask,
    finalThickness: AlbaFinalThickness,
    outerCopperThickness: AlbaOuterCopperThickness,
    innerCopperThickness: Option[AlbaInnerCopperThickness],
    surfaceFinish: AlbaSurfaceFinish,
    numberOfLayers: AlbaNumberOfLayers,
    boardWidth: BoardWidth,
    boardHeight: BoardHeight,
    phCount: PhCount,
    nphCount: NphCount,
    minViaDiameter: MinViaDiameter,
    blindVias: BlindVias,
    buriedVias: BuriedVias,
    outlineLength: OutlineLength,
    depanelization: AlbaDepanelization
) {
  val (hasSoldermaskTop, hasSoldermaskBottom)     = sideToBools(soldermaskSide.value)
  val (hasSilkscreenTop, hasSilkscreenBottom)     = sideToBools(silkscreenSide.value)
  val (hasPeelableMaskTop, hasPeelableMaskBottom) = sideToBools(peelableMask.value)
}

object AlbaPcbRequest {

  def sideToBools(side: Side): (Boolean, Boolean) = side match {
    case Side.Both   => (true, true)
    case Side.Top    => (true, false)
    case Side.Bottom => (false, true)
    case Side.None   => (false, false)
  }

  def convert(
      pcb: PCB,
      calculatedPanelInfo: CalculatedPanelInfo.FromPanelDetails
  )(implicit lang: Lang): Either[PropertyErrors, AlbaPcbRequest] = {
    val props = pcb.properties

    Validation.validateWith(
      Validation.succeed(pcb),
      Validation.succeed(calculatedPanelInfo),
      Validation.succeed(calculatedPanelInfo.numberOfPanels),
      Convert.convertRequired(props.basic.soldermaskColor, AlbaSoldermaskColor.converter),
      Convert.convertRequired(props.basic.silkscreenColor, AlbaSilkscreenColor.converter),
      Convert.convertRequired(props.layer.baseMaterial, AlbaMaterial.converter(props.layer.tgValue)),
      Convert.convertRequired(
        props.layer.finalThickness,
        AlbaFinalThickness.converter
      ), // this is not the base material thickness
      Convert.convertRequired(props.basic.surfaceFinish, AlbaSurfaceFinish.converter),
      Convert.convertRequired(props.layer.outerCopperThickness, AlbaOuterCopperThickness.converter),
      Validation.succeed(AlbaInnerCopperThickness.converter(props.layer.innerCopperThickness)),
      Convert.convertRequired(props.layer.numberOfLayers, AlbaNumberOfLayers.converter),
      Validation.succeed(AlbaDepanelization.converter(calculatedPanelInfo.depanelization))
    )(createRequest).fold(
      errors => Left(PropertyErrors(errors)),
      Right.apply
    )
  }

  def createRequest(
      pcb: PCB,
      calculatedPanelInfo: CalculatedPanelInfo.FromPanelDetails,
      numberOfPanels: NumberOfPanels,
      soldermaskColor: AlbaSoldermaskColor,
      silkscreenColor: AlbaSilkscreenColor,
      materialType: AlbaMaterial,
      finalThickness: AlbaFinalThickness,
      surfaceFinish: AlbaSurfaceFinish,
      outerCopperThickness: AlbaOuterCopperThickness,
      innerCopperThickness: Option[AlbaInnerCopperThickness],
      numberOfLayers: AlbaNumberOfLayers,
      depanelization: AlbaDepanelization
  ): AlbaPcbRequest =
    AlbaPcbRequest(
      pcbName = pcb.name.getOrElse("PCB"),
      calculatedPanelInfo = calculatedPanelInfo,
      numberOfPanels = numberOfPanels,
      soldermaskSide = pcb.properties.basic.soldermaskSide,
      soldermaskColor = soldermaskColor,
      silkscreenSide = pcb.properties.basic.silkscreenSide,
      silkscreenColor = silkscreenColor,
      baseMaterial = materialType,
      peelableMask = pcb.properties.advanced.peelableMask,
      finalThickness = finalThickness,
      outerCopperThickness = outerCopperThickness,
      innerCopperThickness = innerCopperThickness,
      surfaceFinish = surfaceFinish,
      numberOfLayers = numberOfLayers,
      boardWidth = pcb.properties.basic.boardWidth,
      boardHeight = pcb.properties.basic.boardHeight,
      phCount = pcb.properties.mechanical.phCount,
      nphCount = pcb.properties.mechanical.nphCount,
      minViaDiameter = pcb.properties.mechanical.minViaDiameter,
      blindVias = pcb.properties.mechanical.blindVias,
      buriedVias = pcb.properties.mechanical.buriedVias,
      outlineLength = pcb.properties.mechanical.outlineLength,
      depanelization = depanelization
    )
}
