package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.SoldermaskColor.Green
import de.fellows.ems.pcb.api.specification.{SoldermaskColor => SpecSoldermaskColor}
import de.fellows.microservices.pcb.model.pcb.capability.SetCapability

object SoldermaskColor {
  val name: String  = "soldermaskColor"
  val label: String = "pcb.board.basic.soldermaskColor"

  /** According to this specification, https://www.notion.so/luminovo/PCB-Specification-4493fa95f17e4bbf81fe2a6c59259e82
    * the default value for soldermaskColor is Green
    */
  def default: SoldermaskColor                                   = SoldermaskColor(Green)
  def apply(value: Option[SpecSoldermaskColor]): SoldermaskColor = value.map(SoldermaskColor(_)).getOrElse(default)
  type SoldermaskColorCapability = SetCapability[SoldermaskColor, SpecSoldermaskColor]
}

final case class SoldermaskColor(override val value: SpecSoldermaskColor)
    extends PCBRequiredProperty[SpecSoldermaskColor] {
  val fieldName: String = SoldermaskColor.name

  override val legacyNames: Option[Seq[String]] = Some(Seq("color"))

  val label: String = SoldermaskColor.label
  val unit: String  = ""
}
