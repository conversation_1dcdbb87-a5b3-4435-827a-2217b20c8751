package de.fellows.microservices.pcb.model.pcb

import com.osinka.i18n.{<PERSON>, Messages}
import play.api.Logging
import play.api.libs.json.{<PERSON><PERSON>, Writes}

import scala.util.{Failure, Success, Try}

/** Localized version of [[PropertyError]]
  * Describes an error on a particular property
  */
final case class PropertyErrorResponse(name: String, label: String, error: String)

object PropertyErrorResponse extends Logging {
  def fromPropertyError(error: PropertyError)(implicit lang: Lang): PropertyErrorResponse = {
    val msg = Try(Messages(error.property.label)) match {
      case Failure(exception) =>
        logger.error(s"missing translation for ${error.property.label}", exception)
        error.property.label
      case Success(value) => value
    }
    PropertyErrorResponse(error.property.fieldName, msg, error.error)
  }

  implicit val writes: Writes[PropertyErrorResponse] = Json.writes[PropertyErrorResponse]
}
