package de.fellows.microservices.pcb.model.pcb.capability

import com.osinka.i18n.{Lang, Messages}
import de.fellows.microservices.pcb.model.pcb.{PropertyError, PropertyErrorKind}
import de.fellows.microservices.pcb.model.pcb.props.{PCBOptionalProperty, PCBRequiredProperty}
import zio.prelude.Validation
import zio.prelude.Validation._

import scala.reflect.ClassTag

/** PCB manufacture capability for properties with limited number of values
  *
  * @param values
  *   Supported values of the property
  */
case class SetCapability[T <: PCBRequiredProperty[K], K](values: K*)(implicit override val typeTag: ClassTag[T])
    extends NonEmptyRequiredCapability[T, SetCapability[T, K]] {

  def errorMessage(value: T)(implicit lang: Lang): String =
    Messages("pcb.error.capability.set", Messages(value.label))

  def isValid(value: T): Boolean =
    values.contains(value.value)

  /** Validates the property value against the capability
    */
  def validate(property: T)(implicit lang: Lang): Validation[PropertyError, T] =
    if (isValid(property)) {
      succeed(property)
    } else {
      fail(PropertyError(property, errorMessage(property), PropertyErrorKind.NotInSet))
    }

  def -(value: K): SetCapability[T, K] =
    SetCapability(
      this.values.filterNot(_ == value): _*
    )

  def -(value: K*): SetCapability[T, K] =
    SetCapability(
      this.values.filterNot(x => value.contains(x)): _*
    )
}

class SetOptionalCapability[T <: PCBOptionalProperty[K], K](val required: Boolean, values: K*)(implicit
    override val typeTag: ClassTag[T]
) extends NonEmptyCapability[T, SetOptionalCapability[T, K]] {
  def errorMessage(value: T)(implicit lang: Lang): String =
    Messages("pcb.error.capability.set", Messages(value.label))

  def isValid(value: T): Boolean =
    value.value match {
      case Some(value) => values.contains(value)
      case None        => !this.required
    }

  override def validate(property: T)(implicit lang: Lang): Validation[PropertyError, T] =
    if (isValid(property)) {
      succeed(property)
    } else {
      fail(PropertyError(property, errorMessage(property), PropertyErrorKind.NotInSet))
    }
}

/** PCB manufacture capability for properties with limited number of values
  *
  * @param values Supported values of the property
  */
case class SetValueCapability[T <: PCBRequiredProperty[_]](values: T*)(implicit override val typeTag: ClassTag[T])
    extends NonEmptyRequiredCapability[T, SetValueCapability[T]] {

  /** Validates the property value against the capability
    */
  def validate(property: T)(implicit lang: Lang): Validation[PropertyError, T] =
    if (values.contains(property)) {
      succeed(property)
    } else {
      fail(PropertyError(
        property,
        Messages("pcb.error.capability.set", Messages(property.label)),
        PropertyErrorKind.NotInSet
      ))
    }

}
