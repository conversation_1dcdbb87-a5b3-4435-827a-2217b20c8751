package de.fellows.microservices.pcb.model.safePcb

import de.fellows.ems.pcb.api.specification
import de.fellows.microservices.pcb.model.pcb.props

trait SafePcbSurfaceFinish {
  val value: String
}
private case object SafePcbNone extends SafePcbSurfaceFinish {
  val value = "WITHOUT"
}
private case object SafePcbEnig extends SafePcbSurfaceFinish {
  val value: String = "CHEM.GOLD"
}
private case object SafePcbEnepig extends SafePcbSurfaceFinish {
  val value: String = "CHEM.ENEPIG"
}
private case object SafePcbChemicalSilver extends SafePcbSurfaceFinish {
  val value: String = "CHEM.SILVER"
}
private case object SafePcbHal extends SafePcbSurfaceFinish {
  val value: String = "HAL"
}
private case object SafePcbChemicalTin extends SafePcbSurfaceFinish {
  val value: String = "CHEM.TIN"
}
private case object SafePcbOsp extends SafePcbSurfaceFinish {
  val value: String = "OSP"
}

private object SafePcbSurfaceFinish {

  def fromPcb(value: props.SurfaceFinish): Option[SafePcbSurfaceFinish] =
    value.value match {
      case specification.SurfaceFinish.HalPbFree => Some(SafePcbHal)
      case specification.SurfaceFinish.It        => Some(SafePcbChemicalTin)
      case specification.SurfaceFinish.Is        => Some(SafePcbChemicalSilver)
      case specification.SurfaceFinish.Enig      => Some(SafePcbEnig)
      case specification.SurfaceFinish.Enepig    => Some(SafePcbEnepig)
      case specification.SurfaceFinish.Osp       => Some(SafePcbOsp)
      case specification.SurfaceFinish.None      => Some(SafePcbNone)
      case _                                     => None
    }
}
