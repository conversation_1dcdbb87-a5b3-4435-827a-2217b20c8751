package de.fellows.microservices.pcb.model.panel

import de.fellows.ems.pcb.model.{BigPoint, Dimension}
import play.api.libs.json.{Format, JsError, JsString, <PERSON>sSuc<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Writes}

sealed trait RectangleKind

case object Pcb     extends RectangleKind
case object Padding extends RectangleKind
case object Space   extends RectangleKind

object RectangleKind {
  implicit val rec: Format[RectangleKind] = Format(
    Reads[RectangleKind] {
      case JsString(value) => value match {
          case "pcb"     => JsSuccess(Pcb)
          case "padding" => JsSuccess(Padding)
          case "space"   => JsSuccess(Space)
          case _         => JsError()
        }
      case _ => JsError()
    },
    Writes[RectangleKind] { x: RectangleKind =>
      x match {
        case Pcb     => JsString("pcb")
        case Padding => JsString("padding")
        case Space   => JsString("space")
      }
    }
  )
}

/** The output of the PCB distribution algorithm on a panel is a list of rectangles of three types:
  *  - Pcb: the actual PCB
  *  - Padding: the padding between the PCBs and the edge of the panel
  *  - Space: the space between the PCBs
  *
  * The rectangle is defined by its top left corner and its bottom right corner.
  * @param rect the rectangle
  * @param kind the type of the rectangle (pcb, padding, space)
  */
final case class DistributionRectangle(
    rect: Dimension,
    kind: RectangleKind
)

object DistributionRectangle {
  implicit val rec: Format[DistributionRectangle] = Json.format[DistributionRectangle]
  def apply(topLeft: BigPoint, bottomRight: BigPoint, kind: RectangleKind): DistributionRectangle =
    DistributionRectangle(Dimension(topLeft, bottomRight), kind)
}
