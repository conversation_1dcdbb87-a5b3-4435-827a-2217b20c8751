package de.fellows.microservices.pcb.model.lq

import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.utils.CurrencyCode
import play.api.libs.json.{<PERSON><PERSON>, Writes}

/** Helper class to keep the one time costs
  * @param points
  * @param oneTimeCosts
  */
final case class PricePointWithOneTimeCosts(
    point: PricePointInput,
    oneTimeCosts: Option[Seq[Cost]],
    priceType: PriceType,
    sourcingScenarioId: SourcingScenarioId
)

/** Offer price information in Luminovo
  *
  * @param quantity Number of items
  * @param amount Price per item
  * @param leadTimeDays Number of days to deliver
  */
final case class PricePointInput(
    quantity: Long,
    amount: BigDecimal,
    leadTimeDays: Option[Int],
    currency: Option[CurrencyCode]
)

object PricePointInput {
  implicit val writes: Writes[PricePointInput] = s =>
    Json.obj(
      "quantity"       -> s.quantity,
      "amount"         -> s.amount,
      "currency"       -> s.currency,
      "lead_time_days" -> s.leadTimeDays
    )
}
