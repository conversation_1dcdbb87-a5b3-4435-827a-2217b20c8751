package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.luminovo.panel.Depanelization

sealed trait BetaLayoutDepanelization {
  val value: Int
}

private object BetaLayoutDepanelization {
  def converter(depanelization: Depanelization): BetaLayoutDepanelization = depanelization match {
    case Depanelization.VCut           => VCut
    case Depanelization.Milling        => Milling
    case Depanelization.MillingAndVCut => Milling
  }

  case object VCut extends BetaLayoutDepanelization {
    override val value: Int = 1
  }

  case object Milling extends BetaLayoutDepanelization {
    override val value: Int = 4
  }
}
