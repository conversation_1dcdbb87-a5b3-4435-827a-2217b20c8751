package de.fellows.microservices.pcb

import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerApi, ManufacturerLocation}
import play.api.libs.json.Reads

package object model {
  type Percent        = BigDecimal
  type Millimeters    = BigDecimal
  type DegreesCelsius = BigDecimal
  type SquareMm       = BigDecimal
  type Micrometers    = Int

  final case class FileName(name: String) extends AnyVal

  object FileName {
    implicit val reads: Reads[FileName] =
      Reads(json => json.validate[String].map(FileName.apply))
  }

  final case class RequestValidation(
      api: ManufacturerApi,
      manufacturer: Manufacturer,
      location: ManufacturerLocation,
      validation: Option[PcbServerError]
  )
}
