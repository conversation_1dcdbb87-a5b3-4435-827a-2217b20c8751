package de.fellows.microservices.pcb.model.lq

import enumeratum.{Enum, EnumEntry, PlayJsonEnum}

/** wrapper used for some api communication
  * @param tag
  */
case class SupplierTagEntry(tag: SupplierTag)

object SupplierTagEntry {
  import play.api.libs.json._
  implicit val format: Format[SupplierTagEntry] = Json.format[SupplierTagEntry]
}

sealed trait SupplierTag extends EnumEntry {}

object SupplierTag extends Enum[SupplierTag] with PlayJsonEnum[SupplierTag] {
  val values = findValues

  case object QuotePartner            extends SupplierTag
  case object ManuallyAdded           extends SupplierTag
  case object OffTheShelfPartSupplier extends SupplierTag
  case object PcbSupplier             extends SupplierTag
  case object AssemblySupplier        extends SupplierTag
  case object Distributor             extends SupplierTag
  case object Manufacturer            extends SupplierTag
  case object Ems                     extends SupplierTag
  case object Manual                  extends SupplierTag
  case object System                  extends SupplierTag
  case object Erp                     extends SupplierTag
}
