package de.fellows.microservices.pcb.model.pcb.props

object NumberOfLaminationCycles {
  val name: String  = "numberOfLaminationCycles"
  val label: String = "pcb.board.advanced.numberOfLaminationCycles"

  def empty: NumberOfLaminationCycles                             = NumberOfLaminationCycles(Option.empty[Int])
  def apply(value: Int): NumberOfLaminationCycles                 = NumberOfLaminationCycles(Some(value))
  def fromV2(value: Option[BigDecimal]): NumberOfLaminationCycles = NumberOfLaminationCycles(value.map(_.toInt))
}

final case class NumberOfLaminationCycles(override val value: Option[Int]) extends IntPCBProperty {
  val fieldName: String = NumberOfLaminationCycles.name
  val label: String     = NumberOfLaminationCycles.label
  val unit: String      = "cycles"
} 