package de.fellows.microservices.pcb.model.pcb.props

/** The amount of non-plated holes (drills)
  */
object NphCount {
  val name: String  = "nphCount"
  val label: String = "pcb.mechanical.nphCount"

  def empty: NphCount                             = NphCount(Option.empty[Int])
  def apply(value: Int): NphCount                 = NphCount(Some(value))
  def fromV2(value: Option[BigDecimal]): NphCount = NphCount(value.map(_.toInt))
}

final case class NphCount(override val value: Option[Int]) extends IntPCBProperty {
  val fieldName: String = NphCount.name
  val label: String     = NphCount.label
  val unit: String      = "holes"

  override val legacyNames: Option[Seq[String]] = Some(Seq("nph_count"))
}
