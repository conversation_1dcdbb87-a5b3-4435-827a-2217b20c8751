package de.fellows.microservices.pcb.model.safePcb

import de.fellows.microservices.pcb.model.pcb.props
import de.fellows.microservices.pcb.model.pcb.props.{
  Inner105mcr,
  Inner140mcr,
  Inner18mcr,
  Inner210mcr,
  Inner35mcr,
  Inner70mcr,
  InnerNone
}

trait SafePcbInnerCopperThickness {
  val value: String
}
private case object SafePcbInnerThicknessEmpty extends SafePcbInnerCopperThickness {
  override val value: String = "WITHOUT"
}
private case object SafePcbInnerThicknessMcr17 extends SafePcbInnerCopperThickness {
  override val value: String = "17u"
}
private case object SafePcbInnerThicknessMcr35 extends SafePcbInnerCopperThickness {
  override val value: String = "35u"
}
private case object SafePcbInnerThicknessMcr70 extends SafePcbInnerCopperThickness {
  override val value: String = "70u"
}
private case object SafePcbInnerThicknessMcr105 extends SafePcbInnerCopperThickness {
  override val value: String = "105u"
}
private case object SafePcbInnerThicknessMcr140 extends SafePcbInnerCopperThickness {
  override val value: String = "140u"
}
private case object SafePcbInnerThicknessMcr210 extends SafePcbInnerCopperThickness {
  override val value: String = "210u"
}

private object SafePcbInnerCopperThickness {

  def fromPcb(value: props.InnerCopperThickness): Option[SafePcbInnerCopperThickness] =
    value match {
      case Inner18mcr  => Some(SafePcbInnerThicknessMcr17)
      case Inner35mcr  => Some(SafePcbInnerThicknessMcr35)
      case Inner70mcr  => Some(SafePcbInnerThicknessMcr70)
      case Inner105mcr => Some(SafePcbInnerThicknessMcr105)
      case Inner140mcr => Some(SafePcbInnerThicknessMcr140)
      case Inner210mcr => Some(SafePcbInnerThicknessMcr210)
      case InnerNone   => Some(SafePcbInnerThicknessEmpty)
      case _           => None
    }
}
