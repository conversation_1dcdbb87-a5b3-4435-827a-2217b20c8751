package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters

object SoldermaskDam {
  val name: String  = "solderMaskDam"
  val label: String = "pcb.basic.solderMaskDam"

  def empty: SoldermaskDam                     = SoldermaskDam(None)
  def apply(value: Millimeters): SoldermaskDam = SoldermaskDam(Some(value))
}

final case class SoldermaskDam(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = SoldermaskDam.name
  val label: String     = SoldermaskDam.label

  override val legacyNames: Option[Seq[String]] = Some(Seq("soldermask_dam"))
}
