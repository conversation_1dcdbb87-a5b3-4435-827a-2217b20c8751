package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.ems.pcb.api.specification.Side
import de.fellows.ems.pcb.api.specification.Side.{ Both, Bottom, Top }
import de.fellows.microservices.pcb.model.pcb._

/** Name of param in BetaLayout API: silkscreen
  */
trait BetaLayoutSilkscreenSides {
  val value: Int
  val toPCB: props.SilkscreenSide
}
private case object SilkscreenSidesNone extends BetaLayoutSilkscreenSides {
  override val value: Int                   = 0
  override val toPCB: props.SilkscreenSide = props.SilkscreenSide(Side.None)
}
private case object SilkscreenSidesTop extends BetaLayoutSilkscreenSides {
  override val value: Int                   = 1
  override val toPCB: props.SilkscreenSide = props.SilkscreenSide(Top)
}
private case object SilkscreenSidesBottom extends BetaLayoutSilkscreenSides {
  override val value: Int                   = 2
  override val toPCB: props.SilkscreenSide = props.SilkscreenSide(Bottom)
}
private case object SilkscreenSidesTopBottom extends BetaLayoutSilkscreenSides {
  override val value: Int                   = 3
  override val toPCB: props.SilkscreenSide = props.SilkscreenSide(Both)
}

private object BetaLayoutSilkscreenSides {

  /** According to this specification, https://www.notion.so/luminovo/PCB-Specification-4493fa95f17e4bbf81fe2a6c59259e82
    * the default value for silkscreenSides is TopBottom
    */
  def fromPcb(value: props.SilkscreenSide) = value.value match {
    case Side.None => SilkscreenSidesNone
    case Top       => SilkscreenSidesTop
    case Bottom    => SilkscreenSidesBottom
    case _         => SilkscreenSidesTopBottom
  }

  def fromBetaLayout(value: Int): BetaLayoutSilkscreenSides = value match {
    case 0 => SilkscreenSidesNone
    case 1 => SilkscreenSidesTop
    case 2 => SilkscreenSidesBottom
    case 3 => SilkscreenSidesTopBottom
    case _ => throw new IllegalArgumentException(s"Unknown silkscreen: $value")
  }
}
