package de.fellows.microservices.pcb.model.pcb.props

object HasPrepregs {
  val name: String  = "hasPrepregs"
  val label: String = "pcb.board.advanced.hasPrepregs"

  val yes: HasPrepregs = HasPrepregs(true)
  val no: HasPrepregs  = HasPrepregs(false)

  def apply(value: Option[Boolean]): HasPrepregs =
    value.fold(no)(HasPrepregs(_))
}

final case class HasPrepregs(override val value: Boolean) extends YesNoPCBProperty {
  val fieldName: String = HasPrepregs.name
  val label: String     = HasPrepregs.label
} 