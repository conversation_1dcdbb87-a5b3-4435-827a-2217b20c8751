package de.fellows.microservices.pcb.model.panel

import de.fellows.microservices.pcb.model.SquareMm
import play.api.libs.json.{Format, Json}

/** Represents a result of calculation of the wasted area of the PCB distribution.
  * @param wastedInSqm Wasted area on the panel with maximum PCB distribution.
  * @param leftOverWasted Wasted area on the panel with left over PCBs
  * @param fullPanelsAmount Number of panels with maximum PCB distribution.
  */
final case class WastedArea(wastedInSqm: SquareMm, leftOverWasted: SquareMm, fullPanelsAmount: Int) {
  val totalInSqm: SquareMm = wastedInSqm * fullPanelsAmount + leftOverWasted
}

object WastedArea extends RectangleMesh {
  implicit val format: Format[WastedArea] = Json.format[WastedArea]
  def zero: WastedArea                    = WastedArea(0, 0, 0)

  /** Calculates the amount of wasted area for the given set of parameters
    *
    * It returns None if the resulted area exceed the maximum panel size from constraints
    *
    * @param amount Total number of PCBs
    * @param rows Number of PCBs in the row
    * @param columns Number of PCBs in the column
    * @param pcb Width & height of PCB
    * @param gap: Gap between PCBs
    * @param minPanel: Minimum panel size
    * @param maxPanel: Maximum panel size
    */
  def calculate(
      amount: Int,
      rows: Int,
      columns: Int,
      pcb: PcbSize,
      gap: PanelGap,
      minPanel: PanelDimensions,
      maxPanel: PanelDimensions
  ): Option[WastedArea] = {
    val taken = netArea(pcb, gap, rows, columns)
    // If we exceed the size of the maximum panel size, we return None
    if (!maxPanel.fit(taken)) {
      None
    } else {
      val panel      = taken.adjustUp(minPanel)
      val wasted     = panel.area - taken.area
      val fullPanels = amount / (rows * columns)
      val leftOver   = amount % (rows * columns)
      val leftOverWasted =
        if (leftOver > 0) {
          panel.area - calculateLeftOverArea(leftOver, columns, pcb, gap)
        } else {
          BigDecimal(0.0f)
        }
      Some(WastedArea(wasted, leftOverWasted, fullPanels))
    }
  }

  def calculate(
      amount: Int,
      rows: Int,
      columns: Int,
      pcb: PcbSize,
      panelBound: PanelBound
  ): WastedArea = {
    val taken       = netArea(pcb, panelBound.gap, rows, columns)
    val pcbsInPanel = rows * columns
    val panelArea   = panelBound.area
    val wasted      = panelArea - taken.area
    val fullPanels  = amount / pcbsInPanel
    val leftOver    = amount % pcbsInPanel
    val leftOverWasted =
      if (leftOver > 0) {
        panelArea - calculateLeftOverArea(leftOver, columns, pcb, panelBound.gap)
      } else {
        BigDecimal(0.0f)
      }
    WastedArea(wasted, leftOverWasted, fullPanels)
  }

  /** Returns the area taken by the left over PCBs
    * @param amount Number of PCBs left over
    * @param columns Number of columns of PCBs per panel
    * @param pcb Width & Height of the PCB
    * @param gap Gap between the PCBs
    */
  private def calculateLeftOverArea(
      amount: Int,
      columns: Int,
      pcb: PcbSize,
      gap: PanelGap
  ): SquareMm = {
    val fullRows     = amount / columns
    val leftOverPCBs = amount % columns
    val fullRowArea  = netArea(pcb, gap, fullRows, columns).area
    val leftOverArea =
      if (leftOverPCBs > 0) {
        netWidth(pcb, gap, leftOverPCBs) * (pcb.heightInMm + gap.y)
      } else {
        BigDecimal(0.0f)
      }
    fullRowArea + leftOverArea
  }

}
