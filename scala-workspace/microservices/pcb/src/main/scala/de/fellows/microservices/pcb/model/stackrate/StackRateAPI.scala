package de.fellows.microservices.pcb.model.stackrate

import cats.data.EitherT
import akka.Done
import cats.syntax.traverse._
import cats.instances.future._
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.typesafe.config.Config
import de.fellows.app.assembly.commons.ProjectType
import de.fellows.app.assemby.api.{AssemblyService, FileState, FileTypeUpdate, FileTypeUpdates}
import de.fellows.ems.layerstack.api.{LayerDefinition, LayerStacksAPI, LayerstackService}
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.model.{APIOut<PERSON>, HoleList, LayerFile}
import de.fellows.microservices.pcb.PcbServer.{AUTH_HEADER_NAME, AsyncBackend}
import de.fellows.microservices.pcb._
import de.fellows.microservices.pcb.model.FileName
import de.fellows.microservices.pcb.model.pcb.PCB
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI.GraphicError._
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI._
import de.fellows.microservices.pcb.utils.json.JacksonJsonUtils.RawJsonValue
import de.fellows.microservices.pcb.utils.{StubLoader, StubPCB}
import de.fellows.utils.model.{PCBId, ShareId}
import de.fellows.utils.playutils.UploadFilesResult
import play.api.Logging
import play.api.libs.json._
import sttp.client3.playJson.SttpPlayJsonApi
import sttp.client3.{asFile, basicRequest, multipartFile}
import sttp.model.{StatusCode, Uri}

import java.io.File
import java.nio.file.{Files, StandardCopyOption}
import java.util.UUID
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import io.opentelemetry.extension.annotations.WithSpan
import java.net.URLEncoder

import scala.util.control.NonFatal

class StackRateAPI(
    config: Config,
    asyncBackend: AsyncBackend,
    assemblyService: AssemblyService,
    pcbService: PCBService,
    layerstackService: LayerstackService
) extends SttpPlayJsonApi with Logging
    with SttpHelper {

  private def usePcbStub: Boolean = config.getBoolean("stackrate.use_pcb_stub")
  private def useZipStub: Boolean = config.getBoolean("stackrate.use_zip_stub")
  private def useSvgStub: Boolean = config.getBoolean("stackrate.use_svg_stub")

  def getPcb(
      pcbId: PCBId,
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, PCB]] =
    if (usePcbStub) {
      Future.successful(StubPCB.get(pcbId, token))
    } else {
      getRealPcb(pcbId, token)
    }

  def getSharedPcb(share: ShareId, token: AuthToken)(implicit ec: ExecutionContext): Future[Either[ServiceError, PCB]] =
    handleErrors {
      pcbService
        .getSharedPCBV2(share.value)
        .handleRequestHeader(_.withHeader(AUTH_HEADER_NAME, token))
        .invoke()
        .map(PCB.apply)
    }

  def getPcbNoToken(
      pcbId: PCBId,
      team: Team
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, PCB]] =
    if (usePcbStub) {
      Future.successful(StubPCB.get(pcbId, ""))
    } else {
      getRealPcbNoToken(pcbId, team)
    }

  @WithSpan
  def getZipForShare(
      shareId: ShareId,
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, File]] =
    if (useZipStub) {
      val zipFile = Files.createTempFile("pcb", ".zip").toFile
      zipFile.deleteOnExit()
      val stubZip = getClass.getResourceAsStream("/stubs/gerberFiles.zip")
      Files.copy(stubZip, zipFile.toPath, StandardCopyOption.REPLACE_EXISTING)
      Future.successful(Right(zipFile))
    } else {
      val path = s"${config.getString("stackrate.pcb")}/api/ems/pcb/v2/shares/${shareId.toString}/files/all?format"
      doGetZip(path, token)
    }

  @WithSpan
  def getZip(
      pcbId: PCBId,
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, File]] =
    if (useZipStub) {
      val zipFile = Files.createTempFile("pcb", ".zip").toFile
      zipFile.deleteOnExit()
      val stubZip = getClass.getResourceAsStream("/stubs/gerberFiles.zip")
      Files.copy(stubZip, zipFile.toPath, StandardCopyOption.REPLACE_EXISTING)
      Future.successful(Right(zipFile))
    } else {
      val path = s"${config.getString("stackrate.pcb")}/api/ems/pcb/v2/pcbs/${pcbId.toString}/files/all?format"
      doGetZip(path, token)
    }

  private def doGetZip(
      endpoint: String,
      token: AuthToken
  )(implicit
      ec: ExecutionContext
  ): Future[Either[ServerError, File]] =
    Tracing.withTrace("doGetZip") { _ =>
      val zipFile = Files.createTempFile("pcb", ".zip").toFile
      zipFile.deleteOnExit()
      val uri = Uri.unsafeParse(endpoint)

      basicRequest
        .header(AUTH_HEADER_NAME, token)
        .get(uri)
        .response(asFile(zipFile))
        .send(asyncBackend)
        .map { response =>
          response.body match {
            case Left(error) =>
              Tracing.error(error)
              Left(ServerError(error))
            case Right(file) =>
              logger.info(s"Downloaded zip from ${endpoint}")
              Right(file)
          }
        }
    }

  def getPreviewUrl(path: String): String = s"${config.getString("stackrate.assembly")}$path"

  private def getRealPcb(
      pcbId: PCBId,
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, PCB]] =
    handleErrors {
      logger.info(s"Requesting PCB $pcbId from StackRate")
      pcbService
        .getPCBV2(pcbId.value)
        .handleRequestHeader(_.withHeader(AUTH_HEADER_NAME, token))
        .invoke()
        .map { pcb =>
          logger.info(s"Received PCB from StackRate with id ${pcb.id}")
          PCB(pcb)
        }
    }

  private def getRealPcbNoToken(
      pcbId: PCBId,
      team: Team
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, PCB]] =
    handleErrors {
      logger.info(s"Requesting PCB $pcbId from StackRate")
      pcbService
        ._getPCBV2(team.value, pcbId.value)
        .invoke()
        .map { pcb =>
          logger.info(s"Received PCB from StackRate with id ${pcb.id}")
          PCB(pcb)
        }
    }

  /** Get the PCB SVG layers and outline from StackRate
    */
  def getPCBGraphics(
      pcbId: PCBId,
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[GraphicError, Seq[PCBGraphic]]] =
    (for {
      pcb <- EitherT(getPcb(pcbId, token)).leftMap(GraphicError.apply)
      _ <- EitherT.cond(
        pcb.projectType == ProjectType.WithFiles,
        (),
        ServiceErrorWrapper(NotFoundError("No files found"))
      )

      futLayerStack = getLayerStack(pcb.assemblyId, pcbId, token)
      futOutline    = getPCBOutline(pcb.assemblyId, pcbId, token)
      futDrillHoles = getPCBDrillHoles(pcb.assemblyId, pcbId, token)

      layerStack <- EitherT(futLayerStack).leftMap(GraphicError.apply)
      layers     <- EitherT.fromEither(PCBLayerSupport.visibleLayers(layerStack))
      graphics   <- EitherT(getSVGGraphics(pcb.assemblyId, pcbId, layers, token)).leftMap(GraphicError.apply)
      outline    <- EitherT(futOutline)
      drillHoles <- EitherT(futDrillHoles).leftMap(GraphicError.apply)
    } yield graphics :+ drillHoles :+ outline).value

  /** Get the PCB's layer stack from StackRate
    */
  private def getLayerStack(
      assemblyId: AssemblyId,
      pcbId: PCBId,
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, LayerStacksAPI]] =
    if (useSvgStub) {
      val file = "/stubs/layerstack.json"
      Future.successful {
        StubLoader.readJsonStubFile[LayerStacksAPI](file)
      }
    } else {
      handleErrors {
        logger.info(s"Requesting PCB Layerstack $pcbId from StackRate")
        layerstackService
          .getPCBLayerstack(
            assemblyId.value,
            pcbId.value,
            materials = Some(true)
          )
          .handleRequestHeader(_.withHeader(AUTH_HEADER_NAME, token))
          .invoke()
      }
    }

  /** Get the PCB's outline from StackRate. The outline is needed to
    * compute the soldermark's bounding box
    */
  private def getPCBOutline(
      assemblyId: AssemblyId,
      pcbId: PCBId,
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[GraphicError, PCBOutlineGraphic]] =
    if (useSvgStub) {
      val file = "/stubs/outline.json"
      Future.successful {
        StubLoader
          .readJsonStubFile[APIOutline](file)
          .fold(
            error => Left(ServiceErrorWrapper(error)),
            outline => Right(PCBOutlineGraphic(OutlineJson(outline.toString)))
          )
      }
    } else {
      logger.info(s"Requesting PCB Outline $pcbId from StackRate")
      val path =
        s"${config.getString("stackrate.pcb")}/api/ems/pcb/$assemblyId/versions/$pcbId/outline?withGraphic=true"
      val uri = Uri.unsafeParse(path)

      basicRequest
        .header(AUTH_HEADER_NAME, token)
        .get(uri)
        .send(asyncBackend)
        .map { response =>
          response.body match {
            case Left(error) =>
              response.code match {
                case StatusCode.NotFound =>
                  logger.info(s"Outline not found for PCB with id $pcbId")
                  Left(MissingOutline)
                case _ =>
                  Left(ServiceErrorWrapper(ServerError(error)))
              }
            case Right(response) =>
              logger.info(s"Received PCB outline from StackRate for PCB with id $pcbId")
              Right(PCBOutlineGraphic(OutlineJson(response)))
          }
        }
    }

  /** Get the PCB's SVG graphics from StackRate
    */
  private def getPCBFileGraphic(
      assemblyId: AssemblyId,
      pcbId: PCBId,
      fileName: String,
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, RendererGraphicJson]] =
    if (useSvgStub) {
      val file = s"/stubs/svg-graphics-$fileName.json"
      Future.successful {
        StubLoader.readJsonStubFile[JsValue](file).map(s => RendererGraphicJson(s.toString()))
      }
    } else {
      logger.info(s"Requesting PCB SVG Graphic $pcbId (fileName=$fileName) from StackRate")
      val path =
        s"${config.getString("stackrate.renderer")}/api/ems/renderer/assemblies/$assemblyId/versions/$pcbId/files/graphics?file=${URLEncoder.encode(fileName, "UTF-8")}"
      val uri = Uri.unsafeParse(path)

      basicRequest
        .readTimeout(1.minutes)
        .header(AUTH_HEADER_NAME, token)
        .get(uri)
        .send(asyncBackend)
        .map { response =>
          response.body match {
            case Left(error) =>
              logger.error(s"Failed to get PCB Graphic for file ${fileName} from StackRate, error: ${error}")
              Left(ServerError(error))
            case Right(response) =>
              logger.info(s"Received SVG graphic from StackRate for PCB with id $pcbId")
              Right(RendererGraphicJson(response))
          }
        }
    }

  private def getPCBDrillHoles(
      assemblyId: AssemblyId,
      pcbId: PCBId,
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, PCBHoleListGraphic]] =
    if (useSvgStub) {
      val file = s"/stubs/drills.json"

      Future.successful {
        StubLoader.readJsonStubFile[Seq[HoleList]](file).map(s => PCBHoleListGraphic(HoleListJson(s.toString())))
      }
    } else {
      logger.info(s"Requesting drills for PCB $pcbId from StackRate")
      val path = s"${config.getString("stackrate.pcb")}/api/ems/pcb/$assemblyId/versions/$pcbId/drills"
      val uri  = Uri.unsafeParse(path)

      basicRequest
        .header(AUTH_HEADER_NAME, token)
        .get(uri)
        .send(asyncBackend)
        .map { response =>
          response.body match {
            case Left(error) =>
              logger.error(s"Failed to get PCB Hole list from StackRate, error: ${error}")
              Left(ServerError(error))
            case Right(response) =>
              logger.info(s"Received hole list from StackRate for PCB $pcbId")
              Right(PCBHoleListGraphic(HoleListJson(response)))
          }
        }
    }

  def unlockPcb(
      team: Team,
      assemblyId: AssemblyId,
      pcbId: PCBId,
      _token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, Done]] =
    if (usePcbStub) {
      Future.successful(Right(Done))
    } else {
      handleErrors {
        logger.info(s"Unlocking PCB in StackRate")
        assemblyService._approveFileMatchesWithState(
          team = team.value,
          assembly = assemblyId.value,
          version = Some(pcbId.value),
          initial = None
        )
          .invoke(FileState(locked = false))
          .map(_ => Done)
      }
    }

  def updateFileTypes(
      team: Team,
      assemblyId: AssemblyId,
      pcbId: PCBId,
      _token: AuthToken,
      fileTypeUpdates: Seq[FileTypeUpdate]
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, Done]] =
    if (usePcbStub) {
      Future.successful(Right(Done))
    } else {
      handleErrors {
        logger.info(s"Updating PCB file types in StackRate")
        assemblyService._updateFileTypes(
          team = team.value,
          assembly = assemblyId.value,
          version = Some(pcbId.value)
        )
          .invoke(FileTypeUpdates(fileTypeUpdates, timeline = None))
          .map(_ => Done)
      }
    }

  def uploadFiles(
      assemblyId: AssemblyId,
      pcbId: PCBId,
      files: Seq[(FileName, File)],
      authToken: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, UploadFilesResult]] = {
    logger.info(s"Uploading PCB files to StackRate")
    val path = s"${config.getString("stackrate.assembly")}/files/assembly/$assemblyId/versions/$pcbId/files"
    val uri  = Uri.unsafeParse(path)

    basicRequest
      .header(AUTH_HEADER_NAME, authToken)
      .post(uri)
      .multipartBody(
        files.map { case (fileName, file) => multipartFile(fileName.name, file) }
      )
      .response(asJson[UploadFilesResult])
      .send(asyncBackend)
      .map { response =>
        handleJsonResponse(response, "Stackrate")(Right(_))
      }

  }

  def deleteFiles(
      team: Team,
      assemblyId: AssemblyId,
      pcbId: PCBId,
      files: Seq[FileName]
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, Done]] =
    Future
      .traverse(files) { file =>
        handleErrors {
          assemblyService._deleteFile(
            team = team.value,
            assembly = assemblyId.value,
            version = Some(pcbId.value),
            file = file.name
          )
            .invoke()
        }
      }
      .map(
        _.sequence.map(_ => Done)
      )

  /** Get the PCB's SVG graphics from StackRate
    *
    * For each layer, we need to get the SVG graphic from StackRate.
    */
  private[stackrate] def getSVGGraphics(
      assemblyId: AssemblyId,
      pcbId: PCBId,
      layers: Seq[PCBLayer],
      token: AuthToken
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, Seq[PCBLayerGraphic]]] =
    Future
      .traverse(layers) { layer =>
        layer.file.flatMap(_.name) match {
          case Some(fileName) =>
            getPCBFileGraphic(assemblyId, pcbId, fileName, token)
              .map(_.map(graphic => PCBLayerGraphic(layer, Some(graphic))))
          case None => Future.successful(Right(PCBLayerGraphic(layer, None)))
        }
      }
      .map(_.sequence)

  private def handleErrors[Response](
      call: => Future[Response]
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, Response]] =
    call
      .map(Right(_))
      .recover {
        case e: TransportException =>
          e.errorCode match {
            case TransportErrorCode.NotFound     => Left(NotFoundError(e.getMessage))
            case TransportErrorCode.Unauthorized => Left(UnauthorizedError(e.getMessage))
            case error                           => Left(ThirdPartyError(e.getMessage, StatusCode.apply(error.http)))
          }

        case NonFatal(e) =>
          Left(ServerError(e.getMessage))
      }
}

object StackRateAPI {

  case class Team(value: String) extends AnyVal {
    override def toString: String = value
  }

  case class AssemblyId(value: UUID) extends AnyVal {
    override def toString: String = value.toString
  }

  sealed trait GraphicError
  object GraphicError {
    final case object MissingLayerstack                       extends GraphicError
    final case object MissingOutline                          extends GraphicError
    final case class ServiceErrorWrapper(error: ServiceError) extends GraphicError

    def apply(error: ServiceError): GraphicError = ServiceErrorWrapper(error)
  }

  final case class PCBLayer(
      definition: Option[LayerDefinition],
      file: Option[LayerFile]
  )

  object PCBLayer {
    implicit val writes: Writes[PCBLayer] = Json.writes[PCBLayer]
  }

  /** No PlayJson serializers are defined for PCBGraphics and each case class here
    *
    * We do this to avoid having to deserialize Stackrate responses just to serialize them again.
    * see [[de.fellows.microservices.pcb.utils.json.JacksonJsonUtils]] for a more in-depth explanation
    */
  sealed trait PCBGraphic

  case class RendererGraphicJson(value: RawJsonValue)                              extends AnyVal
  case class PCBLayerGraphic(data: PCBLayer, graphic: Option[RendererGraphicJson]) extends PCBGraphic

  final case class OutlineJson(value: RawJsonValue)     extends AnyVal
  final case class PCBOutlineGraphic(data: OutlineJson) extends PCBGraphic

  final case class HoleListJson(value: RawJsonValue)      extends AnyVal
  final case class PCBHoleListGraphic(data: HoleListJson) extends PCBGraphic
}
