package de.fellows.microservices.pcb.model.pcb.props

object MaxXOutsAllowed {
  val name: String  = "maxXOutsAllowed"
  val label: String = "pcb.mechanical.maxXOutsAllowed"

  def empty: MaxXOutsAllowed                             = MaxXOutsAllowed(Option.empty[Int])
  def apply(value: Int): MaxXOutsAllowed                 = MaxXOutsAllowed(Some(value))
  def fromV2(value: Option[BigDecimal]): MaxXOutsAllowed = MaxXOutsAllowed(value.map(_.toInt))
}

final case class MaxXOutsAllowed(override val value: Option[Int]) extends IntPCBProperty {
  val fieldName: String = MaxXOutsAllowed.name
  val label: String     = MaxXOutsAllowed.label
  val unit: String      = "X-Outs"
}
