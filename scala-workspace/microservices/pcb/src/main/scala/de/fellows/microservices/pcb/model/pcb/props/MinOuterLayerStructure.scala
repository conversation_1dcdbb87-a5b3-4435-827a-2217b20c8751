package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters
import de.fellows.microservices.pcb.model.pcb.capability.MinMaxCapability

object MinOuterLayerStructure {
  val name: String  = "minOuterLayerStructure"
  val label: String = "pcb.layer.minOuterLayerStructure"

  // I just take a really big value here (500 mm considering that the biggest value is 0.25 mm for Wurth)
  private val Max: Millimeters = 500

  def empty: MinOuterLayerStructure                     = MinOuterLayerStructure(None)
  def apply(value: Millimeters): MinOuterLayerStructure = MinOuterLayerStructure(Some(value))

  /** According to Nils Minor, there is no sense in defining upper limit for layer structure as it's important
    * for manufacturers to know the minimum layer structure.
    */
  case class MinOuterLayerStructureCapability(min: Millimeters)
      extends MinMaxCapability[MinOuterLayerStructure, Millimeters](min, Max, required = true)
}

final case class MinOuterLayerStructure(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = MinOuterLayerStructure.name
  val label: String     = MinOuterLayerStructure.label

  override val legacyNames: Option[Seq[String]] = Some(Seq("min_outer_structure"))

}
