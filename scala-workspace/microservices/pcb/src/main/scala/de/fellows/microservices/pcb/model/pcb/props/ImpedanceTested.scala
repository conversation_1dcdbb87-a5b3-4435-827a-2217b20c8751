package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.YesNoCapability

object ImpedanceTested {
  val name: String  = "impedanceTested"
  val label: String = "pcb.board.advanced.impedanceTested"

  def no: ImpedanceTested                            = ImpedanceTested(false)
  def apply(value: Option[Boolean]): ImpedanceTested = ImpedanceTested(value.getOrElse(false))

  type ImpedanceTestedCapability = YesNoCapability[ImpedanceTested]
}

final case class ImpedanceTested(override val value: Boolean) extends YesNoPCBProperty {
  override val legacyNames: Option[Seq[String]] = Some(Seq("impedance_controlled"))
  val fieldName: String                         = ImpedanceTested.name
  val label: String                             = ImpedanceTested.label
}
