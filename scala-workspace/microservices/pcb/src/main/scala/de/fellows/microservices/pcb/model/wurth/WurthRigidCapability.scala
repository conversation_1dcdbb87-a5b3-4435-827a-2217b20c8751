package de.fellows.microservices.pcb.model.wurth

import com.osinka.i18n.{Lang, Messages}
import de.fellows.ems.pcb.api.specification.IPC600Class.{IPC1, IPC2}
import de.fellows.ems.pcb.api.specification.ViaFillingType.PluggedSingleSided
import de.fellows.ems.pcb.api.specification.{
  BaseMaterial => PCBBaseMaterial,
  Chamfering,
  LayerstackType => SpecLayerstackType,
  Side,
  SurfaceFinish => PCBSurfaceFinish,
  ViaFillingType
}
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.{No, Yes}
import de.fellows.microservices.pcb.model.pcb.capability._
import de.fellows.microservices.pcb.model.pcb.props.BaseMaterial.BaseMaterialCapability
import de.fellows.microservices.pcb.model.pcb.props.BlindVias.BlindViasCapability
import de.fellows.microservices.pcb.model.pcb.props.BuriedVias.BuriedViasCapability
import de.fellows.microservices.pcb.model.pcb.props.Chamfering.ChamferingCapability
import de.fellows.microservices.pcb.model.pcb.props.ETest.ETestCapability
import de.fellows.microservices.pcb.model.pcb.props.EdgeMetalization.EdgeMetalizationCapability
import de.fellows.microservices.pcb.model.pcb.props.FinalThickness.FinalThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.IPCA600Class.IPCA600ClassCapability
import de.fellows.microservices.pcb.model.pcb.props.ImpedanceTested.ImpedanceTestedCapability
import de.fellows.microservices.pcb.model.pcb.props.InnerCopperThickness.InnerCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.LayerstackType.LayerstackTypeCapability
import de.fellows.microservices.pcb.model.pcb.props.MinInnerLayerStructure.MinInnerLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.MinOuterLayerStructure.MinOuterLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.MinViaDiameter.MinViaDiameterCapability
import de.fellows.microservices.pcb.model.pcb.props.NumberOfLayers.NumberOfLayersCapability
import de.fellows.microservices.pcb.model.pcb.props.OuterCopperThickness.OuterCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.PCBPropertyExtractor._
import de.fellows.microservices.pcb.model.pcb.props.PeelableMask.PeelableMaskCapability
import de.fellows.microservices.pcb.model.pcb.props.PressFit.PressFitCapability
import de.fellows.microservices.pcb.model.pcb.props.SurfaceFinish.SurfaceFinishCapability
import de.fellows.microservices.pcb.model.pcb.props.ULLayerStack.ULLayerStackCapability
import de.fellows.microservices.pcb.model.pcb.props.ViaFillingType.ViaFillingTypeCapability
import de.fellows.microservices.pcb.model.pcb.props._
import zio.prelude.Validation

object WurthRigidCapability {

  // Buried Vias: only available if thickness is 0.8mm <= x < 2.00mm
  // and if the number of layers is 4 <= x < 10
  //
  // NOTE: to improve the messaging when this condition fails, we hardcoded a message
  // on the base capability. See below on the non-conditioned capabilities
  //
  private val thicknessBuriedViasCondition = Condition(
    Rule.And(
      Rule.RangeIncl(props.NumberOfLayers(4), props.NumberOfLayers(8)),
      Rule.RangeIncl(props.FinalThickness(0.8), props.FinalThickness(1.55)),
      Rule.Eq(props.BlindVias.no)
    ),
    new BuriedViasCapability(No, Yes)
  )

  // Blind Vias: only available if no buried vias are present
  //
  // NOTE: to improve the messaging when this condition fails, we hardcoded a message
  // on the base capability. See below on the non-conditioned capabilities
  private val blindViasCondition = Condition(
    Rule.Eq(props.BuriedVias.no),
    new BlindViasCapability(No, Yes)
  )

  // smallest via
  //   - if thickness is <= 1.55mm, wurth allows 0.1 be picked
  //   - otherwise, wurth only allows 0.25
  private val smallestViaCondition = Condition(
    Rule.LtEq(props.FinalThickness(1.55)),
    new MinViaDiameterCapability(0.1, Int.MaxValue, required = true)
  )

  // thickness -> available layer options
  //   - if thickness is < 1.00mm        => max of  4 layers allowed
  //   - if 1.00mm <= thickness < 2.40mm => max of 12 layers allowed
  //   - if thickness >= 2.40mm          => max of 16 layers allowed
  private val thicknessConditions = Seq(
    Condition(
      Rule.LtEq(props.FinalThickness(0.8)),
      new NumberOfLayersCapability(props.OneLayer, props.TwoLayers, props.FourLayers)
    ),
    Condition(
      Rule.RangeInclEnd(props.FinalThickness(0.8), props.FinalThickness(1.0)),
      new NumberOfLayersCapability(props.OneLayer, props.TwoLayers, props.FourLayers, props.SixLayers)
    ),
    Condition(
      Rule.RangeInclEnd(props.FinalThickness(1.0), props.FinalThickness(2.0)),
      new NumberOfLayersCapability(
        props.OneLayer,
        props.TwoLayers,
        props.FourLayers,
        props.SixLayers,
        props.EightLayers,
        props.TenLayers,
        props.TwelveLayers
      )
    ),
    Condition(
      Rule.Gt(props.FinalThickness(2.0)),
      new NumberOfLayersCapability(
        props.OneLayer,
        props.TwoLayers,
        props.FourLayers,
        props.SixLayers,
        props.EightLayers,
        props.TenLayers,
        props.TwelveLayers,
        props.FourteenLayers,
        props.SixteenLayers
      )
    )
  )

  // the number of layers also affects the available thickness options.
  //  - < 6 layers allow any final thickness
  //  - 6 layers require a minimum of 1.00mm final thickness
  //  - 8 to 12 layers require a minimum of 1.55mm final thickness
  //  - 14 or more require a minimum of 2.40mm final thickness
  private val layerConditions = Seq(
    Condition(
      Rule.LtEq(props.NumberOfLayers(4)),
      FinalThicknessCapability.fromMinMax(
        FinalThickness.Thickness05mm,
        FinalThickness.Thickness32mm
      )
    ),
    Condition(
      Rule.Eq(props.NumberOfLayers(6)),
      FinalThicknessCapability.fromMinMax(
        FinalThickness.Thickness1mm,
        FinalThickness.Thickness32mm
      )
    ),
    Condition(
      Rule.RangeIncl(props.NumberOfLayers(8), props.NumberOfLayers(12)),
      FinalThicknessCapability.fromMinMax(
        FinalThickness.Thickness155mm,
        FinalThickness.Thickness32mm
      )
    ),
    Condition(
      Rule.GtEq(props.NumberOfLayers(14)),
      FinalThicknessCapability.fromMinMax(
        FinalThickness.Thickness24mm,
        FinalThickness.Thickness32mm
      )
    ),
    Condition(
      Rule.LtEq(props.NumberOfLayers(2)),
      new OuterCopperThicknessCapability(Outer18mcr, Outer35mcr, Outer70mcr, Outer105mcr)
    ),
    Condition(
      Rule.RangeInclEnd(props.NumberOfLayers(2), props.NumberOfLayers(8)),
      Seq(
        new OuterCopperThicknessCapability(Outer35mcr, Outer70mcr, Outer105mcr),
        new InnerCopperThicknessCapability(Inner18mcr, Inner35mcr, Inner70mcr, Inner105mcr)
      )
    ),
    Condition(
      Rule.RangeInclEnd(props.NumberOfLayers(8), props.NumberOfLayers(12)),
      Seq(
        new OuterCopperThicknessCapability(Outer35mcr, Outer70mcr),
        new InnerCopperThicknessCapability(Inner18mcr, Inner35mcr, Inner70mcr)
      )
    ),
    Condition(
      Rule.Gt(props.NumberOfLayers(12)),
      Seq(
        new OuterCopperThicknessCapability(Outer35mcr),
        new InnerCopperThicknessCapability(Inner18mcr, Inner35mcr)
      )
    )
  )

  private val eTestCondition = Seq(
    Condition(
      Rule.Lt(props.NumberOfLayers(4)),
      new ETestCapability(Yes, No)
    )
  )

  private val outsideCopperThicknessConditions = Seq(
    Condition(
      Rule.RangeInclEnd(props.OuterCopperThickness(0), props.OuterCopperThickness(18)),
      MinOuterLayerStructureCapability(0.085)
    ),
    Condition(
      Rule.RangeInclEnd(props.OuterCopperThickness(18), props.OuterCopperThickness(35)),
      MinOuterLayerStructureCapability(0.100)
    ),
    Condition(
      Rule.RangeInclEnd(props.OuterCopperThickness(35), props.OuterCopperThickness(70)),
      MinOuterLayerStructureCapability(0.192)
    ),
    Condition(
      Rule.RangeInclEnd(props.OuterCopperThickness(70), props.OuterCopperThickness(105)),
      MinOuterLayerStructureCapability(0.250)
    )
  )

  private val innerCopperThicknessConditions = Seq(
    Condition(
      // we need this first range to be 0 < x <= 18 because otherwise an empty inner thickness
      // would match which would cause MinInnerLayerStructureCapability to be required
      Rule.RangeInclEnd(props.InnerCopperThickness(0), props.InnerCopperThickness(18)),
      MinInnerLayerStructureCapability(0.085)
    ),
    Condition(
      Rule.RangeInclEnd(props.InnerCopperThickness(18), props.InnerCopperThickness(35)),
      MinInnerLayerStructureCapability(0.100)
    ),
    Condition(
      Rule.RangeInclEnd(props.InnerCopperThickness(35), props.InnerCopperThickness(70)),
      MinInnerLayerStructureCapability(0.192)
    ),
    Condition(
      Rule.RangeInclEnd(props.InnerCopperThickness(70), props.InnerCopperThickness(105)),
      MinInnerLayerStructureCapability(0.250)
    )
  )

  val surfaceFinishCapability = Condition(
    Rule.Eq(props.OuterCopperThickness(35)),
    new SurfaceFinishCapability(PCBSurfaceFinish.Enig, PCBSurfaceFinish.It, PCBSurfaceFinish.HalPbFree)
  )

  val conditionalCapabilities = Seq(
    thicknessBuriedViasCondition,
    smallestViaCondition,
    blindViasCondition,
    surfaceFinishCapability
  ) ++ layerConditions ++
    thicknessConditions ++
    outsideCopperThicknessConditions ++
    innerCopperThicknessConditions ++
    eTestCondition

  val advancedBoardCapability = AdvancedBoardCapability(
    new IPCA600ClassCapability(IPC1, IPC2),
    new ETestCapability(Yes),
    new PressFitCapability(No),
    new ImpedanceTestedCapability(No),
    new PeelableMaskCapability(Side.None),
    new EdgeMetalizationCapability(Yes, No)
  )

  val layerStackCapability = LayerStackCapability(
    new LayerstackTypeCapability(SpecLayerstackType.Rigid),
    new ULLayerStackCapability(No),
    new NumberOfLayersCapability(props.OneLayer, props.TwoLayers),
    FinalThicknessCapability.fromMinMax(FinalThickness.Thickness24mm, FinalThickness.Thickness32mm),
    new BaseMaterialCapability(PCBBaseMaterial.FR4),
    new OuterCopperThicknessCapability(Outer35mcr, Outer70mcr, Outer105mcr),
    new InnerCopperThicknessCapability(),
    MinOuterLayerStructureCapability(0.250),
    MinInnerLayerStructureCapability(0.250)
  )

  val mechanicalCapability = MechanicalCapability(
    new MinViaDiameterCapability(0.25, Int.MaxValue, required = true),
    new ViaFillingTypeCapability(ViaFillingType.None, PluggedSingleSided),
    // Both blind and buried vias validation have hardcoded messages.
    // This is because the default message is not very clear, particularly in blind/buried vias case.
    // wurth doesn't allow both at the same time, so it would look confusing
    new BlindViasCapability(No) {
      override def validate(value: BlindVias)(implicit lang: Lang): Validation[PropertyError, BlindVias] =
        super.validate(value).mapError(_ =>
          PropertyError(
            value,
            Messages("pcb.error.capability.wurth.blindVias.error"),
            PropertyErrorKind.CustomLabel(CustomLabelKind.WurthBlindViasError)
          )
        )
    },
    new BuriedViasCapability(No) {
      override def validate(value: BuriedVias)(implicit lang: Lang): Validation[PropertyError, BuriedVias] =
        super.validate(value).mapError(_ =>
          PropertyError(
            value,
            Messages("pcb.error.capability.wurth.buriedVias.error"),
            PropertyErrorKind.CustomLabel(CustomLabelKind.WurthBuriedViasError)
          )
        )
    },
    new ChamferingCapability(Chamfering.None)
  )

  val capability = ManufacturerCapability(
    WurthCapability.basicBoardCapability,
    WurthCapability.advancedBoardCapability,
    layerStackCapability,
    mechanicalCapability,
    conditionalCapabilities
  )
}
