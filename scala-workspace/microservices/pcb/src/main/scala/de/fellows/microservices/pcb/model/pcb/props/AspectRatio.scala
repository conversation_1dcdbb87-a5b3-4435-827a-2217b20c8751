package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Percent

/** The smallest aspect-ratio found on the PCB
  */
object AspectRatio {
  val name: String  = "aspectRatio"
  val label: String = "pcb.mechanical.aspectRatio"

  def empty: AspectRatio                 = AspectRatio(None)
  def apply(value: Percent): AspectRatio = AspectRatio(Some(value))
}

final case class AspectRatio(override val value: Option[Percent]) extends DecimalPCBProperty {
  val fieldName: String = AspectRatio.name
  val label: String     = AspectRatio.label
  val unit: String      = "%"
}
