package de.fellows.microservices.pcb.api

import play.api.libs.json.{<PERSON><PERSON>, Writes}
import de.fellows.microservices.pcb.model.pcb.PropertyErrorResponse

sealed trait PcbServerErrorResponse extends Product with Serializable

object PcbServerErrorResponse {
  case class BadRequest(message: String)                        extends PcbServerErrorResponse
  case class TechnicalError(message: String)                    extends PcbServerErrorResponse
  case class PropertyErrors(errors: Seq[PropertyErrorResponse]) extends PcbServerErrorResponse
  case class PanelError(message: String)                        extends PcbServerErrorResponse
  case object MissingLayerstack                                 extends PcbServerErrorResponse
  case object MissingOutline                                    extends PcbServerErrorResponse
  case object StackratePricingIssue                             extends PcbServerErrorResponse
  case object CustomStackup                                     extends PcbServerErrorResponse

  implicit val writes: Writes[PcbServerErrorResponse] = {
    (o: PcbServerErrorResponse) =>
      val errorData = o match {
        case BadRequest(message)     => Json.obj("type" -> "BadRequest", "message" -> message)
        case TechnicalError(message) => Json.obj("type" -> "TechnicalError", "message" -> message)
        case PropertyErrors(errors)  => Json.obj("type" -> "PropertyErrors", "errors" -> errors)
        case PanelError(error)       => Json.obj("type" -> "PanelError", "message" -> error)
        case MissingLayerstack       => Json.obj("type" -> "MissingLayerstack")
        case MissingOutline          => Json.obj("type" -> "MissingOutline")
        case StackratePricingIssue   => Json.obj("type" -> "StackratePricingIssue")
        case CustomStackup           => Json.obj("type" -> "CustomStackup")
      }

      Json.obj(
        "status" -> "error",
        "error"  -> errorData
      )
  }
}
