<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="QuoteOrder_Data">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sPwd" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Data1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Data2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Data3" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Data4" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="QuoteOrder_DataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="QuoteOrder_DataResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Get_QuoteOrder_PricingByID">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sPwd" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="QuoteOrderID" type="s:long" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Get_QuoteOrder_PricingByIDResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Get_QuoteOrder_PricingByIDResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="QuoteOrder_DataSoapIn">
    <wsdl:part name="parameters" element="tns:QuoteOrder_Data" />
  </wsdl:message>
  <wsdl:message name="QuoteOrder_DataSoapOut">
    <wsdl:part name="parameters" element="tns:QuoteOrder_DataResponse" />
  </wsdl:message>
  <wsdl:message name="Get_QuoteOrder_PricingByIDSoapIn">
    <wsdl:part name="parameters" element="tns:Get_QuoteOrder_PricingByID" />
  </wsdl:message>
  <wsdl:message name="Get_QuoteOrder_PricingByIDSoapOut">
    <wsdl:part name="parameters" element="tns:Get_QuoteOrder_PricingByIDResponse" />
  </wsdl:message>
  <wsdl:portType name="WebServiceSoap">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="QuoteOrder_Data">
      <wsdl:input message="tns:QuoteOrder_DataSoapIn" />
      <wsdl:output message="tns:QuoteOrder_DataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Get_QuoteOrder_PricingByID">
      <wsdl:input message="tns:Get_QuoteOrder_PricingByIDSoapIn" />
      <wsdl:output message="tns:Get_QuoteOrder_PricingByIDSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="WebServiceSoap" type="tns:WebServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QuoteOrder_Data">
      <soap:operation soapAction="http://tempuri.org/QuoteOrder_Data" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Get_QuoteOrder_PricingByID">
      <soap:operation soapAction="http://tempuri.org/Get_QuoteOrder_PricingByID" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="WebServiceSoap12" type="tns:WebServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QuoteOrder_Data">
      <soap12:operation soapAction="http://tempuri.org/QuoteOrder_Data" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Get_QuoteOrder_PricingByID">
      <soap12:operation soapAction="http://tempuri.org/Get_QuoteOrder_PricingByID" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="WebService">
    <wsdl:port name="WebServiceSoap" binding="tns:WebServiceSoap">
      <soap:address location="https://perceptiveresource.com/ErpApctApiSales/webservice.asmx" />
    </wsdl:port>
    <wsdl:port name="WebServiceSoap12" binding="tns:WebServiceSoap12">
      <soap12:address location="https://perceptiveresource.com/ErpApctApiSales/webservice.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>