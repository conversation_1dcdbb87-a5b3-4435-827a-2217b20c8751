package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.ems.pcb.api.specification.Side.{ Both, Bottom, Top }
//import de.fellows.microservices.pcb.model.pcb._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class SilkscreenSideTest extends AnyFlatSpec with should.Matchers {
  val silkscreenSides = de.fellows.microservices.pcb.model.pcb.props.SilkscreenSide(None)
  "BetaLayout Silkscreen" should "be both sides if no silkscreen is given" in {
    BetaLayoutSilkscreenSides.fromPcb(silkscreenSides) shouldBe SilkscreenSidesTopBottom
  }
  it should "be bottom" in {
    BetaLayoutSilkscreenSides.fromPcb(silkscreenSides.copy(value = Bottom)) shouldBe SilkscreenSidesBottom
  }
  it should "be top" in {
    BetaLayoutSilkscreenSides.fromPcb(silkscreenSides.copy(value = Top)) shouldBe SilkscreenSidesTop
  }
  it should "be TopBottom" in {
    BetaLayoutSilkscreenSides.fromPcb(silkscreenSides.copy(value = Both)) shouldBe SilkscreenSidesTopBottom
  }
  it should "be None" in {
    BetaLayoutSilkscreenSides.fromPcb(
      silkscreenSides.copy(value = de.fellows.ems.pcb.api.specification.Side.None)
    ) shouldBe SilkscreenSidesNone
  }
}
