package de.fellows.microservices.pcb

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>s<PERSON>ath, <PERSON>s<PERSON><PERSON><PERSON>, JsonValidationError}
import sttp.client3.playJson.SttpPlayJsonApi
import sttp.client3.{basicRequest, DeserializationException, UriContext}
import sttp.client3.testing.SttpBackendStub
import sttp.model._

class SttpHelperTest extends AnyFlatSpec with should.Matchers with SttpPlayJsonApi {

  "JSON response handler" should "return NotFound when http code = 404 " in {
    class Handler extends SttpHelper {
      def sendTestRequest() = {
        val backend  = SttpBackendStub.synchronous.whenRequestMatches(_.method == Method.GET).thenRespondNotFound()
        val response = basicRequest.get(uri"https://google.com").response(asJson[JsValue]).send(backend)
        handleJsonResponse(response, "Handler") { success =>
          Right(success)
        }
      }
    }
    new Handler().sendTestRequest() shouldBe Left(
      NotFoundError("Not found error in Handler. URI: GET http://example.com")
    )
  }
  it should "return ThirdPartyError in other 4xx or 5xx cases" in {
    class Handler extends SttpHelper {
      def sendTestRequest() = {
        val backend  = SttpBackendStub.synchronous.whenRequestMatches(_.method == Method.GET).thenRespondServerError()
        val response = basicRequest.get(uri"https://google.com").response(asJson[JsValue]).send(backend)
        handleJsonResponse(response, "Handler") { success =>
          Right(success)
        }
      }
    }
    new Handler().sendTestRequest() shouldBe Left(
      ThirdPartyError(
        "A problem in communication with Handler. Http code: 500, Error: Internal server error",
        StatusCode.InternalServerError
      )
    )
  }
  it should "return ThirdPartyError in case of JSON deserialization error" in {
    class Handler extends SttpHelper {
      def sendTestRequest(): Either[ServiceError, JsValue] = {
        val backend =
          SttpBackendStub.synchronous.whenRequestMatches(_.method == Method.GET).thenRespond("<html>OK</html>")
        val response = basicRequest.get(uri"https://google.com").response(asJson[JsValue]).send(backend)
        handleJsonResponse(response, "Handler") { success =>
          Right(success)
        }
      }
    }
    new Handler().sendTestRequest() shouldBe Left(ExceptionError(
      "Could not deserialize response \"<html>OK</html>\" from Handler",
      DeserializationException(
        "<html>OK</html>",
        JsError(
          List(
            (
              JsPath,
              List(
                JsonValidationError(
                  List(
                    "Unexpected character ('<' (code 60)): expected a valid value (JSON String, Number, Array, Object or token 'null', 'true' or 'false')\n at [Source: (String)\"<html>OK</html>\"; line: 1, column: 2]"
                  )
                )
              )
            )
          )
        )
      )
    ))
  }
  it should "return the response in case of success" in {
    class Handler extends SttpHelper {
      def sendTestRequest() = {
        val backend  = SttpBackendStub.synchronous.whenRequestMatches(_.method == Method.GET).thenRespond("{}")
        val response = basicRequest.get(uri"https://google.com").response(asJson[JsValue]).send(backend)
        handleJsonResponse(response, "Handler") { success =>
          Right(success)
        }
      }
    }
    new Handler().sendTestRequest() shouldBe Right(play.api.libs.json.Json.obj())
  }

}
