package de.fellows.microservices.pcb.model.panel

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class EmsPreferencesTest extends AnyFlatSpec with should.Matchers {

  val panelPreferences = PanelPreferences.Empty.copy(
    minWidth = 10.0,
    minHeight = 10.0,
    maxWidth = 200.0,
    maxHeight = 100.0,
    maxPCBs = None,
    padding = new PanelPadding(10.0, 5.0),
    spacing = new PanelGap(2.0, 1.0)
  )

  "Harmonize" should "return bounds with min panel rotated " in {
    val constraints = panelPreferences.copy(maxHeight = 150, minHeight = 50)
    val (min, _)    = constraints.harmonize
    min.w should be(50.0)
    min.h should be(10.0)
    min.padding.leftInMm should be(5.0)
    min.padding.rightInMm should be(5.0)
    min.padding.topInMm should be(10.0)
    min.padding.bottomInMm should be(10.0)
    min.gap.x should be(1.0)
    min.gap.y should be(2.0)
  }
  it should "return bounds with max panel not rotated" in {
    val constraints = panelPreferences.copy(maxHeight = 150, minHeight = 50)
    val (_, max)    = constraints.harmonize
    max.w should be(200.0)
    max.h should be(150.0)
    max.padding.leftInMm should be(10.0)
    max.padding.rightInMm should be(10.0)
    max.padding.topInMm should be(5.0)
    max.padding.bottomInMm should be(5.0)
    max.gap.x should be(2.0)
    max.gap.y should be(1.0)
  }

  "Limit" should "return a new min and max constraints for height and width" in {
    val limit   = PanelConstraints(30, 30, 50, 150)
    val limited = panelPreferences.limit(limit)
    limited.minWidth should be(30)
    limited.minHeight should be(30)
    limited.maxWidth should be(150)
    limited.maxHeight should be(50)
  }
}
