package de.fellows.microservices.pcb.model.panel

import de.fellows.ems.pcb.model.{BigPoint, Dimension}
import de.fellows.microservices.pcb.{PanelError, PanelErrorKind}
import org.scalatest.EitherValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import de.fellows.luminovo.panel.Depanelization

/** Here we test several easy corner cases + the output for padding strips:
  *  - padding for X and Y > 0
  *  - 1 PCB exceeds the maximum panel size
  *  - Amount of PCBs = 1
  *  - Max number of PCBs per panel = 1
  */
class OnePCBDistributionTestAlgorithm extends AnyFlatSpec with should.Matchers with EitherValues {

  private val panelPreferences = PanelPreferences.Empty.copy(
    minWidth = 10.0,
    minHeight = 10.0,
    maxWidth = 200.0,
    maxHeight = 150.0,
    maxPCBs = None,
    padding = new PanelPadding(10.0, 5.0),
    spacing = Rectangle(2.0, 1.0)
  )
  "Distribution" should "return no distribution if PCB width = 181.0 exceeds panel width minus padding (200 - 10*2)" in {
    // Max Panel width is 200.0, padding is 10.0
    val res = DistributionAlgorithm.calculate(10, new PcbSize(181.0, 50.0), panelPreferences)
    res shouldBe Left(PanelError(PanelErrorKind.PcbWidthExceedsDeliveryPanel))
  }
  it should "return no distribution if PCB height = 91.0 exceeds maximum panel height minus padding (100 - 5*2)" in {
    // Max Panel height is 100.0, padding is 10.0
    val res = DistributionAlgorithm.calculate(10, new PcbSize(150.0, 91.0), panelPreferences.copy(maxHeight = 100))
    res shouldBe Left(PanelError(PanelErrorKind.PcbHeightExceedsDeliveryPanel))
  }
  it should "return 1 PCB center-aligned if it's just one PCB and less than minimal panel size" in {
    val constraints = panelPreferences.copy(minWidth = 150.0, minHeight = 100.0)
    val rects = Seq(
      DistributionRectangle(Dimension(BigPoint(0, 0), BigPoint(150.0, 5.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(0, 5.0), BigPoint(10.0, 100.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 95.0), BigPoint(150.0, 100.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(140.0, 5.0), BigPoint(150.0, 95.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(55.0, 35.0), BigPoint(95.0, 65.0)), Pcb)
    )
    calculateDistribution(1, new PcbSize(40, 30), constraints).value.items shouldBe rects
  }
  it should "return 1 PCB center-aligned on rotated min panel if it's just one PCB and less than minimal panel size" in {
    val constraints = panelPreferences.copy(minWidth = 100.0, minHeight = 150.0)
    val rects = Seq(
      DistributionRectangle(Dimension(BigPoint(0, 0), BigPoint(150.0, 10.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(0, 10.0), BigPoint(5.0, 100.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(5.0, 90.0), BigPoint(150.0, 100.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(145.0, 10.0), BigPoint(150.0, 90.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(55.0, 35.0), BigPoint(95.0, 65.0)), Pcb)
    )
    calculateDistribution(1, new PcbSize(40, 30), constraints).value.items shouldBe rects
  }
  it should "return 1 PCB center-aligned by X if it's just one PCB and width is less than minimal panel width" in {
    // panel height = 110 = 100 + 5 * 2
    val constraints = panelPreferences.copy(minWidth = 150.0, minHeight = 100.0)
    val rects = Seq(
      DistributionRectangle(Dimension(BigPoint(0, 0), BigPoint(150.0, 5.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(0, 5.0), BigPoint(10.0, 110.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 105.0), BigPoint(150.0, 110.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(140.0, 5.0), BigPoint(150.0, 105.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(15.0, 5.0), BigPoint(135.0, 105.0)), Pcb)
    )
    calculateDistribution(1, new PcbSize(120, 100), constraints).value.items shouldBe rects
  }
  it should "return 1 PCB center-aligned by Y if it's just one PCB and height is less than minimal panel height" in {
    // panel width = 200 = 180 + 10 * 2
    val constraints = panelPreferences.copy(minWidth = 150.0, minHeight = 100.0)
    val rects = Seq(
      DistributionRectangle(Dimension(BigPoint(0, 0), BigPoint(200.0, 5.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(0, 5.0), BigPoint(10.0, 100.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 95.0), BigPoint(200.0, 100.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(190.0, 5.0), BigPoint(200.0, 95.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 35.0), BigPoint(190.0, 65.0)), Pcb)
    )
    calculateDistribution(1, new PcbSize(180, 30), constraints).value.items shouldBe rects
  }
  it should "return 1 PCB taking the whole area of the panel if it's just one PCB" in {
    // panel width = 200 = 180 + 10 * 2
    // panel height = 110 = 100 + 5 * 2
    val constraints = panelPreferences.copy(minWidth = 150.0, minHeight = 100.0)
    val rects = Seq(
      DistributionRectangle(Dimension(BigPoint(0, 0), BigPoint(200.0, 5.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(0, 5.0), BigPoint(10.0, 110.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 105.0), BigPoint(200.0, 110.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(190.0, 5.0), BigPoint(200.0, 105.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 5.0), BigPoint(190.0, 105.0)), Pcb)
    )
    calculateDistribution(1, new PcbSize(180, 100), constraints).value.items shouldBe rects
  }
  it should "return 1 PCB taking the whole area of the panel if max number of PCB per panel = 1" in {
    // panel width = 200 = 180 + 10 * 2
    // panel height = 110 = 100 + 5 * 2
    val constraints = panelPreferences.copy(minWidth = 150.0, minHeight = 100.0, maxPCBs = Some(1))
    val rects = Seq(
      DistributionRectangle(Dimension(BigPoint(0, 0), BigPoint(200.0, 5.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(0, 5.0), BigPoint(10.0, 110.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 105.0), BigPoint(200.0, 110.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(190.0, 5.0), BigPoint(200.0, 105.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 5.0), BigPoint(190.0, 105.0)), Pcb)
    )
    calculateDistribution(5, new PcbSize(180, 100), constraints).value.items shouldBe rects
  }

  it should "prefer perfect amount panels" in {
    val prefs = PanelPreferences(
      minWidth = 50,
      minHeight = 50,
      maxWidth = 450,
      maxHeight = 400,
      maxPCBs = Some(10),
      padding = new PanelPadding(5, 5),
      spacing = new PanelGap(5, 5),
      depanelization = Depanelization.VCut
    )

    calculateDistribution(5, new PcbSize(32, 32), prefs).value.pcbPerPanel shouldBe 5
  }

  private def calculateDistribution(
      quantity: Int,
      pcbSize: PcbSize,
      constraints: PanelPreferences
  ) =
    DistributionAlgorithm.calculate(quantity, pcbSize, constraints)
}
