package de.fellows.microservices.pcb.model.pcb.capability

import com.osinka.i18n.Lang
import de.fellows.ems.pcb.api.specification.SilkscreenColor.Yellow
import de.fellows.ems.pcb.api.specification.SoldermaskColor.Red
import de.fellows.ems.pcb.api.specification.SurfaceFinish.Enepig
import de.fellows.ems.pcb.api.specification.{Side, SilkscreenColor => PCBSkColor, SoldermaskColor => PCBSdColor, SurfaceFinish => PCBSurfaceFinish}
import de.fellows.microservices.pcb.helper
import de.fellows.microservices.pcb.model.pcb.PCBProperties
import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.{No, Yes}
import de.fellows.microservices.pcb.model.pcb.props.BoardHeight.BoardHeightCapability
import de.fellows.microservices.pcb.model.pcb.props.BoardWidth.BoardWidthCapability
import de.fellows.microservices.pcb.model.pcb.props.HardGold.HardGoldCapability
import de.fellows.microservices.pcb.model.pcb.props.SilkscreenColor.SilkscreenColorCapability
import de.fellows.microservices.pcb.model.pcb.props.SilkscreenSide.SilkscreenSideCapability
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskColor.SoldermaskColorCapability
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskSide.SoldermaskSideCapability
import de.fellows.microservices.pcb.model.pcb.props.SurfaceFinish.SurfaceFinishCapability
import de.fellows.microservices.pcb.model.pcb.props._
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec

class BoardBasicCapabilityTest extends AnyWordSpec with should.Matchers {
  implicit val lang: Lang = Lang("en")

  val props = helper.emptyProperties

  "Board width validation" should {
    val capability = BoardBasicCapability(new BoardWidthCapability(10, 100))
    "fail" in {
      validate(capability, props.boardWidth(5)) should contain(BoardWidth.name)
    }
    "succeed" in {
      capability.validate(props.boardWidth(50).basic) should not contain BoardWidth.name
    }
  }
  "Board height validation" should {
    val capability = BoardBasicCapability(boardHeight = new BoardHeightCapability(10, 100))
    "fail" in {
      validate(capability, props.boardHeight(5)) should contain(BoardHeight.name)
    }
    "succeed" in {
      capability.validate(props.boardHeight(50).basic) should not contain BoardWidth.name
    }
  }
  "Surface finish validation" should {
    val capability =
      BoardBasicCapability(surfaceFinish = new SurfaceFinishCapability(PCBSurfaceFinish.Enig))
    "succeed" in {
      capability.validate(props.surfaceFinish(PCBSurfaceFinish.Enig).basic) should not contain SurfaceFinish.name
    }
  }

  "HardGold validation" should {
    val capability =
      BoardBasicCapability(hardGold = new HardGoldCapability(YesNoCapability.No))
    "fail" in {
      validate(capability, props.hardGold(true)) should contain(HardGold.name)
    }
    "succeed" in {
      capability.validate(props.hardGold(false).basic) should not contain HardGold.name
    }
  }

  "Silkscreen side validation" should {
    val capability =
      BoardBasicCapability(silkscreenSide = new SilkscreenSideCapability(Side.None, Side.Both))
    "fail" in {
      validate(capability, props.silkscreenSide(Side.Top)) should contain(SilkscreenSide.name)
    }
    "succeed" in {
      capability.validate(props.silkscreenSide(Side.None).basic) should not contain SilkscreenSide.name
    }
  }
  "Silkscreen color validation" should {
    val capability =
      BoardBasicCapability(silkscreenColor = new SilkscreenColorCapability(PCBSkColor.Yellow))
    "fail" in {
      validate(capability, props.silkscreenColor(PCBSkColor.Black)) should contain(SilkscreenColor.name)
    }
    "succeed" in {
      capability.validate(props.silkscreenColor(PCBSkColor.Yellow).basic) should not contain SilkscreenColor.name
    }
  }

  "Soldermask side validation" should {
    val capability =
      BoardBasicCapability(soldermaskSide = new SoldermaskSideCapability(Side.None, Side.Both))
    "fail" in {
      validate(capability, props.soldermaskSide(Side.Top)) should contain(SoldermaskSide.name)
    }
    "succeed" in {
      capability.validate(props.soldermaskSide(Side.None).basic) should not contain SoldermaskSide.name
    }
  }

  "Soldermask color validation" should {
    val capability =
      BoardBasicCapability(soldermaskColor = new SoldermaskColorCapability(PCBSdColor.Red))
    "fail" in {
      validate(capability, props.soldermaskColor(PCBSdColor.Black)) should contain(SoldermaskColor.name)
    }
    "succeed" in {
      capability.validate(props.soldermaskColor(PCBSdColor.Red).basic) should not contain SoldermaskColor.name
    }
  }

  "After applying set of capabilities, result basic capability" should {
    val boardCapability = BoardBasicCapability()
    "have a new board width capability" in {
      val capability = new BoardWidthCapability(10, 10)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.boardWidth should be(capability)
    }
    "have a new board height capability" in {
      val capability = new BoardHeightCapability(10, 10)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.boardHeight should be(capability)
    }
    "have a new silkscreen side capability" in {
      val capability = new SilkscreenSideCapability(Side.Both)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.silkscreenSide should be(capability)
    }
    "have a new silkscreen color capability" in {
      val capability = new SilkscreenColorCapability(Yellow)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.silkscreenColor should be(capability)
    }
    "have a new hard gold capability" in {
      val capability = new HardGoldCapability(Yes, No)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.hardGold should be(capability)
    }
    "have a new surface finish capability" in {
      val capability = new SurfaceFinishCapability(Enepig)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.surfaceFinish should be(capability)
    }
    "have a new soldermask side capability" in {
      val capability = new SoldermaskSideCapability(Side.Both)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.soldermaskSide should be(capability)
    }
    "have a new soldermask color capability" in {
      val capability = new SoldermaskColorCapability(Red)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.soldermaskColor should be(capability)
    }
  }

  protected def validate(capability: BoardBasicCapability, props: PCBProperties): Seq[String] =
    capability.validate(props.basic).map(_.property.fieldName)
}
