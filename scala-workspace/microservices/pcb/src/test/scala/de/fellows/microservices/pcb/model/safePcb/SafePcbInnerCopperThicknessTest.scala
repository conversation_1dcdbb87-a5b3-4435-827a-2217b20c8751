package de.fellows.microservices.pcb.model.safePcb

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import de.fellows.microservices.pcb.model.pcb.props
import de.fellows.microservices.pcb.model.pcb.props.Inner350mcr

class SafePcbInnerCopperThicknessTest extends AnyFlatSpec with should.Matchers {
  val innerCopperThickness = props.InnerCopperThickness(None)
  "Inner copper thickness" should "be `not support, empty` if PCB value is empty" in {
    SafePcbInnerCopperThickness.fromPcb(innerCopperThickness) shouldBe Some(SafePcbInnerThicknessEmpty)
  }
  it should "be Mcr16 if PCB is less than 16" in {
    val origin = props.InnerCopperThickness(17)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr17)
  }
  it should "be Mcr17 if PCB is  17" in {
    val origin = props.InnerCopperThickness(17)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr17)
  }
  it should "be Mcr35 if PCB is less than 35" in {
    val origin = props.InnerCopperThickness(34)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr35)
  }
  it should "be Mcr35 if PCB is 35" in {
    val origin = props.InnerCopperThickness(35)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr35)
  }
  it should "be Mcr70 if PCB is less than 70" in {
    val origin = props.InnerCopperThickness(69)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr70)
  }
  it should "be Mcr70 if PCB is 70" in {
    val origin = props.InnerCopperThickness(70)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr70)
  }
  it should "be Mcr105 if PCB is less than 105" in {
    val origin = props.InnerCopperThickness(104)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr105)
  }
  it should "be Mcr105 if PCB is 105" in {
    val origin = props.InnerCopperThickness(105)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr105)
  }
  it should "be Mcr140 if PCB is less than 140" in {
    val origin = props.InnerCopperThickness(120)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr140)
  }
  it should "be Mcr140 if PCB is 140" in {
    val origin = props.InnerCopperThickness(140)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr140)
  }
  it should "be Mcr210 if PCB is less than 210" in {
    val origin = props.InnerCopperThickness(200)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr210)
  }
  it should "be Mcr210 if PCB is 210" in {
    val origin = props.InnerCopperThickness(210)
    SafePcbInnerCopperThickness.fromPcb(origin) shouldBe Some(SafePcbInnerThicknessMcr210)
  }
  it should "be `not supported` if PCB is more than 210" in {
    SafePcbInnerCopperThickness.fromPcb(Inner350mcr) shouldBe None
  }

}
