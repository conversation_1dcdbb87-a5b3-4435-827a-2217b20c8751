package de.fellows.microservices.pcb.model.stackrate

import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.price.api.PriceService
import de.fellows.app.quotation.QuotationService
import de.fellows.app.supplier.SupplierService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.microservices.pcb.PcbServer.AsyncBackend
import de.fellows.microservices.pcb.client.LagomServiceClient
import de.fellows.microservices.pcb.model.stackrate.StubLagomApi._
import sttp.client3.testing.SttpBackendStub

import scala.concurrent.ExecutionContext

object StubStackRateAPI {

  def mk: StackRateAPI =
    apply(ConfigFactory.empty())

  def mk(config: Config): StackRateAPI =
    apply(config)

  def apply(): StackRateAPI =
    apply(ConfigFactory.empty())

  def apply(config: Config): StackRateAPI =
    new StackRateAPI(
      config = config,
      asyncBackend = asyncStubBackend,
      assemblyService = assemblyService,
      pcbService = pcbService,
      layerstackService = layerstackService
    )
}

object StubLagomApi {

  private val executionContext        = ExecutionContext.global
  val asyncStubBackend: AsyncBackend  = SttpBackendStub.asynchronousFuture
  val lagomClient: LagomServiceClient = new LagomServiceClient(asyncStubBackend)(executionContext)

  val assemblyService: AssemblyService     = lagomClient.implement[AssemblyService]
  val pcbService: PCBService               = lagomClient.implement[PCBService]
  val panelService: PanelService           = lagomClient.implement[PanelService]
  val quotationService: QuotationService   = lagomClient.implement[QuotationService]
  val priceService: PriceService           = lagomClient.implement[PriceService]
  val supplierService: SupplierService     = lagomClient.implement[SupplierService]
  val layerstackService: LayerstackService = lagomClient.implement[LayerstackService]
}
