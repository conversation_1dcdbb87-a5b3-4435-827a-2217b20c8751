package de.fellows.microservices.pcb.model.panel

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class WastedAreaTest extends AnyFlatSpec with should.Matchers {

  private val preferences = PanelPreferences.Empty.copy(
    minWidth = 106,
    minHeight = 53,
    maxWidth = 212,
    maxHeight = 106,
    maxPCBs = None,
    padding = new PanelPadding(10, 5),
    spacing = Rectangle(2, 1)
  )
  val maxPanel = preferences.maxPanel
  val minPanel = preferences.minPanel
  val gap      = preferences.spacing

  "Wasted area" should "be none if the total area taken by PCBs exceeds the maximum size panel" in {
    val area = WastedArea.calculate(10, 10, 10, new PcbSize(20, 10), gap, minPanel, maxPanel)
    area should be(None)
  }
  it should "be 0 if the total area taken by PCBs completely" in {
    val area = WastedArea.calculate(16, 4, 4, new PcbSize(20, 10), gap, minPanel, maxPanel)
    area should be(Some(WastedArea(0.0, 0.0, 1)))
  }
  it should "be 1720 sq mm as we have to select the minimum size panel" in {
    val area = WastedArea.calculate(16, 4, 4, new PcbSize(10, 10), gap, minPanel, maxPanel)
    area should be(Some(WastedArea(1720.00, 0, 1)))
  }
  it should "be 3440 sq mm as we have to select 2 panels of the minimum size panel" in {
    val area = WastedArea.calculate(32, 4, 4, new PcbSize(10, 10), gap, minPanel, maxPanel)
    area should be(Some(WastedArea(1720.00, 0, 2)))
    area.map(_.totalInSqm) should be(Some(3440.00))
  }
  it should "be 2838 sq mm as there is one panel with fewer PCBs on it but others are filled completely" in {
    val area = WastedArea.calculate(20, 4, 4, new PcbSize(20, 10), gap, minPanel, maxPanel)
    area should be(Some(WastedArea(0, 2838, 1)))
    area.map(_.totalInSqm) should be(Some(2838))
  }
}
