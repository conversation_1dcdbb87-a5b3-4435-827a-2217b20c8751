package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props.{LayerstackType, MinInnerLayerStructure}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class InnerLayerStructureTest extends AnyFlatSpec with should.Matchers {

  def rigidConverter = WurthInnerLayerStructure.converter(LayerstackType.rigid) _

  val minInnerLayerStructure = MinInnerLayerStructure(None)
  "Inner layout structure thickness" should "be empty if PCB inner layout structure is empty" in {
    rigidConverter(minInnerLayerStructure) shouldBe Some(InnerLayerEmpty(minInnerLayerStructure))
  }
  it should "be Mcr85 if PCB thickness is 85" in {
    val origin = MinInnerLayerStructure(0.085)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr85(origin))
  }
  it should "be `not supported` if PCB thickness is less than 085" in {
    rigidConverter(minInnerLayerStructure.copy(value = Some(0.045))) shouldBe None
  }
  it should "be Mcr085 if PCB thickness is more than 85 but less than 100" in {
    val origin = MinInnerLayerStructure(0.09)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr85(origin))
  }
  it should "be Mcr100 if PCB thickness is 100" in {
    val origin = MinInnerLayerStructure(0.1)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr100(origin))
  }
  it should "be Mcr100 if PCB thickness is more than 100" in {
    val origin = MinInnerLayerStructure(0.124)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr100(origin))
  }
  it should "be Mcr125 if PCB thickness is 125" in {
    val origin = MinInnerLayerStructure(0.125)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr125(origin))
  }
  it should "be Mcr125 if PCB thickness is more than 125 but less than 150" in {
    val origin = MinInnerLayerStructure(0.149)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr125(origin))
  }
  it should "be Mcr150 if PCB thickness is 150" in {
    val origin = MinInnerLayerStructure(0.150)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr150(origin))
  }
  it should "be Mcr150 if PCB thickness is more than 150 but less than 192" in {
    val origin = MinInnerLayerStructure(0.191)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr150(origin))
  }
  it should "be Mcr192 if PCB thickness is 192" in {
    val origin = MinInnerLayerStructure(0.192)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr192(origin))
  }
  it should "be Mcr192 if PCB thickness is less than 192 but less then 250" in {
    val origin = MinInnerLayerStructure(0.249)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr192(origin))
  }
  it should "be Mcr250 if PCB thickness is 250" in {
    val origin = MinInnerLayerStructure(0.250)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr250(origin))
  }
  it should "be Mcr250 if PCB thickness is more than 250" in {
    val origin = MinInnerLayerStructure(0.392)
    rigidConverter(origin) shouldBe Some(WurthInnerMcr250(origin))
  }
}
