package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props.{BlindVias, BuriedVias}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import play.api.libs.json.Json

class JsonWurthPcbRequestTest extends AnyWordSpec with Matchers {

  val conf = helper.rigidPCBConfiguration

  "Name of configuration" should {
    "be test" in {
      val json = Json.toJson(conf)
      (json \ "reference_name").as[String] should be("test")
    }
    "be `configuration`" in {
      val json = Json.toJson(conf.copy(pcbName = "configuration"))
      (json \ "reference_name").as[String] should be("configuration")
    }
  }
  "Technology" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.Rigid))
      (json \ "technology").as[Int] should be(1)
    }

    "be 10" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.Flex1Layer))
      (json \ "technology").as[Int] should be(10)
    }
    "be 11" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.Flex2Layer))
      (json \ "technology").as[Int] should be(11)
    }
    "be 14" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.RigidFlex2Layers))
      (json \ "technology").as[Int] should be(14)
    }
    "be 15" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.RigidFlex4Layers))
      (json \ "technology").as[Int] should be(15)
    }
    "be 16" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.RigidFlex6Layers))
      (json \ "technology").as[Int] should be(16)
    }
  }
  "Number of layers" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(numberOfLayers = OneLayer))
      (json \ "layers").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(numberOfLayers = TwoLayers))
      (json \ "layers").as[Int] should be(2)
    }
    "be 4" in {
      val json = Json.toJson(conf.copy(numberOfLayers = FourLayers))
      (json \ "layers").as[Int] should be(4)
    }
    "be 6" in {
      val json = Json.toJson(conf.copy(numberOfLayers = SixLayers))
      (json \ "layers").as[Int] should be(6)
    }
    "be 8" in {
      val json = Json.toJson(conf.copy(numberOfLayers = EightLayers))
      (json \ "layers").as[Int] should be(8)
    }
    "be 10" in {
      val json = Json.toJson(conf.copy(numberOfLayers = TenLayers))
      (json \ "layers").as[Int] should be(10)
    }
    "be 12" in {
      val json = Json.toJson(conf.copy(numberOfLayers = TwelveLayers))
      (json \ "layers").as[Int] should be(12)
    }
    "not be included if rigid-flex 2 board" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.RigidFlex2Layers))
      (json \ "layers").asOpt[Int] should be(None)
    }
    "not be included if rigid-flex 4 board" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.RigidFlex4Layers))
      (json \ "layers").asOpt[Int] should be(None)
    }
    "not be included if rigid-flex 6 board" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.RigidFlex6Layers))
      (json \ "layers").asOpt[Int] should be(None)
    }
    "not be included if flex 1 board" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.Flex1Layer))
      (json \ "layers").asOpt[Int] should be(None)
    }
    "not be included if flex 2 board" in {
      val json = Json.toJson(conf.copy(layerstackType = WurthLayerstackType.Flex2Layer))
      (json \ "layers").asOpt[Int] should be(None)
    }
  }
  "Material" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(material = Tg135))
      (json \ "material2").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(material = Tg150))
      (json \ "material2").as[Int] should be(2)
    }
  }
  "Thickness" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(thickness = Mm080()))
      (json \ "thickness").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(thickness = Mm100()))
      (json \ "thickness").as[Int] should be(2)
    }
    "be 3" in {
      val json = Json.toJson(conf.copy(thickness = Mm155()))
      (json \ "thickness").as[Int] should be(3)
    }
    "be 4" in {
      val json = Json.toJson(conf.copy(thickness = Mm200()))
      (json \ "thickness").as[Int] should be(4)
    }
    "be 5" in {
      val json = Json.toJson(conf.copy(thickness = Mm240()))
      (json \ "thickness").as[Int] should be(5)
    }
    "be 6" in {
      val json = Json.toJson(conf.copy(thickness = Mm320()))
      (json \ "thickness").as[Int] should be(6)
    }
    "be 12" in {
      val json = Json.toJson(conf.copy(thickness = Mm050()))
      (json \ "thickness").as[Int] should be(12)
    }
    "be 13" in {
      val json = Json.toJson(conf.copy(thickness = Mm120()))
      (json \ "thickness").as[Int] should be(13)
    }
  }
  "LaserDrill" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(laserDrill = NoDrill))
      (json \ "microvia_side").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(laserDrill = TopDrill))
      (json \ "microvia_side").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(laserDrill = BottomDrill))
      (json \ "microvia_side").as[Int] should be(2)
    }
    "be 3" in {
      val json = Json.toJson(conf.copy(laserDrill = TopBottomDrill))
      (json \ "microvia_side").as[Int] should be(3)
    }
  }
  "Buried via" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(buriedVia = BuriedVias.no))
      (json \ "buried_via").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(buriedVia = BuriedVias.yes))
      (json \ "buried_via").as[Int] should be(1)
    }
  }
  "Min outer layout structure" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(outerLayerStructure = OuterMcr192()))
      (json \ "pattern_structures").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(outerLayerStructure = OuterMcr150()))
      (json \ "pattern_structures").as[Int] should be(2)
    }
    "be 3" in {
      val json = Json.toJson(conf.copy(outerLayerStructure = OuterMcr125()))
      (json \ "pattern_structures").as[Int] should be(3)
    }
    "be 4" in {
      val json = Json.toJson(conf.copy(outerLayerStructure = OuterMcr100()))
      (json \ "pattern_structures").as[Int] should be(4)
    }
    "be 5" in {
      val json = Json.toJson(conf.copy(outerLayerStructure = OuterMcr85()))
      (json \ "pattern_structures").as[Int] should be(5)
    }
    "be 10" in {
      val json = Json.toJson(conf.copy(outerLayerStructure = OuterMcr250()))
      (json \ "pattern_structures").as[Int] should be(10)
    }
  }
  "Min inner layout structure" should {
    "not exist" in {
      val json = Json.toJson(conf.copy(innerLayerStructure = InnerLayerEmpty()))
      (json \ "pattern_structures_inward").asOpt[Int] should be(None)
    }
    "be 1" in {
      val json =
        Json.toJson(conf.copy(innerLayerStructure = WurthInnerMcr192(), innerCopperThickness = InnerThicknessMcr35()))
      (json \ "pattern_structures_inward").as[Int] should be(1)
    }
    "be 2" in {
      val json =
        Json.toJson(conf.copy(innerLayerStructure = WurthInnerMcr150(), innerCopperThickness = InnerThicknessMcr35()))
      (json \ "pattern_structures_inward").as[Int] should be(2)
    }
    "be 3" in {
      val json =
        Json.toJson(conf.copy(innerLayerStructure = WurthInnerMcr125(), innerCopperThickness = InnerThicknessMcr35()))
      (json \ "pattern_structures_inward").as[Int] should be(3)
    }
    "be 4" in {
      val json =
        Json.toJson(conf.copy(innerLayerStructure = WurthInnerMcr100(), innerCopperThickness = InnerThicknessMcr35()))
      (json \ "pattern_structures_inward").as[Int] should be(4)
    }
    "be 5" in {
      val json =
        Json.toJson(conf.copy(innerLayerStructure = WurthInnerMcr85(), innerCopperThickness = InnerThicknessMcr35()))
      (json \ "pattern_structures_inward").as[Int] should be(5)
    }
    "be 10" in {
      val json =
        Json.toJson(conf.copy(innerLayerStructure = WurthInnerMcr250(), innerCopperThickness = InnerThicknessMcr35()))
      (json \ "pattern_structures_inward").as[Int] should be(10)
    }
  }
  "Inner copper thickness" should {
    "not exist" in {
      val json =
        Json.toJson(conf.copy(innerCopperThickness = InnerThicknessEmpty(), innerLayerStructure = InnerLayerEmpty()))
      (json \ "inward_overlay").asOpt[Int] should be(None)
    }
    "be 1" in {
      val json =
        Json.toJson(conf.copy(innerCopperThickness = InnerThicknessMcr35(), innerLayerStructure = WurthInnerMcr85()))
      (json \ "inward_overlay").as[Int] should be(1)
    }
    "be 2" in {
      val json =
        Json.toJson(conf.copy(innerCopperThickness = InnerThicknessMcr70(), innerLayerStructure = WurthInnerMcr85()))
      (json \ "inward_overlay").as[Int] should be(2)
    }
    "be 3" in {
      val json =
        Json.toJson(conf.copy(innerCopperThickness = InnerThicknessMcr105(), innerLayerStructure = WurthInnerMcr85()))
      (json \ "inward_overlay").as[Int] should be(3)
    }
    "be 4" in {
      val json =
        Json.toJson(conf.copy(innerCopperThickness = InnerThicknessMcr18(), innerLayerStructure = WurthInnerMcr85()))
      (json \ "inward_overlay").as[Int] should be(4)
    }
  }
  "Outer copper thickness" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(outerCopperThickness = OuterThicknessMcr35()))
      (json \ "outward_overlay").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(outerCopperThickness = OuterThicknessMcr70()))
      (json \ "outward_overlay").as[Int] should be(2)
    }
    "be 3" in {
      val json = Json.toJson(conf.copy(outerCopperThickness = OuterThicknessMcr105()))
      (json \ "outward_overlay").as[Int] should be(3)
    }
    "be 4" in {
      val json = Json.toJson(conf.copy(outerCopperThickness = OuterThicknessMcr18()))
      (json \ "outward_overlay").as[Int] should be(4)
    }
  }
  "Smallest via" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(smallestVia = Mm25))
      (json \ "pattern_drill_diameter").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(smallestVia = Mm10))
      (json \ "pattern_drill_diameter").as[Int] should be(2)
    }
  }
  "Smallest rout tool" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(smallestRoutTool = MoreThan160mm))
      (json \ "material").as[Int] should be(1)
    }
    "be 13" in {
      val json = Json.toJson(conf.copy(smallestRoutTool = Between150mmAnd110mm))
      (json \ "material").as[Int] should be(13)
    }
    "be 14" in {
      val json = Json.toJson(conf.copy(smallestRoutTool = Between100mmAnd050mm))
      (json \ "material").as[Int] should be(14)
    }
  }
  "PCB type" should {
    "be 3" in {
      val json = Json.toJson(conf)
      (json \ "multiple_options").as[Int] should be(3)
    }
  }
  "Via filling" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(viaFilling = ViaFillingNo))
      (json \ "plugged_via").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(viaFilling = ViaFillingTopBottom))
      (json \ "plugged_via").as[Int] should be(1)
    }
  }
  "Shape" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(shape = OtherShape))
      (json \ "dimensions").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(shape = RoundShape))
      (json \ "dimensions").as[Int] should be(2)
    }
  }
  "Width" should {
    "be 150" in {
      val json = Json.toJson(conf.copy(width = 150f))
      (json \ "size_x").as[String] should be("150.0")
    }
    "be 255.5" in {
      val json = Json.toJson(conf.copy(width = 255.5f))
      (json \ "size_x").as[String] should be("255.5")
    }
  }
  "Height" should {
    "be 55.6" in {
      val json = Json.toJson(conf.copy(height = 55.6f))
      (json \ "size_y").as[String] should be("55.6")
    }
    "be 255.5" in {
      val json = Json.toJson(conf.copy(height = 255.5f))
      (json \ "size_y").as[String] should be("255.5")
    }
  }
  "Surface finish" should {
    "be 2" in {
      val json = Json.toJson(conf.copy(surfaceFinish = ChemicalTin))
      (json \ "surface").as[Int] should be(2)
    }
    "be 4" in {
      val json = Json.toJson(conf.copy(surfaceFinish = ENIG))
      (json \ "surface").as[Int] should be(4)
    }
    "be 5" in {
      val json = Json.toJson(conf.copy(surfaceFinish = HAL))
      (json \ "surface").as[Int] should be(5)
    }
  }
  "Solder resist" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(solderMaskSides = SolderMaskSidesTopBottom))
      (json \ "solder_resist").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(solderMaskSides = SolderMaskSidesTop))
      (json \ "solder_resist").as[Int] should be(2)
    }
    "be 3" in {
      val json = Json.toJson(conf.copy(solderMaskSides = SolderMaskSidesBottom))
      (json \ "solder_resist").as[Int] should be(3)
    }
    "be 4" in {
      val json = Json.toJson(conf.copy(solderMaskSides = SolderMaskSidesNone))
      (json \ "solder_resist").as[Int] should be(4)
    }
  }
  "Solder resist color" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(solderMaskColor = SolderMaskColorGreen))
      (json \ "solder_resist_color").as[Int] should be(1)
    }
  }
  "Legend printing" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(silkscreenSides = SilkscreenSidesNone))
      (json \ "service_print").as[Int] should be(1)
    }
    "be 3" in {
      val json = Json.toJson(conf.copy(silkscreenSides = SilkscreenSidesTopBottom))
      (json \ "service_print").as[Int] should be(3)
    }
    "be 4" in {
      val json = Json.toJson(conf.copy(silkscreenSides = SilkscreenSidesTop))
      (json \ "service_print").as[Int] should be(4)
    }
    "be 5" in {
      val json = Json.toJson(conf.copy(silkscreenSides = SilkscreenSidesBottom))
      (json \ "service_print").as[Int] should be(5)
    }
  }
  "Silkcreen color" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(silkscreenColor = WurthSilkscreenColor.White))
      (json \ "service_print_color").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(silkscreenColor = WurthSilkscreenColor.Yellow))
      (json \ "service_print_color").as[Int] should be(2)
    }
    "be 3" in {
      val json = Json.toJson(conf.copy(silkscreenColor = WurthSilkscreenColor.Red))
      (json \ "service_print_color").as[Int] should be(3)
    }
    "be 4" in {
      val json = Json.toJson(conf.copy(silkscreenColor = WurthSilkscreenColor.Blue))
      (json \ "service_print_color").as[Int] should be(4)
    }
    "be 5" in {
      val json = Json.toJson(conf.copy(silkscreenColor = WurthSilkscreenColor.Black))
      (json \ "service_print_color").as[Int] should be(5)
    }
  }
  "Fully coated silkscreen" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(fullyCoatedSilkscreen = FullyCoatedSilkscreenNone))
      (json \ "screen_printing").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(fullyCoatedSilkscreen = FullyCoatedSilkscreenTop))
      (json \ "screen_printing").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(fullyCoatedSilkscreen = FullyCoatedSilkscreenBottom))
      (json \ "screen_printing").as[Int] should be(2)
    }
    "be 3" in {
      val json = Json.toJson(conf.copy(fullyCoatedSilkscreen = FullyCoatedSilkscreenTopBottom))
      (json \ "screen_printing").as[Int] should be(3)
    }
  }
  "eTest" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(eTest = WurthETestNo))
      (json \ "e_test").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(eTest = WurthETestYes))
      (json \ "e_test").as[Int] should be(1)
    }
  }
  "Chamfer" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(chamfer = ChamferNone))
      (json \ "beveling").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(chamfer = Chamfer20PCI))
      (json \ "beveling").as[Int] should be(1)
    }
    "be 3" in {
      val json = Json.toJson(conf.copy(chamfer = Chamfer45ISA))
      (json \ "beveling").as[Int] should be(3)
    }
  }
  "Edge plating" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(edgeMetalization = WurthEdgeMetalizationNo))
      (json \ "sideplating").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(edgeMetalization = WurthEdgeMetalizationYes))
      (json \ "sideplating").as[Int] should be(1)
    }
  }
  "Hard gold" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(hardGold = WurthHardGoldNo))
      (json \ "surface2").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(hardGold = WurthHardGoldYes))
      (json \ "surface2").as[Int] should be(1)
    }
  }
  "Depanelization" should {
    "be 1" in {
      val json = Json.toJson(conf.copy(depanelization = WurthDepanelization.Milling))
      (json \ "multiple_image_contour").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(depanelization = WurthDepanelization.VCut))
      (json \ "multiple_image_contour").as[Int] should be(2)
    }
  }
  "Paste files" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(orderingFiles = WurthOrderingFiles.WurthNoOrdering))
      (json \ "paste_files").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(orderingFiles = WurthOrderingFiles.WurthPasteFiles))
      (json \ "paste_files").as[Int] should be(1)
    }
    "be 2" in {
      val json = Json.toJson(conf.copy(orderingFiles = WurthOrderingFiles.WurthWorkingGerber))
      (json \ "paste_files").as[Int] should be(2)
    }
  }
  "Additional documentation" should {
    "be 0" in {
      val json = Json.toJson(conf.copy(additionalDocumentation = false))
      (json \ "additional_documentation").as[Int] should be(0)
    }
    "be 1" in {
      val json = Json.toJson(conf.copy(additionalDocumentation = true))
      (json \ "additional_documentation").as[Int] should be(1)
    }
  }
  "Tolerant parsing" should {
    "handle missing values and changes in types" in {
      val json =
        """
          {
            "technology" : 1,
            "pattern_structures" : 1,
            "pattern_structures_inward" : "1",
            "outward_overlay": 1.1,
            "thickness": "1.1",
            "reference_name": "test_pcb_name",
            "quantity": 1.123,
            "layers": "1",
            "material2": 1,
            "microvia_side": 1,
            "pattern_drill_diameter": 2.1,
            "size_x": 32,
            "size_y": 32,
            "beveling": "1.23"
          }
        """

      val parsed = Json.parse(json).as[WurthPcbRequest](WurthPcbRequest.reads)

      val expected = WurthPcbRequest(
        pcbName = "test_pcb_name",
        orderNumber = None,
        quantity = 1,
        quantityMax = None,
        layerstackType = WurthLayerstackType.Rigid,
        numberOfLayers = OneLayer,
        material = Tg135,
        width = 32.0f,
        height = 32.0f,
        thickness = Mm080(),
        laserDrill = TopDrill,
        buriedVia = BuriedVias.no,
        blindVia = BlindVias.no,
        outerLayerStructure = OuterMcr192(),
        outerCopperThickness = OuterThicknessMcr35(),
        innerLayerStructure = WurthInnerMcr192(),
        innerCopperThickness = InnerThicknessEmpty(),
        smallestVia = Mm10,
        smallestRoutTool = MoreThan160mm,
        shape = OtherShape,
        viaFilling = ViaFillingNo,
        surfaceFinish = HAL,
        solderMaskSides = SolderMaskSidesTopBottom,
        solderMaskColor = SolderMaskColorGreen,
        silkscreenColor = WurthSilkscreenColor.White,
        silkscreenSides = SilkscreenSidesTopBottom,
        fullyCoatedSilkscreen = FullyCoatedSilkscreenNone,
        eTest = WurthETestYes,
        chamfer = Chamfer20PCI,
        edgeMetalization = WurthEdgeMetalizationNo,
        hardGold = WurthHardGoldNo,
        numberOfPCBs = 1,
        depanelization = WurthDepanelization.VCut,
        orderingFiles = WurthOrderingFiles.WurthNoOrdering,
        additionalDocumentation = false,
        ulMarkingType = NoUlMarking,
        deliveryTime = None
      )

      parsed should be(expected)
    }
  }
}
