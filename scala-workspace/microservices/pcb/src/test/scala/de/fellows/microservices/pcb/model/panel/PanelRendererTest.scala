package de.fellows.microservices.pcb.model.panel

import de.fellows.ems.pcb.model.{BigPoint, Dimension}
import de.fellows.luminovo.panel.{Depanelization, LuminovoPadding, PanelDetails, PanelId}
import org.scalatest.EitherValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class PanelRendererTest extends AnyFlatSpec with should.Matchers with EitherValues {

  "PanelRenderer" should "render a simple panel" in {
    val panel = Seq(
      DistributionRectangle(Dimension(BigPoint(0.0, 0.0), BigPoint(10.0, 100.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 0.0), BigPoint(90.0, 10.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(10.0, 10.0), BigPoint(45.0, 45.0)), Pcb),
      DistributionRectangle(Dimension(BigPoint(50.0, 10.0), BigPoint(90.0, 45.0)), Pcb),
      DistributionRectangle(Dimension(BigPoint(10.0, 50.0), BigPoint(45.0, 90.0)), Pcb),
      DistributionRectangle(Dimension(BigPoint(50.0, 50.0), BigPoint(90.0, 90.0)), Pcb),
      DistributionRectangle(Dimension(BigPoint(10.0, 90.0), BigPoint(90.0, 100.0)), Padding),
      DistributionRectangle(Dimension(BigPoint(90.0, 0.0), BigPoint(100.0, 100.0)), Padding)
    )

    val svg          = PanelRenderer.renderToString(panel)
    val expectedFile = getClass.getResourceAsStream("/panel/simplePanel.svg")
    val source       = scala.io.Source.fromInputStream(expectedFile).getLines().mkString("\n")

    source should be(svg)
  }

  it should "handle rouding issues" in {
    val panel = calcItems(
      pcbSize = new PcbSize(30.2345, 35.8976),
      rows = 2,
      columns = 2,
      padding = LuminovoPadding(
        topInMm = 10.2231,
        rightInMm = 9.7899,
        bottomInMm = 10.2239,
        leftInMm = 9.7899
      ),
      horizontalSpacingMm = 2,
      verticalSpacingMm = 2
    )

    val svg          = PanelRenderer.renderToString(panel)
    val expectedFile = getClass.getResourceAsStream("/panel/singlePcbPanelWithPadding.svg")
    val source       = scala.io.Source.fromInputStream(expectedFile).getLines().mkString("\n")

    source should be(svg)
  }

  it should "handle rouding issues without padding" in {
    val panel = calcItems(
      pcbSize = new PcbSize(30.2345, 35.8976),
      rows = 1,
      columns = 1,
      padding = LuminovoPadding.zero
    )

    val svg          = PanelRenderer.renderToString(panel)
    val expectedFile = getClass.getResourceAsStream("/panel/singlePcbPanelNoPadding.svg")
    val source       = scala.io.Source.fromInputStream(expectedFile).getLines().mkString("\n")

    source should be(svg)
  }

  private def calcItems(
      pcbSize: PcbSize,
      rows: Int,
      columns: Int,
      padding: LuminovoPadding,
      horizontalSpacingMm: Double = 0,
      verticalSpacingMm: Double = 0
  ): Seq[DistributionRectangle] = {
    val distribution = DistributionAlgorithm
      .calculateDistributionFromDetails(
        amount = 1,
        pcbSize = pcbSize,
        panelDetails = PanelDetails(
          id = Some(PanelId.random),
          rowCount = rows,
          columnCount = columns,
          horizontalSpacingInMm = horizontalSpacingMm,
          verticalSpacingInMm = verticalSpacingMm,
          minMillingDistanceInMm = 1,
          padding = padding,
          depanelization = Depanelization.VCut,
          pcbIsRotated = false
        ),
        panelConstraints = None
      )

    distribution.value.items
  }
}
