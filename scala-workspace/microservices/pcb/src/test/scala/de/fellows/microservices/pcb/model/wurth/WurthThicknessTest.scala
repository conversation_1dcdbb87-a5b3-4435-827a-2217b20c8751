package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props.FinalThickness
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class WurthThicknessTest extends AnyFlatSpec with should.Matchers {

  val thickness = FinalThickness(None)
  "Thickness" should "be Mm050 if PCB thickness is 050" in {
    val origin = FinalThickness(0.50)
    WurthThickness.converter(origin) shouldBe Some(Mm050(origin))
  }
  it should "be Mm050 if PCB thickness is less than 050" in {
    val origin = FinalThickness(0.45)
    WurthThickness.converter(origin) shouldBe Some(Mm050(origin))
  }
  it should "be Mm080 if PCB thickness is 080" in {
    val origin = FinalThickness(0.80)
    WurthThickness.converter(origin) shouldBe Some(Mm080(origin))
  }
  it should "be Mm080 if PCB thickness is less than 080" in {
    val origin = FinalThickness(0.75)
    WurthThickness.converter(origin) shouldBe Some(Mm080(origin))
  }
  it should "be Mm100 if PCB thickness is 100" in {
    val origin = FinalThickness(1.00)
    WurthThickness.converter(origin) shouldBe Some(Mm100(origin))
  }
  it should "be Mm100 if PCB thickness is less than 100" in {
    val origin = FinalThickness(0.95)
    WurthThickness.converter(origin) shouldBe Some(Mm100(origin))
  }
  it should "be Mm120 if PCB thickness is less than 120" in {
    val origin = FinalThickness(1.10)
    WurthThickness.converter(origin) shouldBe Some(Mm120(origin))
  }
  it should "be Mm120 if PCB thickness is 120" in {
    val origin = FinalThickness(1.20)
    WurthThickness.converter(origin) shouldBe Some(Mm120(origin))
  }
  it should "be Mm155 if PCB thickness is 155" in {
    val origin = FinalThickness(1.55)
    WurthThickness.converter(origin) shouldBe Some(Mm155(origin))
  }
  it should "be Mm155 if PCB thickness is less than 155" in {
    val origin = FinalThickness(1.32)
    WurthThickness.converter(origin) shouldBe Some(Mm155(origin))
  }
  it should "be Mm200 if PCB thickness is 200" in {
    val origin = FinalThickness(2.00)
    WurthThickness.converter(origin) shouldBe Some(Mm200(origin))
  }
  it should "be Mm200 if PCB thickness is less than 200" in {
    val origin = FinalThickness(1.95)
    WurthThickness.converter(origin) shouldBe Some(Mm200(origin))
  }
  it should "be Mm240 if PCB thickness is 240" in {
    val origin = FinalThickness(2.40)
    WurthThickness.converter(origin) shouldBe Some(Mm240(origin))
  }
  it should "be Mm240 if PCB thickness is less than 240" in {
    val origin = FinalThickness(2.25)
    WurthThickness.converter(origin) shouldBe Some(Mm240(origin))
  }
  it should "be Mm320 if PCB thickness is 320" in {
    val origin = FinalThickness(3.20)
    WurthThickness.converter(origin) shouldBe Some(Mm320(origin))
  }
  it should "be Mm320 if PCB thickness is less than 320" in {
    val origin = FinalThickness(2.95)
    WurthThickness.converter(origin) shouldBe Some(Mm320(origin))
  }

  "WurthThickness.converter" should "return Mm155 when value is 160 mm (Tim wants this)" in {
    // Mocking the input and expected output
    val inputThickness = FinalThickness(BigDecimal(1.60)) // 1.60 mm
    val expectedOutput = Some(Mm155(inputThickness))

    // Invoking the method
    val result = WurthThickness.converter(inputThickness)

    // Asserting the result
    result shouldEqual expectedOutput
  }
}
