package de.fellows.microservices.pcb.model.pcb.capability

import com.osinka.i18n.Lang
import de.fellows.ems.pcb.api.specification.BaseMaterial
import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.{No, Yes}
import de.fellows.microservices.pcb.model.pcb.props.BaseMaterial.BaseMaterialCapability
import de.fellows.microservices.pcb.model.pcb.props.FinalThickness.FinalThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.InnerCopperThickness.InnerCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.MinInnerLayerStructure.MinInnerLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.MinOuterLayerStructure.MinOuterLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.NumberOfLayers.NumberOfLayersCapability
import de.fellows.microservices.pcb.model.pcb.props.{
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Inner35mcr,
  <PERSON><PERSON><PERSON><PERSON>,
  Outer70mcr,
  TwoLayers
}
import de.fellows.microservices.pcb.model.pcb.props.OuterCopperThickness.OuterCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.ULLayerStack.ULLayerStackCapability
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec

class LayerStackCapabilityTest extends AnyWordSpec with should.Matchers {
  implicit val lang: Lang = Lang("en")

  "After applying set of capabilities, result layer stack capability" should {
    val layerStackCapability = LayerStackCapability()
    "have a new ul layer stack capability" in {
      val capability = new ULLayerStackCapability(Yes, No)
      val changed    = layerStackCapability.applyCapabilities(Seq(capability))
      changed.ulLayerStack should be(capability)
    }
    "have a new number of layers capability" in {
      val capability = new NumberOfLayersCapability(OneLayer, TwoLayers, FourLayers)
      val changed    = layerStackCapability.applyCapabilities(Seq(capability))
      changed.numberOfLayers should be(capability)
    }
    "have a new final thickness capability" in {
      val capability = new FinalThicknessCapability(FinalThickness.Thickness155mm)
      val changed    = layerStackCapability.applyCapabilities(Seq(capability))
      changed.finalThickness should be(capability)
    }
    "have a new base material capability" in {
      val capability = new BaseMaterialCapability(BaseMaterial.FR2)
      val changed    = layerStackCapability.applyCapabilities(Seq(capability))
      changed.baseMaterial should be(capability)
    }
    "have a new outer copper thickness capability" in {
      val capability = new OuterCopperThicknessCapability(Outer70mcr)
      val changed    = layerStackCapability.applyCapabilities(Seq(capability))
      changed.outerCopperThickness should be(capability)
    }
    "have a new inner copper thickness capability" in {
      val capability = new InnerCopperThicknessCapability(Inner35mcr)
      val changed    = layerStackCapability.applyCapabilities(Seq(capability))
      changed.innerCopperThickness should be(capability)
    }
    "have a new min outer layer structure capability" in {
      val capability = MinOuterLayerStructureCapability(1)
      val changed    = layerStackCapability.applyCapabilities(Seq(capability))
      changed.minOuterLayerStructure should be(capability)
    }
    "have a new min inner layer structure capability" in {
      val capability = MinInnerLayerStructureCapability(1)
      val changed    = layerStackCapability.applyCapabilities(Seq(capability))
      changed.minInnerLayerStructure should be(capability)
    }
  }

}
