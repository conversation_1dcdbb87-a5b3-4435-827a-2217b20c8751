package de.fellows.microservices.pcb.model.safePcb

import de.fellows.microservices.pcb.model.pcb.props.FinalThickness
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class SafePcbFinalThicknessTest extends AnyFlatSpec with should.Matchers {

  val thickness = FinalThickness(None)
  "Thickness" should "be Mm025 if PCB thickness is 0.2" in {
    val origin = FinalThickness(0.2)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm025)
  }
  it should "be Mm025 if PCB thickness is less than 0.25" in {
    val origin = FinalThickness(0.15)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm025)
  }
  it should "be Mm030 if PCB thickness is 0.30" in {
    val origin = FinalThickness(0.30)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm030)
  }
  it should "be Mm040 if PCB thickness is 0.40" in {
    val origin = FinalThickness(0.40)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm040)
  }
  it should "be Mm050 if PCB thickness is 0.50" in {
    val origin = FinalThickness(0.50)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm050)
  }
  it should "be Mm060 if PCB thickness is 0.60" in {
    val origin = FinalThickness(0.60)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm060)
  }
  it should "be Mm080 if PCB thickness is 080" in {
    val origin = FinalThickness(0.80)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm080)
  }
  it should "be Mm100 if PCB thickness is 100" in {
    val origin = FinalThickness(1.00)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm100)
  }
  it should "be Mm100 if PCB thickness is less than 100" in {
    val origin = FinalThickness(0.95)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm100)
  }
  it should "be Mm120 if PCB thickness is 120" in {
    val origin = FinalThickness(1.20)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm120)
  }
  it should "be Mm160 if PCB thickness is 140" in {
    val origin = FinalThickness(1.40)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm140)
  }
  it should "be Mm160 if PCB thickness is 160" in {
    val origin = FinalThickness(1.60)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm160)
  }
  it should "be Mm200 if PCB thickness is 200" in {
    val origin = FinalThickness(2.00)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm200)
  }
  it should "be Mm200 if PCB thickness is less than 200" in {
    val origin = FinalThickness(1.95)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm200)
  }
  it should "be Mm240 if PCB thickness is 240" in {
    val origin = FinalThickness(2.40)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm240)
  }
  it should "be Mm240 if PCB thickness is less than 240" in {
    val origin = FinalThickness(2.25)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm240)
  }
  it should "be Mm320 if PCB thickness is 320" in {
    val origin = FinalThickness(3.20)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm320)
  }
  it should "be Mm320 if PCB thickness is less than 320" in {
    val origin = FinalThickness(3.15)
    SafePcbFinalThickness.fromPcb(origin) shouldBe Some(Mm320)
  }
}
