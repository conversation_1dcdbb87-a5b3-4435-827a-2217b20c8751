package de.fellows.microservices.pcb.model.pcb

import de.fellows.app.assembly.commons.ProjectType
import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.PCBV2Properties
import de.fellows.ems.pcb.api.specification.units.UnitConversions._
import de.fellows.microservices.pcb.helper
import de.fellows.microservices.pcb.model.pcb.props.{CustomStackUp, InnerCopperThickness, MinInnerLayerStructure, NumberOfLayers}
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI.AssemblyId
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

import java.time.Instant
import java.util.UUID

class PcbTest extends AnyFlatSpec with should.Matchers {
  "Creating a PCB" should "remove inner thickness if number of layers is less than 4" in {
    val properties = helper.properties.copy(
      layerStack = helper.properties.layerStack.copy(
        layercount = Some(2),
        innerCopperThickness = Some(35.0 micrometers),
        minInnerLayerStructure = Some(0.1 millimeters)
      )
    )

    val id = UUID.randomUUID()
    val pcb = PCB(
      id = helper.uuid,
      assemblyId = AssemblyId(id),
      name = Some("test"),
      properties = PCBProperties(properties, CustomStackUp.no),
      orderId = None,
      previews = None,
      hash = None,
      files = Seq.empty,
      projectType = ProjectType.NoFiles,
      original = PCBV2(
        id = helper.uuid.value,
        name = None,
        assembly = id,
        description = None,
        created = Instant.now,
        files = None,
        filesLocked = false,
        lifecycles = Seq(),
        orderId = None,
        outline = None,
        specifications = Seq(),
        properties = PCBV2Properties.EMPTY,
        customer = None,
        projectType = ProjectType.NoFiles
      )
    )

    pcb.properties.layer.numberOfLayers shouldBe NumberOfLayers(2)
    pcb.properties.layer.innerCopperThickness shouldBe InnerCopperThickness(None)
    pcb.properties.layer.minInnerLayerStructure shouldBe MinInnerLayerStructure(None)
  }
}
