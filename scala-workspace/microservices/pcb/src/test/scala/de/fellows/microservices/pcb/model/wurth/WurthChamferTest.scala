package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.Chamfering.{Isa, Pci}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class WurthChamferTest extends AnyFlatSpec with should.Matchers {
  "Chamfer" should "be 20PCI" in {
    WurthChamfer.fromStackRateChamfer(Pci) shouldBe Chamfer20PCI
  }
  it should "be 45ISA" in {
    WurthChamfer.fromStackRateChamfer(Isa) shouldBe Chamfer45ISA
  }
  it should "be none" in {
    WurthChamfer.fromStackRateChamfer(de.fellows.ems.pcb.api.specification.Chamfering.None) shouldBe ChamferNone
  }
}
