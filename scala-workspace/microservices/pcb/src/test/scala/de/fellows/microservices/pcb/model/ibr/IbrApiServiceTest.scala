package de.fellows.microservices.pcb.model.ibr

import de.fellows.ems.pcb.api.specification.Side
import de.fellows.microservices.pcb.model.ibr.props.{IBRSides, IbrNumberOfLayers}
import de.fellows.microservices.pcb.model.pcb.props._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class IbrApiServiceTest extends AnyFlatSpec with should.Matchers {

  "Number of layers" should
    "fail for unexpected Numbers" in {

      IbrNumberOfLayers.fromPcb(ZeroLayers) shouldBe None
      IbrNumberOfLayers.fromPcb(TwentyEightLayers) shouldBe None
      IbrNumberOfLayers.fromPcb(InvalidNumberOfLayers(120)) shouldBe None

    }

  it should "succeed for expected numbers" in {
    IbrNumberOfLayers.fromPcb(OneLayer).map(_.value) shouldBe Some(1)
    IbrNumberOfLayers.fromPcb(TwoLayers).map(_.value) shouldBe Some(2)
    IbrNumberOfLayers.fromPcb(ThreeLayers).map(_.value) shouldBe Some(3)
    IbrNumberOfLayers.fromPcb(FourLayers).map(_.value) shouldBe Some(4)
    IbrNumberOfLayers.fromPcb(FiveLayers).map(_.value) shouldBe Some(5)
    IbrNumberOfLayers.fromPcb(SixLayers).map(_.value) shouldBe Some(6)
    IbrNumberOfLayers.fromPcb(EightLayers).map(_.value) shouldBe Some(8)
    IbrNumberOfLayers.fromPcb(TenLayers).map(_.value) shouldBe Some(10)
    IbrNumberOfLayers.fromPcb(TwelveLayers).map(_.value) shouldBe Some(12)
    IbrNumberOfLayers.fromPcb(FourteenLayers).map(_.value) shouldBe Some(14)
    IbrNumberOfLayers.fromPcb(SixteenLayers).map(_.value) shouldBe Some(16)
    IbrNumberOfLayers.fromPcb(EighteenLayers).map(_.value) shouldBe Some(18)
    IbrNumberOfLayers.fromPcb(TwentyLayers).map(_.value) shouldBe Some(20)
    IbrNumberOfLayers.fromPcb(TwentyTwoLayers).map(_.value) shouldBe Some(22)
    IbrNumberOfLayers.fromPcb(TwentyFourLayers).map(_.value) shouldBe Some(24)
    IbrNumberOfLayers.fromPcb(TwentySixLayers).map(_.value) shouldBe Some(26)
  }

  "Sides" should "be correct" in {

    IBRSides.convertSides(Side.Top) should be((1, 0))
    IBRSides.convertSides(Side.Bottom) should be((0, 1))
    IBRSides.convertSides(Side.Both) should be((1, 1))
    IBRSides.convertSides(Side.None) should be((0, 0))

  }
}
