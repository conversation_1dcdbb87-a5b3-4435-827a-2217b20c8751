package de.fellows.microservices.pcb.model.panel

import de.fellows.ems.pcb.model.{BigPoint, Dimension}
import de.fellows.microservices.pcb.PanelError
import org.scalatest.EitherValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import de.fellows.luminovo.panel.Depanelization

/** Here we test how PCBs are positioned on the panel of the minimum size to make sure that they are centered on
  * the panel. To simplify the output, we set padding to 0.
  */
class PositioningPCBTest extends AnyFlatSpec with should.Matchers with EitherValues {

  private val panelPreferences = PanelPreferences.Empty.copy(
    minWidth = 100,
    minHeight = 100,
    maxWidth = 100,
    maxHeight = 100,
    maxPCBs = Some(2),
    padding = new PanelPadding(0, 0),
    spacing = Rectangle(1.0, 2.0)
  )

  "Two PCBs" should "be centered on the panel" in {
    val distribution = calculateDistribution(5, new PcbSize(10, 5), panelPreferences)
    val rects = Seq(
      DistributionRectangle(BigPoint(44.5, 45.0), BigPoint(49.5, 55.0), Pcb),
      DistributionRectangle(BigPoint(50.5, 45.0), BigPoint(55.5, 55.0), Pcb)
    )
    distribution.value.items should be(rects)
  }
  "Three PCBs" should "be centered on the panel" in {
    val preferences = panelPreferences.copy(
      minWidth = 100,
      minHeight = 100,
      maxWidth = 100,
      maxHeight = 100,
      maxPCBs = Some(3),
      padding = new PanelPadding(0, 0),
      spacing = Rectangle(1.0, 2.0)
    )
    val distribution = calculateDistribution(5, new PcbSize(5, 10), preferences)
    val rects = Seq(
      DistributionRectangle(BigPoint(41.5, 45.0), BigPoint(46.5, 55.0), Pcb),
      DistributionRectangle(BigPoint(47.5, 45.0), BigPoint(52.5, 55.0), Pcb),
      DistributionRectangle(BigPoint(53.5, 45.0), BigPoint(58.5, 55.0), Pcb)
    )
    distribution.value.items should be(rects)
  }
  "Four PCBs" should "be centered on the panel in two lines" in {
    val preferences = panelPreferences.copy(
      minWidth = 100,
      minHeight = 100,
      maxWidth = 426,
      maxHeight = 271,
      maxPCBs = Some(5),
      padding = new PanelPadding(10, 10),
      spacing = Rectangle(2, 2),
      depanelization = Depanelization.Milling
    )
    val distribution = calculateDistribution(132, new PcbSize(32, 32), preferences)
    val rects = Seq(
      DistributionRectangle(Dimension(BigPoint(0, 0), BigPoint(100, 10)), Padding),
      DistributionRectangle(Dimension(BigPoint(0, 10), BigPoint(10, 100)), Padding),
      DistributionRectangle(Dimension(BigPoint(10, 90), BigPoint(100, 100)), Padding),
      DistributionRectangle(Dimension(BigPoint(90, 10), BigPoint(100, 90)), Padding),
      DistributionRectangle(Dimension(BigPoint(17, 17), BigPoint(49, 49)), Pcb),
      DistributionRectangle(Dimension(BigPoint(51, 17), BigPoint(83, 49)), Pcb),
      DistributionRectangle(Dimension(BigPoint(17, 51), BigPoint(49, 83)), Pcb),
      DistributionRectangle(Dimension(BigPoint(51, 51), BigPoint(83, 83)), Pcb)
    )
    distribution.value.items should be(rects)
  }
  "Five PCBs" should "be centered on the panel in one line" in {
    val constraints = panelPreferences.copy(
      minWidth = 100,
      minHeight = 100,
      maxWidth = 100,
      maxHeight = 100,
      maxPCBs = Some(5),
      padding = new PanelPadding(0, 0),
      spacing = Rectangle(1.0, 2.0)
    )
    val distribution = calculateDistribution(5, new PcbSize(10, 5), constraints)
    val rects = Seq(
      DistributionRectangle(BigPoint(35.5, 45.0), BigPoint(40.5, 55.0), Pcb),
      DistributionRectangle(BigPoint(41.5, 45.0), BigPoint(46.5, 55.0), Pcb),
      DistributionRectangle(BigPoint(47.5, 45.0), BigPoint(52.5, 55.0), Pcb),
      DistributionRectangle(BigPoint(53.5, 45.0), BigPoint(58.5, 55.0), Pcb),
      DistributionRectangle(BigPoint(59.5, 45.0), BigPoint(64.5, 55.0), Pcb)
    )
    distribution.value.items should be(rects)
  }
  it should "be centered on the panel in two lines" in {
    val constraints = panelPreferences.copy(
      minWidth = 100,
      minHeight = 25,
      maxWidth = 100,
      maxHeight = 100,
      maxPCBs = Some(5),
      padding = new PanelPadding(0, 0),
      spacing = Rectangle(1.0, 2.0)
    )
    val distribution = calculateDistribution(5, new PcbSize(30, 5), constraints)
    val rects = Seq(
      DistributionRectangle(BigPoint(4.0, 6.5), BigPoint(34.0, 11.5), Pcb),
      DistributionRectangle(BigPoint(35.0, 6.5), BigPoint(65.0, 11.5), Pcb),
      DistributionRectangle(BigPoint(66.0, 6.5), BigPoint(96.0, 11.5), Pcb),
      DistributionRectangle(BigPoint(4.0, 13.5), BigPoint(34.0, 18.5), Pcb),
      DistributionRectangle(BigPoint(35.0, 13.5), BigPoint(65.0, 18.5), Pcb)
    )
    distribution.value.items should be(rects)
  }
  it should "be centered rotated on the panel in two lines " in {
    val constraints = panelPreferences.copy(
      minWidth = 25,
      minHeight = 100,
      maxWidth = 25,
      maxHeight = 100,
      maxPCBs = Some(5),
      padding = new PanelPadding(0, 0),
      spacing = Rectangle(1.0, 2.0)
    )
    val distribution = calculateDistribution(5, new PcbSize(30, 5), constraints)
    val rects = List(
      DistributionRectangle(Dimension(BigPoint(6.00, 3.00), BigPoint(11.00, 33.00)), Pcb),
      DistributionRectangle(Dimension(BigPoint(6.00, 35.00), BigPoint(11.00, 65.00)), Pcb),
      DistributionRectangle(Dimension(BigPoint(6.00, 67.00), BigPoint(11.00, 97.00)), Pcb),
      DistributionRectangle(Dimension(BigPoint(0.00, 3.00), BigPoint(5.00, 33.00)), Pcb),
      DistributionRectangle(Dimension(BigPoint(0.00, 35.00), BigPoint(5.00, 65.00)), Pcb)
    )
    distribution.value.items should be(rects)
  }

  private def calculateDistribution(
      quantity: Int,
      pcbSize: PcbSize,
      constraints: PanelPreferences
  ): Either[PanelError, PanelDistribution] =
    DistributionAlgorithm.calculate(quantity, pcbSize, constraints)
}
