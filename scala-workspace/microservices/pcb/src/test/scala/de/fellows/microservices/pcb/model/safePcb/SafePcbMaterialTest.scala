package de.fellows.microservices.pcb.model.safePcb

import de.fellows.ems.pcb.api.specification.BaseMaterial.{FR2, FR4, Polyimide}
import de.fellows.microservices.pcb.model.pcb.props.BaseMaterial
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class SafePcbMaterialTest extends AnyFlatSpec with should.Matchers {
  "Material" should "be FR4" in {
    SafePcbMaterial.fromPcb(BaseMaterial(FR4)) shouldBe Some(SafePcbFR4)
  }
  it should "be Polyimide" in {
    SafePcbMaterial.fromPcb(BaseMaterial(Polyimide)) shouldBe Some(SafePcbPolyimide)
  }
  it should "be None" in {
    SafePcbMaterial.fromPcb(BaseMaterial(FR2)) shouldBe None
  }
}
