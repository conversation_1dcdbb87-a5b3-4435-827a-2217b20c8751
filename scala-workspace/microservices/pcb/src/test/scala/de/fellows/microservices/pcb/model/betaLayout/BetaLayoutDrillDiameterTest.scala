package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.microservices.pcb.model.pcb.props.MinViaDiameter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class BetaLayoutDrillDiameterTest extends AnyFlatSpec with should.Matchers {

  val minViaDiameter = MinViaDiameter(None)
  "Drill diameter" should "be `not supported, empty` if PCB min via diameter is empty" in {
    BetaLayoutDrillDiameter.fromPcb(minViaDiameter) shouldBe None
  }
  it should "be `empty` if PCB min via diameter is 0.05 (less than 0.10mm)" in {
    BetaLayoutDrillDiameter.fromPcb(minViaDiameter.copy(value = Some(0.05f))) shouldBe None
  }
  it should "be 0.1mm if PCB min via diameter is 0.13 (less than 0.25mm)" in {
    BetaLayoutDrillDiameter.fromPcb(minViaDiameter.copy(value = Some(0.13f))) shouldBe Some(DrillDiameter0_1mm)
  }
  it should "be 0.15 if PCB min via diameter is 0.15" in {
    BetaLayoutDrillDiameter.fromPcb(minViaDiameter.copy(value = Some(0.15f))) shouldBe Some(DrillDiameter0_15mm)
  }
  it should "be 0.15 if PCB min via diameter is 0.199 (less than 0.2mm)" in {
    BetaLayoutDrillDiameter.fromPcb(minViaDiameter.copy(value = Some(0.199f))) shouldBe Some(DrillDiameter0_15mm)
  }
  it should "be 0.2mm if PCB min via diameter is 0.2" in {
    BetaLayoutDrillDiameter.fromPcb(minViaDiameter.copy(value = Some(0.2))) shouldBe Some(DrillDiameter0_2mm)
  }
  it should "be 0.3mm if PCB min via diameter is 0.3 (more or equal to 0.3mm)" in {
    BetaLayoutDrillDiameter.fromPcb(minViaDiameter.copy(value = Some(0.3f))) shouldBe Some(DrillDiameter0_3mm)
  }
  it should "be 0.3mm if PCB min via diameter is 0.35 (more or equal to 0.3mm)" in {
    BetaLayoutDrillDiameter.fromPcb(minViaDiameter.copy(value = Some(0.35f))) shouldBe Some(DrillDiameter0_3mm)
  }

}
