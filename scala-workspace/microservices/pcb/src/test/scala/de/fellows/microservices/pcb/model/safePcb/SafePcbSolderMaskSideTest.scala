package de.fellows.microservices.pcb.model.safePcb

import de.fellows.ems.pcb.api.specification.Side.{ Both, Bottom, Top }
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskSide
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class SafePcbSolderMaskSideTest extends AnyFlatSpec with should.Matchers {

  val solderMaskSide = SoldermaskSide(None)
  "Soldermask side" should "be top+bottom if no side is given" in {
    SafePcbSolderMaskSide(solderMaskSide) shouldBe SafePcbSolderMaskSidesTopBottom
  }
  it should "be bottom" in {
    SafePcbSolderMaskSide(solderMaskSide.copy(value = Bottom)) shouldBe SafePcbSolderMaskSidesBottom
  }
  it should "be top" in {
    SafePcbSolderMaskSide(solderMaskSide.copy(value = Top)) shouldBe SafePcbSolderMaskSidesTop
  }
  it should "be TopBottom" in {
    SafePcbSolderMaskSide(solderMaskSide.copy(value = Both)) shouldBe SafePcbSolderMaskSidesTopBottom
  }
  it should "be None" in {
    SafePcbSolderMaskSide(
      solderMaskSide.copy(value = de.fellows.ems.pcb.api.specification.Side.None)
    ) shouldBe SafePcbSolderMaskSidesNone
  }
}
