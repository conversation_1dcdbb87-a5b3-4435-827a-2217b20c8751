package de.fellows.microservices.pcb.model.betaLayout

import com.osinka.i18n.Lang
import de.fellows.ems.pcb.api.specification.Side
import de.fellows.ems.pcb.api.specification.SurfaceFinish.Enig
import de.fellows.microservices.pcb.helper
import de.fellows.microservices.pcb.helper._
import de.fellows.microservices.pcb.model.panel.{NumberOfPanels, PanelSample}
import de.fellows.microservices.pcb.model.pcb.props.{
  BaseMaterial,
  BlindViaCount,
  BlindVias,
  BoardHeight,
  BoardWidth,
  BuriedViaCount,
  BuriedVias,
  Chamfering,
  EdgeMetalization,
  FinalThickness,
  IPCA600Class,
  InnerCopperThickness,
  LayerstackType,
  MinInnerLayerStructure,
  MinOuterLayerStructure,
  MinViaDiameter,
  NumberOfLayers,
  OuterCopperThickness,
  SoldermaskColor,
  SoldermaskSide,
  SurfaceFinish,
  TGValue
}
import de.fellows.microservices.pcb.model.pcb.{props, PropertyError, PropertyErrorKind, Quantity}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

import java.util.UUID

class BetaLayoutPcbRequestTest extends AnyFlatSpec with should.Matchers {

  private implicit val lang: Lang = Lang("en")
  private val panel               = PanelSample.calculatedPanelDetailsInfo

  protected def hasError(
      result: Either[Seq[PropertyError], BetaLayoutPcbRequest],
      error: PropertyError
  ): Boolean =
    result.swap.exists(_.contains(error))

  "Validation" should "fail if the width is empty" in {
    val props    = defaultPcb.properties.copy(basic = defaultPcb.properties.basic.copy(boardWidth = BoardWidth.zero))
    val pcb      = defaultPcb.copy(properties = props)
    val rigidPcb = BetaLayoutPcbRequest.validateAndConvert(pcb, panel)
    hasError(rigidPcb, PropertyError(BoardWidth(None), "The value for board width is empty", PropertyErrorKind.Empty))
  }
  it should "fail if the height is empty" in {
    val props    = defaultPcb.properties.copy(basic = defaultPcb.properties.basic.copy(boardHeight = BoardHeight.zero))
    val pcb      = defaultPcb.copy(properties = props)
    val rigidPcb = BetaLayoutPcbRequest.validateAndConvert(pcb, panel)
    hasError(rigidPcb, PropertyError(BoardHeight(None), "The value for board height is empty", PropertyErrorKind.Empty))
  }
  it should "fail if the maximum width is less PCB height" in {
    val rigidPcb = BetaLayoutPcbRequest.validateAndConvert(defaultPcb, panel)
    hasError(
      rigidPcb,
      PropertyError(
        BoardWidth(300.34),
        "The value for board width is above the supported maximum of 285mm",
        PropertyErrorKind.AboveMax(value = 300.34, max = 285, unit = "mm")
      )
    )
  }
  it should "fail if width and height are empty" in {
    val props = defaultPcb.properties.copy(basic =
      defaultPcb.properties.basic.copy(boardHeight = BoardHeight.zero, boardWidth = BoardWidth.zero)
    )
    val pcb      = defaultPcb.copy(properties = props)
    val rigidPcb = BetaLayoutPcbRequest.validateAndConvert(pcb, panel)
    hasError(rigidPcb, PropertyError(BoardWidth(None), "The value for board width is empty", PropertyErrorKind.Empty))
    hasError(rigidPcb, PropertyError(BoardHeight(None), "The value for board height is empty", PropertyErrorKind.Empty))
  }

  private val properties = helper.defaultPcb.properties.copy(
    basic = helper.defaultPcb.properties.basic.copy(
      boardWidth = BoardWidth(300.34f),
      boardHeight = BoardHeight(150.55f),
      soldermaskSide = SoldermaskSide(Side.None),
      soldermaskColor = SoldermaskColor.default,
      surfaceFinish = SurfaceFinish(Enig)
    ),
    advanced = helper.defaultPcb.properties.advanced.copy(
      ipcA600Class = IPCA600Class.default
    ),
    layer = helper.defaultPcb.properties.layer.copy(
      layerstackType = LayerstackType.rigid,
      numberOfLayers = props.NumberOfLayers(4),
      finalThickness = FinalThickness(1.59f),
      baseMaterial = BaseMaterial.default,
      outerCopperThickness = OuterCopperThickness(35),
      innerCopperThickness = InnerCopperThickness(35),
      minOuterLayerStructure = MinOuterLayerStructure(0.1),
      minInnerLayerStructure = MinInnerLayerStructure(0.1),
      tgValue = TGValue(135)
    ),
    mechanical = helper.defaultPcb.properties.mechanical.copy(
      minViaDiameter = MinViaDiameter(0.23),
      viaFillingType = props.ViaFillingType.default,
      blindVias = BlindVias.no,
      buriedVias = BuriedVias.no,
      blindViaCount = BlindViaCount.empty,
      buriedViaCount = BuriedViaCount.empty,
      chamfering = Chamfering.default
    )
  )

  val id = UUID.randomUUID()
  val blPcb = helper.defaultPcb.copy(
    id = helper.uuid,
    properties = properties
  )

  "BetaLayout PCB" should "have quantity = 10" in {
    val pcb = BetaLayoutPcbRequest.validateAndConvert(blPcb, panel.copy(numberOfPanels = NumberOfPanels(44)))
    pcb.map(_.quantity) shouldBe Right(Quantity(44))
  }
  it should "have number of layers = 4" in {
    val pcb = BetaLayoutPcbRequest.validateAndConvert(blPcb, panel)
    pcb.map(_.numberOfLayers) shouldBe Right(FourLayers)
  }
  it should "have width = 300.34" in {
    val pcb = BetaLayoutPcbRequest.validateAndConvert(blPcb, panel)
    pcb.map(_.width) shouldBe Right(BoardWidth(300.34f))
  }
  it should "have height = 150.55" in {
    val pcb = BetaLayoutPcbRequest.validateAndConvert(blPcb, panel)
    pcb.map(_.height) shouldBe Right(BoardHeight(150.55f))
  }
  it should "have thickness = 1.6mm" in {
    val pcb = BetaLayoutPcbRequest.validateAndConvert(blPcb, panel)
    pcb.map(_.finalThickness) shouldBe Right(Thickness1_6mm(FinalThickness(1.6)))
  }
  it should "have edgePlating = true" in {
    val pcb = BetaLayoutPcbRequest.validateAndConvert(blPcb, panel)
    pcb.map(_.edgeMetalization) shouldBe Right(EdgeMetalization.yes)
  }
  it should "have edgePlating = false" in {
    val props = blPcb.properties.copy(advanced =
      blPcb.properties.advanced.copy(edgeMetalization = EdgeMetalization.no)
    )
    val noEdgePlating = blPcb.copy(properties = props)
    val pcb           = BetaLayoutPcbRequest.validateAndConvert(noEdgePlating, panel)
    pcb.map(_.edgeMetalization) shouldBe Right(EdgeMetalization.no)
  }
  it should "have Silkscreen sides = Bottom" in {
    val pcb = BetaLayoutPcbRequest.validateAndConvert(blPcb, panel)
    pcb.map(_.silkscreenSides) shouldBe Right(SilkscreenSidesBottom)
  }
  it should "have Surface Finish = HAL" in {
    val pcb = BetaLayoutPcbRequest.validateAndConvert(blPcb, panel)
    pcb.map(_.surfaceFinish) shouldBe Right(ENIG)
  }
  it should "have Drill Diameter = 0.2" in {
    val pcb = BetaLayoutPcbRequest.validateAndConvert(blPcb, panel)
    pcb.map(_.drillDiameter) shouldBe Right(DrillDiameter0_2mm)
  }
  it should "have 6 layers and 1.6mm thickness" in {
    val props = blPcb.properties.copy(
      layer = blPcb.properties.layer.copy(
        finalThickness = FinalThickness(1.55),
        numberOfLayers = NumberOfLayers(6)
      )
    )
    val pcb      = blPcb.copy(properties = props)
    val rigidPcb = BetaLayoutPcbRequest.validateAndConvert(pcb, panel)

    rigidPcb.map(_.finalThickness) shouldBe Right(Thickness1_6mm(FinalThickness(1.55)))
    rigidPcb.map(_.numberOfLayers) shouldBe Right(SixLayers)
  }

}
