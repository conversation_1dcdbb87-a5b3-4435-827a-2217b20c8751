package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.microservices.pcb.model.pcb.props
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class BetaLayoutNumberOfLayersTest extends AnyFlatSpec with should.Matchers {

  "Number of layers" should "be 1 if number of layers in PCB is 1" in {
    BetaLayoutNumberOfLayers.fromPcb(props.OneLayer) shouldBe Some(OneLayer)
  }
  it should "be 2 if number of layers in PCB is 2" in {
    BetaLayoutNumberOfLayers.fromPcb(props.TwoLayers) shouldBe Some(TwoLayers)
  }
  it should "be 4 if number of layers in PCB is 4" in {
    BetaLayoutNumberOfLayers.fromPcb(props.FourLayers) shouldBe Some(FourLayers)
  }
  it should "be 6 if number of layers in PCB is 6" in {
    BetaLayoutNumberOfLayers.fromPcb(props.SixLayers) shouldBe Some(SixLayers)
  }
  it should "be error if number of layers in PCB is 16" in {
    BetaLayoutNumberOfLayers.fromPcb(props.SixteenLayers) shouldBe None
  }
  it should "be error if number of layers in PCB is invalid" in {
    BetaLayoutNumberOfLayers.fromPcb(props.InvalidNumberOfLayers(120)) shouldBe None
  }
}
