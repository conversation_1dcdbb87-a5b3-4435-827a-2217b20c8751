package de.fellows.microservices.pcb.model.panel

import de.fellows.ems.pcb.model.BigPoint
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec
import de.fellows.luminovo.panel.Depanelization

class PanelDistributionTest extends AnyWordSpec with should.Matchers {

  val rects = List(
    DistributionRectangle(BigPoint(5.0, 10.0), BigPoint(70.0, 40.0), Pcb),
    DistributionRectangle(BigPoint(71.0, 10.0), BigPoint(141.0, 40.0), Pcb),
    DistributionRectangle(BigPoint(5.0, 42.0), BigPoint(70.0, 82.0), Pcb),
    DistributionRectangle(BigPoint(71.0, 42.0), BigPoint(141.0, 82.0), Pcb),
    DistributionRectangle(BigPoint(5.0, 84.0), BigPoint(70.0, 124.0), Pcb),
    DistributionRectangle(BigPoint(71.0, 84.0), BigPoint(141.0, 124.0), Pcb)
  )
  val distribution = PanelDistribution.withoutPrecomputedGap(
    panel = new PanelDimensions(150.0, 150.0),
    pcbPerPanel = 6,
    mesh = PcbMesh(2, 3),
    waste = WastedArea.zero,
    items = rects,
    depanelization = Depanelization.Milling,
    minMillingDistanceInMm = 8,
    pcbIsRotated = false
  )

  "Padding" should {
    "be = 5.0 on the left" in {
      distribution.padding.leftInMm should be(5.0)
    }
    "be = 10.0 on the top" in {
      distribution.padding.topInMm should be(10.0)
    }
    "be = 9.0 on the right" in {
      distribution.padding.rightInMm should be(9.0)
    }
    "be = 26.0 on the bottom" in {
      distribution.padding.bottomInMm should be(26.0)
    }
  }
  "Gap" should {
    val rects = List(
      DistributionRectangle(BigPoint(5.0, 10.0), BigPoint(70.0, 40.0), Pcb),
      DistributionRectangle(BigPoint(100.0, 10.0), BigPoint(141.0, 40.0), Pcb),
      DistributionRectangle(BigPoint(5.0, 42.0), BigPoint(70.0, 82.0), Pcb),
      DistributionRectangle(BigPoint(100.0, 42.0), BigPoint(141.0, 82.0), Pcb),
      DistributionRectangle(BigPoint(5.0, 84.0), BigPoint(70.0, 124.0), Pcb),
      DistributionRectangle(BigPoint(100.0, 84.0), BigPoint(141.0, 124.0), Pcb)
    )
    val gap = PanelDistribution.gap(rects)
    "be = 29.0 on the horizontal" in {
      gap.x should be(30.0)
    }
    "be = 1.0 on the vertical" in {
      gap.y should be(2.0)
    }
  }
  "Rotate" should {
    "work" in {
      val distribution = PanelDistribution(
        panel = new PanelDimensions(304, 166),
        gap = new PanelGap(0, 2),
        waste = WastedArea(0, 0, 50),
        pcbPerPanel = 2,
        mesh = PcbMesh(2, 1),
        items = List(
          DistributionRectangle(BigPoint(0, 0), BigPoint(304, 2), Padding),
          DistributionRectangle(BigPoint(0, 2), BigPoint(2, 166), Padding),
          DistributionRectangle(BigPoint(2, 164), BigPoint(304, 166), Padding),
          DistributionRectangle(BigPoint(302, 2), BigPoint(304, 164), Padding),
          DistributionRectangle(BigPoint(2, 2), BigPoint(302, 82), Pcb),
          DistributionRectangle(BigPoint(2, 84), BigPoint(302, 164), Pcb)
        ),
        depanelization = Depanelization.Milling,
        minMillingDistanceInMm = 8,
        pcbIsRotated = false
      )

      val notRotated = distribution.rotate(500, 500)
      notRotated shouldBe distribution

      val rotated = distribution.rotate(300, 200)
      rotated should be(PanelDistribution(
        panel = new PanelDimensions(166, 304),
        gap = new PanelGap(2, 0),
        waste = WastedArea(0, 0, 50),
        pcbPerPanel = 2,
        mesh = PcbMesh(1, 2),
        items = List(
          DistributionRectangle(BigPoint(164, 0), BigPoint(166, 304), Padding),
          DistributionRectangle(BigPoint(0, 0), BigPoint(164, 2), Padding),
          DistributionRectangle(BigPoint(0, 2), BigPoint(2, 304), Padding),
          DistributionRectangle(BigPoint(2, 302), BigPoint(164, 304), Padding),
          DistributionRectangle(BigPoint(84, 2), BigPoint(164, 302), Pcb),
          DistributionRectangle(BigPoint(2, 2), BigPoint(82, 302), Pcb)
        ),
        depanelization = Depanelization.Milling,
        minMillingDistanceInMm = 8,
        pcbIsRotated = true
      ))
    }
  }
}
