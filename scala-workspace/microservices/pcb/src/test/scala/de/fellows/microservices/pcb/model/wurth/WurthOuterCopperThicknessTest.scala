package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class WurthOuterCopperThicknessTest extends AnyFlatSpec with should.Matchers {

  val outerCopperThickness = props.OuterCopperThickness(None)
  "Copper outside thickness" should "be `not support, empty` if PCB value is empty" in {
    WurthOuterCopperThickness.converter(outerCopperThickness) shouldBe None
  }

  it should "be Mcr18 if PCB is less than 18" in {
    val origin = props.OuterCopperThickness(17)
    WurthOuterCopperThickness.converter(origin) shouldBe Some(OuterThicknessMcr18(origin))
  }
  it should "be Mcr18 if PCB is  18" in {
    val origin = props.OuterCopperThickness(18)
    WurthOuterCopperThickness.converter(origin) shouldBe Some(OuterThicknessMcr18(origin))
  }
  it should "be Mcr35 if PCB is less than 35" in {
    val origin = props.OuterCopperThickness(34)
    WurthOuterCopperThickness.converter(origin) shouldBe Some(OuterThicknessMcr35(origin))
  }
  it should "be Mcr35 if PCB is 35" in {
    val origin = props.OuterCopperThickness(35)
    WurthOuterCopperThickness.converter(origin) shouldBe Some(OuterThicknessMcr35(origin))
  }
  it should "be Mcr70 if PCB is less than 70" in {
    val origin = props.OuterCopperThickness(69)
    WurthOuterCopperThickness.converter(origin) shouldBe Some(OuterThicknessMcr70(origin))
  }
  it should "be Mcr70 if PCB is 70" in {
    val origin = props.OuterCopperThickness(70)
    WurthOuterCopperThickness.converter(origin) shouldBe Some(OuterThicknessMcr70(origin))
  }
  it should "be Mcr105 if PCB is less than 105" in {
    val origin = props.OuterCopperThickness(104)
    WurthOuterCopperThickness.converter(origin) shouldBe Some(OuterThicknessMcr105(origin))
  }
  it should "be Mcr105 if PCB is 105" in {
    val origin = props.OuterCopperThickness(105)
    WurthOuterCopperThickness.converter(origin) shouldBe Some(OuterThicknessMcr105(origin))
  }
  it should "be `not supported` if PCB is more than 105" in {
    val origin = props.OuterCopperThickness(155)
    WurthOuterCopperThickness.converter(origin) shouldBe None
  }
}
