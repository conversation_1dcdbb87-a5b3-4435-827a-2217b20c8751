package de.fellows.microservices.pcb.model.safePcb

import de.fellows.microservices.pcb.model.pcb.props
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class SafePcbNumberOfLayersTest extends AnyFlatSpec with should.Matchers {

  "Number of layers" should "be 1 if number of layers in PCB is 1" in {
    SafePcbNumberOfLayers.fromPcb(props.OneLayer) shouldBe Some(OneLayer)
  }
  it should "be 2 if number of layers in PCB is 2" in {
    SafePcbNumberOfLayers.fromPcb(props.TwoLayers) shouldBe Some(TwoLayers)
  }
  it should "be 4 if number of layers in PCB is 4" in {
    SafePcbNumberOfLayers.fromPcb(props.FourLayers) shouldBe Some(FourLayers)
  }
  it should "be 6 if number of layers in PCB is 6" in {
    SafePcbNumberOfLayers.fromPcb(props.SixLayers) shouldBe Some(SixLayers)
  }
  it should "be 8 if number of layers in PCB is 8" in {
    SafePcbNumberOfLayers.fromPcb(props.EightLayers) shouldBe Some(EightLayers)
  }
  it should "be 10 if number of layers in PCB is 10" in {
    SafePcbNumberOfLayers.fromPcb(props.TenLayers) shouldBe Some(TenLayers)
  }
  it should "be 12 if number of layers in PCB is 12" in {
    SafePcbNumberOfLayers.fromPcb(props.TwelveLayers) shouldBe Some(TwelveLayers)
  }
  it should "be 14 if number of layers in PCB is 14" in {
    SafePcbNumberOfLayers.fromPcb(props.FourteenLayers) shouldBe Some(FourteenLayers)
  }
  it should "be 16 if number of layers in PCB is 16" in {
    SafePcbNumberOfLayers.fromPcb(props.SixteenLayers) shouldBe Some(SixteenLayers)
  }
  it should "be error if number of layers in PCB is 18" in {
    SafePcbNumberOfLayers.fromPcb(props.EighteenLayers) shouldBe None
  }
  it should "be error if number of layers in PCB is invalid" in {
    SafePcbNumberOfLayers.fromPcb(props.InvalidNumberOfLayers(120)) shouldBe None
  }
}
