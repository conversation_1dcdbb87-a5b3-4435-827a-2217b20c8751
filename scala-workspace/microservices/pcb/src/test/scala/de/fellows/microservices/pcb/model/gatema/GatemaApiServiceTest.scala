package de.fellows.microservices.pcb.model.gatema

import com.osinka.i18n.Lang
import com.typesafe.config.ConfigFactory
import de.fellows.microservices.pcb.model.panel.EmsPreferences
import de.fellows.microservices.pcb.model.pcb.Manufacturer
import de.fellows.microservices.pcb.{helper, ApiNotSetUpError, ApiNotSetUpErrorKind, PcbServerConfig}
import org.scalatest.flatspec.AsyncFlatSpec
import org.scalatest.matchers.should
import sttp.client3.testing.SttpBackendStub

import java.util.UUID

class GatemaApiServiceTest extends AsyncFlatSpec with should.Matchers {

  private val backend             = SttpBackendStub.asynchronousFuture
  implicit private val lang: Lang = Lang.Default

  "api service" should "return error if no customerId exists" in {
    val config     = ConfigFactory.load()
    val apiService = GatemaApiService.fromConfig(PcbServerConfig(config), backend)

    val result = apiService.doMakeQuotes(
      tenant = "some-team",
      pcb = helper.defaultPcb,
      manufacturer = Manufacturer(
        supplier = UUID.randomUUID(),
        locations = Seq.empty,
        name = "Gatema"
      ),
      scenarioRequestsWithPanels = Seq.empty,
      existingOffers = Seq.empty,
      emsPreferences = EmsPreferences.default("some-team"),
      credentials = None,
      requestValidations = Map.empty
    )

    val expected = Left(ApiNotSetUpError(ApiNotSetUpErrorKind.MissingGatemaCustomerId, s"missing.customerId.gatema"))

    result.map(r => r should be(expected))
  }

}
