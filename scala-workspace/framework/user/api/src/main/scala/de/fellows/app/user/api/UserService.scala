// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.user.api

import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Service, ServiceAcl, ServiceCall}
import de.fellows.app.notification.common.{Notification, NotificationTopic}
import de.fellows.app.user.api.Teams.Team
import de.fellows.app.user.api.TemplateAPI.{PrintTemplateAPI, RenderRequest}
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.communication.{BinaryMessageSerializer, ServiceExceptionSerializer}
import de.fellows.utils.service.StackrateServiceAPI
import de.fellows.utils.streams.StreamMessage
import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.tags.Tag
import play.api.libs.json.JsValue

import java.util.UUID

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate User API"
  )
)
@Tag(name = UserService.API_TAG_TEAM, description = "Team")
@Tag(name = UserService.API_TAG_USER, description = "User")
@Tag(name = UserService.API_TAG_AUTH, description = "Authentication")
trait UserService extends Service with StackrateServiceAPI {

  def ip(): ServiceCall[String, String]

  @StackrateApi
  @Tag(name = UserService.API_TAG_AUTH)
  @Tag(name = UserService.API_TAG_USER)
  def login(team: String, emailOrName: String): ServiceCall[Login, UserLogin]

  @StackrateApi
  @Tag(name = UserService.API_TAG_AUTH)
  @Tag(name = UserService.API_TAG_USER)
  def apiToken(): ServiceCall[ApiTokenLogin, UserLogin]

  @StackrateApi
  @Tag(name = UserService.API_TAG_AUTH)
  @Tag(name = UserService.API_TAG_USER)
  def createApiToken(id: UUID, team: String): ServiceCall[NotUsed, ApiToken]

  @StackrateApi
  @Tag(name = UserService.API_TAG_AUTH)
  @Tag(name = UserService.API_TAG_USER)
  def refresh(updateFields: Option[Boolean] = None): ServiceCall[NotUsed, TokenRefreshDone]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def register(team: String, invitation: Option[String]): ServiceCall[CreateUser, UserLogin]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def registerTechnical(team: String, invitation: Option[String]): ServiceCall[CreateUser, UserLogin]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  @Tag(name = UserService.API_TAG_TEAM)
  def addToTeam(id: UUID): ServiceCall[Seq[String], Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  @Tag(name = UserService.API_TAG_TEAM)
  def addToTeamByEmailOrName(emailOrName: Option[String]): ServiceCall[Seq[String], Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  @Tag(name = UserService.API_TAG_TEAM)
  def removeFromTeam(id: UUID, team: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def invite(team: String): ServiceCall[Invite, String]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def nameAvailable(): ServiceCall[AvailableRequest, AvailableResponse]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def activate(emailOrName: String): ServiceCall[ActivationToken, Done]

  def _sendMail(sendMail: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def changeMail(): ServiceCall[EmailChange, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def openSendMail(sendMail: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def requestPasswordReset(emailOrName: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def resetPassword(emailOrName: String): ServiceCall[ResetPassword, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def deleteUser(): ServiceCall[Login, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def deleteSpecificUser(id: UUID): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_USER)
  def changePassword(): ServiceCall[ChangePassword, Done]

  def _getUserInfo(id: UUID): ServiceCall[NotUsed, User]

  def getUserInfo: ServiceCall[NotUsed, SessionInfo]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEAM)
  def createTeam(): ServiceCall[Team, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEAM)
  def getTeams(domain: Option[String]): ServiceCall[NotUsed, Seq[Team]]

  def _getTeam(domain: String): ServiceCall[NotUsed, Team]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEAM)
  @Tag(name = UserService.API_TAG_USER)
  def getUsersOfTeams(team: String): ServiceCall[NotUsed, Seq[User]]

  def getAllUsers: ServiceCall[NotUsed, Seq[User]]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEAM)
  def updateTeam(id: String): ServiceCall[Team, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEAM)
  def deleteTeam(id: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEMPLATES)
  def createTemplate(): ServiceCall[PrintTemplateAPI, PrintTemplateAPI]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEMPLATES)
  def updateTemplate(id: String): ServiceCall[PrintTemplateAPI, PrintTemplateAPI]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEMPLATES)
  def getTemplate(id: String): ServiceCall[NotUsed, PrintTemplateAPI]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEMPLATES)
  def searchTemplates(category: Option[String], name: Option[String]): ServiceCall[NotUsed, Seq[PrintTemplateAPI]]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEMPLATES)
  def deleteTemplate(id: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEMPLATES)
  def renderTemplate(id: String): ServiceCall[Map[String, JsValue], Array[Byte]]

  def _renderTemplate(team: String, category: String, name: Option[String]): ServiceCall[RenderRequest, Array[Byte]]

  def _renderHtmlTemplate(team: String, category: String, name: Option[String]): ServiceCall[RenderRequest, String]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEMPLATES)
  def getResources(): ServiceCall[NotUsed, Seq[String]]

  @StackrateApi
  @Tag(name = UserService.API_TAG_TEMPLATES)
  def deleteResource(name: String): ServiceCall[NotUsed, Done]

  override final def descriptor = {
    import Service._

    withDocumentation(
      named("user")
        .withCalls(
          restCall(Method.GET, "/api/user/ip", ip _),
          restCall(Method.GET, "/api/user/session", getUserInfo _),
          restCall(Method.GET, "/api/user/users", getAllUsers _),
          restCall(Method.POST, "/api/user/teams", createTeam _),
          restCall(Method.GET, "/api/user/teams?domain", getTeams _),
          restCall(Method.GET, "/internal/user/teams/:domain", _getTeam _),
          restCall(Method.GET, "/api/user/teams/:team/users", getUsersOfTeams _),
          restCall(Method.PUT, "/api/user/teams/:id", updateTeam _),
          restCall(Method.DELETE, "/api/user/teams/:id", deleteTeam _),
          //        restCall(Method.DELETE, "/api/user/teams/:id", deleteTeam _),

          restCall(Method.POST, "/api/user/available", nameAvailable _),
          restCall(Method.POST, "/api/user/login/:team/:emailOrName", login _),
          restCall(Method.POST, "/api/user/token", apiToken _),

          // TODO change path
          restCall(Method.PUT, "/api/user/:id/teams/:team/token", createApiToken _),
          restCall(Method.PUT, "/api/user/teams?emailOrName", addToTeamByEmailOrName _),
          restCall(Method.PUT, "/api/user/:id/teams", addToTeam _),
          restCall(Method.DELETE, "/api/user/:id/teams/:team", removeFromTeam _),
          restCall(Method.DELETE, "/api/user/:id", deleteSpecificUser _),
          restCall(Method.POST, "/api/user/activate/:emailOrName", activate _),
          restCall(Method.POST, "/api/user/refresh?updateFields", refresh _),
          restCall(Method.POST, "/api/user/register/:team?invitation", register _),
          restCall(Method.POST, "/api/user/registerTechnical/:team?invitation", registerTechnical _),
          restCall(Method.POST, "/api/user/teams/:team/invite", invite _),
          restCall(Method.POST, "/api/user/templates", createTemplate _),
          restCall(Method.GET, "/api/user/templates/resources", getResources _),
          restCall(Method.DELETE, "/api/user/templates/resources/:name", deleteResource _),
          restCall(Method.GET, "/api/user/templates/:id", getTemplate _),
          restCall(Method.PUT, "/api/user/templates/:id", updateTemplate _),
          restCall(Method.GET, "/api/user/templates?category&name", searchTemplates _),
          restCall(Method.DELETE, "/api/user/templates/:id", deleteTemplate _),
          restCall(Method.PUT, "/api/user/templates/:id/render", renderTemplate _)
            .withResponseSerializer(new BinaryMessageSerializer()),
          restCall(
            Method.PUT,
            "/internal/user/teams/:team/templates/categories/:category/render?name",
            _renderTemplate _
          )
            .withResponseSerializer(new BinaryMessageSerializer()),
          restCall(
            Method.PUT,
            "/internal/user/teams/:team/templates/categories/:category/renderHtml?name",
            _renderHtmlTemplate _
          ),
          restCall(Method.POST, "/api/user/requestPasswordReset/:emailOrName", requestPasswordReset _),
          restCall(Method.POST, "/api/user/resetPassword/:emailOrName", resetPassword _),
          restCall(Method.POST, "/api/user/sendActivationMail/:emailOrName", openSendMail _),
          restCall(Method.POST, "/internal/user/sendActivationMail/:emailOrName", _sendMail _),
          restCall(Method.GET, "/internal/user/info/:id", _getUserInfo _),
          restCall(Method.PUT, "/api/user/changePassword", changePassword _),
          restCall(Method.PUT, "/api/user/email", changeMail _),
          restCall(Method.POST, "/api/user/delete", deleteUser _)
        ).withAcls(ServiceAcl(pathRegex = Some("/api/user/.*")))
        .withTopics(
          topic(UserService.USER_EVENTS, userTopic()),
          topic(UserService.TEAM_EVENTS, teamTopic()),

          //          topic(UserService.USER_CREATED, userCreatedTopic()),
          //          topic(UserService.TEAM_CREATED, teamCreatedTopic()),
          //          topic(UserService.USER_DELETED, userDeletedTopic()),
          topic(UserService.NOTIFICATION, userNotificationTopic())
          //          topic(UserService.USER_STATE, userStateTopic()),
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )
  }

  def userTopic(): Topic[StreamMessage[UserMessage]]

  def teamTopic(): Topic[StreamMessage[TeamMessage]]

  //  def userStateTopic(): Topic[UserStateMessage]
  //
  //  def userCreatedTopic(): Topic[User]
  //
  //  def teamCreatedTopic(): Topic[Team]
  //
  //  def userDeletedTopic(): Topic[User]
  //
  def userNotificationTopic(): Topic[Notification]
}

object UserService {
  val VERSION     = "v1.0"
  val USER_EVENTS = s"domain.user.event-$VERSION"
  val TEAM_EVENTS = s"domain.team.event-$VERSION"

  val USER_STATE   = s"domain.user.state-$VERSION"
  val USER_CREATED = s"domain.user.created-$VERSION"
  val USER_DELETED = s"domain.user.deleted-$VERSION"
  val TEAM_CREATED = s"domain.team.created-$VERSION"
  val NOTIFICATION = s"${NotificationTopic.prefix}.user-$VERSION"

  final val API_TAG_USER      = "User"
  final val API_TAG_AUTH      = "Authentication"
  final val API_TAG_TEAM      = "Team"
  final val API_TAG_TEMPLATES = "Print Templates"
}
//
