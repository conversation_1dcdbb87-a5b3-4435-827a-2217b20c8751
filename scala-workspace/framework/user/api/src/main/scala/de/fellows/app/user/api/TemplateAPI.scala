package de.fellows.app.user.api

import play.api.libs.json.{Format, JsValue, Json}

object TemplateAPI {

  case class PrintTemplateAPI(
      id: Option[String],
      team: Option[String],
      category: String,
      name: String,
      content: String,
      header: Option[String],
      footer: Option[String]
  )

  object PrintTemplateAPI {
    implicit val f: Format[PrintTemplateAPI] = Json.format[PrintTemplateAPI]
  }

  case class RenderRequest(resources: Seq[String], variables: Map[String, JsValue])

  object RenderRequest {
    implicit val f: Format[RenderRequest] = Json.format[RenderRequest]
  }

}
