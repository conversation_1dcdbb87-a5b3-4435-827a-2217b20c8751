package de.fellows.app.user.api

import play.api.libs.json.{Format, Json}

import java.time.{Duration, Instant}
import java.util.UUID

case class CreateUser(email: Option[String], username: String, password: Option[String]) {
  override def toString: String = s"CreateUser($email, $username)"
}

case class SessionInfo(
    user: User,
    team: String
)

case class User(
    email: Option[String],
    team: Seq[String],
    username: String,
    uid: UUID,
    technical: Boolean = false,
    activated: Boolean = false
)

case class UserLogin(user: User, authToken: Option[String], refreshToken: Option[String])

case class Login(password: String) {
  override def toString: String = "Login(???)"
}

case class ApiTokenLogin(apiToken: UUID) {
  override def toString: String = "Login(???)"
}

case class TokenRefreshDone(authToken: String)

//case class Session(id: UUID)
//case class UserSession(sessionID: UUID, userID: UUID, email: String)F

case class AvailableRequest(username: Option[String], email: Option[String])

case class AvailableResponse(username: Option[Boolean], email: Option[Boolean])

case class ActivationToken(token: UUID)

case class ResetPassword(token: UUID, password: String) {
  override def toString: String = s"ResetPassword(???, ???)"
}

case class ChangePassword(oldPassword: String, newPassword: String) {
  override def toString: String = s"ChangePassword(???, ???)"
}

case class EmailChange(email: String)

case class UserTeamsChanged(user: User, teams: Seq[String], oldTeams: Seq[String])

case class ApiToken(token: UUID, team: String, created: Instant, validUntil: Option[Instant]) {
  def isExpired(): Boolean =
    validUntil match {
      case None     => false
      case Some(ex) => Duration.between(Instant.now(), ex).isNegative
    }
}

case class Invite(
    mail: Option[String],
    greeting: Option[String],
    sendNotification: Option[Boolean],
    language: Option[String]
)

object Invite {
  implicit val format: Format[Invite] = Json.format
}

object CreateUser {
  implicit val format: Format[CreateUser] = Json.format
}

object SessionInfo {
  implicit val format: Format[SessionInfo] = Json.using[Json.WithDefaultValues].format
}
object User {
  implicit val format: Format[User] = Json.using[Json.WithDefaultValues].format
}

object UserTeamsChanged {
  implicit val format: Format[UserTeamsChanged] = Json.using[Json.WithDefaultValues].format
}

//object Session{
//  implicit val format: Format[Session] = Json.format[Session]
//}

object AvailableRequest {
  implicit val format: Format[AvailableRequest] = Json.format
}

object AvailableResponse {
  implicit val format: Format[AvailableResponse] = Json.format
}

object UserLogin {
  implicit val format: Format[UserLogin] = Json.format
}

object EmailChange {
  implicit val format: Format[EmailChange] = Json.format
}

object TokenRefreshDone {
  implicit val format: Format[TokenRefreshDone] = Json.format
}

object ActivationToken {
  implicit val format: Format[ActivationToken] = Json.format
}

object ResetPassword {
  implicit val format: Format[ResetPassword] = Json.format
}

object ChangePassword {
  implicit val format: Format[ChangePassword] = Json.format
}

object Login {
  implicit val format: Format[Login] = Json.format
}

object ApiTokenLogin {
  implicit val format: Format[ApiTokenLogin] = Json.format
}

object ApiToken {
  implicit val format: Format[ApiToken] = Json.format
}
