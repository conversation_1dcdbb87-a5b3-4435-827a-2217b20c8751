package de.fellows.app.user.impl.entities.teams

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import de.fellows.app.user.api.Teams.Team
import de.fellows.app.user.impl.entities.teams.Commands.{ CreateTeam, GetTeam, TeamCommand, TeamResponse, UpdateTeam }
import de.fellows.app.user.impl.entities.teams.Events.{ TeamCreated, TeamEvent, TeamUpdated }
import de.fellows.utils.communication.ServiceDefinition
import play.api.Logging

class TeamEntity(implicit val service: ServiceDefinition) extends PersistentEntity with Logging {
  override type Command = TeamCommand
  override type Event   = TeamEvent
  override type State   = Option[Team]

  override def initialState: Option[Team] = None

  override def behavior: Behavior = {
    case Some(s) => existing(s)
    case None    => missing
  }

  def existing(team: Team): Actions =
    Actions()
      .onReadOnlyCommand[GetTeam.type, TeamResponse] {
        case (GetTeam, ctx, _) =>
          ctx.reply(TeamResponse(Some(team)))
      }
      .onCommand[UpdateTeam, Done] {
        case (x: UpdateTeam, ctx, _) =>
          ctx.thenPersist(
            TeamUpdated(x.team)
          )(_ => ctx.reply(Done))
      }
      .onEvent {
        case (x: TeamUpdated, _) => Some(x.team.copy(team.domain))
      }

  def missing: Actions =
    Actions()
      .onReadOnlyCommand[GetTeam.type, TeamResponse] {
        case (GetTeam, ctx, _) =>
          ctx.reply(TeamResponse(None))
      }
      .onCommand[CreateTeam, Done] {
        case (x: CreateTeam, ctx, _) =>
          ctx.thenPersist(
            TeamCreated(x.team)
          )(_ => ctx.reply(Done))
      }
      .onEvent {
        case (x: TeamCreated, _) => Some(x.team)
      }

}
