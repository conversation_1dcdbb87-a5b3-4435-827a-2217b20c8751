package de.fellows.app.user.impl.entities

import java.util.UUID

import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import de.fellows.utils.JsonFormats.singletonFormat
import de.fellows.utils.communication.ServiceError
import play.api.libs.json.{ Format, Json }

object UserNotActivated extends ServiceError(1, "User is not activated") {
  @transient implicit val format: Format[UserNotActivated.type] = singletonFormat(UserNotActivated)
}

object UserAlreadyActivated extends ServiceError(2, "User is already activated") {
  @transient implicit val format: Format[UserAlreadyActivated.type] = singletonFormat(UserAlreadyActivated)
}

object AlreadyRequested extends ServiceError(3, "Already Requested") {
  @transient implicit val format: Format[AlreadyRequested.type] = singletonFormat(AlreadyRequested)
}

object OldToken extends ServiceError(4, "Old Token") {
  @transient implicit val format: Format[OldToken.type] = singletonFormat(OldToken)
}

object WrongToken extends ServiceError(5, "Wrong Token") {
  @transient implicit val format: Format[WrongToken.type] = singletonFormat(WrongToken)
}

object NoToken extends ServiceError(5, "No Token available") {
  @transient implicit val format: Format[NoToken.type] = singletonFormat(NoToken)
}

object UserNotFound extends ServiceError(6, "User Not Found", TransportErrorCode.NotFound) {
  @transient implicit val format: Format[UserNotFound.type] = singletonFormat(UserNotFound)
}

object UserAlreadyExists extends ServiceError(7, "User exists already") {
  @transient implicit val format: Format[UserAlreadyExists.type] = singletonFormat(UserAlreadyExists)
}

object EmailInUse extends ServiceError(8, "Email is already in use") {
  @transient implicit val format: Format[EmailInUse.type] = singletonFormat(EmailInUse)
}
