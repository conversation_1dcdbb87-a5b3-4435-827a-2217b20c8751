package de.fellows.app.user.impl.i18n

import de.fellows.utils.mailgun.GenericMailDescriptor

object InviteI18n {

  def get(lang: String, team: String, link: String): GenericMailDescriptor =
    lang.toLowerCase().trim match {
      case "de" => new InviteDE().get(team, link)
      case "en" => new InviteEN().get(team, link)
      case _    => new InviteEN().get(team, link)
    }

}

sealed trait InviteFactory {
  def get(team: String, link: String): GenericMailDescriptor
}

class InviteDE extends InviteFactory {
  override def get(team: String, link: String): GenericMailDescriptor =
    GenericMailDescriptor(
      subject = "Sie wurden als neuer Benutzer zu Stackrate eingeladen.",
      text = Seq(
        "Um die Registrierung abzuschließen, klicken Sie auf die untenstehende Schaltfläche.",
        "Die Registrierung gilt ausschließlich für Ihre Mailadresse.",
        "",
        "Be<PERSON> und Feedback können Sie uns gerne per Email kontaktieren oder direkt über die Plattform (Chat unten rechts).",
        ""
      ),
      strongtext = Seq(
        s"Ihre Team-Domain lautet : ${team}"
      ),
      button = "Hier registrieren",
      templatelanguage = "de",
      buttonlink = link
    )
}

class InviteEN extends InviteFactory {
  override def get(team: String, link: String): GenericMailDescriptor =
    GenericMailDescriptor(
      subject = "You have been invited to Stackrate",
      text = Seq(
        "Please click the following button to complete your registration.",
        "This registration link is only valid for your individual email address.",
        "",
        "Feel free to contact us in case you have any question.",
        "",
        "mail: <EMAIL>"
      ),
      strongtext = Seq(
        s"Your Team-Domain is: ${team}"
      ),
      button = "Register now",
      templatelanguage = "en",
      buttonlink = link
    )
}
