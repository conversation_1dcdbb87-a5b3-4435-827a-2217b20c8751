package de.fellows.app.user.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
import de.fellows.app.user.api.ApiToken
import de.fellows.utils.HashUtils.HashedPassword
import de.fellows.utils.communication.{ServiceDefinition, ServiceException}
import de.fellows.utils.entities.teamsettings.TeamSettingsJsonSerializerRegistry
import play.api.Logging
import play.api.libs.json.{Format, Json}

import java.time.{Duration, Instant}
import java.util.UUID

class UserEntity extends PersistentEntity with Logging {
  override type Command = UserCommand
  override type Event   = UserEvent
  override type State   = Option[User]

  override def initialState: Option[User] = None

  implicit val service: ServiceDefinition = ServiceDefinition("user")

  override def behavior: Behavior = {
    case Some(user) =>
      existingUser

    case None =>
      missingUser
  }

  private def missingUser =
    Actions()
      .onReadOnlyCommand[GetUser.type, UserResponse] {
        case (GetUser, ctx, state) => ctx.reply(UserResponse(state))
      }.onCommand[CreateUser, User] {
        case (CreateUser(email, team, name, hash, uid, technical), ctx, state) =>
          val token: GeneralToken = createToken

          ctx.thenPersist(UserCreated(email, team, name, hash, uid, token.created, token, technical))(_ =>
            ctx.reply(User(
              email,
              team,
              name,
              hash,
              uid,
              token.created,
              activated = technical,
              deleted = false,
              technical = false,
              Some(token),
              None
            ))
          )
      }
      .onEvent {
        case (UserCreated(email, team, name, hash, uid, created, token, technical), state) => Some(User(
            email,
            team,
            name,
            hash,
            uid,
            created,
            activated = false,
            deleted = false,
            technical = technical,
            Some(token),
            None
          ))
      }

  private def existingUser = {
    Actions().onReadOnlyCommand[GetUser.type, UserResponse] {
      case (GetUser, ctx, state) => ctx.reply(UserResponse(state))
    }.onReadOnlyCommand[CreateUser, User] {
      case (CreateUser(_, _, _, _, _, _), ctx, _) =>
        ctx.commandFailed(new ServiceException(UserAlreadyExists))

    }.onReadOnlyCommand[GetToken.type, GeneralToken] {
      case (GetToken, ctx, state) =>
        getCurrentToken(ctx, state)
    }.onCommand[DeleteUser, User] {
      case (DeleteUser(_), ctx, state) =>
        ctx.thenPersist(UserDeleted(
          state.get.email,
          state.get.team,
          state.get.username,
          state.get.uid,
          state.get.technical,
          state.get.activated
        ))(_ => ctx.reply(state.get))
    }.onCommand[SetEmail, EmailSet] {
      case (SetEmail(mail), ctx, state) =>
        val set = EmailSet(
          state.get.uid,
          state.get.team,
          state.get.username,
          state.get.email,
          mail,
          state.get.technical,
          state.get.activated
        )
        ctx.thenPersist(set)(_ => ctx.reply(set))
    }.onCommand[Activate, Done] {
      case (Activate(user, token), ctx, state) =>
        activateUser(user, token, ctx, state)
    }.onCommand[SetPassword, Done] {
      case (SetPassword(newPassword), ctx, state) =>
        ctx.thenPersist(PasswordSet(state.get, newPassword))(_ => ctx.reply(Done))
    }.onCommand[AddUserToTeam, Done] {
      case (AddUserToTeam(id, team), ctx, state) =>
        val newTeams = (state.get.team ++ team).distinct
        ctx.thenPersist(TeamsChanged(state.get.uid, newTeams, state.get.team))(_ => ctx.reply(Done))
    }.onCommand[RemoveUserFromTeam, Done] {
      case (RemoveUserFromTeam(id, team), ctx, state) =>
        val newTeams = state.get.team.filter(x => !team.contains(x))
        ctx.thenPersist(TeamsChanged(state.get.uid, newTeams, state.get.team))(_ => ctx.reply(Done))
    }.onCommand[AddApiToken, ApiToken] {
      case (AddApiToken(user, team, created, valid), ctx, state) =>
        val newToken = UUID.randomUUID()
        val apiToken = ApiToken(
          newToken,
          team,
          created,
          valid
        )
        ctx.thenPersist(ApiTokenAdded(Some(state.get), state.get.uid, apiToken))(_ => ctx.reply(apiToken))
    }.onCommand[RefreshActivationToken.type, GeneralToken] {
      case (RefreshActivationToken, ctx, state) =>
        if (state.get.activated) {
          ctx.commandFailed(new ServiceException(UserAlreadyActivated))
          ctx.done
        } else {
          val token: GeneralToken = createToken
          ctx.thenPersist(TokenRefreshed(Some(state.get), token))(_ => ctx.reply(token))
        }
    }.onCommand[RequestPasswordReset.type, GeneralToken] {
      case (RequestPasswordReset, ctx, state) =>
        if (!state.exists(_.activated)) {
          ctx.commandFailed(new ServiceException(UserNotActivated))
          ctx.done
        } else if (
          state.flatMap(_.resetToken).isDefined && !state.flatMap(
            _.resetToken.map(_.isExpired(Duration.ofDays(1)))
          ).contains(true)
        ) {
          ctx.commandFailed(new ServiceException(AlreadyRequested))
          ctx.done
        } else {
          val token = createToken
          ctx.thenPersist(ResetTokenCreated(state, token))(_ => ctx.reply(token))
        }
    }.onCommand[ResetPassword, Done] {
      case (ResetPassword(_, resetToken, password), ctx, state) =>
        val token = state.get.resetToken
        if (token.map(_.token).contains(resetToken)) {
          if (!token.get.isExpired(Duration.ofDays(1))) {
            ctx.thenPersist(PasswordReset(state.get.uid, password))(_ => ctx.reply(Done))
          } else {
            ctx.commandFailed(new ServiceException(OldToken))
            ctx.done
          }
        } else if (token.isEmpty) {
          ctx.commandFailed(new ServiceException(NoToken))
          ctx.done
        } else {
          ctx.commandFailed(new ServiceException(WrongToken))
          ctx.done

        }
    }
      .onEvent {
        case (EmailSet(_, _, _, _, mail, _, _), state) => Some(state.get.copy(email = Some(mail)))
        case (Activated(_, _), state)                  => Some(state.get.copy(activated = true, activationToken = None))
        case (TokenRefreshed(_, token), state) =>
          Some(state.get.copy(activationToken = Some(token)))
        case (ResetTokenCreated(_, token), state) =>
          Some(state.get.copy(resetToken = Some(token)))
        case (PasswordReset(_, hash), state) =>
          Some(state.get.copy(hash = Some(hash), resetToken = None))
        case (PasswordSet(u, newHash), state) =>
          Some(state.get.copy(hash = Some(newHash)))
        case (TeamsChanged(u, newTeams, oldTeams), state) =>
          Some(state.get.copy(team = newTeams))
        case (UserDeleted(_, _, _, _, _, _), _) =>
          None
        case (t: ApiTokenAdded, s) =>
          s.map(_.copy(apiToken = Some(t.apiToken)))
      }
  }

  private def createToken = {
    val created = Instant.now()
    val token   = GeneralToken(UUID.randomUUID(), created)
    token
  }

  private def activateUser(user: UUID, token: UUID, ctx: CommandContext[Done], state: Option[User]) =
    if (state.get.activated) {
      ctx.commandFailed(new ServiceException(UserAlreadyActivated))
      ctx.done
    } else if (!state.get.activationToken.map(_.token).contains(token)) {
      ctx.commandFailed(new ServiceException(WrongToken))
      ctx.done
    } else {
      ctx.thenPersist(Activated(Some(state.get), user)) {
        _ => ctx.reply(Done)
      }
    }

  private def getCurrentToken(ctx: ReadOnlyCommandContext[GeneralToken], state: Option[User]) =
    if (state.get.activated) {
      ctx.commandFailed(new ServiceException(UserAlreadyActivated))
    } else {
      state.get.activationToken match {
        case Some(token) => ctx.reply(token)
        case None        => ctx.commandFailed(new ServiceException(NoToken))
      }
    }
}

case class User(
    email: Option[String],
    team: Seq[String],
    username: String,
    hash: Option[HashedPassword],
    uid: UUID,
    created: Instant,
    activated: Boolean,
    deleted: Boolean,
    technical: Boolean,
    activationToken: Option[GeneralToken],
    resetToken: Option[GeneralToken],
    apiToken: Option[ApiToken] = None
)

case class GeneralToken(token: UUID, created: Instant) {
  def isExpired(validDuration: Duration): Boolean =
    Duration.between(created, Instant.now()).compareTo(validDuration) > 0
}

object GeneralToken {
  implicit val format: Format[GeneralToken] = Json.format
}

object User {
  implicit val format: Format[User] = Json.format
}

object UserSerializerRegistry extends JsonSerializerRegistry {
  override def serializers = List(
    JsonSerializer[User],
    JsonSerializer[UserResponse],
    JsonSerializer[HashedPassword],
    JsonSerializer[GeneralToken],
    JsonSerializer[UserCreated],
    JsonSerializer[TeamsChanged],
    JsonSerializer[UserDeleted],
    JsonSerializer[Activated],
    JsonSerializer[TokenRefreshed],
    JsonSerializer[EmailSet],
    JsonSerializer[PasswordReset],
    JsonSerializer[PasswordSet],
    JsonSerializer[ResetTokenCreated],
    JsonSerializer[CreateUser],
    JsonSerializer[AddUserToTeam],
    JsonSerializer[DeleteUser],
    JsonSerializer[GetUser.type],
    JsonSerializer[RefreshActivationToken.type],
    JsonSerializer[RemoveUserFromTeam],
    JsonSerializer[Activate],
    JsonSerializer[SetEmail],
    JsonSerializer[ResetPassword],
    JsonSerializer[SetPassword],
    JsonSerializer[ApiToken],
    JsonSerializer[GetToken.type],
    JsonSerializer[RequestPasswordReset.type],
    JsonSerializer[UserNotActivated.type],
    JsonSerializer[UserAlreadyActivated.type],
    JsonSerializer[AlreadyRequested.type],
    JsonSerializer[OldToken.type],
    JsonSerializer[WrongToken.type],
    JsonSerializer[NoToken.type],
    JsonSerializer[UserNotFound.type],
    JsonSerializer[EmailInUse.type],
    JsonSerializer[AddApiToken],
    JsonSerializer[ApiTokenAdded]
  ) ++ TeamSettingsJsonSerializerRegistry.serializers
}
