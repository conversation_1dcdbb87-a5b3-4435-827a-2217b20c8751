package de.fellows.app.user.impl.entities.templates

import com.lightbend.lagom.scaladsl.playjson.{ JsonSerializer, JsonSerializerRegistry }
import de.fellows.app.user.api.Teams.Team
import de.fellows.app.user.impl.entities.teams.Commands.{ CreateTeam, DeleteTeam, UpdateTeam }
import de.fellows.app.user.impl.entities.teams.Events.{ TeamCreated, TeamDeleted, TeamUpdated }

object PrintTemplateSerializerRegistry extends JsonSerializerRegistry {
  override def serializers =
    List(
      JsonSerializer[PrintTemplate],
      JsonSerializer[SetTemplate],
      JsonSerializer[GetTemplate],
      JsonSerializer[DeleteTemplate],
      JsonSerializer[TemplateChanged]
    )

}
