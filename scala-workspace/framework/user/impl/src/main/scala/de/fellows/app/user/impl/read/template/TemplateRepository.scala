package de.fellows.app.user.impl.read.template

import akka.stream.Materializer
import com.datastax.driver.core.Row
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import de.fellows.app.user.impl.entities.templates.PrintTemplate
import de.fellows.utils.communication.ServiceDefinition

import scala.concurrent.{ ExecutionContext, Future }

class TemplateRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {

  // language=SQL
  def getTemplates(team: String): Future[Seq[PrintTemplate]] =
    session.selectAll("SELECT * FROM templatesbyname WHERE team = ?", team).map(_.map(toTemplate))

  // language=SQL
  def getTemplatesByCategory(team: String, category: String): Future[Seq[PrintTemplate]] =
    session.selectAll("SELECT * FROM templatesbyname WHERE team = ? AND category = ?", team, category).map(
      _.map(toTemplate)
    )

  // language=SQL
  def getTemplatesByCategoryAndName(team: String, category: String, name: String): Future[Seq[PrintTemplate]] =
    session.selectAll(
      "SELECT * FROM templatesbyname WHERE team = ? AND category = ? AND name = ?",
      team,
      category,
      name
    ).map(_.map(toTemplate))

  def toTemplate(r: Row): PrintTemplate =
    PrintTemplate(
      id = r.getString("templateid"),
      team = r.getString("team"),
      category = r.getString("category"),
      name = r.getString("name"),
      content = r.getString("content"),
      header = Option(r.getString("header")),
      footer = Option(r.getString("footer"))
    )

}
