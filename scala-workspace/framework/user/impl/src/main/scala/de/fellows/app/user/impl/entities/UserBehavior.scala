package de.fellows.app.user.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag}
import de.fellows.app.user.api.ApiToken
import de.fellows.utils.HashUtils.HashedPassword
import de.fellows.utils.JsonFormats.singletonFormat
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID

sealed trait UserCommand

sealed trait UserEvent extends AggregateEvent[UserEvent] {
  override def aggregateTag = UserEvent.Tag
}

object UserEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[UserEvent](NumShards)
}

case class CreateUser(
    email: Option[String],
    team: Seq[String],
    username: String,
    hash: Option[HashedPassword],
    uid: UUID,
    technical: Boolean
) extends UserCommand with ReplyType[User]

object CreateUser {
  implicit val format: Format[CreateUser] = Json.format
}

case class AddUserToTeam(id: UUID, teams: Seq[String]) extends UserCommand with ReplyType[Done]

case class RemoveUserFromTeam(id: UUID, teams: Seq[String]) extends UserCommand with ReplyType[Done]

object AddUserToTeam {
  implicit val format: Format[AddUserToTeam] = Json.format
}

object RemoveUserFromTeam {
  implicit val format: Format[RemoveUserFromTeam] = Json.format
}

case class DeleteUser(uid: UUID) extends UserCommand with ReplyType[User]

object DeleteUser {
  implicit val format: Format[DeleteUser] = Json.format
}

case object GetUser extends UserCommand with ReplyType[UserResponse] {
  implicit val format: Format[GetUser.type] = singletonFormat(GetUser)
}

case class UserResponse(response: Option[User])

object UserResponse {
  implicit val format: Format[UserResponse] = Json.format
}

case object RefreshActivationToken extends UserCommand with ReplyType[GeneralToken] {
  implicit val format: Format[RefreshActivationToken.type] = singletonFormat(RefreshActivationToken)
}

case object RequestPasswordReset extends UserCommand with ReplyType[GeneralToken] {
  implicit val format: Format[RequestPasswordReset.type] = singletonFormat(RequestPasswordReset)
}

case object GetToken extends UserCommand with ReplyType[GeneralToken] {
  implicit val format: Format[GetToken.type] = singletonFormat(GetToken)
}

case class AddApiToken(id: UUID, team: String, created: Instant, validUntil: Option[Instant]) extends UserCommand
    with ReplyType[ApiToken]

object AddApiToken {
  implicit val f: Format[AddApiToken] = Json.format[AddApiToken]
}

case class SetEmail(email: String) extends UserCommand with ReplyType[EmailSet]

object SetEmail {
  implicit val format: Format[SetEmail] = Json.format
}

case class ResetPassword(user: UUID, token: UUID, password: HashedPassword) extends UserCommand with ReplyType[Done]

object ResetPassword {
  implicit val format: Format[ResetPassword] = Json.format

}

case class SetPassword(password: HashedPassword) extends UserCommand with ReplyType[Done]

object SetPassword {
  implicit val format: Format[SetPassword] = Json.format
}

case class Activate(user: UUID, token: UUID) extends UserCommand with ReplyType[Done]

object Activate {
  implicit val format: Format[Activate] = Json.format
}

case class UserCreated(
    email: Option[String],
    team: Seq[String],
    username: String,
    hash: Option[HashedPassword],
    uid: UUID,
    created: Instant,
    activationToken: GeneralToken,
    technical: Boolean = false
) extends UserEvent

object UserCreated {
  implicit val format: Format[UserCreated] = Json.using[Json.WithDefaultValues].format
}

case class UserDeleted(
    email: Option[String],
    team: Seq[String],
    username: String,
    uid: UUID,
    technical: Boolean = false,
    activated: Boolean = false
) extends UserEvent

object UserDeleted {
  implicit val format: Format[UserDeleted] = Json.using[Json.WithDefaultValues].format
}

case class PasswordReset(user: UUID, hash: HashedPassword) extends UserEvent

object PasswordReset {
  implicit val format: Format[PasswordReset] = Json.format

}

case class PasswordSet(user: User, newPassword: HashedPassword) extends UserEvent

object PasswordSet {
  implicit val format: Format[PasswordSet] = Json.format
}

case class EmailSet(
    user: UUID,
    team: Seq[String],
    username: String,
    oldMail: Option[String],
    newMail: String,
    technical: Boolean = false,
    activated: Boolean = false
) extends UserEvent

object EmailSet {
  implicit val format: Format[EmailSet] = Json.using[Json.WithDefaultValues].format
}

case class TeamsChanged(user: UUID, teams: Seq[String], oldTeams: Seq[String]) extends UserEvent

object TeamsChanged {
  implicit val format: Format[TeamsChanged] = Json.format
}

case class Activated(user: Option[User], id: UUID) extends UserEvent

object Activated {
  implicit val format: Format[Activated] = Json.format
}

case class TokenRefreshed(user: Option[User], token: GeneralToken) extends UserEvent

object TokenRefreshed {
  implicit val format: Format[TokenRefreshed] = Json.format
}

case class ResetTokenCreated(user: Option[User], token: GeneralToken) extends UserEvent

object ResetTokenCreated {
  implicit val format: Format[ResetTokenCreated] = Json.format
}

case class ApiTokenAdded(user: Option[User], id: UUID, apiToken: ApiToken) extends UserEvent

object ApiTokenAdded {
  implicit val f: Format[ApiTokenAdded] = Json.format[ApiTokenAdded]
}
