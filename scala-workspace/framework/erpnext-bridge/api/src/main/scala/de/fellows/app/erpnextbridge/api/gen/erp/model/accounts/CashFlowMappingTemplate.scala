package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class CashFlowMappingTemplate(
    name: String,
    template_name: String,
    mapping: Seq[CashFlowMappingTemplateDetails]
)

object CashFlowMappingTemplate {
  val NAME_FIELD = "name"

  def apply(v: JsValue): CashFlowMappingTemplate = new CashFlowMappingTemplate(
    name = (v \ "name").get.as[String],
    template_name = (v \ "template_name").get.as[String],
    mapping = (v \ "mapping").toOption.map(x => x.as[JsArray].value.map(_.as[CashFlowMappingTemplateDetails])).get.toSeq
  )

  implicit val reads: Reads[CashFlowMappingTemplate] = Reads[CashFlowMappingTemplate] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Cash Flow Mapping Template") => JsSuccess(CashFlowMappingTemplate(js))
      case Some(_)                            => JsError("Wrong Doctype")
      case _                                  => JsError("Doctype not Found")
    }
  }

}
