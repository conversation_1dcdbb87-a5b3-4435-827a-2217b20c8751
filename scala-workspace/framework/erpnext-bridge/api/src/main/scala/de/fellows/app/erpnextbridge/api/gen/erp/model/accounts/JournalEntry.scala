package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class JournalEntry(
    name: String,
    title: Option[String],
    voucher_type: String,
    naming_series: String,
    posting_date: String,
    company: String,
    finance_book: Option[String],
    accounts: Seq[JournalEntryAccount],
    cheque_no: Option[String],
    cheque_date: Option[String],
    total_debit: Option[Double],
    total_credit: Option[Double],
    difference: Option[Double],
    multi_currency: Option[Int],
    total_amount_currency: Option[String],
    total_amount: Option[Double],
    total_amount_in_words: Option[String],
    clearance_date: Option[String],
    paid_loan: Option[String],
    inter_company_journal_entry_reference: Option[String],
    bill_no: Option[String],
    bill_date: Option[String],
    due_date: Option[String],
    write_off_based_on: Option[String],
    write_off_amount: Option[Double],
    pay_to_recd_from: Option[String],
    letter_head: Option[String],
    select_print_heading: Option[String],
    mode_of_payment: Option[String],
    payment_order: Option[String],
    is_opening: Option[String],
    stock_entry: Option[String],
    auto_repeat: Option[String],
    amended_from: Option[String]
)

object JournalEntry {
  val NAME_FIELD = "name"

  def apply(v: JsValue): JournalEntry = new JournalEntry(
    name = (v \ "name").get.as[String],
    title = (v \ "title").toOption.map(_.as[String]),
    voucher_type = (v \ "voucher_type").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    posting_date = (v \ "posting_date").get.as[String],
    company = (v \ "company").get.as[String],
    finance_book = (v \ "finance_book").toOption.map(_.as[String]),
    accounts = (v \ "accounts").toOption.map(x => x.as[JsArray].value.map(_.as[JournalEntryAccount])).get.toSeq,
    cheque_no = (v \ "cheque_no").toOption.map(_.as[String]),
    cheque_date = (v \ "cheque_date").toOption.map(_.as[String]),
    total_debit = (v \ "total_debit").toOption.map(_.as[Double]),
    total_credit = (v \ "total_credit").toOption.map(_.as[Double]),
    difference = (v \ "difference").toOption.map(_.as[Double]),
    multi_currency = (v \ "multi_currency").toOption.map(_.as[Int]),
    total_amount_currency = (v \ "total_amount_currency").toOption.map(_.as[String]),
    total_amount = (v \ "total_amount").toOption.map(_.as[Double]),
    total_amount_in_words = (v \ "total_amount_in_words").toOption.map(_.as[String]),
    clearance_date = (v \ "clearance_date").toOption.map(_.as[String]),
    paid_loan = (v \ "paid_loan").toOption.map(_.as[String]),
    inter_company_journal_entry_reference = (v \ "inter_company_journal_entry_reference").toOption.map(_.as[String]),
    bill_no = (v \ "bill_no").toOption.map(_.as[String]),
    bill_date = (v \ "bill_date").toOption.map(_.as[String]),
    due_date = (v \ "due_date").toOption.map(_.as[String]),
    write_off_based_on = (v \ "write_off_based_on").toOption.map(_.as[String]),
    write_off_amount = (v \ "write_off_amount").toOption.map(_.as[Double]),
    pay_to_recd_from = (v \ "pay_to_recd_from").toOption.map(_.as[String]),
    letter_head = (v \ "letter_head").toOption.map(_.as[String]),
    select_print_heading = (v \ "select_print_heading").toOption.map(_.as[String]),
    mode_of_payment = (v \ "mode_of_payment").toOption.map(_.as[String]),
    payment_order = (v \ "payment_order").toOption.map(_.as[String]),
    is_opening = (v \ "is_opening").toOption.map(_.as[String]),
    stock_entry = (v \ "stock_entry").toOption.map(_.as[String]),
    auto_repeat = (v \ "auto_repeat").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[JournalEntry] = Reads[JournalEntry] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Journal Entry") => JsSuccess(JournalEntry(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
