package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemPrice(
    name: String,
    item_code: String,
    uom: Option[String],
    packing_unit: Option[Int],
    min_qty: Option[Int],
    item_name: Option[String],
    item_description: Option[String],
    price_list: String,
    customer: Option[String],
    supplier: Option[String],
    buying: Option[Int],
    selling: Option[Int],
    currency: Option[String],
    price_list_rate: Double,
    valid_from: Option[String],
    lead_time_days: Option[Int],
    valid_upto: Option[String],
    note: Option[String],
    reference: Option[String]
)

object ItemPrice {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ItemPrice = new ItemPrice(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    uom = (v \ "uom").toOption.map(_.as[String]),
    packing_unit = (v \ "packing_unit").toOption.map(_.as[Int]),
    min_qty = (v \ "min_qty").toOption.map(_.as[Int]),
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    item_description = (v \ "item_description").toOption.map(_.as[String]),
    price_list = (v \ "price_list").get.as[String],
    customer = (v \ "customer").toOption.map(_.as[String]),
    supplier = (v \ "supplier").toOption.map(_.as[String]),
    buying = (v \ "buying").toOption.map(_.as[Int]),
    selling = (v \ "selling").toOption.map(_.as[Int]),
    currency = (v \ "currency").toOption.map(_.as[String]),
    price_list_rate = (v \ "price_list_rate").get.as[Double],
    valid_from = (v \ "valid_from").toOption.map(_.as[String]),
    lead_time_days = (v \ "lead_time_days").toOption.map(_.as[Int]),
    valid_upto = (v \ "valid_upto").toOption.map(_.as[String]),
    note = (v \ "note").toOption.map(_.as[String]),
    reference = (v \ "reference").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ItemPrice] = Reads[ItemPrice] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Price") => JsSuccess(ItemPrice(js))
      case Some(_)            => JsError("Wrong Doctype")
      case _                  => JsError("Doctype not Found")
    }
  }

}
