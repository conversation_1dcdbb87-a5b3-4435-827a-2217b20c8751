package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PricingRuleDetail(
    name: String,
    pricing_rule: Option[String],
    item_code: Option[String],
    margin_type: Option[String],
    rate_or_discount: Option[String],
    child_docname: Option[String],
    rule_applied: Option[Int]
)

object PricingRuleDetail {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PricingRuleDetail = new PricingRuleDetail(
    name = (v \ "name").get.as[String],
    pricing_rule = (v \ "pricing_rule").toOption.map(_.as[String]),
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    margin_type = (v \ "margin_type").toOption.map(_.as[String]),
    rate_or_discount = (v \ "rate_or_discount").toOption.map(_.as[String]),
    child_docname = (v \ "child_docname").toOption.map(_.as[String]),
    rule_applied = (v \ "rule_applied").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[PricingRuleDetail] = Reads[PricingRuleDetail] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Pricing Rule Detail") => JsSuccess(PricingRuleDetail(js))
      case Some(_)                     => JsError("Wrong Doctype")
      case _                           => JsError("Doctype not Found")
    }
  }

}
