package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SubscriptionPlanDetail(
    name: String,
    qty: Int,
    plan: String
)

object SubscriptionPlanDetail {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SubscriptionPlanDetail = new SubscriptionPlanDetail(
    name = (v \ "name").get.as[String],
    qty = (v \ "qty").get.as[Int],
    plan = (v \ "plan").get.as[String]
  )

  implicit val reads: Reads[SubscriptionPlanDetail] = Reads[SubscriptionPlanDetail] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Subscription Plan Detail") => JsSuccess(SubscriptionPlanDetail(js))
      case Some(_)                          => JsError("Wrong Doctype")
      case _                                => JsError("Doctype not Found")
    }
  }

}
