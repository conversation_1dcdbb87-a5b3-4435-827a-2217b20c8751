package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PaymentOrder(
    name: String,
    naming_series: String,
    company: String,
    payment_order_type: String,
    party: Option[String],
    posting_date: Option[String],
    company_bank: Option[String],
    company_bank_account: String,
    account: Option[String],
    references: Seq[PaymentOrderReference],
    amended_from: Option[String]
)

object PaymentOrder {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PaymentOrder = new PaymentOrder(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    company = (v \ "company").get.as[String],
    payment_order_type = (v \ "payment_order_type").get.as[String],
    party = (v \ "party").toOption.map(_.as[String]),
    posting_date = (v \ "posting_date").toOption.map(_.as[String]),
    company_bank = (v \ "company_bank").toOption.map(_.as[String]),
    company_bank_account = (v \ "company_bank_account").get.as[String],
    account = (v \ "account").toOption.map(_.as[String]),
    references = (v \ "references").toOption.map(x => x.as[JsArray].value.map(_.as[PaymentOrderReference])).get.toSeq,
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PaymentOrder] = Reads[PaymentOrder] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Payment Order") => JsSuccess(PaymentOrder(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
