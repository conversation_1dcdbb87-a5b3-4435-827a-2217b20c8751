package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class InvoiceDiscounting(
    name: String,
    posting_date: String,
    loan_start_date: Option[String],
    loan_period: Option[Int],
    loan_end_date: Option[String],
    status: Option[String],
    company: String,
    invoices: Seq[DiscountedInvoice],
    total_amount: Option[Double],
    bank_charges: Option[Double],
    short_term_loan: String,
    bank_account: String,
    bank_charges_account: String,
    accounts_receivable_credit: String,
    accounts_receivable_discounted: String,
    accounts_receivable_unpaid: String,
    amended_from: Option[String]
)

object InvoiceDiscounting {
  val NAME_FIELD = "name"

  def apply(v: JsValue): InvoiceDiscounting = new InvoiceDiscounting(
    name = (v \ "name").get.as[String],
    posting_date = (v \ "posting_date").get.as[String],
    loan_start_date = (v \ "loan_start_date").toOption.map(_.as[String]),
    loan_period = (v \ "loan_period").toOption.map(_.as[Int]),
    loan_end_date = (v \ "loan_end_date").toOption.map(_.as[String]),
    status = (v \ "status").toOption.map(_.as[String]),
    company = (v \ "company").get.as[String],
    invoices = (v \ "invoices").toOption.map(x => x.as[JsArray].value.map(_.as[DiscountedInvoice])).get.toSeq,
    total_amount = (v \ "total_amount").toOption.map(_.as[Double]),
    bank_charges = (v \ "bank_charges").toOption.map(_.as[Double]),
    short_term_loan = (v \ "short_term_loan").get.as[String],
    bank_account = (v \ "bank_account").get.as[String],
    bank_charges_account = (v \ "bank_charges_account").get.as[String],
    accounts_receivable_credit = (v \ "accounts_receivable_credit").get.as[String],
    accounts_receivable_discounted = (v \ "accounts_receivable_discounted").get.as[String],
    accounts_receivable_unpaid = (v \ "accounts_receivable_unpaid").get.as[String],
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[InvoiceDiscounting] = Reads[InvoiceDiscounting] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Invoice Discounting") => JsSuccess(InvoiceDiscounting(js))
      case Some(_)                     => JsError("Wrong Doctype")
      case _                           => JsError("Doctype not Found")
    }
  }

}
