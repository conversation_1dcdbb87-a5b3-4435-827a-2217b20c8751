package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class LeadSource(
    source_name: String,
    details: Option[String]
)

object LeadSource {
  val NAME_FIELD = "source_name"

  def apply(v: JsValue): LeadSource = new LeadSource(
    source_name = (v \ "source_name").get.as[String],
    details = (v \ "details").toOption.map(_.as[String])
  )

  implicit val reads: Reads[LeadSource] = Reads[LeadSource] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Lead Source") => JsSuccess(LeadSource(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
