package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PaymentGatewayAccount(
    name: String,
    payment_gateway: String,
    is_default: Option[Int],
    payment_account: String
)

object PaymentGatewayAccount {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PaymentGatewayAccount = new PaymentGatewayAccount(
    name = (v \ "name").get.as[String],
    payment_gateway = (v \ "payment_gateway").get.as[String],
    is_default = (v \ "is_default").toOption.map(_.as[Int]),
    payment_account = (v \ "payment_account").get.as[String]
  )

  implicit val reads: Reads[PaymentGatewayAccount] = Reads[PaymentGatewayAccount] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Payment Gateway Account") => JsSuccess(PaymentGatewayAccount(js))
      case Some(_)                         => JsError("Wrong Doctype")
      case _                               => JsError("Doctype not Found")
    }
  }

}
