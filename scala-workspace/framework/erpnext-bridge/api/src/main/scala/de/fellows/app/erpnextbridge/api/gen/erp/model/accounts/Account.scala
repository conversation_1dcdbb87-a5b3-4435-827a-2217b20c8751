package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Account(
    name: String,
    disabled: Option[Int],
    account_name: String,
    account_number: Option[String],
    is_group: Option[Int],
    company: String,
    root_type: Option[String],
    report_type: Option[String],
    account_currency: Option[String],
    inter_company_account: Option[Int],
    parent_account: String,
    account_type: Option[String],
    tax_rate: Option[Double],
    freeze_account: Option[String],
    balance_must_be: Option[String],
    lft: Option[Int],
    rgt: Option[Int],
    old_parent: Option[String],
    include_in_gross: Option[Int]
)

object Account {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Account = new Account(
    name = (v \ "name").get.as[String],
    disabled = (v \ "disabled").toOption.map(_.as[Int]),
    account_name = (v \ "account_name").get.as[String],
    account_number = (v \ "account_number").toOption.map(_.as[String]),
    is_group = (v \ "is_group").toOption.map(_.as[Int]),
    company = (v \ "company").get.as[String],
    root_type = (v \ "root_type").toOption.map(_.as[String]),
    report_type = (v \ "report_type").toOption.map(_.as[String]),
    account_currency = (v \ "account_currency").toOption.map(_.as[String]),
    inter_company_account = (v \ "inter_company_account").toOption.map(_.as[Int]),
    parent_account = (v \ "parent_account").get.as[String],
    account_type = (v \ "account_type").toOption.map(_.as[String]),
    tax_rate = (v \ "tax_rate").toOption.map(_.as[Double]),
    freeze_account = (v \ "freeze_account").toOption.map(_.as[String]),
    balance_must_be = (v \ "balance_must_be").toOption.map(_.as[String]),
    lft = (v \ "lft").toOption.map(_.as[Int]),
    rgt = (v \ "rgt").toOption.map(_.as[Int]),
    old_parent = (v \ "old_parent").toOption.map(_.as[String]),
    include_in_gross = (v \ "include_in_gross").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[Account] = Reads[Account] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Account") => JsSuccess(Account(js))
      case Some(_)         => JsError("Wrong Doctype")
      case _               => JsError("Doctype not Found")
    }
  }

}
