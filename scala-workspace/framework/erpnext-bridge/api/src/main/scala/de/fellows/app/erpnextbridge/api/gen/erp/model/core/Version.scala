package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Version(
    name: String,
    ref_doctype: String,
    docname: String
)

object Version {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Version = new Version(
    name = (v \ "name").get.as[String],
    ref_doctype = (v \ "ref_doctype").get.as[String],
    docname = (v \ "docname").get.as[String]
  )

  implicit val reads: Reads[Version] = Reads[Version] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Version") => JsSuccess(Version(js))
      case Some(_)         => JsError("Wrong Doctype")
      case _               => JsError("Doctype not Found")
    }
  }

}
