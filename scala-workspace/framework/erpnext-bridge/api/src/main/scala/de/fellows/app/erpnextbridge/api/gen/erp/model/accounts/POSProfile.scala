package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class POSProfile(
    name: String,
    disabled: Option[Int],
    naming_series: String,
    customer: Option[String],
    company: String,
    warehouse: Option[String],
    campaign: Option[String],
    company_address: Option[String],
    update_stock: Option[Int],
    ignore_pricing_rule: Option[Int],
    allow_delete: Option[Int],
    allow_user_to_edit_rate: Option[Int],
    allow_user_to_edit_discount: Option[Int],
    allow_print_before_pay: Option[Int],
    display_items_in_stock: Option[Int],
    applicable_for_users: Option[Seq[POSProfileUser]],
    payments: Option[Seq[SalesInvoicePayment]],
    item_groups: Option[Seq[POSItemGroup]],
    customer_groups: Option[Seq[POSCustomerGroup]],
    print_format_for_online: Option[String],
    letter_head: Option[String],
    tc_name: Option[String],
    select_print_heading: Option[String],
    territory: String,
    print_format: Option[String],
    customer_group: String,
    selling_price_list: Option[String],
    currency: String,
    write_off_account: String,
    write_off_cost_center: String,
    account_for_change_amount: Option[String],
    income_account: Option[String],
    expense_account: Option[String],
    taxes_and_charges: Option[String],
    apply_discount_on: Option[String],
    cost_center: Option[String]
)

object POSProfile {
  val NAME_FIELD = "name"

  def apply(v: JsValue): POSProfile = new POSProfile(
    name = (v \ "name").get.as[String],
    disabled = (v \ "disabled").toOption.map(_.as[Int]),
    naming_series = (v \ "naming_series").get.as[String],
    customer = (v \ "customer").toOption.map(_.as[String]),
    company = (v \ "company").get.as[String],
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    campaign = (v \ "campaign").toOption.map(_.as[String]),
    company_address = (v \ "company_address").toOption.map(_.as[String]),
    update_stock = (v \ "update_stock").toOption.map(_.as[Int]),
    ignore_pricing_rule = (v \ "ignore_pricing_rule").toOption.map(_.as[Int]),
    allow_delete = (v \ "allow_delete").toOption.map(_.as[Int]),
    allow_user_to_edit_rate = (v \ "allow_user_to_edit_rate").toOption.map(_.as[Int]),
    allow_user_to_edit_discount = (v \ "allow_user_to_edit_discount").toOption.map(_.as[Int]),
    allow_print_before_pay = (v \ "allow_print_before_pay").toOption.map(_.as[Int]),
    display_items_in_stock = (v \ "display_items_in_stock").toOption.map(_.as[Int]),
    applicable_for_users =
      (v \ "applicable_for_users").toOption.map(x => x.as[JsArray].value.map(_.as[POSProfileUser]).toSeq),
    payments = (v \ "payments").toOption.map(x => x.as[JsArray].value.map(_.as[SalesInvoicePayment]).toSeq),
    item_groups = (v \ "item_groups").toOption.map(x => x.as[JsArray].value.map(_.as[POSItemGroup]).toSeq),
    customer_groups = (v \ "customer_groups").toOption.map(x => x.as[JsArray].value.map(_.as[POSCustomerGroup]).toSeq),
    print_format_for_online = (v \ "print_format_for_online").toOption.map(_.as[String]),
    letter_head = (v \ "letter_head").toOption.map(_.as[String]),
    tc_name = (v \ "tc_name").toOption.map(_.as[String]),
    select_print_heading = (v \ "select_print_heading").toOption.map(_.as[String]),
    territory = (v \ "territory").get.as[String],
    print_format = (v \ "print_format").toOption.map(_.as[String]),
    customer_group = (v \ "customer_group").get.as[String],
    selling_price_list = (v \ "selling_price_list").toOption.map(_.as[String]),
    currency = (v \ "currency").get.as[String],
    write_off_account = (v \ "write_off_account").get.as[String],
    write_off_cost_center = (v \ "write_off_cost_center").get.as[String],
    account_for_change_amount = (v \ "account_for_change_amount").toOption.map(_.as[String]),
    income_account = (v \ "income_account").toOption.map(_.as[String]),
    expense_account = (v \ "expense_account").toOption.map(_.as[String]),
    taxes_and_charges = (v \ "taxes_and_charges").toOption.map(_.as[String]),
    apply_discount_on = (v \ "apply_discount_on").toOption.map(_.as[String]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String])
  )

  implicit val reads: Reads[POSProfile] = Reads[POSProfile] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("POS Profile") => JsSuccess(POSProfile(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
