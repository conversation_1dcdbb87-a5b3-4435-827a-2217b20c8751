package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PackingSlip(
    name: String,
    delivery_note: String,
    naming_series: String,
    from_case_no: Int,
    to_case_no: Option[Int],
    items: Seq[PackingSlipItem],
    net_weight_pkg: Option[Double],
    net_weight_uom: Option[String],
    gross_weight_pkg: Option[Double],
    gross_weight_uom: Option[String],
    letter_head: Option[String],
    amended_from: Option[String]
)

object PackingSlip {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PackingSlip = new PackingSlip(
    name = (v \ "name").get.as[String],
    delivery_note = (v \ "delivery_note").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    from_case_no = (v \ "from_case_no").get.as[Int],
    to_case_no = (v \ "to_case_no").toOption.map(_.as[Int]),
    items = (v \ "items").toOption.map(x => x.as[JsArray].value.map(_.as[PackingSlipItem])).get.toSeq,
    net_weight_pkg = (v \ "net_weight_pkg").toOption.map(_.as[Double]),
    net_weight_uom = (v \ "net_weight_uom").toOption.map(_.as[String]),
    gross_weight_pkg = (v \ "gross_weight_pkg").toOption.map(_.as[Double]),
    gross_weight_uom = (v \ "gross_weight_uom").toOption.map(_.as[String]),
    letter_head = (v \ "letter_head").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PackingSlip] = Reads[PackingSlip] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Packing Slip") => JsSuccess(PackingSlip(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
