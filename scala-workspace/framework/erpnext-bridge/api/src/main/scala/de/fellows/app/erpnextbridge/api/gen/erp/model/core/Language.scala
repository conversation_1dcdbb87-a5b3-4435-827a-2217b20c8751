package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Language(
    language_code: String,
    language_name: String,
    flag: Option[String],
    based_on: Option[String]
)

object Language {
  val NAME_FIELD = "language_code"

  def apply(v: JsValue): Language = new Language(
    language_code = (v \ "language_code").get.as[String],
    language_name = (v \ "language_name").get.as[String],
    flag = (v \ "flag").toOption.map(_.as[String]),
    based_on = (v \ "based_on").toOption.map(_.as[String])
  )

  implicit val reads: Reads[Language] = Reads[Language] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Language") => JsSuccess(Language(js))
      case Some(_)          => JsError("Wrong Doctype")
      case _                => JsError("Doctype not Found")
    }
  }

}
