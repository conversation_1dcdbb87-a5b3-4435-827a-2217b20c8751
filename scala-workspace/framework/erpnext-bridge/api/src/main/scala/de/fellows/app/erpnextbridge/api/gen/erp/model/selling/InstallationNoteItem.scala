package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class InstallationNoteItem(
    name: String,
    item_code: String,
    qty: Double,
    description: Option[String],
    prevdoc_detail_docname: Option[String],
    prevdoc_docname: Option[String],
    prevdoc_doctype: Option[String]
)

object InstallationNoteItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): InstallationNoteItem = new InstallationNoteItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    qty = (v \ "qty").get.as[Double],
    description = (v \ "description").toOption.map(_.as[String]),
    prevdoc_detail_docname = (v \ "prevdoc_detail_docname").toOption.map(_.as[String]),
    prevdoc_docname = (v \ "prevdoc_docname").toOption.map(_.as[String]),
    prevdoc_doctype = (v \ "prevdoc_doctype").toOption.map(_.as[String])
  )

  implicit val reads: Reads[InstallationNoteItem] = Reads[InstallationNoteItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Installation Note Item") => JsSuccess(InstallationNoteItem(js))
      case Some(_)                        => JsError("Wrong Doctype")
      case _                              => JsError("Doctype not Found")
    }
  }

}
