package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemSupplier(
    name: String,
    supplier: Option[String],
    supplier_part_no: Option[String]
)

object ItemSupplier {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ItemSupplier = new ItemSupplier(
    name = (v \ "name").get.as[String],
    supplier = (v \ "supplier").toOption.map(_.as[String]),
    supplier_part_no = (v \ "supplier_part_no").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ItemSupplier] = Reads[ItemSupplier] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Supplier") => JsSuccess(ItemSupplier(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
