package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuccess, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BankStatementTransactionSettingsItem(
    name: String,
    mapping_type: String,
    bank_data: String,
    mapped_data_type: Option[String]
)

object BankStatementTransactionSettingsItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BankStatementTransactionSettingsItem = new BankStatementTransactionSettingsItem(
    name = (v \ "name").get.as[String],
    mapping_type = (v \ "mapping_type").get.as[String],
    bank_data = (v \ "bank_data").get.as[String],
    mapped_data_type = (v \ "mapped_data_type").toOption.map(_.as[String])
  )

  implicit val reads: Reads[BankStatementTransactionSettingsItem] = Reads[BankStatementTransactionSettingsItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bank Statement Transaction Settings Item") => JsSuccess(BankStatementTransactionSettingsItem(js))
      case Some(_)                                          => JsError("Wrong Doctype")
      case _                                                => JsError("Doctype not Found")
    }
  }

}
