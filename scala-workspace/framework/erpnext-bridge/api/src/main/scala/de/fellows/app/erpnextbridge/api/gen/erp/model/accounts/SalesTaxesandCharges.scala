package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SalesTaxesandCharges(
    name: String,
    charge_type: String,
    row_id: Option[String],
    account_head: String,
    included_in_print_rate: Option[Int],
    cost_center: Option[String],
    rate: Option[Double],
    tax_amount: Option[Double],
    total: Option[Double],
    tax_amount_after_discount_amount: Option[Double],
    base_tax_amount: Option[Double],
    base_total: Option[Double],
    base_tax_amount_after_discount_amount: Option[Double],
    parenttype: Option[String]
)

object SalesTaxesandCharges {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SalesTaxesandCharges = new SalesTaxesandCharges(
    name = (v \ "name").get.as[String],
    charge_type = (v \ "charge_type").get.as[String],
    row_id = (v \ "row_id").toOption.map(_.as[String]),
    account_head = (v \ "account_head").get.as[String],
    included_in_print_rate = (v \ "included_in_print_rate").toOption.map(_.as[Int]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    rate = (v \ "rate").toOption.map(_.as[Double]),
    tax_amount = (v \ "tax_amount").toOption.map(_.as[Double]),
    total = (v \ "total").toOption.map(_.as[Double]),
    tax_amount_after_discount_amount = (v \ "tax_amount_after_discount_amount").toOption.map(_.as[Double]),
    base_tax_amount = (v \ "base_tax_amount").toOption.map(_.as[Double]),
    base_total = (v \ "base_total").toOption.map(_.as[Double]),
    base_tax_amount_after_discount_amount = (v \ "base_tax_amount_after_discount_amount").toOption.map(_.as[Double]),
    parenttype = (v \ "parenttype").toOption.map(_.as[String])
  )

  implicit val reads: Reads[SalesTaxesandCharges] = Reads[SalesTaxesandCharges] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Sales Taxes and Charges") => JsSuccess(SalesTaxesandCharges(js))
      case Some(_)                         => JsError("Wrong Doctype")
      case _                               => JsError("Doctype not Found")
    }
  }

}
