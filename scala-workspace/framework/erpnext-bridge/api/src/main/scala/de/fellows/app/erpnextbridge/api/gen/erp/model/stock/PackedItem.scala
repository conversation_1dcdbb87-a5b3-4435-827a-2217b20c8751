package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PackedItem(
    name: String,
    parent_item: Option[String],
    item_code: Option[String],
    item_name: Option[String],
    description: Option[String],
    warehouse: Option[String],
    target_warehouse: Option[String],
    qty: Option[Double],
    serial_no: Option[String],
    batch_no: Option[String],
    actual_batch_qty: Option[Double],
    actual_qty: Option[Double],
    projected_qty: Option[Double],
    uom: Option[String],
    page_break: Option[Int],
    prevdoc_doctype: Option[String],
    parent_detail_docname: Option[String]
)

object PackedItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PackedItem = new PackedItem(
    name = (v \ "name").get.as[String],
    parent_item = (v \ "parent_item").toOption.map(_.as[String]),
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    description = (v \ "description").toOption.map(_.as[String]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    target_warehouse = (v \ "target_warehouse").toOption.map(_.as[String]),
    qty = (v \ "qty").toOption.map(_.as[Double]),
    serial_no = (v \ "serial_no").toOption.map(_.as[String]),
    batch_no = (v \ "batch_no").toOption.map(_.as[String]),
    actual_batch_qty = (v \ "actual_batch_qty").toOption.map(_.as[Double]),
    actual_qty = (v \ "actual_qty").toOption.map(_.as[Double]),
    projected_qty = (v \ "projected_qty").toOption.map(_.as[Double]),
    uom = (v \ "uom").toOption.map(_.as[String]),
    page_break = (v \ "page_break").toOption.map(_.as[Int]),
    prevdoc_doctype = (v \ "prevdoc_doctype").toOption.map(_.as[String]),
    parent_detail_docname = (v \ "parent_detail_docname").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PackedItem] = Reads[PackedItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Packed Item") => JsSuccess(PackedItem(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
