package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class HasRole(
    name: String,
    role: Option[String]
)

object HasRole {
  val NAME_FIELD = "name"

  def apply(v: JsValue): HasRole = new HasRole(
    name = (v \ "name").get.as[String],
    role = (v \ "role").toOption.map(_.as[String])
  )

  implicit val reads: Reads[HasRole] = Reads[HasRole] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Has Role") => JsSuccess(HasRole(js))
      case Some(_)          => JsError("Wrong Doctype")
      case _                => JsError("Doctype not Found")
    }
  }

}
