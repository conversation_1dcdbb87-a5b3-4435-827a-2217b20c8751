package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DocShare(
    name: String,
    user: Option[String],
    share_doctype: String,
    read: Option[Int],
    write: Option[Int],
    share: Option[Int],
    everyone: Option[Int],
    notify_by_email: Option[Int]
)

object DocShare {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DocShare = new DocShare(
    name = (v \ "name").get.as[String],
    user = (v \ "user").toOption.map(_.as[String]),
    share_doctype = (v \ "share_doctype").get.as[String],
    read = (v \ "read").toOption.map(_.as[Int]),
    write = (v \ "write").toOption.map(_.as[Int]),
    share = (v \ "share").toOption.map(_.as[Int]),
    everyone = (v \ "everyone").toOption.map(_.as[Int]),
    notify_by_email = (v \ "notify_by_email").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[DocShare] = Reads[DocShare] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("DocShare") => JsSuccess(DocShare(js))
      case Some(_)          => JsError("Wrong Doctype")
      case _                => JsError("Doctype not Found")
    }
  }

}
