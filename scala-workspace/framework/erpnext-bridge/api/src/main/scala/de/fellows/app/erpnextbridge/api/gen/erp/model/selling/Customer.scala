package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsE<PERSON>r, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Customer(
    name: String,
    naming_series: Option[String],
    salutation: Option[String],
    customer_name: String,
    gender: Option[String],
    customer_type: String,
    default_bank_account: Option[String],
    lead_name: Option[String],
    image: Option[String],
    account_manager: Option[String],
    customer_group: String,
    territory: String,
    tax_id: Option[String],
    tax_category: Option[String],
    disabled: Option[Int],
    is_internal_customer: Option[Int],
    represents_company: Option[String],
    companies: Option[Seq[AllowedToTransactWith]],
    default_currency: Option[String],
    default_price_list: Option[String],
    language: Option[String],
    website: Option[String],
    customer_primary_contact: Option[String],
    customer_primary_address: Option[String],
    accounts: Option[Seq[PartyAccount]],
    payment_terms: Option[String],
    credit_limits: Option[Seq[CustomerCreditLimit]],
    customer_details: Option[String],
    market_segment: Option[String],
    industry: Option[String],
    is_frozen: Option[Int],
    loyalty_program: Option[String],
    loyalty_program_tier: Option[String],
    default_sales_partner: Option[String],
    default_commission_rate: Option[Double],
    sales_team: Option[Seq[SalesTeam]],
    customer_pos_id: Option[String],
    allows_qty_exceedance: Option[Int],
    domain_items: Option[Seq[CustomerDomainItem]],
    identifier: Option[String]
)

object Customer {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Customer = new Customer(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").toOption.map(_.as[String]),
    salutation = (v \ "salutation").toOption.map(_.as[String]),
    customer_name = (v \ "customer_name").get.as[String],
    gender = (v \ "gender").toOption.map(_.as[String]),
    customer_type = (v \ "customer_type").get.as[String],
    default_bank_account = (v \ "default_bank_account").toOption.map(_.as[String]),
    lead_name = (v \ "lead_name").toOption.map(_.as[String]),
    image = (v \ "image").toOption.map(_.as[String]),
    account_manager = (v \ "account_manager").toOption.map(_.as[String]),
    customer_group = (v \ "customer_group").get.as[String],
    territory = (v \ "territory").get.as[String],
    tax_id = (v \ "tax_id").toOption.map(_.as[String]),
    tax_category = (v \ "tax_category").toOption.map(_.as[String]),
    disabled = (v \ "disabled").toOption.map(_.as[Int]),
    is_internal_customer = (v \ "is_internal_customer").toOption.map(_.as[Int]),
    represents_company = (v \ "represents_company").toOption.map(_.as[String]),
    companies = (v \ "companies").toOption.map(x => x.as[JsArray].value.map(_.as[AllowedToTransactWith]).toSeq),
    default_currency = (v \ "default_currency").toOption.map(_.as[String]),
    default_price_list = (v \ "default_price_list").toOption.map(_.as[String]),
    language = (v \ "language").toOption.map(_.as[String]),
    website = (v \ "website").toOption.map(_.as[String]),
    customer_primary_contact = (v \ "customer_primary_contact").toOption.map(_.as[String]),
    customer_primary_address = (v \ "customer_primary_address").toOption.map(_.as[String]),
    accounts = (v \ "accounts").toOption.map(x => x.as[JsArray].value.map(_.as[PartyAccount]).toSeq),
    payment_terms = (v \ "payment_terms").toOption.map(_.as[String]),
    credit_limits = (v \ "credit_limits").toOption.map(x => x.as[JsArray].value.map(_.as[CustomerCreditLimit]).toSeq),
    customer_details = (v \ "customer_details").toOption.map(_.as[String]),
    market_segment = (v \ "market_segment").toOption.map(_.as[String]),
    industry = (v \ "industry").toOption.map(_.as[String]),
    is_frozen = (v \ "is_frozen").toOption.map(_.as[Int]),
    loyalty_program = (v \ "loyalty_program").toOption.map(_.as[String]),
    loyalty_program_tier = (v \ "loyalty_program_tier").toOption.map(_.as[String]),
    default_sales_partner = (v \ "default_sales_partner").toOption.map(_.as[String]),
    default_commission_rate = (v \ "default_commission_rate").toOption.map(_.as[Double]),
    sales_team = (v \ "sales_team").toOption.map(x => x.as[JsArray].value.map(_.as[SalesTeam]).toSeq),
    customer_pos_id = (v \ "customer_pos_id").toOption.map(_.as[String]),
    allows_qty_exceedance = (v \ "allows_qty_exceedance").toOption.map(_.as[Int]),
    domain_items = (v \ "domain_items").toOption.map(x => x.as[JsArray].value.map(_.as[CustomerDomainItem]).toSeq),
    identifier = (v \ "identifier").toOption.map(_.as[String])
  )

  implicit val reads: Reads[Customer] = Reads[Customer] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Customer") => JsSuccess(Customer(js))
      case Some(_)          => JsError("Wrong Doctype")
      case _                => JsError("Doctype not Found")
    }
  }

}
