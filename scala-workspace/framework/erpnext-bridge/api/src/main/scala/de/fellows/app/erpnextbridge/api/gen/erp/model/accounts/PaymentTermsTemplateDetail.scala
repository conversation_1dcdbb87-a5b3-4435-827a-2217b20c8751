package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PaymentTermsTemplateDetail(
    name: String,
    payment_term: Option[String],
    due_date_based_on: String,
    credit_days: Option[Int],
    credit_months: Option[Int],
    mode_of_payment: Option[String]
)

object PaymentTermsTemplateDetail {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PaymentTermsTemplateDetail = new PaymentTermsTemplateDetail(
    name = (v \ "name").get.as[String],
    payment_term = (v \ "payment_term").toOption.map(_.as[String]),
    due_date_based_on = (v \ "due_date_based_on").get.as[String],
    credit_days = (v \ "credit_days").toOption.map(_.as[Int]),
    credit_months = (v \ "credit_months").toOption.map(_.as[Int]),
    mode_of_payment = (v \ "mode_of_payment").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PaymentTermsTemplateDetail] = Reads[PaymentTermsTemplateDetail] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Payment Terms Template Detail") => JsSuccess(PaymentTermsTemplateDetail(js))
      case Some(_)                               => JsError("Wrong Doctype")
      case _                                     => JsError("Doctype not Found")
    }
  }

}
