package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, Js<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Role(
    role_name: String,
    disabled: Option[Int],
    desk_access: Option[Int],
    two_factor_auth: Option[Int],
    restrict_to_domain: Option[String]
)

object Role {
  val NAME_FIELD = "role_name"

  def apply(v: JsValue): Role = new Role(
    role_name = (v \ "role_name").get.as[String],
    disabled = (v \ "disabled").toOption.map(_.as[Int]),
    desk_access = (v \ "desk_access").toOption.map(_.as[Int]),
    two_factor_auth = (v \ "two_factor_auth").toOption.map(_.as[Int]),
    restrict_to_domain = (v \ "restrict_to_domain").toOption.map(_.as[String])
  )

  implicit val reads: Reads[Role] = Reads[Role] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Role") => JsSuccess(Role(js))
      case Some(_)      => JsError("Wrong Doctype")
      case _            => JsError("Doctype not Found")
    }
  }

}
