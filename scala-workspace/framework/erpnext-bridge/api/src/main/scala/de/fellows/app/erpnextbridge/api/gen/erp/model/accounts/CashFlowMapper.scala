package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class CashFlowMapper(
    section_name: String,
    section_header: Option[String],
    section_leader: Option[String],
    section_subtotal: Option[String],
    section_footer: Option[String],
    accounts: Option[Seq[CashFlowMappingTemplateDetails]],
    position: Int
)

object CashFlowMapper {
  val NAME_FIELD = "section_name"

  def apply(v: JsValue): CashFlowMapper = new CashFlowMapper(
    section_name = (v \ "section_name").get.as[String],
    section_header = (v \ "section_header").toOption.map(_.as[String]),
    section_leader = (v \ "section_leader").toOption.map(_.as[String]),
    section_subtotal = (v \ "section_subtotal").toOption.map(_.as[String]),
    section_footer = (v \ "section_footer").toOption.map(_.as[String]),
    accounts = (v \ "accounts").toOption.map(x => x.as[JsArray].value.map(_.as[CashFlowMappingTemplateDetails]).toSeq),
    position = (v \ "position").get.as[Int]
  )

  implicit val reads: Reads[CashFlowMapper] = Reads[CashFlowMapper] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Cash Flow Mapper") => JsSuccess(CashFlowMapper(js))
      case Some(_)                  => JsError("Wrong Doctype")
      case _                        => JsError("Doctype not Found")
    }
  }

}
