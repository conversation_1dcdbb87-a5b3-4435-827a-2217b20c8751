package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class StockSettings(
    name: String,
    item_naming_by: Option[String],
    item_group: Option[String],
    stock_uom: Option[String],
    default_warehouse: Option[String],
    sample_retention_warehouse: Option[String],
    valuation_method: Option[String],
    over_delivery_receipt_allowance: Option[Double],
    action_if_quality_inspection_is_not_submitted: Option[String],
    show_barcode_field: Option[Int],
    clean_description_html: Option[Int],
    auto_insert_price_list_rate_if_missing: Option[Int],
    allow_negative_stock: Option[Int],
    automatically_set_serial_nos_based_on_fifo: Option[Int],
    set_qty_in_transactions_based_on_serial_no_input: Option[Int],
    auto_indent: Option[Int],
    reorder_email_notify: Option[Int],
    stock_frozen_upto: Option[String],
    stock_frozen_upto_days: Option[Int],
    stock_auth_role: Option[String],
    use_naming_series: Option[Int],
    naming_series_prefix: Option[String]
)

object StockSettings {
  val NAME_FIELD = "name"

  def apply(v: JsValue): StockSettings = new StockSettings(
    name = (v \ "name").get.as[String],
    item_naming_by = (v \ "item_naming_by").toOption.map(_.as[String]),
    item_group = (v \ "item_group").toOption.map(_.as[String]),
    stock_uom = (v \ "stock_uom").toOption.map(_.as[String]),
    default_warehouse = (v \ "default_warehouse").toOption.map(_.as[String]),
    sample_retention_warehouse = (v \ "sample_retention_warehouse").toOption.map(_.as[String]),
    valuation_method = (v \ "valuation_method").toOption.map(_.as[String]),
    over_delivery_receipt_allowance = (v \ "over_delivery_receipt_allowance").toOption.map(_.as[Double]),
    action_if_quality_inspection_is_not_submitted =
      (v \ "action_if_quality_inspection_is_not_submitted").toOption.map(_.as[String]),
    show_barcode_field = (v \ "show_barcode_field").toOption.map(_.as[Int]),
    clean_description_html = (v \ "clean_description_html").toOption.map(_.as[Int]),
    auto_insert_price_list_rate_if_missing = (v \ "auto_insert_price_list_rate_if_missing").toOption.map(_.as[Int]),
    allow_negative_stock = (v \ "allow_negative_stock").toOption.map(_.as[Int]),
    automatically_set_serial_nos_based_on_fifo =
      (v \ "automatically_set_serial_nos_based_on_fifo").toOption.map(_.as[Int]),
    set_qty_in_transactions_based_on_serial_no_input =
      (v \ "set_qty_in_transactions_based_on_serial_no_input").toOption.map(_.as[Int]),
    auto_indent = (v \ "auto_indent").toOption.map(_.as[Int]),
    reorder_email_notify = (v \ "reorder_email_notify").toOption.map(_.as[Int]),
    stock_frozen_upto = (v \ "stock_frozen_upto").toOption.map(_.as[String]),
    stock_frozen_upto_days = (v \ "stock_frozen_upto_days").toOption.map(_.as[Int]),
    stock_auth_role = (v \ "stock_auth_role").toOption.map(_.as[String]),
    use_naming_series = (v \ "use_naming_series").toOption.map(_.as[Int]),
    naming_series_prefix = (v \ "naming_series_prefix").toOption.map(_.as[String])
  )

  implicit val reads: Reads[StockSettings] = Reads[StockSettings] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Stock Settings") => JsSuccess(StockSettings(js))
      case Some(_)                => JsError("Wrong Doctype")
      case _                      => JsError("Doctype not Found")
    }
  }

}
