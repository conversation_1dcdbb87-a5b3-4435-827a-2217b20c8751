package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class LoyaltyPointEntry(
    name: String,
    loyalty_program: Option[String],
    loyalty_program_tier: Option[String],
    customer: Option[String],
    sales_invoice: Option[String],
    redeem_against: Option[String],
    loyalty_points: Option[Int],
    purchase_amount: Option[Double],
    expiry_date: Option[String],
    posting_date: Option[String],
    company: Option[String]
)

object LoyaltyPointEntry {
  val NAME_FIELD = "name"

  def apply(v: JsValue): LoyaltyPointEntry = new LoyaltyPointEntry(
    name = (v \ "name").get.as[String],
    loyalty_program = (v \ "loyalty_program").toOption.map(_.as[String]),
    loyalty_program_tier = (v \ "loyalty_program_tier").toOption.map(_.as[String]),
    customer = (v \ "customer").toOption.map(_.as[String]),
    sales_invoice = (v \ "sales_invoice").toOption.map(_.as[String]),
    redeem_against = (v \ "redeem_against").toOption.map(_.as[String]),
    loyalty_points = (v \ "loyalty_points").toOption.map(_.as[Int]),
    purchase_amount = (v \ "purchase_amount").toOption.map(_.as[Double]),
    expiry_date = (v \ "expiry_date").toOption.map(_.as[String]),
    posting_date = (v \ "posting_date").toOption.map(_.as[String]),
    company = (v \ "company").toOption.map(_.as[String])
  )

  implicit val reads: Reads[LoyaltyPointEntry] = Reads[LoyaltyPointEntry] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Loyalty Point Entry") => JsSuccess(LoyaltyPointEntry(js))
      case Some(_)                     => JsError("Wrong Doctype")
      case _                           => JsError("Doctype not Found")
    }
  }

}
