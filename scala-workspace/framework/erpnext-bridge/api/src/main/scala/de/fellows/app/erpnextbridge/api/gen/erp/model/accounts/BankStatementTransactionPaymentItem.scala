package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BankStatementTransactionPaymentItem(
    name: String,
    transaction_date: String,
    description: String,
    amount: Option[Double],
    party_type: Option[String],
    reference_type: Option[String],
    account: Option[String],
    mode_of_payment: Option[String],
    outstanding_amount: Option[Double],
    payment_reference: Option[String],
    invoices: Option[String]
)

object BankStatementTransactionPaymentItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BankStatementTransactionPaymentItem = new BankStatementTransactionPaymentItem(
    name = (v \ "name").get.as[String],
    transaction_date = (v \ "transaction_date").get.as[String],
    description = (v \ "description").get.as[String],
    amount = (v \ "amount").toOption.map(_.as[Double]),
    party_type = (v \ "party_type").toOption.map(_.as[String]),
    reference_type = (v \ "reference_type").toOption.map(_.as[String]),
    account = (v \ "account").toOption.map(_.as[String]),
    mode_of_payment = (v \ "mode_of_payment").toOption.map(_.as[String]),
    outstanding_amount = (v \ "outstanding_amount").toOption.map(_.as[Double]),
    payment_reference = (v \ "payment_reference").toOption.map(_.as[String]),
    invoices = (v \ "invoices").toOption.map(_.as[String])
  )

  implicit val reads: Reads[BankStatementTransactionPaymentItem] = Reads[BankStatementTransactionPaymentItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bank Statement Transaction Payment Item") => JsSuccess(BankStatementTransactionPaymentItem(js))
      case Some(_)                                         => JsError("Wrong Doctype")
      case _                                               => JsError("Doctype not Found")
    }
  }

}
