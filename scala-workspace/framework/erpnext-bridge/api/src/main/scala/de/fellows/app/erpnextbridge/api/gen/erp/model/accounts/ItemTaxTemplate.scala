package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemTaxTemplate(
    title: String,
    taxes: Seq[ItemTaxTemplateDetail]
)

object ItemTaxTemplate {
  val NAME_FIELD = "title"

  def apply(v: JsValue): ItemTaxTemplate = new ItemTaxTemplate(
    title = (v \ "title").get.as[String],
    taxes = (v \ "taxes").toOption.map(x => x.as[JsArray].value.map(_.as[ItemTaxTemplateDetail])).get.toSeq
  )

  implicit val reads: Reads[ItemTaxTemplate] = Reads[ItemTaxTemplate] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Tax Template") => JsSuccess(ItemTaxTemplate(js))
      case Some(_)                   => JsError("Wrong Doctype")
      case _                         => JsError("Doctype not Found")
    }
  }

}
