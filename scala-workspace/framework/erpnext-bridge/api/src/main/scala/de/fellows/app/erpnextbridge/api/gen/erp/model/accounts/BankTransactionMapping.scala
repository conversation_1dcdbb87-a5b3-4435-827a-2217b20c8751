package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BankTransactionMapping(
    name: String,
    bank_transaction_field: String,
    file_field: String
)

object BankTransactionMapping {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BankTransactionMapping = new BankTransactionMapping(
    name = (v \ "name").get.as[String],
    bank_transaction_field = (v \ "bank_transaction_field").get.as[String],
    file_field = (v \ "file_field").get.as[String]
  )

  implicit val reads: Reads[BankTransactionMapping] = Reads[BankTransactionMapping] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bank Transaction Mapping") => JsSuccess(BankTransactionMapping(js))
      case Some(_)                          => JsError("Wrong Doctype")
      case _                                => JsError("Doctype not Found")
    }
  }

}
