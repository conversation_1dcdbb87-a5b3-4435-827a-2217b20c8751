package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ScheduledJobType(
    name: String,
    stopped: Option[Int],
    method: String,
    frequency: String,
    cron_format: Option[String],
    last_execution: Option[String],
    create_log: Option[Int]
)

object ScheduledJobType {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ScheduledJobType = new ScheduledJobType(
    name = (v \ "name").get.as[String],
    stopped = (v \ "stopped").toOption.map(_.as[Int]),
    method = (v \ "method").get.as[String],
    frequency = (v \ "frequency").get.as[String],
    cron_format = (v \ "cron_format").toOption.map(_.as[String]),
    last_execution = (v \ "last_execution").toOption.map(_.as[String]),
    create_log = (v \ "create_log").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[ScheduledJobType] = Reads[ScheduledJobType] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Scheduled Job Type") => JsSuccess(ScheduledJobType(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
