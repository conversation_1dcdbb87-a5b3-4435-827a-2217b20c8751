package de.fellows.app.erpnextbridge.api.gen.erp.model.contacts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Gender(
    gender: Option[String]
)

object Gender {
  val NAME_FIELD = "gender"

  def apply(v: JsValue): Gender = new Gender(
    gender = (v \ "gender").toOption.map(_.as[String])
  )

  implicit val reads: Reads[Gender] = Reads[Gender] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Gender") => JsSuccess(Gender(js))
      case Some(_)        => JsError("Wrong Doctype")
      case _              => JsError("Doctype not Found")
    }
  }

}
