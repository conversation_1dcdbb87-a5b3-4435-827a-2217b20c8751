package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ModeofPayment(
    mode_of_payment: String,
    enabled: Option[Int],
    `type`: Option[String],
    accounts: Option[Seq[ModeofPaymentAccount]]
)

object ModeofPayment {
  val NAME_FIELD = "mode_of_payment"

  def apply(v: JsValue): ModeofPayment = new ModeofPayment(
    mode_of_payment = (v \ "mode_of_payment").get.as[String],
    enabled = (v \ "enabled").toOption.map(_.as[Int]),
    `type` = (v \ "type").toOption.map(_.as[String]),
    accounts = (v \ "accounts").toOption.map(x => x.as[JsArray].value.map(_.as[ModeofPaymentAccount]).toSeq)
  )

  implicit val reads: Reads[ModeofPayment] = Reads[ModeofPayment] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Mode of Payment") => JsSuccess(ModeofPayment(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
