package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON>s<PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ProductBundleItem(
    name: String,
    item_code: String,
    qty: Double,
    description: Option[String],
    rate: Option[Double],
    uom: Option[String]
)

object ProductBundleItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ProductBundleItem = new ProductBundleItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    qty = (v \ "qty").get.as[Double],
    description = (v \ "description").toOption.map(_.as[String]),
    rate = (v \ "rate").toOption.map(_.as[Double]),
    uom = (v \ "uom").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ProductBundleItem] = Reads[ProductBundleItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Product Bundle Item") => JsSuccess(ProductBundleItem(js))
      case Some(_)                     => JsError("Wrong Doctype")
      case _                           => JsError("Doctype not Found")
    }
  }

}
