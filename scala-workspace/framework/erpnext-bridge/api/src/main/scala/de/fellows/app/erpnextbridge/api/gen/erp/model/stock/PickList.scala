package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PickList(
    name: String,
    naming_series: String,
    company: String,
    purpose: Option[String],
    customer: Option[String],
    work_order: Option[String],
    material_request: Option[String],
    for_qty: Option[Double],
    parent_warehouse: Option[String],
    locations: Option[Seq[PickListItem]],
    amended_from: Option[String]
)

object PickList {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PickList = new PickList(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    company = (v \ "company").get.as[String],
    purpose = (v \ "purpose").toOption.map(_.as[String]),
    customer = (v \ "customer").toOption.map(_.as[String]),
    work_order = (v \ "work_order").toOption.map(_.as[String]),
    material_request = (v \ "material_request").toOption.map(_.as[String]),
    for_qty = (v \ "for_qty").toOption.map(_.as[Double]),
    parent_warehouse = (v \ "parent_warehouse").toOption.map(_.as[String]),
    locations = (v \ "locations").toOption.map(x => x.as[JsArray].value.map(_.as[PickListItem]).toSeq),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PickList] = Reads[PickList] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Pick List") => JsSuccess(PickList(js))
      case Some(_)           => JsError("Wrong Doctype")
      case _                 => JsError("Doctype not Found")
    }
  }

}
