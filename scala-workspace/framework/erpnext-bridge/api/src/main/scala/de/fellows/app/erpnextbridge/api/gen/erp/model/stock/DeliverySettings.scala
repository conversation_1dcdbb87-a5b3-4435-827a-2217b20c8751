package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DeliverySettings(
    name: String,
    dispatch_template: Option[String],
    dispatch_attachment: Option[String],
    send_with_attachment: Option[Int],
    stop_delay: Option[Int]
)

object DeliverySettings {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DeliverySettings = new DeliverySettings(
    name = (v \ "name").get.as[String],
    dispatch_template = (v \ "dispatch_template").toOption.map(_.as[String]),
    dispatch_attachment = (v \ "dispatch_attachment").toOption.map(_.as[String]),
    send_with_attachment = (v \ "send_with_attachment").toOption.map(_.as[Int]),
    stop_delay = (v \ "stop_delay").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[DeliverySettings] = Reads[DeliverySettings] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Delivery Settings") => JsSuccess(DeliverySettings(js))
      case Some(_)                   => JsError("Wrong Doctype")
      case _                         => JsError("Doctype not Found")
    }
  }

}
