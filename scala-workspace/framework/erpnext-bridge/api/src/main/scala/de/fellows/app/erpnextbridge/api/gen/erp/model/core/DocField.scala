package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsE<PERSON>r, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DocField(
    name: String,
    label: Option[String],
    fieldtype: String,
    fieldname: Option[String],
    reqd: Option[Int],
    precision: Option[String],
    length: Option[Int],
    search_index: Option[Int],
    in_list_view: Option[Int],
    in_standard_filter: Option[Int],
    in_global_search: Option[Int],
    in_preview: Option[Int],
    allow_in_quick_entry: Option[Int],
    bold: Option[Int],
    translatable: Option[Int],
    collapsible: Option[Int],
    fetch_if_empty: Option[Int],
    hidden: Option[Int],
    read_only: Option[Int],
    unique: Option[Int],
    set_only_once: Option[Int],
    allow_bulk_edit: Option[Int],
    permlevel: Option[Int],
    ignore_user_permissions: Option[Int],
    allow_on_submit: Option[Int],
    report_hide: Option[Int],
    remember_last_selected_value: Option[Int],
    ignore_xss_filter: Option[Int],
    in_filter: Option[Int],
    no_copy: Option[Int],
    print_hide: Option[Int],
    print_hide_if_no_value: Option[Int],
    print_width: Option[String],
    width: Option[String],
    columns: Option[Int],
    oldfieldname: Option[String],
    oldfieldtype: Option[String]
)

object DocField {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DocField = new DocField(
    name = (v \ "name").get.as[String],
    label = (v \ "label").toOption.map(_.as[String]),
    fieldtype = (v \ "fieldtype").get.as[String],
    fieldname = (v \ "fieldname").toOption.map(_.as[String]),
    reqd = (v \ "reqd").toOption.map(_.as[Int]),
    precision = (v \ "precision").toOption.map(_.as[String]),
    length = (v \ "length").toOption.map(_.as[Int]),
    search_index = (v \ "search_index").toOption.map(_.as[Int]),
    in_list_view = (v \ "in_list_view").toOption.map(_.as[Int]),
    in_standard_filter = (v \ "in_standard_filter").toOption.map(_.as[Int]),
    in_global_search = (v \ "in_global_search").toOption.map(_.as[Int]),
    in_preview = (v \ "in_preview").toOption.map(_.as[Int]),
    allow_in_quick_entry = (v \ "allow_in_quick_entry").toOption.map(_.as[Int]),
    bold = (v \ "bold").toOption.map(_.as[Int]),
    translatable = (v \ "translatable").toOption.map(_.as[Int]),
    collapsible = (v \ "collapsible").toOption.map(_.as[Int]),
    fetch_if_empty = (v \ "fetch_if_empty").toOption.map(_.as[Int]),
    hidden = (v \ "hidden").toOption.map(_.as[Int]),
    read_only = (v \ "read_only").toOption.map(_.as[Int]),
    unique = (v \ "unique").toOption.map(_.as[Int]),
    set_only_once = (v \ "set_only_once").toOption.map(_.as[Int]),
    allow_bulk_edit = (v \ "allow_bulk_edit").toOption.map(_.as[Int]),
    permlevel = (v \ "permlevel").toOption.map(_.as[Int]),
    ignore_user_permissions = (v \ "ignore_user_permissions").toOption.map(_.as[Int]),
    allow_on_submit = (v \ "allow_on_submit").toOption.map(_.as[Int]),
    report_hide = (v \ "report_hide").toOption.map(_.as[Int]),
    remember_last_selected_value = (v \ "remember_last_selected_value").toOption.map(_.as[Int]),
    ignore_xss_filter = (v \ "ignore_xss_filter").toOption.map(_.as[Int]),
    in_filter = (v \ "in_filter").toOption.map(_.as[Int]),
    no_copy = (v \ "no_copy").toOption.map(_.as[Int]),
    print_hide = (v \ "print_hide").toOption.map(_.as[Int]),
    print_hide_if_no_value = (v \ "print_hide_if_no_value").toOption.map(_.as[Int]),
    print_width = (v \ "print_width").toOption.map(_.as[String]),
    width = (v \ "width").toOption.map(_.as[String]),
    columns = (v \ "columns").toOption.map(_.as[Int]),
    oldfieldname = (v \ "oldfieldname").toOption.map(_.as[String]),
    oldfieldtype = (v \ "oldfieldtype").toOption.map(_.as[String])
  )

  implicit val reads: Reads[DocField] = Reads[DocField] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("DocField") => JsSuccess(DocField(js))
      case Some(_)          => JsError("Wrong Doctype")
      case _                => JsError("Doctype not Found")
    }
  }

}
