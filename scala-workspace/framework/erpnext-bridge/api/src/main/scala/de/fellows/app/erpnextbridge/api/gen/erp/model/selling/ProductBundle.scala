package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ProductBundle(
    name: String,
    new_item_code: String,
    description: Option[String],
    items: Seq[ProductBundleItem]
)

object ProductBundle {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ProductBundle = new ProductBundle(
    name = (v \ "name").get.as[String],
    new_item_code = (v \ "new_item_code").get.as[String],
    description = (v \ "description").toOption.map(_.as[String]),
    items = (v \ "items").toOption.map(x => x.as[JsArray].value.map(_.as[ProductBundleItem])).get.toSeq
  )

  implicit val reads: Reads[ProductBundle] = Reads[ProductBundle] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Product Bundle") => JsSuccess(ProductBundle(js))
      case Some(_)                => JsError("Wrong Doctype")
      case _                      => JsError("Doctype not Found")
    }
  }

}
