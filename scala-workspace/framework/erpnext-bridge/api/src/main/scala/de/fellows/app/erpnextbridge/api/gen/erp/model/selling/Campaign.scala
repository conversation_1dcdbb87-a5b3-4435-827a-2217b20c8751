package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Campaign(
    name: String,
    campaign_name: String,
    naming_series: Option[String],
    description: Option[String]
)

object Campaign {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Campaign = new Campaign(
    name = (v \ "name").get.as[String],
    campaign_name = (v \ "campaign_name").get.as[String],
    naming_series = (v \ "naming_series").toOption.map(_.as[String]),
    description = (v \ "description").toOption.map(_.as[String])
  )

  implicit val reads: Reads[Campaign] = Reads[Campaign] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Campaign") => JsSuccess(Campaign(js))
      case Some(_)          => JsError("Wrong Doctype")
      case _                => JsError("Doctype not Found")
    }
  }

}
