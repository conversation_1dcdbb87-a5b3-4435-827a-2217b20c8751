package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class CashierClosing(
    name: String,
    naming_series: Option[String],
    user: String,
    date: Option[String],
    expense: Option[Double],
    custody: Option[Double],
    returns: Option[Double],
    outstanding_amount: Option[Double],
    payments: Option[Seq[CashierClosingPayments]],
    net_amount: Option[Double],
    amended_from: Option[String]
)

object CashierClosing {
  val NAME_FIELD = "name"

  def apply(v: JsValue): CashierClosing = new CashierClosing(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").toOption.map(_.as[String]),
    user = (v \ "user").get.as[String],
    date = (v \ "date").toOption.map(_.as[String]),
    expense = (v \ "expense").toOption.map(_.as[Double]),
    custody = (v \ "custody").toOption.map(_.as[Double]),
    returns = (v \ "returns").toOption.map(_.as[Double]),
    outstanding_amount = (v \ "outstanding_amount").toOption.map(_.as[Double]),
    payments = (v \ "payments").toOption.map(x => x.as[JsArray].value.map(_.as[CashierClosingPayments]).toSeq),
    net_amount = (v \ "net_amount").toOption.map(_.as[Double]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[CashierClosing] = Reads[CashierClosing] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Cashier Closing") => JsSuccess(CashierClosing(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
