package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DeliveryTrip(
    name: String,
    naming_series: Option[String],
    company: String,
    email_notification_sent: Option[Int],
    driver: String,
    driver_name: Option[String],
    driver_email: Option[String],
    driver_address: Option[String],
    total_distance: Option[Double],
    uom: Option[String],
    vehicle: String,
    departure_time: String,
    delivery_stops: Seq[DeliveryStop],
    status: Option[String],
    amended_from: Option[String]
)

object DeliveryTrip {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DeliveryTrip = new DeliveryTrip(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").toOption.map(_.as[String]),
    company = (v \ "company").get.as[String],
    email_notification_sent = (v \ "email_notification_sent").toOption.map(_.as[Int]),
    driver = (v \ "driver").get.as[String],
    driver_name = (v \ "driver_name").toOption.map(_.as[String]),
    driver_email = (v \ "driver_email").toOption.map(_.as[String]),
    driver_address = (v \ "driver_address").toOption.map(_.as[String]),
    total_distance = (v \ "total_distance").toOption.map(_.as[Double]),
    uom = (v \ "uom").toOption.map(_.as[String]),
    vehicle = (v \ "vehicle").get.as[String],
    departure_time = (v \ "departure_time").get.as[String],
    delivery_stops = (v \ "delivery_stops").toOption.map(x => x.as[JsArray].value.map(_.as[DeliveryStop])).get.toSeq,
    status = (v \ "status").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[DeliveryTrip] = Reads[DeliveryTrip] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Delivery Trip") => JsSuccess(DeliveryTrip(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
