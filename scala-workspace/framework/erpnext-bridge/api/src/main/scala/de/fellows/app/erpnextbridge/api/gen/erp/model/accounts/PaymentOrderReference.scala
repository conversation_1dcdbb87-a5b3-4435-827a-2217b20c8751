package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PaymentOrderReference(
    name: String,
    reference_doctype: String,
    amount: Double,
    supplier: Option[String],
    payment_request: Option[String],
    payment_entry: Option[String],
    mode_of_payment: Option[String],
    bank_account: String,
    account: Option[String],
    payment_reference: Option[String]
)

object PaymentOrderReference {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PaymentOrderReference = new PaymentOrderReference(
    name = (v \ "name").get.as[String],
    reference_doctype = (v \ "reference_doctype").get.as[String],
    amount = (v \ "amount").get.as[Double],
    supplier = (v \ "supplier").toOption.map(_.as[String]),
    payment_request = (v \ "payment_request").toOption.map(_.as[String]),
    payment_entry = (v \ "payment_entry").toOption.map(_.as[String]),
    mode_of_payment = (v \ "mode_of_payment").toOption.map(_.as[String]),
    bank_account = (v \ "bank_account").get.as[String],
    account = (v \ "account").toOption.map(_.as[String]),
    payment_reference = (v \ "payment_reference").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PaymentOrderReference] = Reads[PaymentOrderReference] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Payment Order Reference") => JsSuccess(PaymentOrderReference(js))
      case Some(_)                         => JsError("Wrong Doctype")
      case _                               => JsError("Doctype not Found")
    }
  }

}
