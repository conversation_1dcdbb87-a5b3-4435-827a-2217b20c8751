package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class CustomsTariffNumber(
    tariff_number: String,
    description: String
)

object CustomsTariffNumber {
  val NAME_FIELD = "tariff_number"

  def apply(v: JsValue): CustomsTariffNumber = new CustomsTariffNumber(
    tariff_number = (v \ "tariff_number").get.as[String],
    description = (v \ "description").get.as[String]
  )

  implicit val reads: Reads[CustomsTariffNumber] = Reads[CustomsTariffNumber] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Customs Tariff Number") => JsSuccess(CustomsTariffNumber(js))
      case Some(_)                       => JsError("Wrong Doctype")
      case _                             => JsError("Doctype not Found")
    }
  }

}
