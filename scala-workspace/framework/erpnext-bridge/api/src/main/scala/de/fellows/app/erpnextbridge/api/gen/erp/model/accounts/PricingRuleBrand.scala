package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PricingRuleBrand(
    name: String,
    brand: Option[String],
    uom: Option[String]
)

object PricingRuleBrand {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PricingRuleBrand = new PricingRuleBrand(
    name = (v \ "name").get.as[String],
    brand = (v \ "brand").toOption.map(_.as[String]),
    uom = (v \ "uom").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PricingRuleBrand] = Reads[PricingRuleBrand] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Pricing Rule Brand") => JsSuccess(PricingRuleBrand(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
