package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class LandedCostVoucher(
    name: String,
    naming_series: String,
    company: String,
    purchase_receipts: Seq[LandedCostPurchaseReceipt],
    items: Seq[LandedCostItem],
    taxes: Seq[LandedCostTaxesandCharges],
    total_taxes_and_charges: Double,
    distribute_charges_based_on: String,
    amended_from: Option[String]
)

object LandedCostVoucher {
  val NAME_FIELD = "name"

  def apply(v: JsValue): LandedCostVoucher = new LandedCostVoucher(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    company = (v \ "company").get.as[String],
    purchase_receipts =
      (v \ "purchase_receipts").toOption.map(x => x.as[JsArray].value.map(_.as[LandedCostPurchaseReceipt])).get.toSeq,
    items = (v \ "items").toOption.map(x => x.as[JsArray].value.map(_.as[LandedCostItem])).get.toSeq,
    taxes = (v \ "taxes").toOption.map(x => x.as[JsArray].value.map(_.as[LandedCostTaxesandCharges])).get.toSeq,
    total_taxes_and_charges = (v \ "total_taxes_and_charges").get.as[Double],
    distribute_charges_based_on = (v \ "distribute_charges_based_on").get.as[String],
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[LandedCostVoucher] = Reads[LandedCostVoucher] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Landed Cost Voucher") => JsSuccess(LandedCostVoucher(js))
      case Some(_)                     => JsError("Wrong Doctype")
      case _                           => JsError("Doctype not Found")
    }
  }

}
