package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PricingRuleItemCode(
    name: String,
    item_code: Option[String],
    uom: Option[String]
)

object PricingRuleItemCode {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PricingRuleItemCode = new PricingRuleItemCode(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    uom = (v \ "uom").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PricingRuleItemCode] = Reads[PricingRuleItemCode] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Pricing Rule Item Code") => JsSuccess(PricingRuleItemCode(js))
      case Some(_)                        => JsError("Wrong Doctype")
      case _                              => JsError("Doctype not Found")
    }
  }

}
