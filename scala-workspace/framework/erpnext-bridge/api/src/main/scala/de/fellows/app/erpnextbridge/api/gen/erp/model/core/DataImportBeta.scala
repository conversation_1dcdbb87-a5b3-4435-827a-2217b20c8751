package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DataImportBeta(
    name: String,
    reference_doctype: String,
    import_type: String,
    status: Option[String],
    submit_after_import: Option[Int],
    mute_emails: Option[Int],
    show_failed_logs: Option[Int]
)

object DataImportBeta {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DataImportBeta = new DataImportBeta(
    name = (v \ "name").get.as[String],
    reference_doctype = (v \ "reference_doctype").get.as[String],
    import_type = (v \ "import_type").get.as[String],
    status = (v \ "status").toOption.map(_.as[String]),
    submit_after_import = (v \ "submit_after_import").toOption.map(_.as[Int]),
    mute_emails = (v \ "mute_emails").toOption.map(_.as[Int]),
    show_failed_logs = (v \ "show_failed_logs").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[DataImportBeta] = Reads[DataImportBeta] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Data Import Beta") => JsSuccess(DataImportBeta(js))
      case Some(_)                  => JsError("Wrong Doctype")
      case _                        => JsError("Doctype not Found")
    }
  }

}
