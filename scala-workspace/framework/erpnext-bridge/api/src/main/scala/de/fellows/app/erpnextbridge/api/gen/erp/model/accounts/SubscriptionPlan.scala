package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SubscriptionPlan(
    plan_name: String,
    currency: Option[String],
    item: String,
    price_determination: String,
    cost: Option[Double],
    price_list: Option[String],
    billing_interval: String,
    billing_interval_count: Int,
    payment_plan_id: Option[String],
    payment_gateway: Option[String]
)

object SubscriptionPlan {
  val NAME_FIELD = "plan_name"

  def apply(v: JsValue): SubscriptionPlan = new SubscriptionPlan(
    plan_name = (v \ "plan_name").get.as[String],
    currency = (v \ "currency").toOption.map(_.as[String]),
    item = (v \ "item").get.as[String],
    price_determination = (v \ "price_determination").get.as[String],
    cost = (v \ "cost").toOption.map(_.as[Double]),
    price_list = (v \ "price_list").toOption.map(_.as[String]),
    billing_interval = (v \ "billing_interval").get.as[String],
    billing_interval_count = (v \ "billing_interval_count").get.as[Int],
    payment_plan_id = (v \ "payment_plan_id").toOption.map(_.as[String]),
    payment_gateway = (v \ "payment_gateway").toOption.map(_.as[String])
  )

  implicit val reads: Reads[SubscriptionPlan] = Reads[SubscriptionPlan] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Subscription Plan") => JsSuccess(SubscriptionPlan(js))
      case Some(_)                   => JsError("Wrong Doctype")
      case _                         => JsError("Doctype not Found")
    }
  }

}
