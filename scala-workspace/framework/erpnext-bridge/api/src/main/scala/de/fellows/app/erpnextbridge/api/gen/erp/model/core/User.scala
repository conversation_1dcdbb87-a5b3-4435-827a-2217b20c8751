package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class User(
    name: String,
    enabled: Option[Int],
    email: String,
    first_name: String,
    middle_name: Option[String],
    last_name: Option[String],
    full_name: Option[String],
    send_welcome_email: Option[Int],
    unsubscribed: Option[Int],
    username: Option[String],
    language: Option[String],
    time_zone: Option[String],
    user_image: Option[String],
    role_profile_name: Option[String],
    roles: Option[Seq[HasRole]],
    gender: Option[String],
    phone: Option[String],
    mobile_no: Option[String],
    birth_date: Option[String],
    location: Option[String],
    banner_image: Option[String],
    mute_sounds: Option[Int],
    logout_all_sessions: Option[Int],
    reset_password_key: Option[String],
    last_password_reset_date: Option[String],
    document_follow_notify: Option[Int],
    document_follow_frequency: Option[String],
    thread_notify: Option[Int],
    send_me_a_copy: Option[Int],
    allowed_in_mentions: Option[Int],
    user_emails: Option[Seq[UserEmail]],
    block_modules: Option[Seq[BlockModule]],
    defaults: Option[Seq[DefaultValue]],
    simultaneous_sessions: Option[Int],
    user_type: Option[String],
    login_after: Option[Int],
    login_before: Option[Int],
    restrict_ip: Option[String],
    bypass_restrict_ip_check_if_2fa_enabled: Option[Int],
    last_active: Option[String],
    last_known_versions: Option[String],
    social_logins: Option[Seq[UserSocialLogin]],
    api_key: Option[String]
)

object User {
  val NAME_FIELD = "name"

  def apply(v: JsValue): User = new User(
    name = (v \ "name").get.as[String],
    enabled = (v \ "enabled").toOption.map(_.as[Int]),
    email = (v \ "email").get.as[String],
    first_name = (v \ "first_name").get.as[String],
    middle_name = (v \ "middle_name").toOption.map(_.as[String]),
    last_name = (v \ "last_name").toOption.map(_.as[String]),
    full_name = (v \ "full_name").toOption.map(_.as[String]),
    send_welcome_email = (v \ "send_welcome_email").toOption.map(_.as[Int]),
    unsubscribed = (v \ "unsubscribed").toOption.map(_.as[Int]),
    username = (v \ "username").toOption.map(_.as[String]),
    language = (v \ "language").toOption.map(_.as[String]),
    time_zone = (v \ "time_zone").toOption.map(_.as[String]),
    user_image = (v \ "user_image").toOption.map(_.as[String]),
    role_profile_name = (v \ "role_profile_name").toOption.map(_.as[String]),
    roles = (v \ "roles").toOption.map(x => x.as[JsArray].value.map(_.as[HasRole]).toSeq),
    gender = (v \ "gender").toOption.map(_.as[String]),
    phone = (v \ "phone").toOption.map(_.as[String]),
    mobile_no = (v \ "mobile_no").toOption.map(_.as[String]),
    birth_date = (v \ "birth_date").toOption.map(_.as[String]),
    location = (v \ "location").toOption.map(_.as[String]),
    banner_image = (v \ "banner_image").toOption.map(_.as[String]),
    mute_sounds = (v \ "mute_sounds").toOption.map(_.as[Int]),
    logout_all_sessions = (v \ "logout_all_sessions").toOption.map(_.as[Int]),
    reset_password_key = (v \ "reset_password_key").toOption.map(_.as[String]),
    last_password_reset_date = (v \ "last_password_reset_date").toOption.map(_.as[String]),
    document_follow_notify = (v \ "document_follow_notify").toOption.map(_.as[Int]),
    document_follow_frequency = (v \ "document_follow_frequency").toOption.map(_.as[String]),
    thread_notify = (v \ "thread_notify").toOption.map(_.as[Int]),
    send_me_a_copy = (v \ "send_me_a_copy").toOption.map(_.as[Int]),
    allowed_in_mentions = (v \ "allowed_in_mentions").toOption.map(_.as[Int]),
    user_emails = (v \ "user_emails").toOption.map(x => x.as[JsArray].value.map(_.as[UserEmail]).toSeq),
    block_modules = (v \ "block_modules").toOption.map(x => x.as[JsArray].value.map(_.as[BlockModule]).toSeq),
    defaults = (v \ "defaults").toOption.map(x => x.as[JsArray].value.map(_.as[DefaultValue]).toSeq),
    simultaneous_sessions = (v \ "simultaneous_sessions").toOption.map(_.as[Int]),
    user_type = (v \ "user_type").toOption.map(_.as[String]),
    login_after = (v \ "login_after").toOption.map(_.as[Int]),
    login_before = (v \ "login_before").toOption.map(_.as[Int]),
    restrict_ip = (v \ "restrict_ip").toOption.map(_.as[String]),
    bypass_restrict_ip_check_if_2fa_enabled = (v \ "bypass_restrict_ip_check_if_2fa_enabled").toOption.map(_.as[Int]),
    last_active = (v \ "last_active").toOption.map(_.as[String]),
    last_known_versions = (v \ "last_known_versions").toOption.map(_.as[String]),
    social_logins = (v \ "social_logins").toOption.map(x => x.as[JsArray].value.map(_.as[UserSocialLogin]).toSeq),
    api_key = (v \ "api_key").toOption.map(_.as[String])
  )

  implicit val reads: Reads[User] = Reads[User] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("User") => JsSuccess(User(js))
      case Some(_)      => JsError("Wrong Doctype")
      case _            => JsError("Doctype not Found")
    }
  }

}
