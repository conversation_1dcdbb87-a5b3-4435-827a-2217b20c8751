package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SMSParameter(
    name: String,
    parameter: String,
    value: String,
    header: Option[Int]
)

object SMSParameter {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SMSParameter = new SMSParameter(
    name = (v \ "name").get.as[String],
    parameter = (v \ "parameter").get.as[String],
    value = (v \ "value").get.as[String],
    header = (v \ "header").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[SMSParameter] = Reads[SMSParameter] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("SMS Parameter") => JsSuccess(SMSParameter(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
