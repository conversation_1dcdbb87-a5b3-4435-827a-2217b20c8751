package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class WorkOrderItem(
    name: String,
    operation: Option[String],
    item_code: Option[String],
    source_warehouse: Option[String],
    item_name: Option[String],
    description: Option[String],
    required_qty: Option[Double],
    transferred_qty: Option[Double],
    allow_alternative_item: Option[Int],
    include_item_in_manufacturing: Option[Int],
    consumed_qty: Option[Double],
    available_qty_at_source_warehouse: Option[Double],
    available_qty_at_wip_warehouse: Option[Double]
)

object WorkOrderItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): WorkOrderItem = new WorkOrderItem(
    name = (v \ "name").get.as[String],
    operation = (v \ "operation").toOption.map(_.as[String]),
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    source_warehouse = (v \ "source_warehouse").toOption.map(_.as[String]),
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    description = (v \ "description").toOption.map(_.as[String]),
    required_qty = (v \ "required_qty").toOption.map(_.as[Double]),
    transferred_qty = (v \ "transferred_qty").toOption.map(_.as[Double]),
    allow_alternative_item = (v \ "allow_alternative_item").toOption.map(_.as[Int]),
    include_item_in_manufacturing = (v \ "include_item_in_manufacturing").toOption.map(_.as[Int]),
    consumed_qty = (v \ "consumed_qty").toOption.map(_.as[Double]),
    available_qty_at_source_warehouse = (v \ "available_qty_at_source_warehouse").toOption.map(_.as[Double]),
    available_qty_at_wip_warehouse = (v \ "available_qty_at_wip_warehouse").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[WorkOrderItem] = Reads[WorkOrderItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Work Order Item") => JsSuccess(WorkOrderItem(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
