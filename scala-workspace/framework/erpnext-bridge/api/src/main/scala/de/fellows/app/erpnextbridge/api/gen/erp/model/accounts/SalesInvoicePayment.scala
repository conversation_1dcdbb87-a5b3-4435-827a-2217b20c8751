package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SalesInvoicePayment(
    name: String,
    default: Option[Int],
    mode_of_payment: String,
    amount: Double,
    account: Option[String],
    base_amount: Option[Double],
    clearance_date: Option[String]
)

object SalesInvoicePayment {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SalesInvoicePayment = new SalesInvoicePayment(
    name = (v \ "name").get.as[String],
    default = (v \ "default").toOption.map(_.as[Int]),
    mode_of_payment = (v \ "mode_of_payment").get.as[String],
    amount = (v \ "amount").get.as[Double],
    account = (v \ "account").toOption.map(_.as[String]),
    base_amount = (v \ "base_amount").toOption.map(_.as[Double]),
    clearance_date = (v \ "clearance_date").toOption.map(_.as[String])
  )

  implicit val reads: Reads[SalesInvoicePayment] = Reads[SalesInvoicePayment] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Sales Invoice Payment") => JsSuccess(SalesInvoicePayment(js))
      case Some(_)                       => JsError("Wrong Doctype")
      case _                             => JsError("Doctype not Found")
    }
  }

}
