package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DeliveryNoteItem(
    name: String,
    barcode: Option[String],
    item_code: String,
    item_name: String,
    customer_item_code: Option[String],
    description: String,
    brand: Option[String],
    item_group: Option[String],
    qty: Double,
    stock_uom: String,
    uom: String,
    conversion_factor: Double,
    stock_qty: Option[Double],
    price_list_rate: Option[Double],
    base_price_list_rate: Option[Double],
    margin_type: Option[String],
    margin_rate_or_amount: Option[Double],
    rate_with_margin: Option[Double],
    discount_percentage: Option[Double],
    discount_amount: Option[Double],
    base_rate_with_margin: Option[Double],
    rate: Option[Double],
    amount: Option[Double],
    base_rate: Option[Double],
    base_amount: Option[Double],
    is_free_item: Option[Int],
    net_rate: Option[Double],
    net_amount: Option[Double],
    item_tax_template: Option[String],
    base_net_rate: Option[Double],
    base_net_amount: Option[Double],
    billed_amt: Option[Double],
    weight_per_unit: Option[Double],
    total_weight: Option[Double],
    weight_uom: Option[String],
    warehouse: Option[String],
    target_warehouse: Option[String],
    quality_inspection: Option[String],
    against_sales_order: Option[String],
    so_detail: Option[String],
    against_sales_invoice: Option[String],
    si_detail: Option[String],
    batch_no: Option[String],
    serial_no: Option[String],
    actual_batch_qty: Option[Double],
    actual_qty: Option[Double],
    installed_qty: Option[Double],
    expense_account: Option[String],
    allow_zero_valuation_rate: Option[Int],
    cost_center: Option[String],
    page_break: Option[Int]
)

object DeliveryNoteItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DeliveryNoteItem = new DeliveryNoteItem(
    name = (v \ "name").get.as[String],
    barcode = (v \ "barcode").toOption.map(_.as[String]),
    item_code = (v \ "item_code").get.as[String],
    item_name = (v \ "item_name").get.as[String],
    customer_item_code = (v \ "customer_item_code").toOption.map(_.as[String]),
    description = (v \ "description").get.as[String],
    brand = (v \ "brand").toOption.map(_.as[String]),
    item_group = (v \ "item_group").toOption.map(_.as[String]),
    qty = (v \ "qty").get.as[Double],
    stock_uom = (v \ "stock_uom").get.as[String],
    uom = (v \ "uom").get.as[String],
    conversion_factor = (v \ "conversion_factor").get.as[Double],
    stock_qty = (v \ "stock_qty").toOption.map(_.as[Double]),
    price_list_rate = (v \ "price_list_rate").toOption.map(_.as[Double]),
    base_price_list_rate = (v \ "base_price_list_rate").toOption.map(_.as[Double]),
    margin_type = (v \ "margin_type").toOption.map(_.as[String]),
    margin_rate_or_amount = (v \ "margin_rate_or_amount").toOption.map(_.as[Double]),
    rate_with_margin = (v \ "rate_with_margin").toOption.map(_.as[Double]),
    discount_percentage = (v \ "discount_percentage").toOption.map(_.as[Double]),
    discount_amount = (v \ "discount_amount").toOption.map(_.as[Double]),
    base_rate_with_margin = (v \ "base_rate_with_margin").toOption.map(_.as[Double]),
    rate = (v \ "rate").toOption.map(_.as[Double]),
    amount = (v \ "amount").toOption.map(_.as[Double]),
    base_rate = (v \ "base_rate").toOption.map(_.as[Double]),
    base_amount = (v \ "base_amount").toOption.map(_.as[Double]),
    is_free_item = (v \ "is_free_item").toOption.map(_.as[Int]),
    net_rate = (v \ "net_rate").toOption.map(_.as[Double]),
    net_amount = (v \ "net_amount").toOption.map(_.as[Double]),
    item_tax_template = (v \ "item_tax_template").toOption.map(_.as[String]),
    base_net_rate = (v \ "base_net_rate").toOption.map(_.as[Double]),
    base_net_amount = (v \ "base_net_amount").toOption.map(_.as[Double]),
    billed_amt = (v \ "billed_amt").toOption.map(_.as[Double]),
    weight_per_unit = (v \ "weight_per_unit").toOption.map(_.as[Double]),
    total_weight = (v \ "total_weight").toOption.map(_.as[Double]),
    weight_uom = (v \ "weight_uom").toOption.map(_.as[String]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    target_warehouse = (v \ "target_warehouse").toOption.map(_.as[String]),
    quality_inspection = (v \ "quality_inspection").toOption.map(_.as[String]),
    against_sales_order = (v \ "against_sales_order").toOption.map(_.as[String]),
    so_detail = (v \ "so_detail").toOption.map(_.as[String]),
    against_sales_invoice = (v \ "against_sales_invoice").toOption.map(_.as[String]),
    si_detail = (v \ "si_detail").toOption.map(_.as[String]),
    batch_no = (v \ "batch_no").toOption.map(_.as[String]),
    serial_no = (v \ "serial_no").toOption.map(_.as[String]),
    actual_batch_qty = (v \ "actual_batch_qty").toOption.map(_.as[Double]),
    actual_qty = (v \ "actual_qty").toOption.map(_.as[Double]),
    installed_qty = (v \ "installed_qty").toOption.map(_.as[Double]),
    expense_account = (v \ "expense_account").toOption.map(_.as[String]),
    allow_zero_valuation_rate = (v \ "allow_zero_valuation_rate").toOption.map(_.as[Int]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    page_break = (v \ "page_break").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[DeliveryNoteItem] = Reads[DeliveryNoteItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Delivery Note Item") => JsSuccess(DeliveryNoteItem(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
