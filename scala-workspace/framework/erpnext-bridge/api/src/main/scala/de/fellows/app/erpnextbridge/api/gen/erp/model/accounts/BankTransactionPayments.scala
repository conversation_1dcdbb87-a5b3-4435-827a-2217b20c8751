package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BankTransactionPayments(
    name: String,
    payment_document: String,
    allocated_amount: Double
)

object BankTransactionPayments {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BankTransactionPayments = new BankTransactionPayments(
    name = (v \ "name").get.as[String],
    payment_document = (v \ "payment_document").get.as[String],
    allocated_amount = (v \ "allocated_amount").get.as[Double]
  )

  implicit val reads: Reads[BankTransactionPayments] = Reads[BankTransactionPayments] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bank Transaction Payments") => JsSuccess(BankTransactionPayments(js))
      case Some(_)                           => JsError("Wrong Doctype")
      case _                                 => JsError("Doctype not Found")
    }
  }

}
