package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class LandedCostItem(
    name: String,
    item_code: String,
    description: String,
    receipt_document_type: Option[String],
    qty: Option[Double],
    rate: Option[Double],
    amount: Double,
    is_fixed_asset: Option[Int],
    applicable_charges: Option[Double],
    purchase_receipt_item: Option[String],
    cost_center: Option[String]
)

object LandedCostItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): LandedCostItem = new LandedCostItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    description = (v \ "description").get.as[String],
    receipt_document_type = (v \ "receipt_document_type").toOption.map(_.as[String]),
    qty = (v \ "qty").toOption.map(_.as[Double]),
    rate = (v \ "rate").toOption.map(_.as[Double]),
    amount = (v \ "amount").get.as[Double],
    is_fixed_asset = (v \ "is_fixed_asset").toOption.map(_.as[Int]),
    applicable_charges = (v \ "applicable_charges").toOption.map(_.as[Double]),
    purchase_receipt_item = (v \ "purchase_receipt_item").toOption.map(_.as[String]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String])
  )

  implicit val reads: Reads[LandedCostItem] = Reads[LandedCostItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Landed Cost Item") => JsSuccess(LandedCostItem(js))
      case Some(_)                  => JsError("Wrong Doctype")
      case _                        => JsError("Doctype not Found")
    }
  }

}
