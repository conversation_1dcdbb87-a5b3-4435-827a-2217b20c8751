package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ExchangeRateRevaluation(
    name: String,
    posting_date: String,
    company: String,
    accounts: Seq[ExchangeRateRevaluationAccount],
    total_gain_loss: Option[Double],
    amended_from: Option[String]
)

object ExchangeRateRevaluation {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ExchangeRateRevaluation = new ExchangeRateRevaluation(
    name = (v \ "name").get.as[String],
    posting_date = (v \ "posting_date").get.as[String],
    company = (v \ "company").get.as[String],
    accounts =
      (v \ "accounts").toOption.map(x => x.as[JsArray].value.map(_.as[ExchangeRateRevaluationAccount])).get.toSeq,
    total_gain_loss = (v \ "total_gain_loss").toOption.map(_.as[Double]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ExchangeRateRevaluation] = Reads[ExchangeRateRevaluation] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Exchange Rate Revaluation") => JsSuccess(ExchangeRateRevaluation(js))
      case Some(_)                           => JsError("Wrong Doctype")
      case _                                 => JsError("Doctype not Found")
    }
  }

}
