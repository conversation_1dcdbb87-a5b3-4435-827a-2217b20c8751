package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Batch(
    batch_id: String,
    item: String,
    image: Option[String],
    parent_batch: Option[String],
    disabled: Option[Int],
    manufacturing_date: Option[String],
    expiry_date: Option[String],
    supplier: Option[String],
    reference_doctype: Option[String]
)

object Batch {
  val NAME_FIELD = "batch_id"

  def apply(v: JsValue): Batch = new Batch(
    batch_id = (v \ "batch_id").get.as[String],
    item = (v \ "item").get.as[String],
    image = (v \ "image").toOption.map(_.as[String]),
    parent_batch = (v \ "parent_batch").toOption.map(_.as[String]),
    disabled = (v \ "disabled").toOption.map(_.as[Int]),
    manufacturing_date = (v \ "manufacturing_date").toOption.map(_.as[String]),
    expiry_date = (v \ "expiry_date").toOption.map(_.as[String]),
    supplier = (v \ "supplier").toOption.map(_.as[String]),
    reference_doctype = (v \ "reference_doctype").toOption.map(_.as[String])
  )

  implicit val reads: Reads[Batch] = Reads[Batch] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Batch") => JsSuccess(Batch(js))
      case Some(_)       => JsError("Wrong Doctype")
      case _             => JsError("Doctype not Found")
    }
  }

}
