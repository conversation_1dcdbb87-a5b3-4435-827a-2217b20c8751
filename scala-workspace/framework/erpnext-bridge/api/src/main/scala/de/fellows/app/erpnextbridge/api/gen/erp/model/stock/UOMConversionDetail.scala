package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class UOMConversionDetail(
    name: String,
    uom: Option[String],
    conversion_factor: Option[Double]
)

object UOMConversionDetail {
  val NAME_FIELD = "name"

  def apply(v: JsValue): UOMConversionDetail = new UOMConversionDetail(
    name = (v \ "name").get.as[String],
    uom = (v \ "uom").toOption.map(_.as[String]),
    conversion_factor = (v \ "conversion_factor").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[UOMConversionDetail] = Reads[UOMConversionDetail] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("UOM Conversion Detail") => JsSuccess(UOMConversionDetail(js))
      case Some(_)                       => JsError("Wrong Doctype")
      case _                             => JsError("Doctype not Found")
    }
  }

}
