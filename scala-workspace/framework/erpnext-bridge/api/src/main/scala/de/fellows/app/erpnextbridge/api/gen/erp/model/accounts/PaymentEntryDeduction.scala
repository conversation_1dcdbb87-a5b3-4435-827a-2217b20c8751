package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PaymentEntryDeduction(
    name: String,
    account: String,
    cost_center: String,
    amount: Double
)

object PaymentEntryDeduction {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PaymentEntryDeduction = new PaymentEntryDeduction(
    name = (v \ "name").get.as[String],
    account = (v \ "account").get.as[String],
    cost_center = (v \ "cost_center").get.as[String],
    amount = (v \ "amount").get.as[Double]
  )

  implicit val reads: Reads[PaymentEntryDeduction] = Reads[PaymentEntryDeduction] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Payment Entry Deduction") => JsSuccess(PaymentEntryDeduction(js))
      case Some(_)                         => JsError("Wrong Doctype")
      case _                               => JsError("Doctype not Found")
    }
  }

}
