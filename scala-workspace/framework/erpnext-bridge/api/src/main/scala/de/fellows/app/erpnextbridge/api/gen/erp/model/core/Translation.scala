package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Translation(
    name: String,
    language: Option[String],
    status: Option[String],
    contributed_translation_doctype_name: Option[String]
)

object Translation {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Translation = new Translation(
    name = (v \ "name").get.as[String],
    language = (v \ "language").toOption.map(_.as[String]),
    status = (v \ "status").toOption.map(_.as[String]),
    contributed_translation_doctype_name = (v \ "contributed_translation_doctype_name").toOption.map(_.as[String])
  )

  implicit val reads: Reads[Translation] = Reads[Translation] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Translation") => JsSuccess(Translation(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
