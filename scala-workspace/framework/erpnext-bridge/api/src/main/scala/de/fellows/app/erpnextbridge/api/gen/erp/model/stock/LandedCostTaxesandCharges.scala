package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class LandedCostTaxesandCharges(
    name: String,
    expense_account: String,
    amount: Double
)

object LandedCostTaxesandCharges {
  val NAME_FIELD = "name"

  def apply(v: JsValue): LandedCostTaxesandCharges = new LandedCostTaxesandCharges(
    name = (v \ "name").get.as[String],
    expense_account = (v \ "expense_account").get.as[String],
    amount = (v \ "amount").get.as[Double]
  )

  implicit val reads: Reads[LandedCostTaxesandCharges] = Reads[LandedCostTaxesandCharges] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Landed Cost Taxes and Charges") => JsSuccess(LandedCostTaxesandCharges(js))
      case Some(_)                               => JsError("Wrong Doctype")
      case _                                     => JsError("Doctype not Found")
    }
  }

}
