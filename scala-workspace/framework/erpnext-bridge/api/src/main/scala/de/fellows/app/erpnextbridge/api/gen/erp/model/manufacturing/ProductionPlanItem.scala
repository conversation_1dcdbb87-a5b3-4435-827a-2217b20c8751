package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ProductionPlanItem(
    name: String,
    include_exploded_items: Option[Int],
    item_code: String,
    bom_no: String,
    planned_qty: Double,
    make_work_order_for_sub_assembly_items: Option[Int],
    warehouse: Option[String],
    planned_start_date: String,
    pending_qty: Option[Double],
    ordered_qty: Option[Double],
    produced_qty: Option[Double],
    description: Option[String],
    stock_uom: String,
    sales_order: Option[String],
    sales_order_item: Option[String],
    material_request: Option[String],
    material_request_item: Option[String],
    product_bundle_item: Option[String]
)

object ProductionPlanItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ProductionPlanItem = new ProductionPlanItem(
    name = (v \ "name").get.as[String],
    include_exploded_items = (v \ "include_exploded_items").toOption.map(_.as[Int]),
    item_code = (v \ "item_code").get.as[String],
    bom_no = (v \ "bom_no").get.as[String],
    planned_qty = (v \ "planned_qty").get.as[Double],
    make_work_order_for_sub_assembly_items = (v \ "make_work_order_for_sub_assembly_items").toOption.map(_.as[Int]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    planned_start_date = (v \ "planned_start_date").get.as[String],
    pending_qty = (v \ "pending_qty").toOption.map(_.as[Double]),
    ordered_qty = (v \ "ordered_qty").toOption.map(_.as[Double]),
    produced_qty = (v \ "produced_qty").toOption.map(_.as[Double]),
    description = (v \ "description").toOption.map(_.as[String]),
    stock_uom = (v \ "stock_uom").get.as[String],
    sales_order = (v \ "sales_order").toOption.map(_.as[String]),
    sales_order_item = (v \ "sales_order_item").toOption.map(_.as[String]),
    material_request = (v \ "material_request").toOption.map(_.as[String]),
    material_request_item = (v \ "material_request_item").toOption.map(_.as[String]),
    product_bundle_item = (v \ "product_bundle_item").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ProductionPlanItem] = Reads[ProductionPlanItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Production Plan Item") => JsSuccess(ProductionPlanItem(js))
      case Some(_)                      => JsError("Wrong Doctype")
      case _                            => JsError("Doctype not Found")
    }
  }

}
