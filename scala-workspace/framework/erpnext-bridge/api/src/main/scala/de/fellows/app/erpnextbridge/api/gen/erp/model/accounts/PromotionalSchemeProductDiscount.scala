package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PromotionalSchemeProductDiscount(
    name: String,
    disable: Option[Int],
    min_qty: Option[Double],
    max_qty: Option[Double],
    min_amount: Option[Double],
    max_amount: Option[Double],
    same_item: Option[Int],
    free_item: Option[String],
    free_qty: Option[Double],
    free_item_uom: Option[String],
    free_item_rate: Option[Double],
    warehouse: Option[String],
    priority: Option[String],
    apply_multiple_pricing_rules: Option[Int]
)

object PromotionalSchemeProductDiscount {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PromotionalSchemeProductDiscount = new PromotionalSchemeProductDiscount(
    name = (v \ "name").get.as[String],
    disable = (v \ "disable").toOption.map(_.as[Int]),
    min_qty = (v \ "min_qty").toOption.map(_.as[Double]),
    max_qty = (v \ "max_qty").toOption.map(_.as[Double]),
    min_amount = (v \ "min_amount").toOption.map(_.as[Double]),
    max_amount = (v \ "max_amount").toOption.map(_.as[Double]),
    same_item = (v \ "same_item").toOption.map(_.as[Int]),
    free_item = (v \ "free_item").toOption.map(_.as[String]),
    free_qty = (v \ "free_qty").toOption.map(_.as[Double]),
    free_item_uom = (v \ "free_item_uom").toOption.map(_.as[String]),
    free_item_rate = (v \ "free_item_rate").toOption.map(_.as[Double]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    priority = (v \ "priority").toOption.map(_.as[String]),
    apply_multiple_pricing_rules = (v \ "apply_multiple_pricing_rules").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[PromotionalSchemeProductDiscount] = Reads[PromotionalSchemeProductDiscount] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Promotional Scheme Product Discount") => JsSuccess(PromotionalSchemeProductDiscount(js))
      case Some(_)                                     => JsError("Wrong Doctype")
      case _                                           => JsError("Doctype not Found")
    }
  }

}
