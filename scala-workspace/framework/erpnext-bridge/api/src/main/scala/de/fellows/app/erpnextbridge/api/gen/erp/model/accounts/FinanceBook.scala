package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class FinanceBook(
    finance_book_name: Option[String]
)

object FinanceBook {
  val NAME_FIELD = "finance_book_name"

  def apply(v: JsValue): FinanceBook = new FinanceBook(
    finance_book_name = (v \ "finance_book_name").toOption.map(_.as[String])
  )

  implicit val reads: Reads[FinanceBook] = Reads[FinanceBook] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Finance Book") => JsSuccess(FinanceBook(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
