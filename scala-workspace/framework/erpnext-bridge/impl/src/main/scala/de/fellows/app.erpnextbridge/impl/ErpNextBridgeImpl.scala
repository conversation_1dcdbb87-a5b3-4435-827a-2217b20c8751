package de.fellows.app.erpnextbridge.impl

import akka.NotUsed
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.api.{ServiceCall, ServiceLocator}
import com.lightbend.lagom.scaladsl.client.ServiceClient
import com.typesafe.config.{ConfigException, ConfigFactory}
import de.fellows.app.erpnext.api
import de.fellows.app.erpnext.api.ListResponse
import de.fellows.app.erpnextbridge.api.ErpNextBridgeApi
import play.api.libs.json.{JsArray, JsObject, Json}
import play.api.libs.ws.{WSClient, WSRequest}

import java.io.UnsupportedEncodingException
import java.net.URLEncoder
import scala.concurrent.{ExecutionContext, Future}

class ErpNextBridgeImpl(sc: ServiceClient, loc: ServiceLocator, wsc: WSClient)(implicit ctx: ExecutionContext)
    extends ErpNextBridgeApi {

  val conf = ConfigFactory.load()
  val endpointopt =
    try
      Some(
        conf.getString("fellows.services.erpnext-bridge.endpoint"),
        conf.getString("fellows.services.erpnext-bridge.secret"),
        conf.getString("fellows.services.erpnext-bridge.token")
      )
    catch {
      case e: ConfigException.Missing => None
    }

  //  val secret =
  //  val token =

  def encode(s: String): String =
    try
      URLEncoder.encode(s, "UTF-8")
        .replaceAll("\\+", "%20")
        .replaceAll("\\%21", "!")
        .replaceAll("\\%27", "'")
        .replaceAll("\\%28", "(")
        .replaceAll("\\%29", ")")
        .replaceAll("\\%7E", "~")
    catch {
      case e: UnsupportedEncodingException => s
    }

  def call(team: String, dt: String, dn: String): Option[WSRequest] =
    call(team, dt, Some(dn), None, None, None, None, None)

  def call(
      team: String,
      dt: String,
      dno: Option[String],
      limit_start: Option[Int],
      limit_page_length: Option[Int],
      fields: Option[Seq[String]],
      order_by: Option[String],
      filters: Option[Seq[String]]
  ): Option[WSRequest] =
    endpointopt match {
      case Some((endpoint: String, secret: String, token)) =>
        val url = s"$endpoint/api/resource/${encode(dt)}${dno.map(dn => s"/${encode(dn)}").getOrElse("")}"

        println(s"convert filter $filters")

        val filtersstring = filters.map { fs =>
          s"[${fs.mkString(",")}]"
        }

        val endpointURL = s"$url?${
            Seq(
              Some(s"limit_page_length=" + encode(limit_page_length.getOrElse(0).toString)),
              limit_start.map(x => s"limit_start=" + encode(x.toString)),
              fields.map(x => s"fields=" + encode(s"[${x.map(s => s""""$s"  """).mkString(",")}]")),
              order_by.map(x => s"order_by=" + encode(x)),
              filtersstring.map(x => s"filters=" + encode(x))
            ).flatten.mkString("&")
          }"
        println(s"call $endpointURL")
        Some(wsc.url(endpointURL)
          //      .addHttpHeaders("Authorization" -> "token b24147b214c6726:4891c5a630407ca")
          // key:secret
          // e66410b27d920a8:65cf0657850fbb8
          .addHttpHeaders("Authorization" -> s"token $token:$secret")
          .addHttpHeaders("Accept" -> "application/json")
          .addHttpHeaders("Content-Type" -> "application/json"))

      case _ => None
    }

  override def listDocuments(
      team: String,
      docType: String,
      limit_start: Option[Int],
      limit_page_length: Option[Int],
      fields: Option[Seq[String]],
      order_by: Option[String],
      filters: Option[Seq[String]]
  ): ServiceCall[NotUsed, api.ListResponse] =
    //  override def listDocuments(docType: String): ServiceCall[NotUsed, api.ListResponse] = {
    ServiceCall { _ =>
      call(team, docType, None, limit_start, limit_page_length, fields, order_by, filters).map(_
        .get()
        .map { resp =>
          resp.status match {
            case 200 =>
              ListResponse((Json.parse(resp.body) \ "data").get.as[JsArray].value.map(_.as[JsObject]).toSeq)
            case _ => throw new TransportException(TransportErrorCode.InternalServerError, s"ERP Failure ${resp.body}")
          }
        }).getOrElse(Future.successful(ListResponse(Seq())))
    }

  override def getDocument(team: String, docType: String, docName: String): ServiceCall[NotUsed, api.ObjectContainer] =
    ServiceCall { _ =>
      call(team, docType, docName).map(_
        .get()
        .map { resp =>
          resp.status match {
            case 200 =>
              val jsObject = (Json.parse(resp.body) \ "data").get.as[JsObject]
              api.ObjectContainer(jsObject)
            case _ => throw new TransportException(TransportErrorCode.InternalServerError, s"ERP Failure ${resp.body}")
          }
        }).getOrElse(Future.failed(new TransportException(TransportErrorCode.NotFound, "")))
    }
}
