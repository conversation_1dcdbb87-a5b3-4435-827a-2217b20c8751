// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.notification.api

import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Service, ServiceAcl, ServiceCall}
import de.fellows.app.notification.common.{Notification, NotificationMessage}

import java.util.UUID

trait NotificationService extends Service {

  def streamNotifications(k: String): ServiceCall[NotUsed, Source[NotificationMessage, NotUsed]]

  def updateNotification(id: UUID): ServiceCall[Notification, Done]

  def getNotification(id: UUID): ServiceCall[NotUsed, Notification]

  def deleteNotification(id: UUID): ServiceCall[NotUsed, Done]

  override final def descriptor = {
    import Service._

    named("notification")
      .withCalls(
        restCall(Method.GET, "/api/notification/notifications/stream?k", streamNotifications _),
        restCall(Method.PUT, "/api/notification/notifications/:id", updateNotification _),
        restCall(Method.GET, "/api/notification/notifications/:id", getNotification _),
        restCall(Method.DELETE, "/api/notification/notifications:id", deleteNotification _)
      )
      .withAcls(
        ServiceAcl(pathRegex = Some("/api/notification/.*"))
      )
  }
}
//
