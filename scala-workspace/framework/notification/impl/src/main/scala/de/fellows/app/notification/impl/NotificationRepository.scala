// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.notification.impl

import java.time.Instant
import java.util.UUID

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core._
import com.datastax.driver.extras.codecs.jdk8.InstantCodec
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.notification.common.{Link, Notification, User}
import de.fellows.app.notification.impl.entities.{
  NotificationCreated,
  NotificationDeleted,
  NotificationEvent,
  NotificationRead
}
import de.fellows.utils.communication.ServiceDefinition

import scala.jdk.CollectionConverters._
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}

class NotificationRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {
  session.underlying().map { c =>
    c.getCluster.getConfiguration.getCodecRegistry
      .register(InstantCodec.instance)
  }

  def convertLinks(row: Row): Seq[Link] =
    row.getList("links", classOf[TupleValue]).asScala.map(tuple => Link(tuple.getString(0), tuple.getString(0))).toSeq

  def convert(row: Row): Notification =
    // (id, owner, service, icon, message, links, title, read, resource, instant)
    Notification(
      id = Option(row.get("id", classOf[UUID])),
      owner = User(
        id = row.getUUID("owner"),
        username = Option(row.getString("ownerName")),
        mail = Option(row.getString("ownerMail")),
        avatar = Option(row.getString("ownerAvatar"))
      ),
      priority = row.getInt("priority"),
      service = row.getString("service"),
      icon = Option(row.getString("icon")),
      message = row.getString("message"),
      links = convertLinks(row),
      title = row.getString("title"),
      read = row.getBool("read"),
      resource = Option(row.getString("resource")),
      date = row.get("instant", classOf[Instant])
    )

  def getNotification(user: UUID, id: UUID) =
    session.selectOne(
      """
        |SELECT * FROM notifications WHERE owner = ? AND id = ?
      """.stripMargin,
      user,
      id
    )
      .map(_.map(convert))

  def getNotificationsByUser(user: UUID) =
    session.selectAll(
      """
        |SELECT * FROM notifications WHERE owner = ?
      """.stripMargin,
      user
    ).map(_.map(convert))
}

private[impl] class NotificationEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext
) extends ReadSideProcessor[NotificationEvent] {

  private var insertNotificationStatement: PreparedStatement = _
  private var deleteNotificationStatement: PreparedStatement = _
  private var readNotificationStatement: PreparedStatement   = _

  def insertNotification(event: NotificationCreated): Future[immutable.Seq[BoundStatement]] = {
    val n = event.notification
    Future.successful(List(
      // (id, priority, owner, ownerName, ownerMail, ownerAvatar, service, icon, message, links, title, read, resource, instant)
      insertNotificationStatement.bind(
        n.id.getOrElse(UUID.randomUUID()),
        n.priority.asInstanceOf[AnyRef],
        n.owner.id,
        n.owner.username.orNull,
        n.owner.mail.orNull,
        n.owner.avatar.orNull,
        n.service,
        n.icon.orNull,
        n.message,
        n.links.map(l => (l.text, l.target)).asJava,
        n.title,
        n.read.asInstanceOf[AnyRef],
        n.resource.orNull,
        n.date
      )
    ))
  }

  def deleteNotification(event: NotificationDeleted): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      deleteNotificationStatement.bind(event.id, event.owner)
    ))

  def readNotification(event: NotificationRead): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      readNotificationStatement.bind(true.asInstanceOf[AnyRef], event.id, event.owner)
    ))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[NotificationEvent] =
    readSide.builder[NotificationEvent]("notificationEventOffset")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[NotificationCreated](e => insertNotification(e.event))
      .setEventHandler[NotificationDeleted](e => deleteNotification(e.event))
      .setEventHandler[NotificationRead](e => readNotification(e.event))
      .build

  override def aggregateTags: Set[AggregateEventTag[NotificationEvent]] = NotificationEvent.Tag.allTags

  private def createTables() =
    for {
      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS notifications (
            id UUID,
            priority int,
            owner UUID,
            ownerName text,
            ownerMail text,
            ownerAvatar text,
            service text,
            icon text,
            message text,
            links list<FROZEN<tuple<text,text>>>,
            title text,
            read boolean,
            resource text,
            instant timestamp,

            PRIMARY KEY (owner, id)
          )
        """
      )

    } yield Done

  private def prepareStatements() =
    for {

      insertNotification <- session.prepare(
        """
          | INSERT INTO notifications(id, priority, owner, ownerName, ownerMail, ownerAvatar, service, icon, message, links, title, read, resource, instant) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?)
        """.stripMargin
      )

      deleteNotification <- session.prepare(
        """
          | DELETE FROM notifications WHERE id = ? AND owner = ?
        """.stripMargin
      )

      readNotification <- session.prepare(
        """
          | UPDATE notifications SET read = ? WHERE id = ? AND owner = ?
        """.stripMargin
      )

    } yield {
      session.underlying().map { c =>
        c.getCluster.getConfiguration.getCodecRegistry
          .register(InstantCodec.instance)
      }

      insertNotificationStatement = insertNotification
      deleteNotificationStatement = deleteNotification
      readNotificationStatement = readNotification

      Done
    }
}
