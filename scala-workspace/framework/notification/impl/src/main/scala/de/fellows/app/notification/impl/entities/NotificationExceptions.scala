package de.fellows.app.notification.impl.entities

import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import de.fellows.utils.JsonFormats.singletonFormat
import de.fellows.utils.communication.ServiceError
import play.api.libs.json.Format

object NotificationNotFound
    extends ServiceError(1, "Notification Does Not Exist", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[NotificationNotFound.type] = singletonFormat(NotificationNotFound)
}
object NotificationExist extends ServiceError(2, "Notification Exist") {
  @transient implicit val format: Format[NotificationExist.type] = singletonFormat(NotificationExist)
}
object NotificationNotRead extends ServiceError(3, "Notification is not read") {
  @transient implicit val format: Format[NotificationNotRead.type] = singletonFormat(NotificationNotRead)
}
