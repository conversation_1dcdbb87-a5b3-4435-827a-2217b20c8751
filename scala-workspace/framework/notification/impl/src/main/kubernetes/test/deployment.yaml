apiVersion: "apps/v1beta2"
kind: Deployment
metadata:
  name: "notification-test"
  labels:
    app: notification
    group: fellows
    log: logback
    appNameVersion: "notification-test"
    "akka.lightbend.com/service-name": notification
spec:
  replicas: 2
  selector:
    matchLabels:
      appNameVersion: "notification-test"
  template:
    metadata:
      labels:
        app: notification
        group: fellows
        log: logback
        sha: "${GIT_SHA_FULL}"
        appNameVersion: "notification-test"
        "akka.lightbend.com/service-name": notification
    spec:
      restartPolicy: Always
      imagePullSecrets:
        - name: gitlab-auth
      containers:
        - name: notification
          image: "jira.electronic-fellows.de:5000/app/backend/notification-impl:latest"
          imagePullPolicy: Always
          env:
            - name: "REQUIRED_CONTACT_POINT_NR"
              value: "2"
            - name: "JAVA_OPTS"
              value: "-Dplay.crypto.secret=amazingsecret"
          ports:
            - containerPort: 9000
              name: http
            - containerPort: 2552
              name: remoting
            - containerPort: 8558
              name: management

          readinessProbe:
            httpGet:
              path: "/ready"
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
          livenessProbe:
            httpGet:
              path: "/alive"
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: notification
  name: notification
spec:
  ports:
    - name: http
      port: 9000
      protocol: TCP
      targetPort: 9000
    - name: remoting
      port: 2552
      protocol: TCP
      targetPort: 2552
    - name: management
      port: 8558
      protocol: TCP
      targetPort: 8558
  selector:
    app: notification
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: { }
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: notification
spec:
  rules:
    - http:
        paths:
          - path: /api/notification
            backend:
              serviceName: notification
              servicePort: 9000

status:
  loadBalancer: { }

