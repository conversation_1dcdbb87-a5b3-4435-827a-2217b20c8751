package de.fellows.app.project.api

import java.util.UUID

import play.api.libs.json.{ Format, Json }

case class ProjectCreation(p: Project)

case class ProjectUpdate(name: Option[String], owner: Option[String], desc: Option[String])

case class ProjectVersionCreation(predecessor: String, name: Option[String])

case class ProjectVersionUpdate(identifier: Option[String])

case class ProjectFeatureCreation(predecessor: String, name: Option[String])

case class ProjectFeatureUpdate(identifier: Option[String])

object ProjectCreation {
  implicit val format: Format[ProjectCreation] = Json.format[ProjectCreation]

}

object ProjectUpdate {
  implicit val format: Format[ProjectUpdate] = Json.format[ProjectUpdate]

}

object ProjectVersionCreation {
  implicit val format: Format[ProjectVersionCreation] = Json.format[ProjectVersionCreation]

}

object ProjectVersionUpdate {
  implicit val format: Format[ProjectVersionUpdate] = Json.format[ProjectVersionUpdate]

}

object ProjectFeatureCreation {
  implicit val format: Format[ProjectFeatureCreation] = Json.format[ProjectFeatureCreation]

}

object ProjectFeatureUpdate {
  implicit val format: Format[ProjectFeatureUpdate] = Json.format[ProjectFeatureUpdate]

}
