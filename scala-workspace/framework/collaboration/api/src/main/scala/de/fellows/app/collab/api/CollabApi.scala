package de.fellows.app.collab.api

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.user.api.User
import de.fellows.utils.collaboration.TimelineEvent
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID

case class ShareRetrieval(password: Option[String])

case class ShareFeature(name: String, parameters: Map[String, String], shareOptions: Seq[String])

case class ShareRequest(
    features: Seq[ShareFeature],
    expires: Option[Instant],
    password: Option[String] = None, // used for POSTing a new password.
    clearPassword: Option[Boolean] = Some(false)
)

case class VersionShare(
    shareID: String,
    assembly: AssemblyReference,
    features: Seq[ShareFeature],
    shareOwner: User,
    isProtected: Boolean,
    expires: Option[Instant],
    token: Option[String] = None,
    refresh: Option[String] = None
)

object ShareFeature {
  val VIEW   = "view"        // view only
  val COLLAB = "collaborate" // basic editing
  val MANAGE = "manage"      // edit all

  implicit val f: Format[ShareFeature] = Json.format[ShareFeature]
}

object VersionShare {
  implicit val f: Format[VersionShare] = Json.format[VersionShare]
}

object ShareRetrieval {
  implicit val f: Format[ShareRetrieval] = Json.format[ShareRetrieval]
}

object ShareRequest {
  implicit val f: Format[ShareRequest] = Json.format[ShareRequest]
}

case class TimelineParameters(
    since: Option[Instant],
    max: Option[Int],
    offset: Option[Int],
    filteredChangeCategories: Option[Seq[String]],
    filteredChangeTypes: Option[Seq[String]],
    filteredTypes: Seq[String],
    filteredReferences: Seq[String],
    excludeSystem: Option[Boolean]
)

case class TimelineUserInfo(id: UUID, name: String, avatar: Option[String])

case class Timeline(content: Seq[TimelineEvent], users: Map[String, TimelineUserInfo])

object TimelineParameters {
  implicit val f: Format[TimelineParameters] = Json.format[TimelineParameters]
}

object TimelineUserInfo {
  implicit val f: Format[TimelineUserInfo] = Json.format[TimelineUserInfo]
}

object Timeline {
  implicit val f: Format[Timeline] = Json.format[Timeline]
}

// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
