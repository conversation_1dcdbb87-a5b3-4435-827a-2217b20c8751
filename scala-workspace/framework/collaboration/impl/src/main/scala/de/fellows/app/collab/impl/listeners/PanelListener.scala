package de.fellows.app.collab.impl.listeners

import akka.cluster.sharding.typed.scaladsl.ClusterSharding
import akka.util.Timeout
import de.fellows.ems.panel.api.PanelService
import de.fellows.utils.TopicUtils
import play.api.Logging

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.DurationInt

class PanelListener(service: PanelService, clusterSharding: ClusterSharding)(implicit val exc: ExecutionContext)
    extends Logging {
  implicit val timeout: Timeout = Timeout(5 seconds)
  import TopicUtils.defaultNaming
  TopicUtils.subscribe(service.customerPanelTimeline(), 10) {
    _.payload match {
      case msg => EventHelper.createEvent(msg, clusterSharding)
    }
  }
}
