package de.fellows.app.collab.impl.read.share

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement, Row}
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.collab.impl.entity.share.{ShareChanged, ShareEvent}
import de.fellows.utils.communication.ServiceDefinition
import org.slf4j.{Logger, LoggerFactory}

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

class ShareRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {
  implicit val log: Logger = LoggerFactory.getLogger(classOf[ShareRepository])

  def getShareByID(shareID: String): Future[UUID] =
    getShareByIDOpt(shareID).map(_.getOrElse(throw new TransportException(
      TransportErrorCode.NotFound,
      "Share not found"
    )))

  def getShareByIDOpt(shareID: String): Future[Option[UUID]] =
    session.selectOne("SELECT * FROM shares WHERE shareID = ?", shareID)
      .map(_.map(r => convert(r)))

  def convert(r: Row): UUID =
    r.getUUID("version")

}

private[impl] class ShareEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[ShareEvent] {

  var byShareID: PreparedStatement   = null
  var updateShare: PreparedStatement = null
  var deleteShare: PreparedStatement = null

  // language=SQL
  private def prepareStatements(): Future[Done] =
    for {
      byShareID <- session.prepare("SELECT * FROM shares WHERE shareID = :shareID")
      updateShare <- session.prepare(
        "UPDATE shares SET team = :team, version = :version, assembly = :assembly WHERE shareID = :shareID"
      )
      deleteShare <- session.prepare(
        "DELETE FROM shares WHERE shareID = :shareID"
      )
    } yield {
      this.byShareID = byShareID
      this.updateShare = updateShare
      this.deleteShare = deleteShare
      Done
    }

  // language=SQL
  private def createTables(): Future[Done] =
    for {
      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS shares (
          |            shareID text,
          |            team text,
          |            version UUID,
          |            assembly UUID,
          | PRIMARY KEY (shareID)
          |);
        """.stripMargin
      )
    } yield Done

  def setShare(event: ShareChanged): Future[Seq[BoundStatement]] =
    if (event.shareID != "") {
      event.updated match {
        case Some(share) =>
          val assRef = event.updated.get.assembly
          Future.successful(List(
            updateShare.bind()
              .setString("team", assRef.team)
              .setUUID("version", assRef.version)
              .setUUID("assembly", assRef.version)
              .setString("shareID", share.shareID)
          ))
        case None =>
          Future.successful(List(
            deleteShare.bind()
              .setString("shareID", event.shareID)
          ))
      }
    } else {
      Future.successful(List())
    }

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[ShareEvent] =
    readSide.builder[ShareEvent]("shareEventOffset-v1.0")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[ShareChanged](e => setShare(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[ShareEvent]] = ShareEvent.Tag.allTags

}
