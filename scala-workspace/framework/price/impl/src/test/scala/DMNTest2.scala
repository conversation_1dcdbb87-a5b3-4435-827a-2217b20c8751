import de.fellows.app.price.api.{DecisionTable, DecisionTableVersion, Input, Output, Pricing, VersionedDecisionTable}
import de.fellows.app.price.impl.PriceServiceImpl
import de.fellows.app.price.impl.dmn.DMNBuilder
import org.camunda.bpm.model.dmn.{Dmn, DmnModelInstance}

import java.time.Instant
import java.util.UUID
import scala.xml.XML

object DMNTest2 extends App {

  //  val i = Files.readAllLines(Paths.get("/home/<USER>/.config/JetBrains/IntelliJIdea2020.3/scratches/foo.json")).toArray.mkString("\n")

  //  val p = Json.parse(i).as[Pricing]

  private val table = DecisionTable(
    team = Some(""),
    name = "name",
    inputs = Seq(Input(
      label = "in 1",
      name = "in1",
      varType = "string"
    )),
    outputs = Seq(Output(
      label = "out 1",
      name = "out1",
      varType = "string"
    )),
    rules = Seq(),
    hitpolicy = Some("FIRST")
  )
  val p = Pricing(
    name = "test pricing",
    id = Some(UUID.randomUUID()),
    supplier = UUID.randomUUID(),
    tables = Seq(
      VersionedDecisionTable(
        "",
        0,
        "table",
        "",
        Seq(
          DecisionTableVersion(
            table,
            Instant.now,
            UUID.randomUUID().toString
          )
        )
      )
    ),
    enableApi = false,
    currency = None
  )

  private val instance: DmnModelInstance = DMNBuilder.from(p, Map(table -> "p")).result()
  val str                                = Dmn.convertToString(instance)
  println(str)
  Dmn.validateModel(instance)

  val x = XML.loadString(str)

  val back = PriceServiceImpl.parseXML("", None, x)

  println(back)

}
