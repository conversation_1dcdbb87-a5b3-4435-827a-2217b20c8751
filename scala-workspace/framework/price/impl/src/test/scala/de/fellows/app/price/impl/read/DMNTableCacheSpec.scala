package de.fellows.app.price.impl.read

import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.concurrent.ScalaFutures
import scala.concurrent.{ExecutionContext, Future}
import scala.concurrent.duration._
import de.fellows.app.price.impl.calculator.BuiltDecisionTable
import com.lightbend.lagom.scaladsl.api.transport.TransportException
import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import org.camunda.bpm.model.dmn.Dmn
import java.util.concurrent.atomic.AtomicInteger

class DMNTableCacheSpec extends AnyWordSpec with Matchers with ScalaFutures {
  implicit val ec: ExecutionContext = ExecutionContext.global

  "DMNTableCache" should {
    "cache and return the same table for the same key and version" in {
      val cache = new DMNTableCache
      val key   = DMNTableCacheKey("team1", "deployment1", 1)
      val model = Dmn.createEmptyModel()
      val table = new BuiltDecisionTable("team1", "test1", model)

      val future1 = cache.getOrElseUpdate(key, Future.successful(table))
      val future2 = cache.getOrElseUpdate(key, Future.failed(new RuntimeException("Should not be called")))

      whenReady(future1) { result1 =>
        whenReady(future2) { result2 =>
          result1 shouldBe table
          result2 shouldBe table
        }
      }
    }

    "update cache when version changes" in {
      val cache  = new DMNTableCache
      val key1   = DMNTableCacheKey("team1", "deployment1", 1)
      val key2   = DMNTableCacheKey("team1", "deployment1", 2)
      val model1 = Dmn.createEmptyModel()
      val model2 = Dmn.createEmptyModel()
      val table1 = new BuiltDecisionTable("team1", "test1", model1)
      val table2 = new BuiltDecisionTable("team1", "test2", model2)

      val future1 = cache.getOrElseUpdate(key1, Future.successful(table1))
      val future2 = cache.getOrElseUpdate(key2, Future.successful(table2))

      whenReady(future1) { result1 =>
        whenReady(future2) { result2 =>
          result1 shouldBe table1
          result2 shouldBe table2
        }
      }
    }

    "handle concurrent access with different keys" in {
      val cache  = new DMNTableCache
      val key1   = DMNTableCacheKey("team1", "deployment1", 1)
      val key2   = DMNTableCacheKey("team2", "deployment2", 1)
      val model1 = Dmn.createEmptyModel()
      val model2 = Dmn.createEmptyModel()
      val table1 = new BuiltDecisionTable("team1", "test1", model1)
      val table2 = new BuiltDecisionTable("team2", "test2", model2)

      val futures = Future.sequence(Seq(
        cache.getOrElseUpdate(key1, Future.successful(table1)),
        cache.getOrElseUpdate(key2, Future.successful(table2))
      ))

      whenReady(futures) { results =>
        results should contain theSameElementsAs Seq(table1, table2)
      }
    }

    "handle concurrent access to the same key" in {
      val cache = new DMNTableCache
      val key   = DMNTableCacheKey("team1", "deployment1", 1)
      val model = Dmn.createEmptyModel()
      val table = new BuiltDecisionTable("team1", "test1", model)

      val workDone = new AtomicInteger(0)
      // Create a slow future that will take some time to complete
      val slowFuture = Future {
        Thread.sleep(500) // Simulate some work
        workDone.incrementAndGet()
        table
      }

      // Start multiple concurrent requests for the same key
      val futures: Seq[Future[BuiltDecisionTable]] = (1 to 10).map { _ =>
        cache.getOrElseUpdate(key, slowFuture)
      }

      val f: Future[Seq[BuiltDecisionTable]] = Future.sequence(futures)

      // All futures should complete with the same table
      whenReady(f) { results =>
        results.foreach { result =>
          result shouldBe table
        }
        // Verify that all results are the same instance
        results.toSet.size shouldBe 1

        workDone.get shouldBe 1
      }
    }

    "handle different combinations of teams and IDs" in {
      val cache = new DMNTableCache

      // Same ID, different teams
      val key1 = DMNTableCacheKey("team1", "deployment1", 1)
      val key2 = DMNTableCacheKey("team2", "deployment1", 1)

      // Same team, different IDs
      val key3 = DMNTableCacheKey("team1", "deployment3", 1)
      val key4 = DMNTableCacheKey("team1", "deployment4", 1)

      val model1 = Dmn.createEmptyModel()
      val model2 = Dmn.createEmptyModel()
      val model3 = Dmn.createEmptyModel()
      val model4 = Dmn.createEmptyModel()

      val table1 = new BuiltDecisionTable("team1", "test1", model1)
      val table2 = new BuiltDecisionTable("team2", "test2", model2)

      val table3 = new BuiltDecisionTable("team1", "test3", model3)
      val table4 = new BuiltDecisionTable("team1", "test4", model4)

      // First, cache tables for different teams with the same ID
      val future1 = cache.getOrElseUpdate(key1, Future.successful(table1))
      val future2 = cache.getOrElseUpdate(key2, Future.successful(table2))

      // Then, cache a table for the same team but different ID
      val future3 = cache.getOrElseUpdate(key3, Future.successful(table3))
      val future4 = cache.getOrElseUpdate(key4, Future.successful(table4))

      // Verify that all tables are cached correctly
      whenReady(future1) { result1 =>
        whenReady(future2) { result2 =>
          whenReady(future3) { result3 =>
            whenReady(future4) { result4 =>
              // Verify that tables for different teams with same ID are different
              result1 shouldBe table1
              result2 shouldBe table2
              result3 shouldBe table3
              result4 shouldBe table4
              // Verify that the cache returns the same instances for subsequent requests
              val future1Again =
                cache.getOrElseUpdate(key1, Future.failed(new RuntimeException("Should not be called")))
              val future2Again =
                cache.getOrElseUpdate(key2, Future.failed(new RuntimeException("Should not be called")))
              val future3Again =
                cache.getOrElseUpdate(key3, Future.failed(new RuntimeException("Should not be called")))
              val future4Again =
                cache.getOrElseUpdate(key4, Future.failed(new RuntimeException("Should not be called")))

              whenReady(future1Again) { result1Again =>
                whenReady(future2Again) { result2Again =>
                  whenReady(future3Again) { result3Again =>
                    whenReady(future4Again) { result4Again =>
                      result1Again shouldBe table1
                      result2Again shouldBe table2
                      result3Again shouldBe table3
                      result4Again shouldBe table4
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    "handle failures in the orElse function" in {
      val cache         = new DMNTableCache
      val key           = DMNTableCacheKey("team1", "deployment1", 1)
      val expectedError = new RuntimeException("Test error")

      val future = cache.getOrElseUpdate(key, Future.failed(expectedError))

      whenReady(future.failed) { exception =>
        exception shouldBe expectedError
      }
    }
  }
}
