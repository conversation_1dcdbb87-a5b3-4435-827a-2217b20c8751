package de.fellows.app.price.impl.entity

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.price.impl.entity.pricing.DecisionDeployment
import play.api.libs.json.{Format, Json}

import java.util.UUID

package object capabilities {
  case class CapabilitiesDeployment(
      team: String,
      supplier: UUID,
      capabilities: Seq[DecisionDeployment]
  )

  object CapabilitiesDeployment {
    implicit val format: Format[CapabilitiesDeployment] = Json.format[CapabilitiesDeployment]
  }

  sealed trait CapabilitiesCommand {
    val team: String
    val supplier: UUID
  }
  sealed trait CapabilitiesEvent extends AggregateEvent[CapabilitiesEvent] {
    override def aggregateTag: AggregateEventShards[CapabilitiesEvent] = CapabilitiesEvent.Tag
  }

  object CapabilitiesEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[CapabilitiesEvent](NumShards)
  }

  case class DeployCapabilities(
      override val team: String,
      override val supplier: UUID,
      d: Seq[DecisionDeployment]
  ) extends CapabilitiesCommand with ReplyType[CapabilitiesDeployment]

  object DeployCapabilities {
    implicit val format: Format[DeployCapabilities] = Json.format[DeployCapabilities]
  }

  case class ChangeCapabilityDeployment(
      override val team: String,
      override val supplier: UUID,
      d: Option[Seq[DecisionDeployment]]
  ) extends CapabilitiesCommand
      with ReplyType[CapabilitiesDeployment]

  object ChangeCapabilityDeployment {
    implicit val format: Format[ChangeCapabilityDeployment] = Json.format[ChangeCapabilityDeployment]
  }

  case class GetDeployedCapabilities(
      override val team: String,
      override val supplier: UUID
  ) extends CapabilitiesCommand
      with ReplyType[CapabilitiesDeploymentResponse]

  object GetDeployedCapabilities {
    implicit val format: Format[GetDeployedCapabilities] = Json.format[GetDeployedCapabilities]
  }

  case class DeleteDeployedCapabilities(
      override val team: String,
      override val supplier: UUID
  ) extends CapabilitiesCommand with ReplyType[Done]

  object DeleteDeployedCapabilities {
    implicit val format: Format[DeleteDeployedCapabilities] = Json.format[DeleteDeployedCapabilities]
  }

  case class CapabilitiesDeploymentResponse(response: Option[CapabilitiesDeployment])

  object CapabilitiesDeploymentResponse {
    implicit val format: Format[CapabilitiesDeploymentResponse] = Json.format[CapabilitiesDeploymentResponse]
  }

  case class CapabilityDeploymentDeleted(
      team: String,
      supplier: UUID
  ) extends CapabilitiesEvent

  object CapabilityDeploymentDeleted {
    implicit val format: Format[CapabilityDeploymentDeleted] = Json.format[CapabilityDeploymentDeleted]
  }

  case class CapabilityDeployment(
      team: String,
      supplier: UUID,
      deployment: Seq[DecisionDeployment]
  ) extends CapabilitiesEvent

  object CapabilityDeployment {
    implicit val format: Format[CapabilityDeployment] = Json.format[CapabilityDeployment]
  }
}
