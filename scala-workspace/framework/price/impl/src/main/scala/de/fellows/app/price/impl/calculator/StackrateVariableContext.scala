package de.fellows.app.price.impl.calculator

import de.fellows.app.camunda.api.Variable
import org.camunda.bpm.engine.variable.context.VariableContext
import org.camunda.bpm.engine.variable.impl.value.PrimitiveTypeValueImpl.{
  BooleanValueImpl,
  DoubleValueImpl,
  IntegerValueImpl,
  LongValueImpl,
  StringValueImpl
}
import org.camunda.bpm.engine.variable.impl.value.builder.SerializedObjectValueBuilderImpl
import org.camunda.bpm.engine.variable.value.TypedValue
import play.api.libs.json.{ JsValue, <PERSON><PERSON>, Writes }

import java.util
import java.util.UUID
import scala.jdk.CollectionConverters._

class StackrateVariableContext(vars: Map[String, TypedValue]) extends VariableContext {
  override def resolve(variableName: String): TypedValue =
    vars(variableName)

  override def containsVariable(variableName: String): Boolean = vars.contains(variableName)

  override def keySet(): util.Set[String] = vars.keySet.asJava
}

object StackrateVariableContext {
  def typed(x: String): TypedValue =
    new StringValueImpl(x)

  def typed(x: UUID): TypedValue =
    new StringValueImpl(x.toString)

  def _json[T](x: T)(implicit r: Writes[T]): TypedValue =
    json(Json.toJson(x))

  def json(x: JsValue): TypedValue =
    new SerializedObjectValueBuilderImpl()
      .objectTypeName("json")
      .serializationDataFormat("application/json")
      .serializedValue(Json.stringify(x))
      .create()

  def typed(x: BigDecimal): TypedValue =
    if (x.scale > 0) {
      new DoubleValueImpl(x.doubleValue)
    } else {
      new IntegerValueImpl(x.intValue)
    }

  def typed(x: AnyVal): TypedValue = x match {
    case v: Int     => new IntegerValueImpl(v)
    case v: Boolean => new BooleanValueImpl(v)
    case v: Long    => new LongValueImpl(v)
    case v: Double  => new DoubleValueImpl(v)
  }
}
