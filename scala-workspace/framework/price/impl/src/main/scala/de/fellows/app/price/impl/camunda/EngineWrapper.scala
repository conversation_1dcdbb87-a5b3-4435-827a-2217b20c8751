package de.fellows.app.price.impl.camunda

import de.fellows.utils.logging.StackrateLogging
import org.camunda.feel.FeelEngine
import org.camunda.feel.FeelEngine.{EvalExpressionResult, Failure}
import org.camunda.feel.context.{Context, FunctionProvider, VariableProvider}
import org.camunda.feel.impl.interpreter.{BuiltinFunctions, EvalContext}
import org.camunda.feel.syntaxtree.{ParsedExpression, ValError}

import scala.util.{Success, Try}

/** Wrap the [[FeelEngine]] to be able to use [[ContextWrapper]]
  * @param underlying
  */
class EngineWrapper(underlying: FeelEngine) extends FeelEngine with StackrateLogging {

  private val rootContext: EvalContext = new EvalContext(
    valueMapper = valueMapper,
    variableProvider = VariableProvider.EmptyVariableProvider,
    functionProvider = FunctionProvider.CompositeFunctionProvider(
      List(new BuiltinFunctions(clock), functionProvider)
    )
  )

  override def eval(exp: ParsedExpression, context: Context): EvalExpressionResult =
    Try {
      validate(exp).flatMap(_ => eval(exp, rootContext + context))
    }.recover(failure =>
      Left(
        Failure(s"failed to evaluate expression '${exp.text}' : $failure")
      )
    )
      .get

  private def eval(exp: ParsedExpression, context: EvalContext): EvalExpressionResult =
    Try(
      interpreter.eval(exp.expression)(new ContextWrapper(context)) match {
        case ValError(cause) =>
          Left(Failure(s"failed to evaluate expression '${exp.text}': $cause"))
        case value => Right(valueMapper.unpackVal(value))
      }
    ) match {
      case util.Failure(exception: VariableMissingException) =>
        Left(Failure(s"Variable ${exception.variable} not found in '${exp.text}'"))
      case util.Failure(exception) => Left(Failure(exception.getMessage))
      case Success(value)          => value
    }

  def validate(
      exp: ParsedExpression
  ): Either[Failure, ParsedExpression] =
    validator
      .validateExpression(exp.expression)
      .map(failure =>
        Failure(
          s"""validation of expression '${exp.text}' failed: ${failure.message}"""
        )
      )
      .toLeft(exp)
}
