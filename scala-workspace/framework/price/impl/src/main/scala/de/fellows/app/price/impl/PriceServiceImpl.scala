// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.price.impl

import akka.actor.ActorSystem
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.api.{ServiceCall, ServiceLocator}
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.typesafe.config.ConfigException
import de.fellows.app.assembly.commons.{AbstractAssemblyReference, AssemblyReference, SharedAssemblyReference}
import de.fellows.app.assemby.api.{Assembly, AssemblyService}
import de.fellows.app.camunda.api.{Camunda, CamundaContext}
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.price.api
import de.fellows.app.price.api.CapabilitiesApi.{
  CapabilityCheckFailure,
  CapabilityCheckRequest,
  CapabilityCheckResult,
  StackratePricingFailedField
}
import de.fellows.app.price.api._
import de.fellows.app.price.impl.PriceServiceImpl.decToApi
import de.fellows.app.price.impl.calculator.Calculator.CalculatorBuilder
import de.fellows.app.price.impl.calculator.{Calculator, CalculatorPricing}
import de.fellows.app.price.impl.camunda.{StackrateDmnEngine, StackrateDmnEngineConfiguration}
import de.fellows.app.price.impl.capability.CapabilityChecker
import de.fellows.app.price.impl.dmn.{DMNBuilder, OptionalVariable}
import de.fellows.app.price.impl.entity.capabilities.{
  CapabilitiesDeployment,
  CapabilitiesEntity,
  DeleteDeployedCapabilities,
  DeployCapabilities,
  GetDeployedCapabilities
}
import de.fellows.app.price.impl.entity.pricing._
import de.fellows.app.price.impl.entity.tables.{
  DecisionTableEntity,
  DeleteDecisionTable,
  DeployDecisionTable,
  GetDecisionTable,
  SelectVersion
}
import de.fellows.app.price.impl.read.{CapabilitiesDeploymentDTO, CapabilitiesRepository, DecisionTableRepository}
import de.fellows.app.security.AccessControlServiceComposition.authorizedString
import de.fellows.app.supplier.{
  BooleanCapability,
  ListCapability,
  NumericalCapability,
  StringCapability,
  SupplierService
}
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api._
import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import de.fellows.ems.pcb.model.PCB
import de.fellows.ems.pcb.model.PCBSpecification
import de.fellows.ems.pcb.api.{PCBService, PCBV2Api}
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.meta._
import de.fellows.utils.telemetry.KamonUtils
import de.fellows.utils.{FutureUtils, UUIDUtils}
import org.camunda.bpm.dmn.engine.impl.DefaultDmnEngine
import org.camunda.bpm.model.dmn.Dmn
import org.camunda.bpm.model.xml.ModelValidationException
import play.api.Logging
import play.api.libs.json.{JsArray, JsDefined, JsString, JsValue}

import java.time.Instant
import java.time.format.{DateTimeFormatter, DateTimeFormatterBuilder}
import java.util.concurrent.TimeUnit
import java.util.{Locale, UUID}
import scala.concurrent.duration.{Duration, DurationInt}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success}
import scala.xml.{Elem, XML}
import de.fellows.ems.pcb.api.specification.units.UnitConversions._
import de.fellows.utils.security.TokenContent
import de.fellows.utils.DebugUtils
import de.fellows.app.price.impl.calculator.BuiltDecisionTable
import de.fellows.app.price.impl.read.DMNTableCache
import de.fellows.app.price.impl.read.DMNTableCacheKey

class PriceServiceImpl(
    assService: AssemblyService,
    customerService: CustomerService,
    pcb: PCBService,
    panels: PanelService,
    system: ActorSystem,
    stacks: LayerstackService,
    eReg: PersistentEntityRegistry,
    loc: ServiceLocator,
    supplier: SupplierService,
    pricingRepo: PricingRepo,
    capabilitiesRepository: CapabilitiesRepository,
    decisionTableRepository: DecisionTableRepository,
    tableCache: DMNTableCache
)(implicit ec: ExecutionContext) extends PriceService with Logging
    with StackrateAPIImpl {
  implicit val actorSystem: ActorSystem = system

  def camunda(team: String) = {

    val camundaEndpoint =
      try
        config.getString(s"fellows.services.price.endpoint.${team}")
      catch {
        case e: ConfigException.Missing => config.getString(s"fellows.services.price.defaultendpoint")
      }

    new Camunda(CamundaContext(team, camundaEndpoint))
  }

  override def migrateFromCamunda(): ServiceCall[NotUsed, Done] =
    authorizedString { token =>
      s"*:*:*:*:*:*"
    } { (token, _) =>
      ServerServiceCall { _ =>
        pricingRepo.getPricingsForTeam(token.team)
          .map { s =>
            logger.info(s"migrate ${s.length} pricings")
            s.foreach { x =>
              logger.info(s"migrate pricing ${x}")
              eReg.refFor[PricingEntity](PricingEntity.id(token.team, x._1)).ask(GetDeployedPricing(token.team, x._1))
                .map(_.response)
                .map(_.map(_.pricings.foreach { dd =>
                  logger.info("---")
                  logger.info("---")
                  logger.info("---")

                  val camClient = camunda(token.team)

                  logger.info(s"migrate pricing table ${dd.id}")

                  val metajs = Await.result(
                    camClient.getDecision(dd.id.get)
                      .invoke(),
                    10 seconds
                  )

                  val allVersions = Await.result(
                    camClient.getDecisions(tenantIdIn = Some(dd.tenant.get), key = Some(dd.key))
                      .invoke(),
                    10 seconds
                  )

                  val versionDeployments = allVersions.value.map { v =>
                    val did = (v \ "deploymentId").as[String]
                    val vid = (v \ "id").as[String]
                    Await.result(camClient.getDeployment(did).invoke().map(vid -> _), 10 seconds)
                  }.toMap

                  val versionIds = allVersions.value.map { jsv =>
                    val version = jsv \ "version"
                    val id      = jsv \ "id"
                    version.get.as[Int] -> id.get.as[String]
                  }.sortBy(_._1)

                  val deployment =
                    Await.result(camClient.getDeployment((metajs \ "deploymentId").as[String]).invoke(), 10 seconds)
                  val meta = parseDeploymentInfo(metajs, deployment, allVersions, versionDeployments)

                  val tables = versionIds.flatMap { x =>
                    val xml = Await.result(
                      camClient.getDecisionXML(x._2)
                        .invoke(),
                      10 seconds
                    )

                    (xml \ "dmnXml" match {
                      case JsDefined(value: JsString) =>
                        convertXMLtoPricing(token.team, value, meta, Some(dd.key))

                      case _ => None
                    })
                  }

                  logger.info(s"FOUND PRICING ${tables}")

                  val entity = eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(token.team, dd.key))

                  entity.ask(GetDecisionTable(token.team, dd.key)).map { t =>
                    logger.warn(s"skip deployment of table ${dd} since it already exists")
                    Future.sequence(tables.map { dt =>
                      entity.ask(SelectVersion(token.team, dd.key, None, TimelineCommand.of(token)))
                    })
                  }
                    .recoverWith { _ =>
                      logger.info(s"deploying ${dd}...")
                      Future.sequence(tables.map { dt =>
                        entity.ask(DeployDecisionTable(
                          token.team,
                          dd.name.getOrElse(dd.key),
                          dd.key,
                          dt,
                          TimelineCommand.of(token)
                        ))
                      })
                    }

                }))
            }
          }
          .map(_ => Done)
      }
    }

  override def calculatePricesForShare(
      shareId: UUID,
      specification: UUID,
      panel: Option[String]
  ): ServiceCall[ExtendedPriceRequest, CalculatedPrices] =
    authorizedString { token =>
      s"prices:${token.team}:${token.team}:$shareId:prices:read"
    } { (token, _) =>
      ServerServiceCall { req =>
        doCalculatePrice(
          team = token.team,
          shareId = shareId,
          specification = specification,
          panel = panel,
          req = Seq(req),
          supplierIds = None
        ).map(_.head)
      }
    }

  private def doCalculatePrice(
      team: String,
      shareId: UUID,
      specification: UUID,
      panel: Option[String],
      req: Seq[ExtendedPriceRequest],
      supplierIds: Option[Set[UUID]]
  ): Future[Seq[CalculatedPrices]] =
    for {
      share <- assService._getAssemblyShare(team, shareId).invoke()
      prices <- _doCalculatePrices(
        team = team,
        ass = share.assembly,
        reference = share.toReference,
        version = share.share.ref.version,
        specification = specification,
        panelId = panel,
        reqs = req,
        customer = share.share.information.customer,
        supplierIds = supplierIds
      )
    } yield prices

  override def _calculatePricesForShare(
      team: String,
      share: UUID,
      specification: UUID,
      panel: Option[String]
  ): ServiceCall[FullPriceRequest, Seq[CalculatedPrices]] =
    ServerServiceCall { req =>
      doCalculatePrice(
        team = team,
        shareId = share,
        specification = specification,
        panel = panel,
        req = req.requests,
        supplierIds = Some(req.supplierIds.toSet)
      )
    }

  override def calculatePrices(
      assembly: UUID,
      version: UUID,
      specification: UUID,
      panelId: Option[String]
  ): ServiceCall[PriceRequest, CalculatedPrices] =
    authorizedString { token =>
      s"prices:${token.team}:${token.team}:$specification:prices:read"
    } { (token, _) =>
      ServerServiceCall { req =>
        // gather pcb info

        (for {
          assembly <- assService._getAssembly(token.team, assembly).invoke().map(_.assembly)
        } yield _doCalculatePrices(
          team = token.team,
          ass = assembly,
          reference = AssemblyReference(
            team = assembly.team,
            id = assembly.id,
            gid = Some(assembly.gid),
            version = version
          ),
          version = version,
          specification = specification,
          panelId = panelId,
          reqs = Seq(ExtendedPriceRequest(req)),
          customer = assembly.information.customer,
          supplierIds = None
        )).flatten.map(_.head)
      }
    }

  private def _doCalculatePrices(
      team: String,
      ass: Assembly,
      reference: AbstractAssemblyReference,
      version: UUID,
      specification: UUID,
      panelId: Option[String],
      reqs: Seq[ExtendedPriceRequest],
      customer: Option[UUID],
      supplierIds: Option[Set[UUID]]
  ): Future[Seq[CalculatedPrices]] = {
    val assemblyTeam = ass.team
    val assembly     = ass.id
    (for {
      allPricings <- pricingRepo.getPricingsForTeam(team)
      pricing <- getPricings(team, allPricings, supplierIds)
        .map(_.flatten.filter { pricing =>
          reference match {
            case r: AssemblyReference       => true
            case r: SharedAssemblyReference => pricing.pricing.enableApi.contains(true)
          }

        })
      customer <- FutureUtils.option(customer.map(cst =>
        customerService._getCustomer(team, cst, Some(true)).invoke()
      ))
      pcbv <- pcb._getPCBVersion(assemblyTeam, assembly, version).invoke()
      spec <- pcb._getSpecification(assemblyTeam, assembly, version, specification).invoke().map(_.head)
      panel <- FutureUtils.option(panelId.map(p =>
        panels._getCustomerPanel(assemblyTeam, assembly, version, p).invoke()
      ))

      workingPanels <- panels._getWorkingPanels(team).invoke()
      workingPanelUsage <- reference match {
        case ref: AssemblyReference =>
          panels._getWorkingPanelUsagesForVersion(ref.team, ref.id, ref.version).invoke()
        case sharefRef: SharedAssemblyReference =>
          panels._getWorkingPanelUsagesForShare(sharefRef.team, sharefRef.id).invoke()
      }

      transientWorkingPanelUsage <- Future.sequence(
        reqs.flatMap(_.panel.map { panelhelper =>
          panels._getTransientWorkingPanelUsage(team).invoke(panelhelper).map(panelhelper -> _)
        })
      ).map(_.toMap)

      stack <-
        stacks._getPCBLayerstack(team, reference.getReferenceIdentifier, Some(true)).invoke()
          .map(Some(_))
          .recover(_ => None)
      selectedTechs <- supplier._getSelectedTechnologies(team, assembly, version).invoke()
      technologies  <- supplier._getTechnologies(team).invoke()
    } yield {
      val lstack = stack.flatMap(_.selected)

      val tech = technologies.filter(t => selectedTechs.exists(t2 => t2.technology == t.id))

      reqs.map { req =>
        val calcBuilder = new CalculatorBuilder(req.request)

        lstack.map(calcBuilder.withLayerstack)

        calcBuilder
          .withPCBMeta(pcbv.meta.getOrElse(MetaInfo()))
          .withSpecification(spec.settings)
          .withSpecificationUser(spec.user)

        lstack.map(s => calcBuilder.withLayerstackMeta(s.definition.metaInfo))

        calcBuilder
          .withTechnologies(tech)
          .withCustomer(customer)
          .withPricings(pricing)

        logger.info(s"calculate with panel helper: ${req.panel}")

        req.panel match {
          case Some(value) =>
            calcBuilder.withCustomerPanel(value)
            transientWorkingPanelUsage
              .get(value)
              .foreach { wpu =>
                val validWpus = wpu.filter(_.customerPanels > 0)
                if (validWpus.nonEmpty) {
                  logger.info(s"Using working panel usage: ${validWpus}")
                  val maxPanel = validWpus.maxBy(_.panelYield)

                  calcBuilder.withWorkingPanelUsage(WorkingPanelUsageHelper(
                    workingPanel = Some(maxPanel.workingPanel),
                    customerPanels = maxPanel.customerPanels,
                    customerBoards = maxPanel.customerBoards,
                    panelYield = maxPanel.panelYield
                  ))
                  workingPanels.find(_.id.contains(maxPanel.workingPanel)).foreach(calcBuilder.withWorkingPanel)
                }
              }
          case None =>
            panel.foreach { p =>
              calcBuilder.withCustomerPanel(p)

              workingPanelUsage
                .filter(x =>
                  p.id.contains(x.customerPanel)
                ) // take the working panel usages for this specific customer panel
                .flatMap(usage =>
                  usage.workingPanels.find(wp => usage.selectedWorkingPanel.contains(wp.workingPanel))
                ) // get the selected working panel
                .filter(_.customerPanels > 0)
                .foreach { wpu =>
                  logger.info(s"Using working panel: ${wpu}")
                  calcBuilder.withWorkingPanel(wpu)
                  workingPanels
                  .find(_.id.contains(wpu.workingPanel))
                  .foreach(calcBuilder.withWorkingPanel)
                }
            }
        }

        val calculator = calcBuilder
          .build

        val r =
          calculator.calculate(PriceServiceImpl.engine)

        r.copy(
          request = r.request.copy(
            assembly = Some(reference),
            specification = Some(spec.id),
            panel = panelId
          )
        )
      }

    })
  }

  private def getAllTables(team: String, deployments: Seq[(String, Int)]): Future[Seq[BuiltDecisionTable]] = {

    val futures: Seq[Future[BuiltDecisionTable]] = deployments.map { dd =>
      tableCache.getOrElseUpdate(
        DMNTableCacheKey(team, dd._1, dd._2),
        decisionTableRepository.getTable(team, dd._1)
          .map {
            case Some(Failure(e)) =>
              throw new TransportException(TransportErrorCode.NotFound, s"Table ${dd} not found").initCause(e)
            case Some(Success(tableVersion)) =>
              val id       = UUIDUtils.randomString()
              val instance = DMNBuilder.from(Seq(tableVersion.table), Map((tableVersion.table -> id))).result()
              BuiltDecisionTable(team, id, instance)
            case None => throw new TransportException(TransportErrorCode.NotFound, s"Table ${dd} not found")
          }
      )
    }

    Future.sequence(futures)
  }

  private def getPricings(
      team: String,
      allPricings: Seq[(UUID, String)],
      supplierIds: Option[Set[UUID]] // filter pricings by supplier id, if set. otherwise, return all
  ): Future[Seq[Option[CalculatorPricing]]] = {
    val futures = allPricings.map { x =>
      for {
        pricing <-
          eReg.refFor[PricingEntity](PricingEntity.id(team, x._1)).ask(GetDeployedPricing(team, x._1)).map(
            _.response.filter(response => supplierIds.fold(true)(_.contains(response.supplier)))
          )

        withVersions <-
          pricing.map { p =>
            val deployments = p.pricings.map(dep => dep.key)
            decisionTableRepository.getVersions(team, deployments)
          }.getOrElse(Future.successful(Seq()))

        tables <-
          getAllTables(team, withVersions)

      } yield pricing.map { p =>
        CalculatorPricing(
          p,
          tables
        )
      }
    }

    Future.sequence(futures)
  }

  override def calculateApiPrices(): ServiceCall[ExtendedPriceRequest, CalculatedPrices] =
    authorizedString { token =>
      s"prices:${token.team}:${token.team}:*:prices:read"
    } { (token, _) =>
      ServerServiceCall { exreq =>
        for {
          technologies <- supplier._getTechnologies(token.team).invoke()
          panel        <- panels._getWorkingPanels(token.team).invoke()
          allPricings  <- pricingRepo.getPricingsForTeam(token.team)
          pricing <-
            getPricings(
              team = token.team,
              allPricings = allPricings,
              supplierIds = None
            )
        } yield {
          val b = new CalculatorBuilder(exreq.request)
            .withPCBMeta(exreq.pcb.getOrElse(MetaInfo()))
            .withSpecification(exreq.specification.getOrElse(MetaInfo()))
            .withSpecificationUser(exreq.specificationUser)
            .withLayerstackMeta(exreq.layerstack.getOrElse(MetaInfo()))
            .withPricings(pricing.flatten)

          exreq.panel.foreach(panel => b.withCustomerPanel(panel))

          b
            .build
            .calculate(PriceServiceImpl.engine)
        }
      }

    }

  def convertXMLtoPricing(
      team: String,
      value: JsString,
      info: DeploymentInfo,
      decisionIDFilter: Option[String] = None
  ): Option[DecisionTable] = {
    val xml    = XML.loadString(value.value)
    val tables = PriceServiceImpl.parseXML(team, Some(info), xml, decisionIDFilter)

    tables.headOption
  }

  def toPriceId(team: String, str: String): Option[UUID] =
    Await.result(
      try
        Future.successful(Some(UUID.fromString(str)))
      catch {
        case e: IllegalArgumentException =>
          pricingRepo.getPricing(team, str)
      },
      Duration(10, TimeUnit.SECONDS)
    )

  lazy val camundaDate = new DateTimeFormatterBuilder()
    .parseCaseInsensitive
    .append(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
    .parseLenient()
    .appendOffset("+HHmm", "Z")
    .parseStrict()
    .toFormatter(Locale.getDefault(Locale.Category.FORMAT))

  def parseDeploymentInfo(
      metajs: JsValue,
      deployment: JsValue,
      allVersions: JsArray,
      versionDeployments: Map[String, JsValue]
  ): DeploymentInfo = {

    val versions = allVersions.value.map(_.as[DeployedVersion]).sortBy(_.version).toSeq
      .map(dv =>
        dv.copy(
          time = versionDeployments
            .get(dv.id)
            .map(_ \ "deploymentTime").map(_.as[String]).map(x => Instant.from(camundaDate.parse(x)))
        )
      )

    DeploymentInfo(
      (metajs \ "id").as[String],
      (metajs \ "version").as[Int],
      (metajs \ "versionTag").asOpt[String],
      ((deployment \ "deploymentTime").asOpt[String]).map(x => Instant.from(camundaDate.parse(x))),
      allVersions = Some(versions)
    )
  }

  override def setDeployedTableVersion(
      nameOrID: String,
      table: String,
      version: Int
  ): ServiceCall[NotUsed, VersionedDecisionTable] = {
    var oPID: Option[UUID] = None
    authorizedString { token =>
      oPID = toPriceId(token.team, nameOrID)
      if (oPID.isEmpty) {
        throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
      }
      s"pricetable:${token.team}:${token.team}:${oPID.get}:prices:write"
    } { (token, _) =>
      ServerServiceCall { x =>
        val entity = eReg.refFor[PricingEntity](PricingEntity.id(token.team, oPID.get))

        entity.ask(GetDeployedPricing(token.team, oPID.get))
          .map(_.response)
          .flatMap {
            case None => throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
            case Some(dps) =>
              dps.pricings.find(_.key.toLowerCase == table.toLowerCase) match {
                case None => throw new TransportException(TransportErrorCode.NotFound, "Pricing table not found")
                case Some(dp) =>
                  eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(dp.team, dp.key)).ask(SelectVersion(
                    dp.team,
                    dp.key,
                    Some(version),
                    TimelineCommand.of(token)
                  ))
              }
          }
      }
    }
  }

  override def getDeployedTableVersion(
      nameOrID: String,
      table: String,
      version: Int
  ): ServiceCall[NotUsed, DecisionTable] = {
    var oPID: Option[UUID] = None
    authorizedString { token =>
      oPID = toPriceId(token.team, nameOrID)
      if (oPID.isEmpty) {
        throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
      }
      s"pricetable:${token.team}:${token.team}:${oPID.get}:prices:read"
    } { (token, _) =>
      ServerServiceCall { x =>
        _doGetVersion(oPID.get, token.team, table, Some(version))
      }
    }
  }

  private def _doGetVersion(deplID: UUID, team: String, table: String, version: Option[Int]): Future[DecisionTable] = {
    val entity = eReg.refFor[PricingEntity](PricingEntity.id(team, deplID))

    entity.ask(GetDeployedPricing(team, deplID))
      .map(_.response)
      .flatMap { dpricings =>
        FutureUtils.option(dpricings.map(_.pricings).getOrElse(Seq()).find(_.key.toLowerCase == table.toLowerCase).map {
          dp =>
            eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(team, dp.key))
              .ask(GetDecisionTable(team, dp.key)).map(r => r.versions(version.getOrElse(r.currentVersion)).table)
        }).map(_.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Pricing Table not found")))
      }
  }

  override def deleteCapabilityTable(supplier: UUID, table: String): ServiceCall[NotUsed, Done] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:${supplier}:prices:read"
    } { (token, _) =>
      ServerServiceCall { x =>
        val entity = eReg.refFor[CapabilitiesEntity](CapabilitiesEntity.id(token.getTeam, supplier))

        entity.ask(GetDeployedCapabilities(token.team, supplier))
          .map(_.response)
          .flatMap {
            case None => throw new TransportException(TransportErrorCode.NotFound, "Capabilities not found")
            case Some(dps) =>
              val depltable = dps.capabilities
                .find(_.key == table)

              val filteredTables = dps.capabilities.filter(_.key != table)

              depltable match {
                case Some(value) =>
                  for {
                    b <- eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(value.team, value.key)).ask(
                      DeleteDecisionTable(value.team, value.key)
                    )
                    depl <- entity.ask(
                      DeployCapabilities(
                        team = dps.team,
                        supplier = dps.supplier,
                        d = filteredTables
                      )
                    )
                    tables <- Future.sequence(filteredTables.map { dd =>
                      eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(dd.team, dd.key)).ask(GetDecisionTable(
                        dd.team,
                        dd.key
                      ))
                    })
                  } yield Done

                case None => throw new TransportException(TransportErrorCode.NotFound, s"Table $table not found")
              }

          }
      }
    }

  override def deleteDeployedTable(nameOrID: String, table: String): ServiceCall[NotUsed, Pricing] = {
    var oPID: Option[UUID] = None
    authorizedString { token =>
      oPID = toPriceId(token.team, nameOrID)
      if (oPID.isEmpty) {
        throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
      }
      s"pricetable:${token.team}:${token.team}:${oPID.get}:prices:read"
    } { (token, _) =>
      ServerServiceCall { x =>
        val entity = eReg.refFor[PricingEntity](PricingEntity.id(token.team, oPID.get))
        entity.ask(GetDeployedPricing(token.team, oPID.get))
          .map(_.response)
          .flatMap {
            case None => throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
            case Some(dps) =>
              if (!dps.pricings.exists(_.key == table)) {
                throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
              }

              val depltable = dps.pricings
                .find(_.key == table)

              val filteredTables = dps.pricings.filter(_.key != table)

              depltable match {
                case Some(value) =>
                  for {
                    b <- eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(value.team, value.key)).ask(
                      DeleteDecisionTable(value.team, value.key)
                    )
                    depl <- entity.ask(
                      DeployPricing(
                        team = dps.team,
                        name = dps.name,
                        supplier = dps.supplier,
                        id = dps.id,
                        enableApi = dps.enableApi,
                        currency = dps.currency,
                        d = filteredTables
                      )
                    )
                    tables <- Future.sequence(filteredTables.map { dd =>
                      eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(dd.team, dd.key)).ask(GetDecisionTable(
                        dd.team,
                        dd.key
                      ))
                    })
                  } yield Pricing(
                    dps.name,
                    Some(dps.id),
                    depl.supplier,
                    tables,
                    depl.enableApi.getOrElse(false),
                    depl.currency
                  )

                case None => throw new TransportException(TransportErrorCode.NotFound, s"Table $table not found")
              }

          }
      }
    }
  }

  override def getDeployedTableCurrentVersion(nameOrID: String, table: String): ServiceCall[NotUsed, DecisionTable] = {
    var oPID: Option[UUID] = None
    authorizedString { token =>
      oPID = toPriceId(token.team, nameOrID)
      if (oPID.isEmpty) {
        throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
      }
      s"pricetable:${token.team}:${token.team}:${oPID.get}:prices:read"
    } { (token, _) =>
      ServerServiceCall { x =>
        _doGetVersion(oPID.get, token.team, table, None)
      }
    }
  }

  override def deleteDeployedPricing(nameOrID: String): ServiceCall[NotUsed, Done] = {
    var oPID: Option[UUID] = None
    authorizedString { token =>
      oPID = toPriceId(token.team, nameOrID)
      if (oPID.isEmpty) {
        throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
      }
      s"pricetable:${token.team}:${token.team}:${oPID.get}:prices:write"
    } { (token, _) =>
      ServerServiceCall { x =>
        val entity = eReg.refFor[PricingEntity](PricingEntity.id(token.team, oPID.get))
        entity.ask(GetDeployedPricing(token.team, oPID.get))
          .map(_.response)
          .flatMap {
            case None => throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
            case Some(dps) =>
              val tables = dps.pricings
              // collect all deployment ids

              for {
                _ <- Future.sequence(tables.map(t =>
                  eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(t.team, t.key)).ask(DeleteDecisionTable(
                    t.team,
                    t.key
                  ))
                ))
                _ <- entity.ask(DeleteDeployedPricing(token.team, oPID.get))
              } yield Done
          }
      }
    }
  }

  override def exportDeployedPricing(nameOrID: String): ServiceCall[NotUsed, PricingDeploymentDescriptor] =
    convertPricing(
      nameOrID,
      (p, t) =>
        PricingDeploymentDescriptor(
          name = p.name,
          id = None,
          supplier = p.supplier,
          enableApi = p.enableApi,
          currency = p.currency,
          tables = t.map { vtable =>
            val table = vtable.getCurrentVersion
            DecisionTable(
              team = Some(p.team),
              name = table.table.name,
              inputs = table.table.inputs,
              outputs = table.table.outputs,
              rules = table.table.rules,
              hitpolicy = table.table.hitpolicy
            )
          }
        )
    )
  override def getDeployedPricing(nameOrID: String): ServiceCall[NotUsed, Pricing] =
    convertPricing(
      nameOrID,
      (_p, t) =>
        Pricing(
          _p.name,
          Some(_p.id),
          _p.supplier,
          t,
          _p.enableApi.getOrElse(false),
          _p.currency
        )
    )

  private def convertPricing[T](
      nameOrID: String,
      c: (PricingDeployment, Seq[VersionedDecisionTable]) => T
  ): ServiceCall[NotUsed, T] = {
    var oPID: Option[UUID] = None
    authorizedString { token =>
      oPID = toPriceId(token.team, nameOrID)
      if (oPID.isEmpty) {
        throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
      }
      s"pricetable:${token.team}:${token.team}:${oPID.get}:prices:read"
    } { (token, _) =>
      ServerServiceCall { x =>
        doConvertPricing(oPID.get, token.team)(c)
      }
    }
  }

  private def doConvertPricing[T](
      oPID: UUID,
      team: String
  )(c: (PricingDeployment, Seq[VersionedDecisionTable]) => T) = {
    val entity = eReg.refFor[PricingEntity](PricingEntity.id(team, oPID))

    for {
      p <- entity.ask(GetDeployedPricing(team, oPID)).map(_.response)
      t <- Future.sequence(p.map(_.pricings.map { pr =>
        eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(pr.team, pr.key)).ask(GetDecisionTable(
          pr.team,
          pr.key
        ))
      }).getOrElse(Seq()))
    } yield {
      val _p = p.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Pricing not found"))
      c(_p, t)
    }
  }

  def getVarType(varName: String, team: String): Future[PricingVariable] = {
    varName match {
//      case "specification_rohs" => Future.successful(PricingVariable(varName, "BOOLEAN"))
//      case "specification_etest" => Future.successful(PricingVariable(varName, "BOOLEAN"))
      case "order_cp_area"                => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "order_wp_area"                => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "order_batch_size_sqmm"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "order_batch_size_sqcm"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "order_batch_size_sqdm"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "order_batch_size_sqm"         => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "order_batch_size"             => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "order_batch_weight_g"         => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "order_batch_weight_kg"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_width"                    => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_height"                   => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_area"                     => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_trace_width"              => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_copper_clearance"         => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_outline_clearance"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_nph_min_size"             => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_nph_max_size"             => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_nph_max_distance"         => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_ph_count"                 => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_ph_tool_count"            => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_ph_min_size"              => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_ph_max_size"              => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_ph_annular_ring"          => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_ph_annular_ring_breakout" => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_drill_count"              => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_drill_max_size"           => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "pcb_drill_min_size"           => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_tolerance"         => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_materialtg"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_pressedthickness"  => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_totalheight"       => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_targetheight"      => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_outercopperheight" => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_innercopperheight" => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_area_price"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_unit_price"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "layerstack_weight"            => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "panel_customer_width"         => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "panel_customer_height"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "panel_customer_weight"        => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "panel_customer_area"          => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "panel_panel_yield"            => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "price_fix"                    => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "price_flex"                   => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "price_nre"                    => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "price_unit"                   => Future.successful(PricingVariable(varName, "DOUBLE"))
      case "order_quantity"               => Future.successful(PricingVariable(varName, "INTEGER"))
      case "order_delivery"               => Future.successful(PricingVariable(varName, "INTEGER"))
      case "order_working_panels"         => Future.successful(PricingVariable(varName, "INTEGER"))
      case "pcb_cucount"                  => Future.successful(PricingVariable(varName, "INTEGER"))
      case "pcb_nph_count"                => Future.successful(PricingVariable(varName, "INTEGER"))
      case "pcb_nph_tool_count"           => Future.successful(PricingVariable(varName, "INTEGER"))
//      case "specification_ipc_2221_type" => Future.successful(PricingVariable(varName, "INTEGER"))
//      case "specification_ipc_600_class" => Future.successful(PricingVariable(varName, "INTEGER"))
      case "layerstack_cucount"             => Future.successful(PricingVariable(varName, "INTEGER"))
      case "panel_customer_panels"          => Future.successful(PricingVariable(varName, "INTEGER"))
      case "panel_customer_boards"          => Future.successful(PricingVariable(varName, "INTEGER"))
      case "panel_boards_on_customer_panel" => Future.successful(PricingVariable(varName, "INTEGER"))
      case "technology_price_category"      => Future.successful(PricingVariable(varName, "INTEGER"))
      case "order_unknown"                  => Future.successful(PricingVariable(varName, "STRING"))
//      case "specification_color" => Future.successful(PricingVariable(varName, "STRING"))
//      case "specification_soldermask_sides" => Future.successful(PricingVariable(varName, "STRING"))
//      case "specification_finish" => Future.successful(PricingVariable(varName, "STRING"))
//      case "specification_silkscreen_sides" => Future.successful(PricingVariable(varName, "STRING"))
//      case "specification_silkscreen_color" => Future.successful(PricingVariable(varName, "STRING"))
//      case "specification_ul_material" => Future.successful(PricingVariable(varName, "STRING"))
//      case "specification_standards" => Future.successful(PricingVariable(varName, "STRING"))
      case "layerstack_stackType"        => Future.successful(PricingVariable(varName, "STRING"))
      case "customer_name"               => Future.successful(PricingVariable(varName, "STRING"))
      case "customer_billing_country"    => Future.successful(PricingVariable(varName, "STRING"))
      case "customer_billing_continent"  => Future.successful(PricingVariable(varName, "STRING"))
      case "customer_shipping_country"   => Future.successful(PricingVariable(varName, "STRING"))
      case "customer_shipping_continent" => Future.successful(PricingVariable(varName, "STRING"))
      case "technology_name"             => Future.successful(PricingVariable(varName, "STRING"))
      case _ =>
        supplier._getTechnologies(team).invoke()
          .map(technologies =>
            technologies.flatMap(technology =>
              technology.capabilities.find(capability =>
                "specification_" + capability.name == varName || "user_" + capability.name == varName
              )
            ).headOption
          ).map { cap =>
            if (cap.isDefined) {
              cap.get match {
                case n: NumericalCapability =>
                  PricingVariable(varName, "DOUBLE", label = n.label, min = n.min, max = n.max)
                case n: ListCapability =>
                  PricingVariable(varName, "LIST", label = n.label, options = n.allowed)
                case n: BooleanCapability =>
                  PricingVariable(varName, "BOOLEAN", label = n.label)
                case n: StringCapability =>
                  PricingVariable(varName, "STRING", label = n.label)
                case _ =>
                  PricingVariable(varName, "UNKNOWN")
              }
            } else {
              PricingVariable(varName, "UNKNOWN")
            }
          }
    }
  }

  val functionNames = Set(
    "decimal",
    "floor",
    "ceiling",
    "abs",
    "modulo",
    "sqrt",
    "log",
    "exp",
    "odd",
    "even",
    "list",
    "contains",
    "count",
    "min",
    "max",
    "sum",
    "product",
    "mean",
    "median",
    "stddev",
    "mode",
    "and",
    "all",
    "or",
    "any",
    "sublist",
    "append",
    "concatenate",
    "insert",
    "before",
    "remove",
    "reverse",
    "index of",
    "union",
    "distinct values",
    "flatten",
    "sort",
    "precedes",
    "not",
    "true",
    "false",
    "if",
    "else",
    "then",
    "number",
    "string",
    "null"
  )
  def findVars(value: String): Seq[String] =
    value.replaceAll("\"[^\"]*\"", "")
      .split("[ ()]")
      .distinct
      .filter(part => part.nonEmpty && part.matches("[a-zA-Z_]+[\\w]*") && !(functionNames contains part))

  override def getPricingVariables(nameOrID: String): ServiceCall[NotUsed, Seq[PricingVariable]] = {
    var oPID: Option[UUID] = None
    authorizedString { token =>
      oPID = toPriceId(token.team, nameOrID)
      if (oPID.isEmpty) {
        throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
      }
      s"pricetable:${token.team}:${token.team}:${oPID.get}:prices:read"
    } { (token, _) =>
      ServerServiceCall { x =>
        val entity = eReg.refFor[PricingEntity](PricingEntity.id(token.team, oPID.get))

        val priceVars = for {
          p <- entity.ask(GetDeployedPricing(token.team, oPID.get)).map(_.response)
          t <- Future.sequence(p.map(_.pricings.map { pr =>
            eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(pr.team, pr.key)).ask(GetDecisionTable(
              pr.team,
              pr.key
            ))
          }).getOrElse(Seq()))
        } yield {
          p.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Pricing not found"))

          Future.sequence(t.flatMap { dnt =>
            val currentTable = dnt.getCurrentVersion.table;

            currentTable.inputs.map(input => getVarType(input.name, token.team)) ++
              currentTable.rules.flatMap { rule =>
                rule.inputEntries.flatMap { inputEntry =>
                  findVars(inputEntry.value).map(variable => getVarType(variable, token.team))
                } ++
                  rule.outputEntries.flatMap { outputEntry =>
                    findVars(outputEntry.value).map(variable => getVarType(variable, token.team))
                  }
              }
          })
        }
        priceVars.flatten.map(_.distinctBy(p => p.name))
      }
    }
  }

  override def getPricingsForTeam(): ServiceCall[NotUsed, Seq[DeployedPricing]] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:*:prices:read"
    } { (token, _) =>
      ServerServiceCall { p =>
        _doGetPricingsForTeam(token.getTeam)
      }
    }
  override def _getPricingsForTeam(team: String): ServiceCall[NotUsed, Seq[DeployedPricing]] =
    ServerServiceCall { p =>
      _doGetPricingsForTeam(team)
    }

  override def _createPricingForTeam(team: String): ServiceCall[PricingDeploymentDescriptor, Pricing] =
    ServerServiceCall { pricing =>
      _doDeployPricing(
        team = team,
        pricing = pricing,
        token = None
      )
    }

  private def _doGetPricingsForTeam(team: String) =
    pricingRepo.getPricingsForTeam(team).flatMap { prs =>
      Future.sequence(prs.map { pr =>
        val entity = eReg.refFor[PricingEntity](PricingEntity.id(team, pr._1))
        entity.ask(GetDeployedPricing(team, pr._1))
          .map(_.response)
          .filter(_.isDefined)
          .map(_.get)
          .map(toApi(team))
      })
    }

  override def deleteCapabilitiesForSupplier(supplierId: UUID): ServiceCall[NotUsed, Done] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:*:prices:write"
    } { (token, _) =>
      ServerServiceCall { p =>
        val entity = eReg.refFor[CapabilitiesEntity](CapabilitiesEntity.id(token.getTeam, supplierId))

        for {
          cap <- entity.ask(GetDeployedCapabilities(token.team, supplierId)).map(_.response)
          _ <- Future.sequence(
            cap.toSeq.flatMap(c => c.capabilities).map { dd =>
              eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(dd.team, dd.key)).ask(DeleteDecisionTable(
                dd.team,
                dd.key
              ))
            }
          )
          _ <- entity.ask(DeleteDeployedCapabilities(token.getTeam, supplierId))
        } yield Done
      }
    }

  override def getCapabilitiesForTeam(): ServiceCall[NotUsed, Seq[DeployedCapabilities]] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:*:prices:read"
    } { (token, _) =>
      ServerServiceCall { p =>
        for {
          suppliers <- supplier._getSuppliers(token.team).invoke()
          capabilities <-
            Future.sequence(suppliers.flatMap(_.id).map(supp => _doGetCapabilitiesForSupplier(token.getTeam, supp)))

        } yield capabilities.flatten
      }
    }

  override def getCapabilitiesForSupplier(supplier: UUID): ServiceCall[NotUsed, DeployedCapabilities] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:*:prices:read"
    } { (token, _) =>
      ServerServiceCall { p =>
        _doGetCapabilitiesForSupplier(token.getTeam, supplier)
          .map(_.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Capabilities not found")))
      }
    }

  override def _getCapabilitiesForSupplier(team: String, supplier: UUID): ServiceCall[NotUsed, DeployedCapabilities] =
    ServerServiceCall { p =>
      _doGetCapabilitiesForSupplier(team, supplier)
        .map(_.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Capabilities not found")))
    }

  override def exportDeployedCapabilities(supplier: UUID): ServiceCall[NotUsed, api.CapabilitiesDeployment] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:*:prices:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        val entity = eReg.refFor[CapabilitiesEntity](CapabilitiesEntity.id(token.getTeam, supplier))

        for {
          cap <- capabilitiesRepository.getCapabilityDeployment(token.team, supplier)

          t <- getTables(token.team, cap)
        } yield cap.map { c =>
          api.CapabilitiesDeployment(
            supplier = c.supplier,
            tables = t.map { table =>
              api.DecisionTable(
                team = Some(c.team),
                name = table.table.name,
                inputs = table.table.inputs,
                outputs = table.table.outputs,
                rules = table.table.rules,
                hitpolicy = table.table.hitpolicy
              )
            }
          )
        }.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Capabilities not found"))
      }
    }

  def _doGetCapabilitiesForSupplier(team: String, supplier: UUID): Future[Option[DeployedCapabilities]] = {
    val entity = eReg.refFor[CapabilitiesEntity](CapabilitiesEntity.id(team, supplier))

    for {
      cap <- entity.ask(GetDeployedCapabilities(team, supplier)).map(_.response)

      t <- getTablesWithVersions(cap)
    } yield cap.map(capabilitiesToApi(team)(_, t))
  }

  def _doGetCachedCapabilitiesForSupplier(
      team: String,
      supplier: UUID
  ): Future[Option[(CapabilitiesDeployment, Seq[BuiltDecisionTable])]] =
    for {
      cap <- capabilitiesRepository.getCapabilityDeployment(team, supplier)
      withVersions <-
        cap.map { c =>
          val tables = c.capabilities.map(_.key)
          decisionTableRepository.getVersions(team, tables)
        }.getOrElse(Future.successful(Seq()))

      t <- getAllTables(team, withVersions)
    } yield cap.map(dto =>
      CapabilitiesDeployment(
        team = dto.team,
        supplier = dto.supplier,
        capabilities = dto.capabilities.map { c =>
          DecisionDeployment(team, c.key)
        }
      ) -> t
    )

  private def getTablesWithVersions(
      cap: Option[CapabilitiesDeployment]
  ): Future[Seq[VersionedDecisionTable]] =
    Future.sequence(cap.map(_.capabilities.map { pr =>
      eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(pr.team, pr.key)).ask(GetDecisionTable(
        pr.team,
        pr.key
      ))
    }).getOrElse(Seq()))

  private def getTables(team: String, cap: Option[CapabilitiesDeploymentDTO]): Future[Seq[DecisionTableVersion]] =
    Future.sequence(
      cap.toSeq.flatMap(_.capabilities.map { pr =>
        decisionTableRepository.getTable(team, pr.key)
      })
    ).map(_.flatten.collect {
      case Success(v) => v
    }.toSeq)

  override def changePricing(nameOrID: String): ServiceCall[PricingDescriptor, DeployedPricing] = {
    var oPID: Option[UUID] = None

    authorizedString { token =>
      oPID = toPriceId(token.team, nameOrID)
      if (oPID.isEmpty) {
        throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
      }
      s"pricetable:${token.team}:${token.team}:${oPID.get}:prices:write"
    } { (token, _) =>
      ServerServiceCall { p =>
        val id     = oPID.get
        val entity = eReg.refFor[PricingEntity](PricingEntity.id(token.team, oPID.get))

        for {
          response <- entity.ask(GetDeployedPricing(token.team, oPID.get))

          dp = response.response.getOrElse(
            throw new TransportException(TransportErrorCode.NotFound, "Pricing not found")
          )

          change = ChangeDeployment(
            team = token.team,
            id = id,
            name = Some(p.name),
            supplier = Some(p.supplier),
            enableApi = p.enableApi,
            currency = p.currency,
            p.tables.map(reorder(dp.pricings, _))
          )

          updated <- entity.ask(change)
        } yield {
          val pricings = updated.pricings.map(decToApi(token.team))

          DeployedPricing(
            name = updated.name,
            id = id,
            supplier = updated.supplier,
            pricings = pricings,
            enableApi = updated.enableApi.getOrElse(false),
            currency = updated.currency
          )
        }
      }
    }
  }

  def reorder(pdeps: Seq[DecisionDeployment], tables: Seq[DeployedDecision]): scala.Seq[DecisionDeployment] =
    pdeps.sortBy { x =>
      tables.map(_.key).indexOf(x.key)
    }

  def order(pdeps: Seq[DecisionDeployment], tables: Seq[DecisionTable]): scala.Seq[DecisionDeployment] =
    pdeps.sortBy(x => tables.indexWhere(_.name == x.key))

  override def addCapabilityTable(supplier: UUID): ServiceCall[DecisionTable, Capabilities] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:${supplier}:prices:write"
    } { (token, _) =>
      ServerServiceCall { dt =>
        validateModel(dt)
        val id     = UUIDUtils.createShort()
        val entity = eReg.refFor[CapabilitiesEntity](CapabilitiesEntity.id(token.getTeam, supplier))
        for {
          capabilities <- entity.ask(GetDeployedCapabilities(token.getTeam, supplier))
            .map(_.response)
            .map(_.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Capabilities not found")))

          oldTables <- Future.sequence(capabilities.capabilities.map { dd =>
            eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(dd.team, dd.key)).ask(GetDecisionTable(
              dd.team,
              dd.key
            ))
          })

          _ <- Future.successful(oldTables.exists(_.name == dt.name)).map { b =>
            if (b) {
              throw new TransportException(TransportErrorCode.BadRequest, "Table with this name exists")
            }
          }

          pr <-
            eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(token.team, id)).ask(DeployDecisionTable(
              token.team,
              dt.name,
              id,
              dt.copy(team = Some(token.team)),
              TimelineCommand.of(token)
            ))

          depl <- entity.ask(
            DeployCapabilities(
              team = token.team,
              supplier = capabilities.supplier,
              d = capabilities.capabilities :+ DecisionDeployment(pr.team, pr.id)
            )
          )

        } yield Capabilities(
          supplier = supplier,
          tables = oldTables :+ pr
        )
      }
    }

  override def addTable(pricing: UUID): ServiceCall[DecisionTable, Pricing] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:${pricing}:prices:write"
    } { (token, _) =>
      ServerServiceCall { dt =>
        validateModel(dt)
        val id            = UUIDUtils.createShort()
        val pricingEntity = eReg.refFor[PricingEntity](PricingEntity.id(token.team, pricing))
        for {

          pricing <- pricingEntity
            .ask(GetDeployedPricing(token.team, pricing)).map(_.response.get)

          oldTables <- Future.sequence(pricing.pricings.map { dd =>
            eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(dd.team, dd.key)).ask(GetDecisionTable(
              dd.team,
              dd.key
            ))
          })

          _ <- Future.successful(oldTables.exists(_.name == dt.name)).map { b =>
            if (b) {
              throw new TransportException(TransportErrorCode.BadRequest, "Table with this name exists")
            }
          }

          pr <-
            eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(token.team, id)).ask(DeployDecisionTable(
              token.team,
              dt.name,
              id,
              dt.copy(team = Some(token.team)),
              TimelineCommand.of(token)
            ))

          depl <- pricingEntity.ask(
            DeployPricing(
              team = token.team,
              name = pricing.name,
              supplier = pricing.supplier,
              id = pricing.id,
              enableApi = pricing.enableApi,
              currency = pricing.currency,
              d = pricing.pricings :+ DecisionDeployment(pr.team, pr.id)
            )
          )
        } yield Pricing(
          pricing.name,
          Some(pricing.id),
          pricing.supplier,
          oldTables :+ pr,
          pricing.enableApi.getOrElse(false),
          pricing.currency
        )
      }
    }

  override def deployTable(pricing: UUID, table: String): ServiceCall[DecisionTable, VersionedDecisionTable] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:${pricing}:prices:write"
    } { (token, _) =>
      ServerServiceCall { dt =>
        validateModel(dt)
        val id = UUIDUtils.createShort() // will only be used if the table does not exist yet
        eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(token.team, table)).ask(DeployDecisionTable(
          token.team,
          table,
          id,
          dt.copy(team = Some(token.team)),
          TimelineCommand.of(token)
        ))

      }

    }

  override def deployCapabilityTable(
      supplier: UUID,
      table: String
  ): ServiceCall[DecisionTable, VersionedDecisionTable] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:${supplier}:prices:write"
    } { (token, _) =>
      ServerServiceCall { dt =>
        validateModel(dt)
        val id = UUIDUtils.createShort()
        eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(token.team, table)).ask(DeployDecisionTable(
          token.team,
          table,
          id,
          dt.copy(team = Some(token.team)),
          TimelineCommand.of(token)
        ))

      }

    }

  private def validateModel(dt: DecisionTable) = {
    val m: Map[DecisionTable, String] = Map(dt -> UUIDUtils.randomString())
    val builder                       = DMNBuilder.from(Seq(dt), m)
    val model                         = builder.result()
    try
      Dmn.validateModel(model)
    catch {
      case e: ModelValidationException =>
        logger.error(s"${dt}")
        logger.error(s"${model}")
        throw e
    }
  }

  def uniqueName(names: Seq[String], newName: String): String = {
    var test = newName
    var i    = 1
    while (names.contains(test)) {
      test = s"$newName $i"
      i += 1
    }

    test
  }

  override def changePricingName(id: UUID): ServiceCall[String, Pricing] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:*:prices:write"
    } { (token, _) =>
      ServerServiceCall { newName =>
        val entity = eReg.refFor[PricingEntity](PricingEntity.id(token.team, id))

        for {
          names         <- pricingRepo.getPricingsForTeam(token.team).map(_.map(_._2))
          uniqueNewName <- Future.successful(uniqueName(names, newName))
          x <- entity.ask(
            ChangeDeployment(
              team = token.team,
              id = id,
              name = Some(uniqueNewName),
              supplier = None,
              enableApi = None,
              d = None,
              currency = None
            )
          )
          conv <- doConvertPricing(x.id, x.team) { (_p, t) =>
            Pricing(
              _p.name,
              Some(_p.id),
              _p.supplier,
              t,
              _p.enableApi.getOrElse(false),
              _p.currency
            )
          }
        } yield conv
      }
    }

  override def deployCapabilitiesForSupplier(supplier: UUID)
      : ServiceCall[api.CapabilitiesDeployment, DeployedCapabilities] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:*:prices:write"
    } { (token, _) =>
      ServerServiceCall { capabilities =>
        doDeployCapabilitiesForSupplier(
          team = token.getTeam,
          supplier = supplier,
          capabilities = capabilities,
          timelineCommand = TimelineCommand.of(token)
        )
      }
    }

  override def _deployCapabilitiesForSupplier(
      team: String,
      supplier: UUID
  ): ServiceCall[api.CapabilitiesDeployment, DeployedCapabilities] =
    ServerServiceCall { capabilities =>
      doDeployCapabilitiesForSupplier(
        team = team,
        supplier = supplier,
        capabilities = capabilities,
        timelineCommand = TimelineCommand.system
      )
    }

  private def doDeployCapabilitiesForSupplier(
      team: String,
      supplier: UUID,
      capabilities: api.CapabilitiesDeployment,
      timelineCommand: TimelineCommand
  ): Future[DeployedCapabilities] = {
    val tableNames = capabilities.tables.map(_.name)
    if (tableNames.length != tableNames.distinct.length) {
      throw new TransportException(TransportErrorCode.NotAcceptable, "Table name must be unique")
    }

    val entity = eReg.refFor[CapabilitiesEntity](CapabilitiesEntity.id(team, supplier))
    for {
      tables <- deployNewTables(team, capabilities.tables, timelineCommand)
      capabilities <-
        entity.ask(
          DeployCapabilities(
            team = team,
            supplier = capabilities.supplier,
            d = tables.map { table =>
              DecisionDeployment(
                table.team,
                table.id,
                Some(table.id),
                tenant = Some(team),
                name = Some(table.name)
              )
            }
          )
        )
    } yield capabilitiesToApi(team)(capabilities, tables)
  }

  override def changeCapabilitiesForSupplier(supplier: UUID)
      : ServiceCall[api.CapabilitiesDeploymentDescriptor, DeployedCapabilities] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:*:prices:write"
    } { (token, _) =>
      ServerServiceCall { p =>
        val entity = eReg.refFor[CapabilitiesEntity](CapabilitiesEntity.id(token.team, supplier))

        for {
          existing <- entity.ask(GetDeployedCapabilities(token.team, supplier))
            .map(_.response.getOrElse(throw new TransportException(
              TransportErrorCode.NotFound,
              "Capabilities not found"
            )))
          changed <- entity.ask(
            DeployCapabilities(
              team = token.team,
              supplier = p.supplier,
              d = reorder(existing.capabilities, p.tables)
            )
          )

          t <- getTablesWithVersions(Some(changed))

        } yield capabilitiesToApi(token.team)(changed, t)

      }
    }

  override def _checkCapabilitiesForSupplier(
      team: String
  ): ServiceCall[CapabilityCheckRequest, Seq[CapabilityCheckResult]] =
    ServerServiceCall { request =>
      doCapabilityCheck(team, request.team, request.pcb, request.supplierIds)
    }

  override def checkCapabilitiesForSupplier(): ServiceCall[PCBV2Api.PCBV2, Seq[CapabilityCheckResult]] =
    authorizedString { token =>
      s"prices:${token.team}:${token.team}:*:prices:read"
    } { (token, _) =>
      ServerServiceCall { pcb =>
        doCapabilityCheck(token.getTeam, token.getTeam, pcb, supplierIds = None)
      }
    }

  private def doCapabilityCheck(
      team: String,
      pcbOwningTeam: String,
      pcb2: PCBV2,
      supplierIds: Option[Set[UUID]]
  ): Future[Seq[CapabilityCheckResult]] =
    for {
      suppliers <- supplier._getSuppliers(team).invoke()
      allSupplierIds = suppliers.flatMap(_.id).toSet
      validSuppliers: Seq[UUID] = supplierIds
        .map(supplierIds => allSupplierIds.intersect(supplierIds))
        .getOrElse(allSupplierIds)
        .toSeq
      capabilities <-
        Future.sequence(
          validSuppliers.map(supplierId => _doGetCachedCapabilitiesForSupplier(team, supplierId))
        )
          .map(_.flatten)

      pcb1 <- pcb._getPCB(pcbOwningTeam, pcb2.assembly, pcb2.id).invoke()
      spec <- pcb._getSpecification(pcbOwningTeam, pcb2.assembly, pcb2.id, pcb1.specifications.head).invoke()
      c    <- PriceServiceImpl.doCapabilityChecks(team, pcb1, spec.head, capabilities)
    } yield c

  override def deployPricing(): ServiceCall[PricingDeploymentDescriptor, Pricing] =
    authorizedString { token =>
      s"pricetable:${token.team}:${token.team}:*:prices:write"
    } { (token, _) =>
      ServerServiceCall { pricing =>
        _doDeployPricing(
          team = token.getTeam,
          pricing = pricing,
          token = Some(token)
        )
      }
    }

  private def _doDeployPricing(
      team: String,
      pricing: PricingDeploymentDescriptor,
      token: Option[TokenContent]
  ): Future[Pricing] = {
    val tableNames = pricing.tables.map(_.name)
    if (tableNames.length != tableNames.distinct.length) {
      throw new TransportException(TransportErrorCode.NotAcceptable, "Table name must be unique")
    }

    (pricing.id match {
      case Some(value) =>
        Future.successful(value)
      case None =>
        pricingRepo.getPricingsForTeam(team).map { pr =>
          pr.find(_._2 == pricing.name).map(_._1).getOrElse(UUID.randomUUID())
        }
    }).flatMap { id =>
      for {
        _ <- Future.unit

        timelineCommand = token.fold(TimelineCommand.system)(token => TimelineCommand.of(token))

        tables <- deployNewTables(team, pricing.tables, timelineCommand)

        pricing <- {
          val entity = eReg.refFor[PricingEntity](PricingEntity.id(team, id))
          entity.ask(GetDeployedPricing(team, id))
            .map(_.response)
            .map(_.map(dpd => dpd.pricings))
            .flatMap { existingDeployments =>
              val pdeps =
                (existingDeployments match {
                  case Some(value) => // existing pricing
                    pricing.tables.map { pt =>
                      value.find(_.key == pt.name)
                        .getOrElse(throw new TransportException(
                          TransportErrorCode.InternalServerError,
                          "could not deploy"
                        ))
                    }
                  case None =>
                    // new pricing
                    tables.map { x =>
                      DecisionDeployment(
                        x.team,
                        x.id,
                        Some(x.id),
                        tenant = Some(team),
                        name = Some(x.name)
                      )
                    }
                })

              entity.ask(
                DeployPricing(
                  team = team,
                  name = pricing.name,
                  supplier = pricing.supplier,
                  id = id,
                  enableApi = pricing.enableApi,
                  currency = pricing.currency,
                  d = order(pdeps, tables.map(_.getCurrentVersion.table))
                )
              )
            }
        }
      } yield Pricing(
        pricing.name,
        Some(pricing.id),
        pricing.supplier,
        tables,
        pricing.enableApi.getOrElse(false),
        pricing.currency
      )
    }
  }

  private def deployNewTables(team: String, tables: Seq[DecisionTable], timelineCommand: TimelineCommand) =
    Future.sequence(tables.map { pt =>
      validateModel(pt)
      val tid = UUIDUtils.createShort() // will only be used if the table does not exist yet
      eReg.refFor[DecisionTableEntity](DecisionTableEntity.id(team, tid)).ask(DeployDecisionTable(
        team,
        tid,
        tid,
        pt.copy(team = Some(team)),
        timelineCommand
      ))
    })

  private def toApi(team: String)(x: PricingDeployment): DeployedPricing =
    DeployedPricing(
      name = x.name,
      id = x.id,
      supplier = x.supplier,
      pricings = x.pricings.map(decToApi(team)),
      enableApi = x.enableApi.getOrElse(false),
      currency = x.currency
    )

  private def capabilitiesToApi(team: String)(
      x: CapabilitiesDeployment,
      tables: Seq[VersionedDecisionTable]
  ): DeployedCapabilities =
    DeployedCapabilities(
      supplier = x.supplier,
      tables = tables // x.capabilities.map(decToApi(team))
    )
}

object PriceServiceImpl extends Logging {

  val conf = new StackrateDmnEngineConfiguration()
  conf.init()
  val engine = new StackrateDmnEngine(new DefaultDmnEngine(conf), conf)

  def parseXML(team: String, info: Option[DeploymentInfo], xml: Elem, decisionIDFilter: Option[String] = None) =
    (xml \\ "decision").flatMap { nDec =>
      val name = nDec \@ "name"
      val id   = nDec \@ "id"

      decisionIDFilter.getOrElse(id) match {
        case x if x != id => None
        case x =>
          (nDec \ "decisionTable").map { table =>
            val inputs = (table \ "input").map { nIn =>
              val nInExp = (nIn \ "inputExpression").head

              val invar = nInExp \@ "{http://camunda.org/schema/1.0/dmn}inputVariable"
              val inexp = ((nInExp \ "text").headOption.map(_.text).map {
                case OptionalVariable(variable) => variable
                case e                          => e
              })

              Input(
                nIn \@ "label",
                inexp.getOrElse(invar),
                //              nIn \@ "stackrate_variable",
                nInExp \@ "typeRef"
              )
            }
            val outputs = (table \\ "output").map { nOut =>
              Output(
                nOut \@ "label",
                nOut \@ "name",
                nOut \@ "typeRef"
              )
            }
            val rules = (table \\ "rule").map { nRule =>
              val inEntries = (nRule \ "inputEntry").zipWithIndex.map { nie =>
                InputEntry(
                  name = inputs(nie._2).name,
                  value = (nie._1 \ "text").head.text
                )
              }
              val outEntries = (nRule \ "outputEntry").zipWithIndex.map { nie =>
                OutputEntry(
                  name = outputs(nie._2).name,
                  value = (nie._1 \ "text").head.text
                )
              }

              Rule(inEntries, outEntries)
            }

            val hit = (exc(table \@ "hitPolicy"), (exc(table \@ "aggregation"))) match {
              case (Some("FIRST"), _) => Some("FIRST")
              case (_, Some("MIN"))   => Some("MIN")
              case (_, Some("MAX"))   => Some("MAX")
              case (_, _)             => Some("COLLECT")
            }

            DecisionTable(
              team = Some(team),
              name = name,
              inputs = inputs,
              outputs = outputs,
              rules = rules,
              hitpolicy = hit
            )
          }

      }

    }

  def exc[T](c: => T): Option[T] =
    try
      Some(c)
    catch {
      case e: Throwable => None
    }

  def doCapabilityChecks(
      team: String,
      pcb: PCB,
      spec: PCBSpecification,
      capabilities: Seq[(CapabilitiesDeployment, Seq[BuiltDecisionTable])]
  ): Future[Seq[CapabilityCheckResult]] = {
    implicit val executionContext = PriceServiceImpl.engine.executionContext
    logger.info(s"run capability check for ${team}/${pcb.version}")
    KamonUtils.span(s"run capability check for ${team}/${pcb.version}") {
      Future.sequence(
        capabilities.map { x =>
          val (capablitiesDeployment, tables) = x

          for {
            result <- new CapabilityChecker(pcb, spec, tables).checkWithResults(PriceServiceImpl.engine)
          } yield {
            logger.info(s"capability check ${team}/${pcb.version}: ${result}")

            CapabilityCheckResult(
              capability = api.CapabilitiesDeploymentDescriptor(
                supplier = capablitiesDeployment.supplier,
                tables = capablitiesDeployment.capabilities.map(decToApi(team))
              ),
              failures = result
            )
          }
        }
      )
    }
  }

  def decToApi(team: String)(x: DecisionDeployment): DeployedDecision =
    DeployedDecision(
      team = team,
      key = x.key
    )

}
