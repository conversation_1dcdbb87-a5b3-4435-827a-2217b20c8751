package de.fellows.app.price.impl.entity

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import de.fellows.app.price.api.PricingDescriptor
import de.fellows.utils.CurrencyCode
import play.api.libs.json.{Format, Json}

import java.util.UUID

package object pricing {

  case class PricingDeployment(
      team: String,
      supplier: UUID,
      name: String,
      id: UUID,
      pricings: Seq[DecisionDeployment],
      enableApi: Option[Boolean],
      currency: Option[CurrencyCode]
  )

  case class DecisionDeployment(
      team: String,
      key: String,
      id: Option[String] = None,
      tenant: Option[String] = None,
      name: Option[String] = None
  )

  sealed trait PricingCommand

  case class DeployPricing(
      team: String,
      name: String,
      supplier: UUID,
      id: UUID,
      enableApi: Option[Boolean],
      currency: Option[CurrencyCode],
      d: Seq[DecisionDeployment]
  ) extends PricingCommand with ReplyType[PricingDeployment]

  case class ChangeDeployment(
      team: String,
      id: UUID,
      name: Option[String],
      supplier: Option[UUID],
      enableApi: Option[Boolean],
      currency: Option[CurrencyCode],
      d: Option[Seq[DecisionDeployment]]
  ) extends PricingCommand
      with ReplyType[PricingDeployment]

  case class GetDeployedPricing(team: String, id: UUID) extends PricingCommand with ReplyType[PricingDeploymentResponse]

  case class DeleteDeployedPricing(team: String, id: UUID) extends PricingCommand with ReplyType[Done]

  case class PricingDeploymentResponse(response: Option[PricingDeployment])

  sealed trait PricingEvent extends AggregateEvent[PricingEvent] {
    override def aggregateTag: AggregateEventShards[PricingEvent] = PricingEvent.Tag
  }

  final case class Deployment(
      team: String,
      name: String,
      id: UUID,
      supplier: UUID,
      deployment: Seq[DecisionDeployment],
      enableApi: Option[Boolean],
      currency: Option[CurrencyCode],
      oldstate: Option[PricingDescriptor]
  ) extends PricingEvent

  final case class DeploymentDeleted(d: PricingDeployment) extends PricingEvent

  object PricingEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[PricingEvent](NumShards)
  }

  object DecisionDeployment {
    implicit val f: Format[DecisionDeployment] = Json.format[DecisionDeployment]
  }

  object PricingDeployment {
    implicit val f: Format[PricingDeployment] = Json.format[PricingDeployment]
  }

  object PricingDeploymentResponse {
    implicit val f: Format[PricingDeploymentResponse] = Json.format[PricingDeploymentResponse]
  }

  object DeployPricing {
    implicit val f: Format[DeployPricing] = Json.format[DeployPricing]
  }

  object ChangeDeployment {
    implicit val f: Format[ChangeDeployment] = Json.format[ChangeDeployment]
  }

  object DeleteDeployedPricing {
    implicit val f: Format[DeleteDeployedPricing] = Json.format[DeleteDeployedPricing]
  }

  object GetDeployedPricing {
    implicit val f: Format[GetDeployedPricing] = Json.format[GetDeployedPricing]
  }

  object DeploymentDeleted {
    implicit val f: Format[DeploymentDeleted] = Json.format[DeploymentDeleted]
  }

  object Deployment {
    implicit val f: Format[Deployment] = Json.format[Deployment]
  }

}
