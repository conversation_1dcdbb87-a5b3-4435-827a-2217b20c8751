package de.fellows.app.price.impl.entity.pricing

import akka.Done
import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import de.fellows.app.price.api.{DeployedDecision, PricingDescriptor}
import de.fellows.utils.common.EntityException
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.secure.SecureTeamEntity

import java.util.UUID

class PricingEntity(implicit override val service: ServiceDefinition)
    extends SecureTeamEntity[Option[PricingDeployment]] {
  override type Command = PricingCommand
  override type Event   = PricingEvent

  override def initialState: Option[PricingDeployment] = None

  override def isAllowed(a: PricingCommand, s: Option[PricingDeployment]): Boolean = a match {
    case c: DeployPricing         => allowed(s, c.team, c.id)
    case c: ChangeDeployment      => allowed(s, c.team, c.id)
    case c: GetDeployedPricing    => allowed(s, c.team, c.id)
    case c: DeleteDeployedPricing => allowed(s, c.team, c.id)
  }

  private def allowed(s: Option[PricingDeployment], team: String, id: UUID): Boolean =
    s.isEmpty ||
      (s.map(_.team).contains(team) && s.map(_.id).contains(id))

  override def entityBehavior(state: Option[PricingDeployment]): Actions =
    Actions()
      .onReadOnlyCommand[GetDeployedPricing, PricingDeploymentResponse] {
        case (x: GetDeployedPricing, ctx, s) =>
          ctx.reply(PricingDeploymentResponse(s))
      }
      .onCommand[ChangeDeployment, PricingDeployment] {
        case (x: ChangeDeployment, ctx, s) =>
          if (s.isEmpty) {
            ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Deployment not found"))
            ctx.done
          } else {
            val thisState = s.get
            val updated = thisState.copy(
              name = x.name.getOrElse(thisState.name),
              supplier = x.supplier.getOrElse(thisState.supplier),
              enableApi = x.enableApi.orElse(thisState.enableApi),
              pricings = x.d.getOrElse(thisState.pricings),
              currency = x.currency.orElse(thisState.currency)
            )

            ctx.thenPersist(
              Deployment(
                team = updated.team,
                name = updated.name,
                id = updated.id,
                supplier = updated.supplier,
                deployment = updated.pricings,
                enableApi = updated.enableApi,
                currency = updated.currency,
                oldstate = Some(
                  PricingDescriptor(
                    name = thisState.name,
                    supplier = thisState.supplier,
                    id = Some(thisState.id),
                    tables = Some(thisState.pricings.map(d => DeployedDecision(d.team, d.key))),
                    enableApi = thisState.enableApi,
                    currency = thisState.currency
                  )
                )
              )
            )(_ => ctx.reply(updated))
          }
      }
      .onCommand[DeleteDeployedPricing, Done] {
        case (x: DeleteDeployedPricing, ctx, s) =>
          s match {
            case Some(state) => ctx.thenPersist(DeploymentDeleted(state))(_ => ctx.reply(Done))
            case None =>
              ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Deployment not found"))
              ctx.done
          }

      }
      .onCommand[DeployPricing, PricingDeployment] {
        case (x: DeployPricing, ctx, s) =>
          val oldstate = s.getOrElse(
            PricingDeployment(
              team = x.team,
              supplier = x.supplier,
              name = x.name,
              id = x.id,
              pricings = Seq(),
              enableApi = x.enableApi,
              currency = x.currency
            )
          )

          if (x.name != oldstate.name || x.id != oldstate.id) {
            ctx.commandFailed(new IllegalArgumentException("Cannnot change name or ID with this command"))
            ctx.done
          } else {
            //            var tmpstate = oldstate.pricings
            //            x.d.foreach(p =>
            //              tmpstate = tmpstate.upsert(p, eq)
            //            )
            val updated = oldstate.copy(
              pricings = x.d,
              supplier = x.supplier,
              name = x.name,
              enableApi = x.enableApi,
              currency = x.currency
            )

            ctx.thenPersist(
              Deployment(
                team = x.team,
                name = x.name,
                id = x.id,
                supplier = x.supplier,
                deployment = x.d,
                enableApi = updated.enableApi,
                currency = updated.currency,
                oldstate = Some(
                  PricingDescriptor(
                    name = oldstate.name,
                    supplier = oldstate.supplier,
                    id = Some(oldstate.id),
                    tables = Some(oldstate.pricings.map(d => DeployedDecision(d.team, d.key))),
                    enableApi = oldstate.enableApi,
                    currency = oldstate.currency
                  )
                )
              )
            )(_ => ctx.reply(updated))
          }
      }
      .onEvent {
        case (e: Deployment, s) =>
          val oldstate = s.getOrElse(
            PricingDeployment(
              team = e.team,
              supplier = e.supplier,
              name = e.name,
              id = e.id,
              pricings = Seq(),
              enableApi = e.enableApi,
              currency = e.currency
            )
          )
          Some(
            oldstate.copy(
              pricings = e.deployment,
              name = e.name,
              supplier = e.supplier,
              enableApi = e.enableApi,
              currency = e.currency
            )
          )

        case (DeploymentDeleted(_), _) =>
          None
      }

}

object PricingEntity {
  def id(team: String, id: UUID): String =
    s"PricingEntity-$team-$id"
}
