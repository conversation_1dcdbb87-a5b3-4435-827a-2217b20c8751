package de.fellows.app.price.impl.read

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BatchStatement, BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.price.impl.entity.capabilities.{
  CapabilitiesEvent,
  CapabilityDeployment,
  CapabilityDeploymentDeleted
}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters.SeqHasAsJava

class CapabilitiesRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) extends StackrateLogging {

  def getCapabilityDeployment(team: String, supplier: UUID): Future[Option[CapabilitiesDeploymentDTO]] =
    session.selectAll(
      "SELECT * FROM capabilities WHERE team = ? AND supplier = ?",
      team,
      supplier
    ).map { rows =>
      val tables = rows.map { row =>
        val tableKey   = row.getString("key")
        val tableIndex = row.getInt("orderindex")

        TableDTO(tableKey, tableIndex)

      }

      if(tables.isEmpty){
        None
      } else {
        Some(CapabilitiesDeploymentDTO(team, supplier, tables.sortBy(_.index)))
      }
    }

}

class CapabilitiesIndexProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[CapabilitiesEvent] with StackrateLogging {
  var deleteDeploymentStmt: PreparedStatement = _
  var updateDeploymentStmt: PreparedStatement = _

  private def prepareStatements() =
    for {
      deleteDeployment <- session.prepare(
        "DELETE FROM capabilities WHERE team = :team AND supplier = :supplier"
      )

      updateDeployment <- session.prepare(
        "UPDATE capabilities SET orderindex = :orderindex WHERE team = :team AND supplier = :supplier AND key = :key"
      )

    } yield {
      this.deleteDeploymentStmt = deleteDeployment
      this.updateDeploymentStmt = updateDeployment
      Done
    }

  def updateDeployment(e: CapabilityDeployment): Future[Seq[BoundStatement]] =
    for {
      _ <- session.executeWriteBatch(new BatchStatement()
        .add(createDeleteStatement(e.team, e.supplier)))
      _ <- session.executeWriteBatch(new BatchStatement()
        .addAll(
          e.deployment.zipWithIndex.map {
            case (deployment, index) =>
              updateDeploymentStmt.bind()
                .setString("team", e.team)
                .setUUID("supplier", e.supplier)
                .setString("key", deployment.key)
                .setInt("orderindex", index)
          }.asJava
        ))
    } yield Seq()

  def deleteDeployment(event: CapabilityDeploymentDeleted): Future[Seq[BoundStatement]] =
    Future.successful(Seq(createDeleteStatement(event.team, event.supplier)))

  private def createDeleteStatement(team: String, supplier: UUID): BoundStatement =
    deleteDeploymentStmt.bind()
      .setString("team", team)
      .setUUID("supplier", supplier)

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[CapabilitiesEvent] =
    readSide.builder[CapabilitiesEvent]("capability-deployments-v1.0")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[CapabilityDeployment](e => updateDeployment(e.event))
      .setEventHandler[CapabilityDeploymentDeleted](e => deleteDeployment(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[CapabilitiesEvent]] = CapabilitiesEvent.Tag.allTags

  private def createTables(): Future[Done] =
    for {
      _ <- session.executeCreateTable(
        // language=SQL
        """
          | CREATE TABLE IF NOT EXISTS capabilities (
          |   team text,
          |   supplier uuid,
          |   key text,
          |   orderindex int,
          |   PRIMARY KEY (team, supplier, key)
          | )
          |""".stripMargin
      )
    } yield Done
}
