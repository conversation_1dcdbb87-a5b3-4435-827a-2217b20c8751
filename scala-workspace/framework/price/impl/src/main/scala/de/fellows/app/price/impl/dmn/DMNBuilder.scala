package de.fellows.app.price.impl.dmn

import de.fellows.app.price.api.{DecisionTable, Pricing}
import org.camunda.bpm.model.dmn.instance.{DecisionTable => CDecisionTable, _}
import org.camunda.bpm.model.dmn.{BuiltinAggregator, Dmn, DmnModelInstance, HitPolicy}

class DMNBuilder(val id: String) {
  val model = Dmn.createEmptyModel()

  val definitions: Definitions = {
    val _definitions = model.newInstance(classOf[Definitions])
    _definitions.setNamespace("http://camunda.org/schema/1.0/dmn")
    _definitions.setName(id)
    _definitions.setId(id)
    model.setDefinitions(_definitions)
    _definitions
  }

  def decision(id: String, name: String): DMNDecisionBuilder = {
    val decision1 = model.newInstance(classOf[Decision])
    decision1.setId(id)
    decision1.setName(name)
    definitions.addChildElement(decision1)

    new DMNDecisionBuilder(this, model, decision1)
  }

  def result() =
    model
}

class DMNDecisionBuilder(parent: DMNBuilder, model: DmnModelInstance, node: Decision) {
  def table(id: String): DmnTableBuilder = {
    val table1 = model.newInstance(classOf[CDecisionTable])
    table1.setId(id)
    node.addChildElement(table1)
    new DmnTableBuilder(this, model, table1)
  }

}

class DmnTableBuilder(parent: DMNDecisionBuilder, model: DmnModelInstance, node: CDecisionTable) {
  def input(id: String, typeRef: String, variable: String, label: String, optional: Boolean): DmnTableBuilder = {
    val i1 = model.newInstance(classOf[Input])
    i1.setId(id)

    //    i1.setCamundaInputVariable(variable)
    i1.setLabel(label)
    node.getInputs.add(i1)

    val exp = model.newInstance(classOf[InputExpression])
    exp.setId(s"exp-$id")
    exp.setTypeRef(typeRef)
    val text1 = model.newInstance(classOf[Text])
    if (optional) {
      //      text1.setTextContent( s"${variable}${DMNBuilder.OPTIONAL}")
      text1.setTextContent(OptionalVariable(variable))
    } else {
      text1.setTextContent(variable)
    }
    exp.setText(text1)
    //    i1.setAttributeValue("stackrate_variable", variable)
    i1.setInputExpression(exp)

    this
  }

  def output(id: String, typeRef: String, name: String, label: String): DmnTableBuilder = {
    val o1 = model.newInstance(classOf[Output])
    o1.setId(id)
    o1.setName(name)
    o1.setLabel(label)
    o1.setTypeRef(typeRef)
    node.getOutputs.add(o1)
    this
  }

  def rule(id: String): DmnRuleBuilder = {
    val rule = model.newInstance(classOf[Rule])
    rule.setId(id)
    rule.getInputEntries
    node.getRules.add(rule)
    new DmnRuleBuilder(this, model, rule)
  }

  def hitPolicy(policy: String) = {

    policy.toUpperCase match {
      case "FIRST" =>
        node.setHitPolicy(HitPolicy.FIRST)
      case "COLLECT" =>
        node.setHitPolicy(HitPolicy.COLLECT)
      case "MIN" =>
        node.setHitPolicy(HitPolicy.COLLECT)
        node.setAggregation(BuiltinAggregator.MIN)
      case "MAX" =>
        node.setHitPolicy(HitPolicy.COLLECT)
        node.setAggregation(BuiltinAggregator.MAX)
      case _ =>
        node.setHitPolicy(HitPolicy.COLLECT)
    }

    this
  }

}

class DmnRuleBuilder(parent: DmnTableBuilder, model: DmnModelInstance, node: Rule) {
  def inputEntry(id: String, content: String): InputEntry = {
    val ie = model.newInstance(classOf[InputEntry])
    node.getInputEntries.add(ie)

    ie.setId(id)

    ie.setExpressionLanguage("feel")
    val text = model.newInstance(classOf[Text])
    text.setTextContent(content)
    ie.setText(text)
    ie
  }

  def outputEntry(id: String, content: String): OutputEntry = {
    val ie = model.newInstance(classOf[OutputEntry])
    node.getOutputEntries.add(ie)

    ie.setId(id)
    ie.setExpressionLanguage("feel")
    val text = model.newInstance(classOf[Text])
    text.setTextContent(content)
    ie.setText(text)
    ie
  }
}

object OptionalVariable {
  val p = "^if\\( is defined\\((.*?)\\) \\) then (.*?) else null$".r

  def apply(variable: String): String =
    if (variable.trim != "") {
      s"if( is defined($variable) ) then $variable else null"
    } else {
      variable
    }

  def unapply(arg: String): Option[String] = p.findFirstMatchIn(arg).map(_.group(1))
}

object DMNBuilder {

  def from(tables: Seq[DecisionTable], ids: Map[DecisionTable, String]): DMNBuilder = {
    val builder = new DMNBuilder(s"pricing-decision")

    val dmnTables = tables.zipWithIndex.map { pt =>
      val decId    = ids(pt._1)
      val decision = builder.decision(decId, decId)

      val table    = pt._1
      val tableid  = s"Table-${pt._2 + 1}"
      val dmntable = decision.table(tableid)

      dmntable.hitPolicy(table.hitpolicy.getOrElse("COLLECT"))
      table.inputs.zipWithIndex.foreach(i =>
        dmntable.input(s"${tableid}-i-${i._2}-${i._1.name}", i._1.varType, i._1.name, i._1.label, optional = true)
      )
      table.outputs.zipWithIndex.foreach(i =>
        dmntable.output(s"${tableid}-o-${i._2}-${i._1.name}", i._1.varType, i._1.name, i._1.label)
      )

      table.rules.zipWithIndex.foreach { r =>
        val ruleId  = s"${tableid}-rule-${r._2}"
        val dmnrule = dmntable.rule(ruleId)
        r._1.inputEntries
          .zipWithIndex // TODO: Order
          .foreach { ie =>
            val inEnt = dmnrule.inputEntry(s"$ruleId-inentry-${ie._2}", ie._1.value)
          }
        r._1.outputEntries
          .zipWithIndex // TODO: Order
          .foreach { ie =>
            val outEnt = dmnrule.outputEntry(s"$ruleId-outentry-${ie._2}", ie._1.value)
          }
      }

      table
    }

    builder
  }

  def from(pricing: Pricing, ids: Map[DecisionTable, String]): DMNBuilder =
    from(pricing.tables.map(_.getCurrentVersion.table), ids)

  def name(str: String): String =
    str.replace(" ", "-").toLowerCase()
}
