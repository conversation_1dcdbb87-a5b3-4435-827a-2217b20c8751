include "main-application.conf"

play.application.loader = de.fellows.app.price.impl.PriceServiceLoader


price.cassandra.keyspace = ${fellows.persistence.rootKeyspace}price


cassandra-journal {
  keyspace = ${price.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${price.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${price.cassandra.keyspace}read
}

akka {
  management {
    cluster.bootstrap {
      contact-point-discovery {
        service-name = "price"
      }
    }
  }
}

# fellows.serviceconfig = ${fellows.services.price}

lagom.services {
  //  camunda-pricing = "http://********:8081/engine-rest" //connect to gateway, which in turn connects to the correct external ip
}


