package de.fellows.app.price.api

import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import play.api.libs.json.{Format, Json}

import java.util.UUID

object CapabilitiesApi {
  case class CapabilityCheckRequest(
      pcb: PCBV2,
      team: String,
      supplierIds: Option[Set[UUID]]
  )

  object CapabilityCheckRequest {
    import de.fellows.ems.pcb.api.specification.units.UnitConversions.implicits._
    implicit val format: Format[CapabilityCheckRequest] = Json.format[CapabilityCheckRequest]
  }

  case class CapabilityCheckFailure(
      hint: Option[String],
      variableFailures: Seq[StackratePricingFailedField]
  )

  case class StackratePricingFailedField(
      variable: String,
      variableName: String,
      value: String,
      expression: Option[String]
  )

  case class CapabilityCheckResult(
      capability: CapabilitiesDeploymentDescriptor,
      failures: Seq[CapabilityCheckFailure]
  )

  object StackratePricingFailedField {
    implicit val format: Format[StackratePricingFailedField] = Json.format[StackratePricingFailedField]
  }
  object CapabilityCheckFailure {
    implicit val format: Format[CapabilityCheckFailure] = Json.format[CapabilityCheckFailure]
  }
  object CapabilityCheckResult {
    implicit val format: Format[CapabilityCheckResult] = Json.format[CapabilityCheckResult]
  }

}
