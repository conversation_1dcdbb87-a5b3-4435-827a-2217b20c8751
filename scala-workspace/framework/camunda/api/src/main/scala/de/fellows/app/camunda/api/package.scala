package de.fellows.app.camunda

import play.api.libs.json.{ Format, JsValue, Json, Writes }

import java.util
import java.util.UUID

package object api {

  case class FormCall(variables: Map[String, Variable], businessKey: Option[String] = None)

  case class SignalCall(
      name: String,
      executionId: Option[String],
      variables: Map[String, Variable],
      tenantId: Option[String] = None,
      withoutTenantId: Option[Boolean] = None
  )

  case class MessageCall(
      messageName: String,
      businessKey: Option[String],
      processInstanceId: Option[String],
      tenantId: Option[String],
      withoutTenantId: Option[Boolean],
      processVariables: Map[String, Variable],
      processVariablesLocal: Map[String, Variable],
      all: Option[Boolean]
      // TODO: correlation keys
  )

  case class Variable(value: String, `type`: String, valueInfo: Option[ValueInfo] = None)

  case class Task(
      id: String,
      name: String,
      assignee: String,
      owner: String,
      created: String,
      due: String,
      followUp: String,
      delegationState: String,
      description: String,
      executionId: String,
      parentTaskId: String,
      priority: Int,
      processDefinitionId: String,
      processInstanceId: String,
      caseExecutionId: String,
      caseDefinitionId: String,
      caseInstanceId: String,
      taskDefinitionId: String,
      formKey: String,
      tenantId: String
  )

  case class ValueInfo(
      objectTypeName: Option[String] = None,
      serializationDataFormat: Option[String] = None
  )

  case class CallResult(
      id: String,
      definitionId: Option[String],
      businessKey: Option[String],
      caseInstanceId: Option[String],
      tenantId: Option[String],
      ended: Option[Boolean],
      suspended: Option[Boolean]
      //                         links: Option[Seq[String]],

  )

  object ValueInfo {
    implicit val format: Format[ValueInfo] = Json.format
  }

  object CallResult {
    implicit val format: Format[CallResult] = Json.format
  }

  object FormCall {
    implicit val format: Format[FormCall] = Json.format
  }

  object SignalCall {
    implicit val format: Format[SignalCall] = Json.format
  }

  object MessageCall {
    implicit val format: Format[MessageCall] = Json.format
  }

  object Variable {
    implicit val format: Format[Variable] = Json.format

    def apply(x: String): Variable =
      Variable(value = x, `type` = "string")

    def apply(x: UUID): Variable =
      Variable(value = x.toString, `type` = "string")

    def _json[T](x: T)(implicit r: Writes[T]): Variable =
      json(Json.toJson(x))

    def _object[T](x: T)(implicit r: Writes[T]): Variable =
      obj(Json.toJson(x))

    def json(x: JsValue): Variable =
      Variable(
        value = Json.stringify(x),
        `type` = "json",
        valueInfo =
          Some(ValueInfo(
            //            objectTypeName = Some("com.fasterxml.jackson.databind.JsonNode"),
            serializationDataFormat = Some("application/json")
          ))
      )

    def obj(x: JsValue): Variable =
      Variable(
        value = Json.stringify(x),
        `type` = "Object",
        valueInfo =
          Some(ValueInfo(
            objectTypeName = Some(classOf[util.HashMap[String, _]].getName),
            serializationDataFormat = Some("application/json")
          ))
      )

    def apply(x: BigDecimal): Variable =
      if (x.scale > 0) {
        Variable(value = x.doubleValue.toString, `type` = "double")
      } else {
        Variable(value = x.toIntExact.toString, `type` = "integer")
      }

    def apply(x: AnyVal): Variable = x match {
      case v: Int     => Variable(value = v.toString, `type` = "integer")
      case v: Boolean => Variable(value = v.toString, `type` = "boolean")
      case v: Long    => Variable(value = v.toString, `type` = "long")
      case v: Double  => Variable(value = v.toString, `type` = "double")
    }
  }

  object Task {
    implicit val format: Format[Task] = Json.format
  }

}
