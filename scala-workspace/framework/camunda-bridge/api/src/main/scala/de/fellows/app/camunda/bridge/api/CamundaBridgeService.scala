package de.fellows.app.camunda.bridge.api

import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.app.camunda.api._
import de.fellows.utils.entities.teamsettings.TeamSettings
import play.api.libs.json.{JsObject, JsValue}

abstract class CamundaBridgeService extends Service {
  //  def startProcessByKey(key: String): ServiceCall[FormCall, CallResult]
  val subPath = "camunda-bridge"
  val basePath = s"/api/$subPath"
  val internalBasePath = s"/internal/$subPath/:team/"

  def setTeamSetting(team: String): ServiceCall[SetSetting, TeamSettings]

  def initializeTeam(team: String): ServiceCall[SetSetting, Done]

  def setAdminSetting(): ServiceCall[SetSetting, Done]

  def getTeamSettings(team: String): ServiceCall[NotUsed, TeamSettings]

  def startProcessByKey(team: String, key: String): ServiceCall[FormCall, CallResult]

  def startProcessByID(team: String, id: String): ServiceCall[FormCall, CallResult]

  def getProcessDefinitionList(team: String): ServiceCall[NotUsed, JsValue]

  def submitStartFormByKey(team: String, key: String): ServiceCall[FormCall, NotUsed]

  def submitStartFormByID(team: String, id: String): ServiceCall[FormCall, NotUsed]

  def getProcessInstanceVariables(team: String, id: String): ServiceCall[NotUsed, JsObject]

  def getTasks(
                team: String,
                processInstanceId: Option[String],
                processInstanceBusinessKey: Option[String]
              ): ServiceCall[NotUsed, Seq[Task]]

  def getTaskByID(team: String, id: String): ServiceCall[NotUsed, Task]

  def signal(team: String): ServiceCall[SignalCall, NotUsed]

  def message(team: String): ServiceCall[MessageCall, NotUsed]

  override def descriptor: Descriptor = {
    import Service._

    named("camunda-bridge")
      .withCalls(
        restCall(Method.PUT, s"$basePath/teams/:team/settings", setTeamSetting _),
        restCall(Method.PUT, s"$basePath/teams/:team/initialize", initializeTeam _),
        restCall(Method.PUT, s"$basePath/admin/settings", setAdminSetting _),
        restCall(Method.GET, s"$basePath/teams/:team/settings", getTeamSettings _),

        // Processes
        restCall(Method.POST, s"$internalBasePath/bridge/process-definition/key/:key/start", startProcessByKey _),
        restCall(Method.POST, s"$internalBasePath/bridge/process-definition/:id/start", startProcessByID _),
        restCall(Method.GET, s"$internalBasePath/bridge/process-definition", getProcessDefinitionList _),
        restCall(
          Method.POST,
          s"$internalBasePath/bridge/process-definition/key/:key/submit-form",
          submitStartFormByKey _
        ),
        restCall(Method.POST, s"$internalBasePath/bridge/process-definition/:id/submit-form", submitStartFormByID _),
        restCall(Method.GET, s"$internalBasePath/bridge/process-instance/:id/variables", getProcessInstanceVariables _),
        restCall(Method.POST, s"$internalBasePath/bridge/signal", signal _),
        restCall(Method.POST, s"$internalBasePath/bridge/message", message _),

        // Tasks
        restCall(Method.GET, s"$internalBasePath/bridge/task/:id", getTaskByID _),
        restCall(Method.GET, s"$internalBasePath/bridge/task?processInstanceId&processInstanceBusinessKey", getTasks _)
      )
      .withAcls(
        ServiceAcl(pathRegex = Some("/api/camunda-bridge/.*"))
      )
  }
}
//
