package de.fellows.app.camunda.bridge.impl.entities.settings

import de.fellows.utils.entities.teamsettings.BaseTeamSettingsEntity
import play.api.libs.json.{ JsString, JsValue }

class CamundaTeamSettingsEntity extends BaseTeamSettingsEntity {
  override def legalSettings: Set[String] = CamundaSettings.valid

  override def validate(key: String, value: Option[JsValue]): Boolean =
    key match {
      case CamundaSettings.TOKEN | CamundaSettings.ENDPOINT =>
        value match {
          case Some(JsString(_)) | None => true
          case Some(_)                  => false
        }
      case _ => false
    }
}
