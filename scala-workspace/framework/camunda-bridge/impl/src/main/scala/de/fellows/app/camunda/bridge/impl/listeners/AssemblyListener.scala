package de.fellows.app.camunda.bridge.impl.listeners

import akka.Done
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assembly.commons.AssemblyFeature
import de.fellows.app.assemby.api.{
  AssemblyService,
  FileMatchingMessage,
  IndividualAssemblyLifecycleStage,
  LifecycleMessage,
  VersionDescription
}
import de.fellows.app.camunda.api.{CamundaService, SignalCall, Variable}
import de.fellows.app.camunda.bridge.impl.BridgeSignals
import de.fellows.utils.TopicUtils
import de.fellows.utils.internal.StageStatusName
import de.fellows.utils.streams.ValidMessage
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.json.{JsArray, JsString, JsValue}

import java.util.concurrent.{Executors, ScheduledFuture, TimeUnit}
import scala.concurrent.{ExecutionContext, Future}

class AssemblyListener(
    assembly: AssemblyService,
    camunda: CamundaService,
    preg: PersistentEntityRegistry
)(implicit val ec: ExecutionContext) {
  val started = System.currentTimeMillis()
  private final val log: Logger =
    LoggerFactory.getLogger(classOf[AssemblyListener])

  def features(features: Seq[AssemblyFeature]): JsValue =
    JsArray(features.map(f => JsString(s"${f.service}.${f.feature}")))

  def createSignal(msg: LifecycleMessage, signalName: String, vals: Map[String, Variable]) = {

    val newLc = msg.newLifecycle.map(x => Map("newLifecycle" -> Variable._json(x))).getOrElse(Map())

    SignalCall(
      signalName,
      None,
      Map(
        "businessKey"   -> Variable(msg.assemblyReference.version),
        "assembly"      -> Variable(msg.assemblyReference.id),
        "version"       -> Variable(msg.assemblyReference.version),
        "team"          -> Variable(msg.assemblyReference.team),
        "lifecycleType" -> Variable(msg.t),
        "fileApproval"  -> Variable(msg.fileApproval),
        //        "fileLifecycles" -> Variable._json(msg.files),
        "versionLifecycles" -> Variable._json(msg.version.groupBy(_.name).map(x => (x._1, x._2.head)))
      ) ++ vals ++ newLc
    )
  }

  val statusDispatcher                     = Executors.newSingleThreadScheduledExecutor()
  var schedFut: Option[ScheduledFuture[_]] = None

  assembly.lifecycleTopic().subscribe.atLeastOnce(
    Flow[LifecycleMessage].mapAsync(1) {
      msg =>
        for {
          _ <- sendImportantUpdates(msg)
          //          _ <- sendLifecycleUpdates(msg)
        } yield Done
      //        Future.successful(Done)
      //        Await.result(res, 1 minute)
    }
  )

  private def sendImportantUpdates(msg: LifecycleMessage) = {
    val fb = Seq.newBuilder[Future[Done]]

    if (
      msg.newLifecycle.map(_.name).contains("render") && msg.newLifecycle.map(_.status.name).contains(
        StageStatusName.Success
      )
    ) {
      val signal1 = SignalCall(
        s"backend.pcb.assembly.render.done-${msg.assemblyReference.version}",
        None,
        Map()
      )
      log.warn(s"send render done! $signal1")
      fb += camunda.signal().invoke(signal1).map(_ => Done)
    }

    if (
      msg.newLifecycle.map(_.name).contains("fileanalysis") && msg.newLifecycle.map(_.status.name).contains(
        StageStatusName.Success
      )
    ) {
      val signal1 = SignalCall(
        s"backend.pcb.assembly.analysis.done-${msg.assemblyReference.version}",
        None,
        Map()
      )

      log.warn(s"send analysis done! $signal1")
      fb += camunda.signal().invoke(signal1).map(_ => Done)
    }

    Future.sequence(fb.result())
  }

  private def sendLifecycleUpdates(msg: LifecycleMessage): Future[Done] = {
    // debounce the event
    if (schedFut.isDefined && schedFut.get.isDone) {
      schedFut.get.cancel(false)
    }

    schedFut = Some(statusDispatcher.schedule(
      new Runnable {
        override def run(): Unit = {
          val signalName  = s"backend.pcb.assembly.lifecycle.changed-${msg.t}"
          val signalName2 = s"backend.pcb.assembly.lifecycle.changed-${msg.t}-${msg.assemblyReference.version}"

          //      assembly.approveFileMatches()
          log.warn(s"update ${msg.t} lifecycle ${IndividualAssemblyLifecycleStage.lifecycleString(msg.version)}")
          BridgeSignals.getDefaultVariables(msg.assemblyReference.team, preg)
            .flatMap { vals =>
              val signal1 = createSignal(msg, signalName, vals)
              val signal2 = createSignal(msg, signalName2, vals)
              for {
                _ <- camunda.signal().invoke(signal1)
                _ <- camunda.signal().invoke(signal2)
              } yield Done
            }
            .map(_ => Done).recover {
              case e: Throwable =>
                log.error(s"Failed to send BPM signal $signalName", e)
                Done
            }
        }
      },
      1,
      TimeUnit.SECONDS
    ))

    Future.successful(Done)
  }

  TopicUtils.subscribeLatest(assembly.assemblyTopic(), started) { msg =>
    msg.payload match {
      case ValidMessage(x: FileMatchingMessage) => _doFileMatching(x)
      case ValidMessage(x: VersionDescription)  => _doVersionCreated(x)
    }
  }

  private def _doVersionCreated(msg: VersionDescription) = {
    val signal = "assemblyCreated"

    BridgeSignals.getDefaultVariables(msg.assembly.team, preg).flatMap { vals =>
      val signal1 = SignalCall(
        signal,
        None,
        Map(
          "businessKey" -> Variable(msg.version.id),
          "assembly"    -> Variable(msg.assembly.id),
          "version"     -> Variable(msg.version.id),
          "features"    -> Variable.json(features(msg.assembly.features)),
          "team"        -> Variable(msg.assembly.team)
        ) ++ vals
      )
      //      println(s"call with call $signal1")
      //      camunda.signal().invoke(signal1)
      Future.successful(Done)
    }.map(_ => Done).recover {
      case e: Throwable =>
        log.error(s"Failed to send BPM signal $signal", e)
        Done
    }
  }

  private def _doFileMatching(msg: FileMatchingMessage) = {
    def newSignal(signal1: String, vars: Map[String, Variable], lcs: Option[Seq[IndividualAssemblyLifecycleStage]]) = {
      val lcVars = lcs.map(x =>
        Map(
          "lifecycles" -> Variable._json(x.groupBy(_.name).map(x => (x._1, x._2.head)))
        )
      ).getOrElse(Map())
      SignalCall(
        signal1,
        None,
        vars ++
          Map(
            "businessKey" -> Variable(msg.assRef.version),
            "assembly"    -> Variable(msg.assRef.id),
            "version"     -> Variable(msg.assRef.version)
          ) ++
          lcVars
      )
    }

    if (msg.locked) {
      // TODO change signal name
      //        val signal1 = "fileMatchingApproved"
      val signal1 = "backend.pcb.assembly.fileapproval.locked"
      val signal2 = s"${signal1}-${msg.assRef.version}"

      val sigs = BridgeSignals.getDefaultVariables(msg.assRef.team, preg).map { vars =>
        Seq(
          newSignal(signal1, vars, msg.lifecycles),
          newSignal(signal2, vars, msg.lifecycles)
        )
      }

      sigs.flatMap { scs =>
        Future.sequence(scs.map { s =>
          camunda.signal().invoke(s).map(_ => Done)
        })
      }.map(_ => Done).recover {
        case e: Throwable =>
          log.error(s"Failed to send BPM Signal $signal1", e)
          Done
      }
    } else {
      val signal1 = "backend.pcb.assembly.fileapproval.unlocked"
      val signal2 = s"backend.pcb.assembly.fileapproval.unlocked-${msg.assRef.version}"
      val sc = BridgeSignals.getDefaultVariables(msg.assRef.team, preg).map { vars =>
        Seq(
          newSignal(signal1, vars, msg.lifecycles),
          newSignal(signal2, vars, msg.lifecycles)
        )
      }

      Future.successful(Done)
    }
  }
}
