package de.fellows.app.camunda.bridge.impl.listeners

import akka.Done
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.api.broker
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.camunda.api._
import de.fellows.app.camunda.bridge.impl.BridgeSignals
import de.fellows.app.quotation
import de.fellows.app.quotation.Topics.QuotationMessage
import de.fellows.app.quotation.{QuotationService, Topics}
import play.api.Logging
import play.api.libs.json.Json

import scala.concurrent.{ExecutionContext, Future}

class QuotationListener(quotationService: QuotationService, camunda: CamundaService, preg: PersistentEntityRegistry)(
    implicit val exc: ExecutionContext
) extends Logging {

  def quotationStatusVariables(statusChange: Topics.QuotationStatusChangedMsg): Seq[(String, Variable)] =
    Seq(
      Some("team"          -> Variable(statusChange.team)),
      Some("quotationID"   -> Variable(statusChange.quotationId)),
      Some("customerID"    -> Variable(statusChange.customerId)),
      Some("quotationName" -> Variable(statusChange.name)),
      Some("status"        -> Variable(statusChange.status.name)),
      Some("oldStatus"     -> Variable._json(statusChange.oldStatus)),
      Some("newStatus"     -> Variable._json(statusChange.status))
    ).flatten

  def quotationVariables(quot: quotation.Quotation): Seq[(String, Variable)] =
    Seq(
      Some(("quotationID" -> Variable(quot.quotationId))),
      Some(("customerID"  -> Variable(quot.customerId))),
      Some(("created"     -> Variable(quot.created.toEpochMilli))),
      Some(("quotation"   -> Variable.json(Json.toJson(quot)))),
      quot.externalID.map(eid => ("externalID" -> Variable(eid)))
    ).flatten

  quotationService.quotationTopic().subscribe.withMetadata.atLeastOnce(
    Flow[broker.Message[QuotationMessage]].mapAsync(1) { _msg =>
      logger.warn(s"got quotationmessage ${_msg}")
      _msg.payload match {
        case x: Topics.QuotationStatusChangedMsg =>
          //          val signal1 = s"backend.quotation.status"
          val signal2 = s"backend.quotation.status.${x.status.name.toLowerCase}"

          logger.warn(s"send messages $signal2")
          BridgeSignals.getDefaultVariables(x.team, preg).flatMap { vals =>
            camunda.message().invoke(createStatusMsg(x, signal2, vals))
              .map(_ => Done)
          }.recover {
            case e: Throwable =>
              logger.error(s"Failed to send BPM signal ${signal2}", e)
              Done
          }

        case x: Topics.QuotationCreatedMsg =>
          val signal = s"backend.quotation.created"

          val PROCESS_DEF_KEY = "quotation-lifecycle"
          val team            = x.quotation.team

          camunda.getProcessDefinitionByKeyAndTenant(PROCESS_DEF_KEY, team)
            .invoke().map(_ => Some(team))
            .recover(_ => None)
            .flatMap { tenant =>
              logger.warn(s"start process $PROCESS_DEF_KEY with tenant $tenant")
              BridgeSignals.getDefaultVariables(team, preg).flatMap { vals =>
                (tenant match {
                  case Some(t) => camunda.startProcessByKeyAndTenant(PROCESS_DEF_KEY, t)
                  case None    => camunda.startProcessByKey(PROCESS_DEF_KEY)
                })
                  .invoke(FormCall(
                    vals ++ quotationVariables(x.quotation),
                    Some(x.quotation.quotationId.toString)
                  )).map(_ => Done)
              }

            //          BridgeSignals.getDefaultVariables(x.quotation.assembly.team, preg).flatMap(vals => {
            //            camunda.signal().invoke(createSignal(
            //              signal,
            //              vals ++ quotationVariables(x.quotation),
            //              Some(x.quotation.assembly),
            //              Some(x.quotation.assembly.team)
            //            ))
            //              .map(_ => Done)
            }.recover {
              case e: Throwable =>
                logger.error(s"Failed to send BPM signal ${signal}", e)
                Done
            }

        case _: Topics.QuotationSentMsg => Future.successful(Done)
      }
    }
  )

  private def createStatusMsg(x: Topics.QuotationStatusChangedMsg, signal: String, vals: Map[String, Variable]) =
    MessageCall(
      messageName = signal,
      businessKey = Some(x.quotationId.toString),
      processInstanceId = None,
      tenantId = None,
      withoutTenantId = None,
      processVariables = vals ++ quotationStatusVariables(x),
      processVariablesLocal = Map(),
      all = Some(false)
    )
}
