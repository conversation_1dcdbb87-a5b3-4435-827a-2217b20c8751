package de.fellows.app.customer.impl.entity

import com.lightbend.lagom.scaladsl.playjson.{ JsonSerializer, JsonSerializerRegistry }
import de.fellows.app.customer.impl.entity.contact.Commands.ContactResponse
import de.fellows.app.customer.impl.entity.contact.{
  Commands => ContactCommands,
  Events => ContactEvents,
  InternalContact
}
import de.fellows.app.customer.impl.entity.customer.Commands.CustomerResponse
import de.fellows.app.customer.impl.entity.customer.{
  Commands => CustomerCommands,
  Events => CustomerEvents,
  InternalCustomer
}

object CustomerServiceSerializerRegistry extends JsonSerializerRegistry {
  override def serializers = List(
    JsonSerializer[ContactResponse],
    JsonSerializer[CustomerResponse],
    JsonSerializer[CollaborativeInfo],
    <PERSON>sonSerializer[InternalCustomer],
    JsonSerializer[InternalContact],
    JsonSerializer[CustomerEvents.CustomerSet],
    JsonSerializer[CustomerCommands.SetCustomer],
    JsonSerializer[CustomerEvents.CollaborativeInfoSet],
    JsonSerializer[CustomerCommands.SetCollaboration],
    JsonSerializer[ContactEvents.CollaborativeInfoSet],
    JsonSerializer[ContactCommands.SetCollaboration],
    JsonSerializer[ContactEvents.ContactSet],
    JsonSerializer[ContactCommands.SetContact],
    JsonSerializer[ContactEvents.ImageSet],
    JsonSerializer[ContactCommands.SetImage],
    JsonSerializer[CustomerEvents.ImageSet],
    JsonSerializer[CustomerCommands.SetImage],

//    JsonSerializer[CustomerEvents.CustomerUpdated],
//    JsonSerializer[CustomerCommands.UpdateCustomer],
//
//    JsonSerializer[ContactEvents.ContactUpdated],
//    JsonSerializer[ContactCommands.UpdateContact],

    JsonSerializer[CustomerEvents.CustomerDeleted],
    JsonSerializer[CustomerCommands.DeleteCustomer],
    JsonSerializer[CustomerCommands.GetCustomer],
    JsonSerializer[ContactCommands.GetContact],
    JsonSerializer[ContactEvents.ContactDeleted],
    JsonSerializer[ContactCommands.DeleteContact]
  )
}
