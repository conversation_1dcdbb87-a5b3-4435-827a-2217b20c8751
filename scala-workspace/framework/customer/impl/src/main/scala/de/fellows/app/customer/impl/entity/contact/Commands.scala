package de.fellows.app.customer.impl.entity.contact

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.customer.impl.entity.CollaborativeInfo
import de.fellows.utils.FilePath
import de.fellows.utils.entities.CollaborativeEventInfo
import play.api.libs.json.{Format, Json}

import java.util.UUID

object Commands {

  sealed trait ContactCommand

  case class SetContact(
      team: String,
      customer: UUID,
      contact: UUID,
      content: InternalContact,
      info: CollaborativeEventInfo
  ) extends ContactCommand with ReplyType[ContactResponse]

  case class GetContact(
      team: String,
      customer: UUID,
      contact: UUID
  ) extends ContactCommand with ReplyType[ContactResponse]

  case class DeleteContact(
      team: String,
      customer: UUID,
      contact: UUID,
      info: CollaborativeEventInfo
  ) extends ContactCommand with ReplyType[Done]

  //  case class UpdateContact(
  //                            team: String,
  //                            customer: UUID,
  //                            contact: UUID,
  //                            content: InternalContact,
  //                            info: CollaborativeEventInfo
  //                          ) extends ContactCommand with ReplyType[InternalContact]

  case class SetCollaboration(
      team: String,
      customer: UUID,
      contact: UUID,
      collab: CollaborativeInfo,
      info: CollaborativeEventInfo
  ) extends ContactCommand with ReplyType[Done]

  case class SetImage(
      team: String,
      customer: UUID,
      contact: UUID,
      filePath: Option[FilePath],
      info: CollaborativeEventInfo
  ) extends ContactCommand with ReplyType[Done]

  case class ContactResponse(response: Option[InternalContact])

  object ContactResponse {
    implicit val format: Format[ContactResponse] = Json.format[ContactResponse]
  }

  object SetContact {
    implicit val format: Format[SetContact] = Json.format[SetContact]
  }

  object GetContact {
    implicit val format: Format[GetContact] = Json.format[GetContact]
  }

  object SetCollaboration {
    implicit val format: Format[SetCollaboration] = Json.format[SetCollaboration]
  }

  object SetImage {
    implicit val format: Format[SetImage] = Json.format[SetImage]
  }

  //  object UpdateContact {
  //    implicit val format = Json.format[UpdateContact]
  //  }

  object DeleteContact {
    implicit val format: Format[DeleteContact] = Json.format[DeleteContact]
  }

}
