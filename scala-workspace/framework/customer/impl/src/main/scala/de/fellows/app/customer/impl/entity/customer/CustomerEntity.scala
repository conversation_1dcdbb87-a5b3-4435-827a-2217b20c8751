package de.fellows.app.customer.impl.entity.customer

import akka.Done
import de.fellows.app.customer.impl.entity.customer.Commands._
import de.fellows.app.customer.impl.entity.customer.Events._
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.secure.SecureTeamEntity

class CustomerEntity(override implicit val service: ServiceDefinition)
    extends SecureTeamEntity[Option[InternalCustomer]] {
  override type Command = Commands.CustomerCommand
  override type Event   = Events.CustomerEvent

  override def initialState: State = None

  def missing(): Actions =
    Actions()
      .onReadOnlyCommand[GetCustomer, CustomerResponse] {
        case (x: GetCustomer, ctx, s) =>
          ctx.reply(CustomerResponse(s))
      }
      .onCommand[SetCustomer, CustomerResponse] {
        case (x: SetCustomer, ctx, s) =>
          ctx.thenPersist(
            CustomerSet(x.content.map(_.id).get, x.content.map(_.team).get, x.content, s, x.info)
          )(_ => ctx.reply(CustomerResponse(x.content)))
      }
      .onEvent {
        case (x: CustomerSet, s) => x.content
      }

  def or[T](a: Seq[T], b: Seq[T]): Seq[T] =
    a match {
      case x if x.nonEmpty => x
      case _               => b
    }

  def existing(st: InternalCustomer): Actions =
    Actions()
      .onReadOnlyCommand[GetCustomer, CustomerResponse] {
        case (x: GetCustomer, ctx, s) =>
          ctx.reply(CustomerResponse(s))
      }
      .onCommand[SetCustomer, CustomerResponse] {
        case (x: SetCustomer, ctx, s) =>
          val c =
            if (x.keepCollab) {
              x.content.map(_.copy(collab = st.collab))
            } else {
              x.content
            }
          ctx.thenPersist(CustomerSet(st.id, st.team, c, s, x.info))(_ => ctx.reply(CustomerResponse(c)))
      }
      .onCommand[SetCollaboration, Done] {
        case (x: SetCollaboration, ctx, s) =>
          s match {
            case Some(state) =>
              ctx.thenPersist(CollaborativeInfoSet(state.id, state.team, x.collab, x.info))(_ => ctx.reply(Done))
            case None =>
              ctx.invalidCommand("Entity does not exist")
              ctx.done
          }

      }
      .onCommand[SetImage, Done] {
        case (x: SetImage, ctx, s) =>
          s match {
            case Some(state) =>
              ctx.thenPersist(ImageSet(state.id, state.team, x.filePath, x.info))(_ => ctx.reply(Done))
            case None =>
              ctx.invalidCommand("Entity does not exist")
              ctx.done
          }

      }
      .onCommand[DeleteCustomer, Done] {
        case (x: DeleteCustomer, ctx, s) =>
          s match {
            case Some(state) =>
              ctx.thenPersist(CustomerDeleted(state.id, state.team, state.name, state.lumiquoteTenant, x.info))(_ =>
                ctx.reply(Done)
              )
            case None =>
              ctx.invalidCommand("Entity does not exist")
              ctx.done
          }

      }
      .onEvent {
        case (x: CustomerSet, s) =>
          x.content
        case (x: CustomerDeleted, s) => None
        //        case (x: CustomerUpdated, s) => s.map(y => updated(st, x.updates))
        case (x: ImageSet, s)             => s.map(_.copy(image = x.fp))
        case (x: CollaborativeInfoSet, s) => s.map(_.copy(collab = x.content))
      }

  //  private def updated(st: InternalCustomer, up: InternalCustomer): InternalCustomer = {
  //    st.copy(
  //      name = up.name.orElse(st.name),
  //      domain = up.domain.orElse(st.domain),
  //      image = up.image.orElse(st.image),
  //      addresses = or(up.addresses, st.addresses),
  //      email = up.email.orElse(st.email),
  //      phone = up.phone.orElse(st.phone),
  //      tags = or(up.tags, st.tags),
  //    )
  //  }

  override def entityBehavior(state: Option[InternalCustomer]): Actions = state match {
    case Some(st) => existing(st)
    case None     => missing()
  }

  override def isAllowed(a: CustomerCommand, s: Option[InternalCustomer]): Boolean = a match {
    case SetCustomer(content, info, keepCollab) => s.map(_.team).forall(_ == content.map(_.team).get)
    case GetCustomer(team, id)                  => s.map(_.team).forall(_ == team)
    //    case UpdateCustomer(content, info) => s.map(_.team).forall(_ == content.map(_.team).get)
    case DeleteCustomer(team, id, info)           => s.map(_.team).forall(_ == team)
    case SetImage(team, id, filePath, info)       => s.map(_.team).forall(_ == team)
    case SetCollaboration(team, id, collab, info) => s.map(_.team).forall(_ == team)
  }

}
