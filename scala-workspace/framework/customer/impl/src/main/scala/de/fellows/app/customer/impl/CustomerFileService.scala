package de.fellows.app.customer.impl

import akka.Done
import akka.stream.IOResult
import akka.stream.scaladsl.{FileIO, Sink}
import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.typesafe.config.ConfigFactory
import de.fellows.app.customer.impl.entity.contact.Commands.GetContact
import de.fellows.app.customer.impl.entity.contact.{Commands => ContactCommands, ContactEntity}
import de.fellows.app.customer.impl.entity.customer.Commands.GetCustomer
import de.fellows.app.customer.impl.entity.customer.{Commands => CustomerCommands, CustomerEntity}
import de.fellows.app.security.SecurityApi.PermissionRequest
import de.fellows.app.security.{AccessControl, SecurityBodyParser}
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.CollaborativeEventInfo
import de.fellows.utils.playutils.FileHelper
import de.fellows.utils.security._
import play.api.Logging
import play.api.http.FileMimeTypes
import play.api.libs.json.{JsString, Json}
import play.api.libs.streams.Accumulator
import play.api.mvc.MultipartFormData.FilePart
import play.api.mvc.{DefaultActionBuilder, Handler, PlayBodyParsers, Results}
import play.api.routing.Router
import play.api.routing.sird._
import play.core.parsers.Multipart.{FileInfo, FilePartHandler}

import java.io.File
import java.util.UUID
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.Success

class CustomerFileService(
    action: DefaultActionBuilder,
    parser: PlayBodyParsers,
    registry: PersistentEntityRegistry,
    mime: FileMimeTypes,
    exCtx: ExecutionContext
)(implicit sd: ServiceDefinition) extends Logging {
  implicit val x: ExecutionContext = exCtx
  lazy val conf                    = ConfigFactory.load()
  implicit val m: FileMimeTypes    = mime;
  private val filesize: Long       = 2 * 1024 * **********

  val basePath: String =
    conf.getString("fellows.storage.service")

  private def handleValidFile(
      team: String,
      targetFile: FilePath,
      partName: String,
      filename: String,
      contentType: Option[String]
  ): Future[Accumulator[ByteString, FilePart[FilePath]]] = {
    targetFile.createParentDir()

    val sink: Sink[ByteString, Future[IOResult]] = FileIO.toPath(new File(targetFile.toPath).toPath)
    val acc: Accumulator[ByteString, IOResult]   = Accumulator(sink)
    Future.successful {
      acc.map {
        case akka.stream.IOResult(_, _) =>
          FilePart(partName, filename, contentType, targetFile)
      }
    }
  }

  private def avatarHandler(token: GenericTokenContent, team: String, customer: UUID): FilePartHandler[FilePath] = {
    case FileInfo(partName, fileName, contentType, s) =>
      contentType match {
        case _ =>
          val f =
            registry.refFor[CustomerEntity](customer.toString).ask(GetCustomer(token.getTeam, customer)).map(_.response)
              .flatMap {
                case None => throw new TransportException(TransportErrorCode.NotFound, "Customer not found")
                case Some(customer) =>
                  val path = FilePath(basePath, team, customer.id.toString, "avatar", None, fileName)
                  handleValidFile(token.getTeam, path, partName, fileName, contentType)
              }
          Await.result(f, 10 seconds)
      }
  }

  def avatarHandler(
      token: GenericTokenContent,
      team: String,
      customer: UUID,
      contact: UUID
  ): FilePartHandler[FilePath] = {
    case FileInfo(partName, fileName, contentType, s) =>
      contentType match {
        case _ =>
          val f = registry.refFor[ContactEntity](contact.toString).ask(GetContact(token.getTeam, customer, contact))
            .map(_.response)
            .flatMap {
              case None => throw new TransportException(TransportErrorCode.NotFound, "Customer not found")
              case Some(contact) =>
                val path = FilePath(basePath, team, contact.id.toString, "avatar", None, fileName)
                handleValidFile(token.getTeam, path, partName, fileName, contentType)
            }
          Await.result(f, 10 seconds)
      }
  }

  def uploadAvatar(customer: String, contact: String): Handler = {
    val custId                              = UUID.fromString(customer)
    val contId                              = UUID.fromString(contact)
    var tkncnt: Option[GenericTokenContent] = None
    action(
      SecurityBodyParser(token =>
        s"profile:${
            token.getTeam
          }:${
            customer
          }:${
            contact
          }:avatar:write"
      ) { token =>
        tkncnt = Some(token)
        parser.multipartFormData(avatarHandler(token, token.getTeam, custId, contId), filesize)
      }
    ) {

      request =>
        val files = request.body.files

        val entity = registry.refFor[ContactEntity](contact)
        val res = Future.sequence(files.map(f =>
          entity.ask(ContactCommands.SetImage(
            tkncnt.get.getTeam,
            custId,
            contId,
            Some(f.ref),
            CollaborativeEventInfo(tkncnt.get)
          ))
        ))
        res.map { _ =>
          println(s"uploaded file ${files}")
          Done
        }

        val j = Json.obj(
          "file" -> JsString(files.head.ref.toApi)
        )
        Results.Ok(Json.stringify(j))
    }
  }

  def uploadAvatar(customer: String): Handler = {
    val custId                              = UUID.fromString(customer)
    var tkncnt: Option[GenericTokenContent] = None

    action(
      SecurityBodyParser(token =>
        s"profile:${
            token.getTeam
          }:${
            customer
          }:${
            customer
          }:avatar:write"
      ) { token =>
        tkncnt = Some(token)
        println(s"try upload avatar for team ${token.getTeam}, customer ${custId}")
        parser.multipartFormData(avatarHandler(token, token.getTeam, custId), filesize)
      }
    ) {

      request =>
        val files = request.body.files
        //        val files = request.body.files.map(_.contentType)

        val entity = registry.refFor[CustomerEntity](customer)
        val res = Future.sequence(files.map(f =>
          entity.ask(CustomerCommands.SetImage(
            tkncnt.get.getTeam,
            custId,
            Some(f.ref),
            CollaborativeEventInfo(tkncnt.get)
          ))
        ))
        res.map(_ => Done)

        val j = Json.obj(
          "file" -> JsString(files.head.ref.toApi)
        )
        Results.Ok(Json.stringify(j))
    }
  }

  def downloadAvatar(customer: String, relPath: String): Handler = {
    val custId   = UUID.fromString(customer)
    val resource = s"${customer}"

    download(resource, relPath)
  }

  def downloadAvatar(customer: String, contact: String, relPath: String): Handler = {
    UUID.fromString(customer)
    UUID.fromString(contact)
    val resource = s"$contact"

    download(resource, relPath)
  }

  def download(reqResource: String, relPath: String): Handler =
    action(parser.anyContent) {
      request =>
        val koption = request.queryString.get("k")
        logger.warn(s"get image $reqResource and $relPath with token $koption")
        koption match {
          case Some(k) if k.length == 1 =>
            AuthenticationServiceComposition.decodeAnyToken(Some(request.headers), k.headOption) match {
              case Success(token: FileTokenContent) =>
                val original = FilePath(basePath, token.team, reqResource, "avatar", None, relPath)

                if (
                  token.claims.forall(c =>
                    c.service != "customer" ||
                      c.resource != reqResource ||
                      c.path != s"avatar/$relPath"
                  )
                ) {
                  Results.Forbidden(s"Permission Denied")
                } else {
                  if (original.toJavaFile.exists()) {
                    FileHelper.deliverFile(original.toJavaFile, relPath, request.headers)
                  } else {
                    logger.error(s"File does not exist: $original: ${original.toPath}")
                    Results.NotFound(s"Avatar not found")
                  }
                }

              case Success(token: TokenContent) =>
                val original = FilePath(basePath, token.team, reqResource, "", None, relPath)
                val req      = s"assembly:${token.team}:${token.team}:${reqResource}:files:read"
                val p        = token.claims.flatMap(_.permission).map(Permission.create)

                val pl = AccessControl.checkPermissionsRequest(
                  PermissionRequest(
                    Seq(Seq(Permission.create(req)))
                  ),
                  p
                )

                if (pl.allowed) {
                  FileHelper.deliverFile(original.toJavaFile, relPath, request.headers)
                } else {
                  Results.Forbidden(s"Permission Denied")
                }

              case _ => Results.Forbidden(s"Permission Denied")
            }

          case _ => Results.Forbidden("Invalid Key")
        }
    }

  // @formatter:off
  def router: Router = {
    println("create route")
    Router.from{
      case POST(p"/files/customer/customers/$customer/avatar") =>
        uploadAvatar(customer)

      case POST(p"/files/customer/customers/$customer/contacts/$contact/avatar") =>
        uploadAvatar(customer, contact)
      case GET(p"/files/customer/customers/$customer/avatar/$path*") =>
        downloadAvatar(customer, path)

      case GET(p"/files/customer/customers/$customer/contacts/$contact/avatar/$path*") =>
        downloadAvatar(customer, contact, path)

    }
  }
  // @formatter:on
}
