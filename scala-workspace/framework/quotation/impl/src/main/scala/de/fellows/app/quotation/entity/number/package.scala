package de.fellows.app.quotation.entity

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEvent, AggregateEventShards, AggregateEventTag }
import play.api.libs.json.{ Format, Json }

package object number {

  sealed trait NumberCommand

  sealed trait NumberEvent extends AggregateEvent[NumberEvent] {
    override def aggregateTag: AggregateEventShards[NumberEvent] = NumberEvent.Tag
  }

  object NumberEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[NumberEvent](NumShards)
  }

  case class AllocateNumbers(count: Int) extends NumberCommand with ReplyType[NumberResponse]

  case class InitializeNumber(next: BigInt) extends NumberCommand with ReplyType[Done]

  case class NumberResponse(response: Seq[BigInt])

  object PeekNext extends NumberCommand with ReplyType[NumberResponse] {
    implicit val format: Format[PeekNext.type] = Json.format

  }

  case class NumbersAllocated(numbers: Seq[BigInt], next: BigInt) extends NumberEvent

  case class NumberInitialized(next: BigInt) extends NumberEvent

  object AllocateNumbers {
    implicit val format: Format[AllocateNumbers] = Json.format
  }

  object NumberResponse {
    implicit val format: Format[NumberResponse] = Json.format
  }

  object InitializeNumber {
    implicit val format: Format[InitializeNumber] = Json.format
  }

  object NumbersAllocated {
    implicit val format: Format[NumbersAllocated] = Json.format
  }

  object NumberInitialized {
    implicit val format: Format[NumberInitialized] = Json.format
  }
}
