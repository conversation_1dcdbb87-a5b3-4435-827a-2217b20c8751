package de.fellows.app.quotation.read

import akka.Done
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.quotation.Quotation
import de.fellows.app.quotation.QuotationOrigin.{APIQuotation, ManualQuotation}
import de.fellows.app.quotation.entity.quotation.QuotationEvents.{QuotationCreated, QuotationEvent, QuotationUpdated}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging

import java.time.Instant
import scala.concurrent.{ExecutionContext, Future}

class QuotationOriginIndex(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[QuotationEvent] with StackrateLogging {
  var setQuotationByOriginStmt: PreparedStatement    = _
  var deleteQuotationByOriginStmt: PreparedStatement = _

  private def createTables(): Future[Done.type] =
    for {
      f <- Future.successful(Done)
    } yield Done

  def prepareStatements(): Future[Done] =
    for {
      deleteQuotationByOrigin <-
        session.prepare(
          "DELETE FROM quotationsbyorigin WHERE team = :team AND origin = :origin AND quotation = :quotation"
        )

      setQuotationByOrigin <- session.prepare(
        """
          | UPDATE quotationsbyorigin SET
          |  time = :time
          | WHERE team = :team AND origin = :origin AND quotation = :quotation
          |""".stripMargin
      )

    } yield {
      setQuotationByOriginStmt = setQuotationByOrigin
      deleteQuotationByOriginStmt = deleteQuotationByOrigin

      Done
    }

  private def removeQuotationByName(quotation: Quotation) = {
    println(s"remove quotation $quotation")
    List(
      deleteQuotationByOriginStmt.bind()
        .setString("origin", quotation.origin.entryName)
        .setString("team", quotation.team)
        .setUUID("quotation", quotation.quotationId)
    )
  }

  val backwardsCompatibilityCutoff = Instant.ofEpochMilli(1706005016963L)
  private def setQuotationByName(quotation: Quotation) = {
    val backwardsCompatibilityNeeded = quotation.created.isBefore(backwardsCompatibilityCutoff)

    val origin =
      if (backwardsCompatibilityNeeded) {
        if (quotation.name.startsWith("R")) {
          APIQuotation
        } else {
          ManualQuotation
        }
      } else {
        quotation.origin
      }

    Seq(
      setQuotationByOriginStmt.bind()
        .setString("origin", origin.entryName)
        .setString("team", quotation.team)
        .setUUID("quotation", quotation.quotationId)
        .set("time", Instant.now(), classOf[Instant])
    )
  }

  def updateQuotation(q: Quotation): Future[Seq[BoundStatement]] =
    Future.successful(setQuotationByName(q))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[QuotationEvent] =
    readSide.builder[QuotationEvent]("quotationOriginEventOffset-v1.0")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[QuotationCreated] { e =>
        updateQuotation(e.event.quotation)
      }
      .setEventHandler[QuotationUpdated](e => updateQuotation(e.event.quotation))
      .build()

  override def aggregateTags: Set[AggregateEventTag[QuotationEvent]] = QuotationEvent.Tag.allTags

}
