package de.fellows.app.quotation

import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import de.fellows.utils.JsonFormats.singletonFormat
import de.fellows.utils.communication.ServiceError
import play.api.libs.json.Format

object QuotationExceptions {

  object QuotationNotFound extends ServiceError(1, "Quotation Not Found", TransportErrorCode.NotFound) {
    @transient implicit val format: Format[QuotationNotFound.type] = singletonFormat(QuotationNotFound)
  }

  object QuotationExists extends ServiceError(1, "Quotation Exists", TransportErrorCode.PolicyViolation) {
    @transient implicit val format: Format[QuotationExists.type] = singletonFormat(QuotationExists)
  }

  object QuotationItemNotFound extends ServiceError(1, "Quotation Item Not Found", TransportErrorCode.NotFound) {
    @transient implicit val format: Format[QuotationExists.type] = singletonFormat(QuotationExists)
  }
}
