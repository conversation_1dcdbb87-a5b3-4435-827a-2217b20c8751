package de.fellows.app.quotation

import com.lightbend.lagom.scaladsl.playjson.{JsonMigration, JsonSerializer, JsonSerializerRegistry}
import de.fellows.app.quotation.QuotationOrigin.{APIQuotation, ManualQuotation}
import de.fellows.app.quotation.entity.number.{
  AllocateNumbers,
  InitializeNumber,
  NumberInitialized,
  NumberResponse,
  NumbersAllocated,
  PeekNext
}
import de.fellows.app.quotation.entity.pricelist.PriceListCommands.SetPriceInfo
import de.fellows.app.quotation.entity.pricelist.PriceListEvents.PriceListSet
import de.fellows.app.quotation.entity.quotation.QuotationCommands._
import de.fellows.app.quotation.entity.quotation.QuotationEvents.{
  AddressesSet,
  QuotationCreated,
  QuotationItemAdded,
  QuotationItemRemoved,
  QuotationStatusSet,
  QuotationTimelineChanged,
  QuotationUpdated
}
import de.fellows.app.quotation.entity.quotationitem.QuotationItemCommands.{
  DeleteQuotationItem,
  GetQuotationItem,
  SetQuotationItem,
  SetQuotationItemInfo
}
import de.fellows.app.quotation.entity.quotationitem.QuotationItemEvents.{
  QuotationItemChanged,
  QuotationItemDeleted,
  QuotationItemInfoSet,
  QuotationItemTimelineChange
}
import de.fellows.utils.logging.StackrateLogging
import play.api.libs.json.{JsDefined, JsObject, JsUndefined, JsValue, Json}
import de.fellows.app.quotation.entity.quotationitem.QuotationItemCommands.SetQuotationItemWon
import de.fellows.app.quotation.entity.quotationitem.QuotationItemEvents.QuotationItemWonSet

object QuotationServiceSerializerRegistry
    extends JsonSerializerRegistry with StackrateLogging {
  override def serializers =
    List(
      JsonSerializer[CreateQuotation],
      JsonSerializer[GetQuotation],
      JsonSerializer[AddQuotationItem],
      JsonSerializer[RemoveQuotationItem],
      JsonSerializer[SetAddress],
      JsonSerializer[QuotationCreated],
      JsonSerializer[AddressesSet],
      JsonSerializer[GetQuotationItem],
      JsonSerializer[SetQuotationItem],
      JsonSerializer[DeleteQuotationItem],
      JsonSerializer[SetQuotationItemInfo],
      JsonSerializer[QuotationItemChanged],
      JsonSerializer[QuotationItemAdded],
      JsonSerializer[QuotationItemRemoved],
      JsonSerializer[QuotationItemDeleted],
      JsonSerializer[QuotationItemInfoSet],
      JsonSerializer[PriceBreak],
      JsonSerializer[PriceList],
      JsonSerializer[Quotation],
      JsonSerializer[QuotationItem],
      JsonSerializer[PricedAssembly],
      JsonSerializer[SetPriceInfo],
      JsonSerializer[PriceListSet],
      JsonSerializer[SetQuotation],
      JsonSerializer[QuotationUpdated],
      JsonSerializer[QuotationStatusSet],
      JsonSerializer[SetQuotationStatus],
      JsonSerializer[AllocateNumbers],
      JsonSerializer[PeekNext.type],
      JsonSerializer[InitializeNumber],
      JsonSerializer[NumbersAllocated],
      JsonSerializer[QuotationItemTimelineChange],
      JsonSerializer[QuotationTimelineChanged],
      JsonSerializer[NumberInitialized],
      JsonSerializer[NumberResponse],
      JsonSerializer[SetQuotationItemWon],
      JsonSerializer[QuotationItemWonSet]
    )

  private def addOrigin(json: JsObject): JsValue =
    json \ "name" match {
      case JsDefined(value) =>
        val name = value.as[String]

        val origin =
          if (name.startsWith("R")) {
            APIQuotation
          } else {
            ManualQuotation
          }

        json + ("origin" -> Json.toJson(origin))

      case undefined: JsUndefined =>
        logger.error(s"failed to migrate quotation ${Json.stringify(json)}")
        json
    }
  def migrateQuotation: JsonMigration =
    new JsonMigration(currentVersion = 2) {

      override def transform(fromVersion: Int, json: JsObject): JsValue =
        if (fromVersion < 2) {
          addOrigin(json)
        } else {
          json
        }
    }

  def migrateQuotationCreated: JsonMigration =
    new JsonMigration(currentVersion = 2) {
      override def transform(fromVersion: Int, json: JsObject): JsValue =
        if (fromVersion < 2) {
          json \ "quotation" match {
            case JsDefined(quotation) =>
              val updatedOrigin = addOrigin(quotation.as[JsObject])

              json + ("quotation" -> updatedOrigin)

            case undefined: JsUndefined =>
              logger.error(s"failed to update quotation created ${Json.stringify(json)}")
              json
          }
        } else {
          json
        }

    }

  def migrateQuotationUpdated: JsonMigration =
    new JsonMigration(currentVersion = 2) {
      override def transform(fromVersion: Int, json: JsObject): JsValue =
        if (fromVersion < 2) {

          val q  = (json \ "quotation")
          val oq = (json \ "oldQuotation")

          val updatedQuotation =
            q.toOption.map(_.as[JsObject]).map(q => json + ("quotation" -> addOrigin(q))).getOrElse(json)
          val updatedBoth = oq.toOption.map(_.as[JsObject]).map(q =>
            updatedQuotation + ("oldQuotation" -> addOrigin(q))
          ).getOrElse(updatedQuotation)

          updatedBoth
        } else {
          json
        }
    }

  override def migrations: Map[String, JsonMigration] = Map(
    classOf[Quotation].getName        -> migrateQuotation,
    classOf[QuotationCreated].getName -> migrateQuotationCreated,
    classOf[QuotationUpdated].getName -> migrateQuotationUpdated
  )
}
