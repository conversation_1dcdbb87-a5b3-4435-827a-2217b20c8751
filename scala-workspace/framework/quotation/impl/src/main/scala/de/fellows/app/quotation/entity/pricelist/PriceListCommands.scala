package de.fellows.app.quotation.entity.pricelist

import java.util.UUID

import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.quotation.{ PriceInfo, PricedAssembly }
import play.api.libs.json.{ Format, Json }

object PriceListCommands {

  sealed trait PriceListCommand

  case class SetPriceInfo(info: PriceInfo)     extends PriceListCommand with ReplyType[PricedAssembly]
  case class GetPriceInfo(specification: UUID) extends PriceListCommand with ReplyType[PricedAssembly]

  case object SetPriceInfo {
    implicit val format: Format[SetPriceInfo] = Json.format
  }

  case object GetPriceInfo {
    implicit val format: Format[GetPriceInfo] = Json.format
  }

}
