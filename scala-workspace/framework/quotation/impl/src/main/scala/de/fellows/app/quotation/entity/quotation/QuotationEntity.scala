package de.fellows.app.quotation.entity.quotation

import akka.Done
import com.lightbend.lagom.scaladsl.api.transport.{PolicyViolation, TransportErrorCode}
import de.fellows.app.assembly.commons.{AssemblyReference, SharedAssemblyReference}
import de.fellows.app.quotation.QuotationExceptions.{QuotationExists, QuotationNotFound}
import de.fellows.app.quotation.entity.quotation.QuotationCommands._
import de.fellows.app.quotation.entity.quotation.QuotationEvents._
import de.fellows.app.quotation.{Quotation, QuotationCreation, QuotationItemReference, QuotationStatus}
import de.fellows.utils.collaboration.{TimelineCommand, TimelineEvent, TimelineFeatures}
import de.fellows.utils.common.EntityException
import de.fellows.utils.communication.{ServiceDefinition, ServiceException}
import de.fellows.utils.entities.secure.SecureTeamEntity

import java.time.Instant
import java.util.UUID

class QuotationEntity(implicit val sd: ServiceDefinition) extends SecureTeamEntity[Option[Quotation]]
    with TimelineFeatures[QuotationTimelineChanged] {
  override type Command = QuotationCommand
  override type Event   = QuotationEvent

  override def initialState: State = None

  override def entityType: String = "quotation"

  override def changeCategory: String = "quotation"

  override def event(tl: TimelineEvent): QuotationTimelineChanged = QuotationTimelineChanged(tl)

  def checkState(st: Quotation, allowed: Seq[String]) =
    if (!allowed.contains(st.status.name)) {
      throw PolicyViolation(s"Action not allowed in state ${st.status.name}")
    }

  def existing(st: Quotation): Actions =
    Actions()
      .onReadOnlyCommand[CreateQuotation, Quotation] {
        case (x: CreateQuotation, ctx, s) =>
          ctx.commandFailed(new ServiceException(QuotationExists))
      }
      .onReadOnlyCommand[GetQuotation, Quotation] {
        case (x: GetQuotation, ctx, s) =>
          ctx.reply(st)
      }
      .onCommand[SetQuotation, Quotation] {
        case (x: SetQuotation, ctx, s) =>
          val quot =
            toQuotation(x.team, x.id, x.c, st.creator, st.created)
              .copy(
                status = st.status,
                items = st.items
              )

          val timelineID = st.assembly match {
            case AssemblyReference(team, id, gid, version)         => id
            case SharedAssemblyReference(team, id, sharedAssembly) => id
          }
          val updated = QuotationUpdated(quot, st)
          ctx.thenPersistAll(
            updated,
            timelineEvent(x.team, updated, x.tcmd, st.quotationId.toString, eventParams(st)),
            timelineEvent(x.team, updated, x.tcmd, timelineID.toString, eventParams(st))
          )(() => ctx.reply(quot))
      }
      .onCommand[AddQuotationItem, Quotation] {
        case (x: AddQuotationItem, ctx, s) =>
          checkState(st, QuotationStatus.OPEN)

          val updated = st.copy(items = Some(st.items.getOrElse(Seq()) :+ QuotationItemReference(x.item, None)))

          val event = QuotationItemAdded(st.quotationId, x.item)
          ctx.thenPersistAll(
            event,
            timelineEvent(x.team, event, x.tcmd, st.quotationId.toString, eventParams(st))
          )(() => ctx.reply(updated))
      }
      .onCommand[RemoveQuotationItem, Quotation] {
        case (x: RemoveQuotationItem, ctx, s) =>
          checkState(st, QuotationStatus.OPEN)

          val contains = st.items match {
            case Some(l) => l.find(_.id == x.item)
            case None    => None
          }

          val updated = removeItem(st, x.item)
          contains match {
            case Some(item) =>
              val event = QuotationItemRemoved(st.quotationId, item.id)
              ctx.thenPersistAll(
                event,
                timelineEvent(x.team, event, x.tcmd, st.quotationId.toString, eventParams(st))
              )(() => ctx.reply(updated))

            case None =>
              ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Quotation Item not found"))
              ctx.done
          }
      }
      .onCommand[SetQuotationStatus, Quotation] {
        case (x: SetQuotationStatus, ctx, s) =>
          //          if (x.status.name == QuotationStatus.DELETED_NAME) {
          //            checkState(st, QuotationStatus.OPEN)
          //          } else if (x.status.name == QuotationStatus.ARCHIVED_NAME) {
          //            checkState(st, QuotationStatus.ACTIVE)
          //          } else if (QuotationStatus.ACTIVE.contains(x.status.name)) {
          //            checkState(st, QuotationStatus.ACTIVE ++ QuotationStatus.OPEN)
          //          }

          val event = QuotationStatusSet(st.team, st.quotationId, st.customerId, st.name, st.status, x.status)
          ctx.thenPersistAll(
            event,
            timelineEvent(x.team, event, x.tcmd, st.quotationId.toString, eventParams(st)),
            timelineEvent(x.team, event, x.tcmd, st.assembly.toString, eventParams(st))
          )(() => ctx.reply(st.copy(status = x.status)))
      }
      .onCommand[SetAddress, Done] {
        case (x: SetAddress, ctx, s) =>
          val updated =
            if (x.billing) {
              st.copy(billing = Some(x.address))
            } else {
              st.copy(shipping = Some(x.address))
            }

          val event = AddressesSet(
            updated.billing,
            updated.shipping,
            st.team,
            st.quotationId,
            st.customerId
          )
          ctx.thenPersistAll(
            event,
            timelineEvent(x.team, event, x.tcmd, st.quotationId.toString, eventParams(st))
          )(() => ctx.reply(Done))
      }
      //      .onCommand[SetQuotationItemInfo, QuotationItem]{
      //        case (x: SetQuotationItemInfo, ctx, s) =>
      //          checkState(st, QuotationStatus.OPEN)
      //
      //          st.items.getOrElse(Seq()).find(_.id == x.item) match {
      //            case None =>
      //              ctx.commandFailed(new ServiceException(QuotationItemNotFound))
      //              ctx.done
      //
      //            case Some(qi) =>
      //
      //              val updated = qi.copy(
      //                info = Some(x.info),
      //              )
      //
      //              ctx.thenPersist(
      //                QuotationItemInfoSet(st.team, st.quotationId, st.customerId, x.item, qi.assembly, x.info, qi.info)
      //              )(_ => ctx.reply(updated))
      //          }
      //
      //      }
      .onEvent {
        //        case (x: QuotationItemCreated, _) =>
        //          checkState(st, QuotationStatus.OPEN)
        //
        //          Some(st.copy(
        //            items = Some(st.items.getOrElse(Seq()) :+ x.item)
        //          ))
        //        case (x: QuotationItemInfoSet, _) =>
        //          Some(st.copy(
        //            items = st.items.map(_.map{
        //              case i if i.id == x.item => i.copy(info = Some(x.info))
        //              case i => i
        //            })
        //          ))
        case (x: AddressesSet, _) =>
          Some(st.copy(
            shipping = x.shipping,
            billing = x.billing
          ))
        case (x: QuotationItemRemoved, _) =>
          Some(removeItem(st, x.item))
        case (x: QuotationItemAdded, _) =>
          Some(st.copy(
            items = Some(st.items.getOrElse(Seq()) :+ QuotationItemReference(x.item, None))
          ))
        case (x: QuotationStatusSet, _) =>
          Some(st.copy(status = x.status))
        case (x: QuotationUpdated, _) =>
          Some(x.quotation)
        case (x: QuotationTimelineChanged, s) => s
      }

  private def removeItem(st: Quotation, x: UUID) =
    st.copy(items = st.items.map(_.filter(_.id != x)))

  def missing: Actions =
    Actions()
      .onCommand[CreateQuotation, Quotation] {
        case (x: CreateQuotation, ctx, s) =>
          val quot =
            toQuotation(x.team, x.id, x.c, x.createdBy, x.created)

          val event = QuotationCreated(quot)
          val timelineID = x.c.assembly match {
            case AssemblyReference(team, id, gid, version)         => id
            case SharedAssemblyReference(team, id, sharedAssembly) => id
          }
          ctx.thenPersistAll(
            event,
            timelineEvent(x.team, event, x.tcmd, x.id.toString, eventParams(x.c)),
            timelineEvent(x.team, event, x.tcmd, timelineID.toString, eventParams(x.c))
          )(() => ctx.reply(quot))
      }
      .onReadOnlyCommand[GetQuotation, Quotation] {
        case (x: GetQuotation, ctx, s) =>
          ctx.commandFailed(new ServiceException(QuotationNotFound))
      }
      .onReadOnlyCommand[AddQuotationItem, Quotation] {
        case (x: AddQuotationItem, ctx, s) =>
          ctx.commandFailed(new ServiceException(QuotationNotFound))
      }
      .onReadOnlyCommand[RemoveQuotationItem, Quotation] {
        case (x: RemoveQuotationItem, ctx, s) =>
          ctx.commandFailed(new ServiceException(QuotationNotFound))
      }
      .onReadOnlyCommand[SetQuotationStatus, Quotation] {
        case (x: RemoveQuotationItem, ctx, s) =>
          ctx.commandFailed(new ServiceException(QuotationNotFound))
      }
      .onEvent {
        case (x: QuotationCreated, _)         => Some(x.quotation)
        case (x: QuotationTimelineChanged, s) => s
      }

  private def eventParams(x: QuotationCreation): Map[String, String] =
    Seq(
      x.name.map("name"           -> _),
      x.externalID.map("external" -> _)
    ).flatten.toMap

  private def eventParams(x: Quotation): Map[String, String] =
    Seq(
      Some("name"                 -> x.name),
      x.externalID.map("external" -> _)
    ).flatten.toMap

  private def toQuotation(team: String, id: UUID, x: QuotationCreation, user: UUID, time: Instant) =
    Quotation(
      team = team,
      assembly = x.assembly,
      quotationId = id,
      customerId = x.customerId,
      name = x.name.get,
      externalID = x.externalID,
      description = x.description,
      requestID = x.requestID,
      requestBy = x.requestBy,
      created = time,
      creator = user,
      assignee = x.assignee,
      status = QuotationStatus.DRAFT(time),
      contactId = x.contactId,
      billing = x.billing,
      shipping = x.shipping,
      items = x.items.map(_.map(u => QuotationItemReference(u, None))),
      publicNotes = x.publicNotes,
      validFor = x.validFor,
      requestDate = x.requestDate,
      origin = x.origin
    )

  override def entityBehavior(state: Option[Quotation]): Actions = state match {
    case Some(st) => existing(st)
    case None     => missing
  }

  override def isAllowed(a: QuotationCommand, s: Option[Quotation]): Boolean = a match {
    case CreateQuotation(team, id, c, cre, creBy, tcmd: TimelineCommand) => s.isEmpty
    case SetQuotationStatus(team, quotation, status, tcmd: TimelineCommand) =>
      s.map(_.team).forall(_ == team) && s.map(_.quotationId).forall(_ == quotation)
    case AddQuotationItem(team, q, item, tcmd: TimelineCommand) => s.map(_.team).forall(_ == team)
    case RemoveQuotationItem(team, q, item, tcmd: TimelineCommand) =>
      s.map(_.team).forall(_ == team) && s.map(_.quotationId).forall(_ == q)
    case SetQuotation(team, id, c, tcmd: TimelineCommand) =>
      s.map(_.team).forall(_ == team) && s.map(_.quotationId).forall(_ == id)
    case GetQuotation(team, id) => s.map(_.team).forall(_ == team) && s.map(_.quotationId).forall(_ == id)
    case SetAddress(t, q, billing, address, tcmd: TimelineCommand) =>
      s.map(_.team).forall(_ == t) && s.map(_.quotationId).forall(_ == q)
  }
}
