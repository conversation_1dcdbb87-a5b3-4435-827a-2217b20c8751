package de.fellows.app.quotation.entity.pricelist

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import de.fellows.app.quotation.PricedAssembly
import de.fellows.app.quotation.entity.pricelist.PriceListCommands.{ GetPriceInfo, PriceListCommand, SetPriceInfo }
import de.fellows.app.quotation.entity.pricelist.PriceListEvents.{ PriceListEvent, PriceListSet }

class PriceListEntity extends PersistentEntity {
  override type Command = PriceListCommand
  override type Event   = PriceListEvent
  override type State   = PricedAssembly

  override def initialState: State = new PricedAssembly(Seq())

  override def behavior: Behavior =
    Actions().onCommand[SetPriceInfo, PricedAssembly] {
      case (x: SetPriceInfo, ctx, s) =>
        val (a, rest) = s.lists.partition(l =>
          l.specification == x.info.specification &&
            l.supplier == x.info.supplier &&
            l.version == x.info.version
        )

        val updated = s.copy(rest :+ x.info)

        ctx.thenPersist(
          PriceListSet(x.info.version, x.info, updated)
        )(_ => ctx.reply(updated))
    }
      .onReadOnlyCommand[GetPriceInfo, PricedAssembly] {
        case (x: GetPriceInfo, ctx, s) =>
          ctx.reply(s)
      }
      .onEvent {
        case (x: PriceListSet, _) => x.infos
      }
}
