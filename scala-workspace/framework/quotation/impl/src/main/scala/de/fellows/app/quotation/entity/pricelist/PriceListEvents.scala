package de.fellows.app.quotation.entity.pricelist

import java.util.UUID

import com.lightbend.lagom.scaladsl.persistence.{ AggregateEvent, AggregateEventShards, AggregateEventTag }
import de.fellows.app.quotation.{ PriceInfo, PricedAssembly }
import play.api.libs.json.{ Format, Json }

object PriceListEvents {

  sealed trait PriceListEvent extends AggregateEvent[PriceListEvent] {
    override def aggregateTag: AggregateEventShards[PriceListEvent] = PriceListEvent.Tag
  }

  object PriceListEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[PriceListEvent](NumShards)
  }

  case class PriceListSet(version: UUID, updated: PriceInfo, infos: PricedAssembly) extends PriceListEvent

  case object PriceListSet {
    implicit val format: Format[PriceListSet] = Json.format
  }

}
