package de.fellows.app.quotation.listeners

import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.{ AssemblyService, VersionDescription }
import de.fellows.app.customer.api.Topics.CustomerMessage
import de.fellows.app.customer.api.{ Customer, CustomerService }

import scala.concurrent.ExecutionContext

class CustomerListener(cs: CustomerService, reg: PersistentEntityRegistry, sess: CassandraSession)(implicit
    c: ExecutionContext
) {}
