package de.fellows.app.quotation

import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.inbox.api.InboxService
import de.fellows.app.profile.api.ProfileService
import de.fellows.app.quotation.entity.number.NumberEntity
import de.fellows.app.quotation.entity.quotation.QuotationEntity
import de.fellows.app.quotation.entity.quotationitem.QuotationItemEntity
import de.fellows.app.quotation.listeners.{AssemblyListener, CustomerListener}
import de.fellows.app.quotation.read._
import de.fellows.app.supplier.SupplierService
import de.fellows.app.user.api.UserService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.mailgun.MailgunService
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import de.fellows.utils.health.HealthCheckComponents

class QuotationServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext) =
    new QuotationServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new QuotationServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[QuotationService])
}

trait QuotationServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("quotation")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = QuotationServiceSerializerRegistry

  lazy val notificationRepository = wire[PricedAssemblyRepo]

  val processor = wire[PriceListEventProcessor]
  readSide.register(processor)

  val quotationprocessor = wire[QuotationReadSide]
  readSide.register(quotationprocessor)

  val quotationitemprocessor = wire[QuotationItemReadSide]
  readSide.register(quotationitemprocessor)

  val quotationstatusprocessor = wire[QuotationStatusIndex]
  readSide.register(quotationstatusprocessor)

  val quotationcreatorprocessor = wire[QuotationCreatorIndex]
  readSide.register(quotationcreatorprocessor)

  val quotationcontentprocessor = wire[QuotationContentReadSideProcessor]
  readSide.register(quotationcontentprocessor)

  val quotationoriginprocessor = wire[QuotationOriginIndex]
  readSide.register(quotationoriginprocessor)

  //  persistentEntityRegistry.register(wire[PriceListEntity])
  persistentEntityRegistry.register(wire[QuotationEntity])
  persistentEntityRegistry.register(wire[QuotationItemEntity])
  persistentEntityRegistry.register(wire[NumberEntity])

}

abstract class QuotationServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with QuotationServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)

  override lazy val lagomServer = serverFor[QuotationService](wire[QuotationServiceImpl])

  lazy val quotationContentRepo = wire[QuotationContentReadSide]
  lazy val quotationRepo        = wire[QuotationRepository]
  lazy val quotationItemRepo    = wire[QuotationItemRepository]

  lazy val supplierService   = serviceClient.implement[SupplierService]
  lazy val assService        = serviceClient.implement[AssemblyService]
  lazy val custService       = serviceClient.implement[CustomerService]
  lazy val pcbService        = serviceClient.implement[PCBService]
  lazy val userService       = serviceClient.implement[UserService]
  lazy val mailgunService    = serviceClient.implement[MailgunService]
  lazy val inboxService      = serviceClient.implement[InboxService]
  lazy val panelService      = serviceClient.implement[PanelService]
  lazy val layerstackService = serviceClient.implement[LayerstackService]
  lazy val profileService    = serviceClient.implement[ProfileService]

  implicit val assListener: AssemblyListener  = wire[AssemblyListener]
  implicit val custListener: CustomerListener = wire[CustomerListener]
  //    .additionalRouter(fileRouter.router)

  //  lazy val fileRouter: AssemblyFileUploadService = wire[AssemblyFileUploadService].withApp(this)

}
