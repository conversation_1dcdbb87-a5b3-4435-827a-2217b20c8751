package de.fellows.app.assemby.api

import de.fellows.utils.internal.{FileLifecycleStageName, LifecycleStageName}
import play.api.libs.json._

sealed trait AssemblyLifecycleStageName extends LifecycleStageName

object AssemblyLifecycleStageName extends {
  case object Dfm extends Assembly<PERSON>ifecycleStageName {
    val value = "dfm"
  }

  case object Reconciliation extends AssemblyLifecycleStageName {
    val value = "reconciliation"
  }

  case object Analysis extends AssemblyLifecycleStageName {
    val value = "analysis"
  }

  case object Files extends AssemblyLifecycleStageName {
    val value = "files"
  }

  case object FileAnalysis extends AssemblyLifecycleStageName {
    val value = FileLifecycleStageName.FileAnalysis.value
  }

  case object LayerStack extends AssemblyLifecycleStageName {
    val value = "layerstack"
  }
  case object Outline extends AssemblyLifecycleStageName {
    val value = "outline"
  }

  case object Render extends AssemblyLifecycleStageName {
    val value = FileLifecycleStageName.Render.value
  }

  case object SpecificationRender extends AssemblyLifecycleStageName {
    val value = "specification-render"
  }

  case object ReconciledSpecificationRender extends AssemblyLifecycleStageName {
    val value = "reconciled-specification-render"
  }

  case object Initialization extends AssemblyLifecycleStageName {
    val value = "initialization"
  }

  case object ProductionAnalysis extends AssemblyLifecycleStageName {
    val value = "production-analysis"
  }

  case object CustomerPanel extends AssemblyLifecycleStageName {
    val value = "customer-panel"
  }

  /** @deprecated
    *   this is only used for deserializing old events
    */
  @deprecated
  case object Quotation extends AssemblyLifecycleStageName {
    val value = "quotation"
  }

  val values = Array(
    Dfm,
    Reconciliation,
    Analysis,
    Files,
    Outline,
    FileAnalysis,
    LayerStack,
    Render,
    SpecificationRender,
    ReconciledSpecificationRender,
    Initialization,
    ProductionAnalysis,
    CustomerPanel
  )

  private val names = Array(
    Dfm,
    Reconciliation,
    Analysis,
    Files,
    Outline,
    FileAnalysis,
    LayerStack,
    Render,
    SpecificationRender,
    ReconciledSpecificationRender,
    Initialization,
    ProductionAnalysis,
    CustomerPanel,
    Quotation
  )

  def fromName(name: String): Option[AssemblyLifecycleStageName] = names.find(_.value == name)

  implicit val format: Format[AssemblyLifecycleStageName] = new Format[AssemblyLifecycleStageName] {
    override def writes(o: AssemblyLifecycleStageName): JsValue = JsString(o.value)

    override def reads(json: JsValue): JsResult[AssemblyLifecycleStageName] = json match {
      case JsString(name) =>
        fromName(name) match {
          case Some(value) => JsSuccess(value)
          case None        => JsError(s"Unknown AssemblyLifecycleStageName: $name")
        }
      case _ => JsError(s"Expected JsString, got $json")
    }
  }

  implicit class FileToAssembly(name: FileLifecycleStageName) {
    def toAssemblyName: Option[AssemblyLifecycleStageName] = name match {
      case FileLifecycleStageName.FileAnalysis => Some(AssemblyLifecycleStageName.FileAnalysis)
      case FileLifecycleStageName.Render       => Some(AssemblyLifecycleStageName.Render)
      case _                                   => None
    }
  }
}
