package de.fellows.app.assemby.api

import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.Descriptor.Call
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{CircuitBreaker, Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.app.assemby.api.enums.UIStatus
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.collaboration.TimelineEvent
import de.fellows.utils.communication.{BinaryMessageSerializer, HtmlPageSerializer, ServiceExceptionSerializer}
import de.fellows.utils.internal.{ApiFileLifecycle, DuplicateResults, FileLifecycleStage, LifecycleStageStatus}
import de.fellows.utils.service.StackrateServiceAPI
import de.fellows.utils.{internal, streams, FilePath, PaginationListResult}
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.{OpenAPIDefinition, Operation, Parameter}

import java.util.UUID

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate Assembly API"
  )
)
trait AssemblyService extends Service with StackrateServiceAPI {

  val asubPath                 = "assembly"
  val basePath                 = s"/api/$asubPath/assemblies"
  val findPath                 = s"/api/$asubPath/find"
  val internalBasePath         = s"/internal/$asubPath/assemblies/:team"
  val specificBasePath         = s"$basePath/:assembly/versions/:version"
  val internalSpecificBasePath = s"$internalBasePath/assemblies/:assembly/versions/:version"

  val latestBasePath         = s"$basePath/:assembly/current-version"
  val internalLatestBasePath = s"$internalBasePath/assemblies/:assembly/current-version"

  val basePathV2         = s"$basePath/v2"
  val specificBasePathV2 = s"$basePathV2/:assembly/versions/:version"

  @StackrateApi
  def createNewAssembly(team: Option[String]): ServiceCall[AssemblyCreation, Assembly]

  def _createNewAssembly(creator: String, team: String): ServiceCall[InternalAssemblyCreation, Assembly]

  @StackrateApi
  @Operation(
    summary = "Get or Find all Assemblies from Team",
    description =
      """Gets all Assemblies with an optional filter:
        |
        |the filter is a comma separated list of attributes. the attributes can be:
        |
        | * `uiStatusSeverity`
        | * `uiStatusMessage`
        | * `name`
        | * `assignee`
        | * `gid`
        | * `customer`
        | * `creator`
        | * `orderid`
        |
        |for instance, `uiStatusSeverity=finished,uiStatusMsg=quotation` finds all assemblies with the given UIState
        |"""
  )
  def getTeamAssemblies(
      @Parameter(
        description = "Page for Pagination"
      )
      page: Option[Int],
      @Parameter(
        description = "Pagesize for Pagination"
      )
      pagesize: Option[Int],
      @Parameter(
        description = "The filter as described above"
      )
      filter: Option[String],
      @Parameter(
        description = "a sort direction (ASC od DESC). This is only preliminary for now"
      )
      sort: Option[String],
      @Parameter(
        description = "If true, the current versions in the result will be empty, But the call will be faster"
      )
      flat: Option[Boolean]
  ): ServiceCall[NotUsed, PaginationListResult[Assembly]]

  @StackrateApi
  def streamAssemblies(k: String): ServiceCall[NotUsed, Source[StreamMessage, NotUsed]]

  @StackrateApi
  def streamAssembly(
      k: String,
      assembly: String,
      version: Option[UUID]
  ): ServiceCall[NotUsed, Source[StreamMessage, NotUsed]]

  def _streamAssemblyV2(
      team: String,
      assembly: UUID,
      version: Option[UUID]
  ): ServiceCall[NotUsed, Source[StreamMessageV2, NotUsed]]

  @StackrateApi
  def updateAssembly(assembly: String): ServiceCall[AssemblyUpdate, Assembly]

  def updateSharedAssembly(share: UUID): ServiceCall[SharedAssemblyUpdate, SharedAssembly]

  def _updateAssembly(team: String, assembly: UUID): ServiceCall[InternalAssemblyUpdate, Assembly]

  @StackrateApi
  def setUIStatus(assembly: String): ServiceCall[UIStatus, Assembly]

  @StackrateApi
  def getAssembly(assembly: String): ServiceCall[NotUsed, Assembly]

  @StackrateApi
  def cloneAssembly(assembly: String): ServiceCall[NotUsed, Assembly]

  @StackrateApi
  def duplicateAssemblyToTeam(assembly: String, team: String): ServiceCall[NotUsed, Assembly]

  def _cloneAssembly(): ServiceCall[CloneParameters, Assembly]

  def _getAssembly(team: String, assembly: UUID): ServiceCall[NotUsed, AssemblyWithShares]
  def _getAssemblyShares(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[SharedAssemblyInfo]]

  def _getAssemblyLockState(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Boolean]

  def _getAssemblyByVersion(team: String, version: UUID): ServiceCall[NotUsed, Assembly]
  def _getAssemblyIdByVersion(team: String, version: UUID): ServiceCall[NotUsed, UUID]

  def _getAssemblyFilesByVersion(team: String, version: UUID): ServiceCall[NotUsed, Seq[InternalFile]]

  def _getAssemblyOriginalFilesByVersion(team: String, version: UUID): ServiceCall[NotUsed, Seq[OriginalFile]]

  def _findAssembly(team: String, someId: String): ServiceCall[NotUsed, Assembly]

  def _findAssembliesByOrderId(team: String, someId: String): ServiceCall[NotUsed, Seq[Assembly]]

  @StackrateApi
  def deleteAssembly(assembly: String, keepFiles: Option[Boolean]): ServiceCall[NotUsed, Done]

  def _deleteAssembly(
      team: String,
      assembly: UUID,
      keepFiles: Option[Boolean],
      forceDelete: Option[Boolean]
  ): ServiceCall[NotUsed, Done]

  @StackrateApi
  def createAssemblyVersion(assembly: String): ServiceCall[VersionCreation, Version]

  @StackrateApi
  def createVersionIfMissing(assembly: String): ServiceCall[NotUsed, Version]

  @StackrateApi
  def getAssemblyVersions(assembly: String): ServiceCall[NotUsed, Seq[Version]]

  @StackrateApi
  def getVersion(assembly: String, version: UUID): ServiceCall[NotUsed, Version]

  def _getVersion(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Version]

  @StackrateApi
  def updateVersion(assembly: String, version: UUID): ServiceCall[Version, NotUsed]

  @StackrateApi
  def updateVersionStatus(assembly: String, version: UUID): ServiceCall[ReleaseVersion, Version]

  @StackrateApi
  def deleteVersion(assembly: String, version: UUID): ServiceCall[NotUsed, NotUsed]

  // specific
  @StackrateApi
  def getFiles(assembly: String, version: Option[UUID]): ServiceCall[NotUsed, Seq[File]]

  @StackrateApi
  def approveFileMatches(assembly: String, version: Option[UUID]): ServiceCall[NotUsed, Done]

  @StackrateApi
  def approveFileMatchesWithState(assembly: String, version: Option[UUID]): ServiceCall[FileState, Done]

  def _approveFileMatchesWithState(
      team: String,
      assembly: UUID,
      version: Option[UUID],
      initial: Option[Boolean]
  ): ServiceCall[FileState, Done]

  @StackrateApi
  def getFilesState(assembly: String, version: Option[UUID]): ServiceCall[NotUsed, FileState]

  @StackrateApi
  def deleteFiles(assembly: String, version: Option[UUID]): ServiceCall[NotUsed, Done]

  @StackrateApi
  def deleteFile(assembly: String, version: Option[UUID], file: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  def _deleteFile(team: String, assembly: UUID, version: Option[UUID], file: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  def updateFile(assembly: String, version: Option[UUID], file: String): ServiceCall[FileUpdate, Done]

  @StackrateApi
  def getFile(assembly: String, version: Option[UUID], file: String): ServiceCall[NotUsed, File]

  @StackrateApi
  def streamFileUpdates(
      assembly: String,
      version: Option[UUID],
      k: String
  ): ServiceCall[NotUsed, Source[StreamMessage, NotUsed]]

  @StackrateApi
  def getAllHints(assembly: String, version: Option[UUID]): ServiceCall[NotUsed, Seq[Hint]]

  @StackrateApi
  def updateHint(assembly: String, version: Option[UUID], hint: UUID): ServiceCall[HintUpdate, Done]

  @StackrateApi
  def getHint(assembly: String, version: Option[UUID], hint: UUID): ServiceCall[NotUsed, Hint]

  @StackrateApi
  def deleteHint(assembly: String, version: Option[UUID], hint: UUID): ServiceCall[NotUsed, NotUsed]

  @StackrateApi
  def streamHints(
      assembly: String,
      version: Option[UUID],
      k: String
  ): ServiceCall[NotUsed, Source[StreamMessage, NotUsed]]

  def _updateFile(team: String, assembly: UUID, version: Option[UUID], file: String): ServiceCall[FileUpdate, Done]

  def _updateFileTypes(team: String, assembly: UUID, version: Option[UUID]): ServiceCall[FileTypeUpdates, Done]

  def _updateFileHashes(team: String, assembly: UUID, version: Option[UUID]): ServiceCall[FileHashUpdates, Done]

  /** add files to an assembly.
    * @param fullProject
    *   specifies whether this set of files is the complete set of files needed for this assembly. if true, there will
    *   be additional checks to validate the set of files as a whole assembly
    *
    * @param original
    *   whether to keep the added files as original files, ie. user uploaded original, untouched files. default is true.
    * @return
    */
  def _addFiles(
      team: String,
      assembly: UUID,
      version: Option[UUID],
      fullProject: Boolean,
      original: Option[Boolean]
  ): ServiceCall[FilesAdd, Seq[internal.File]]

  def _setPreview(team: String, assembly: UUID, version: Option[UUID]): ServiceCall[FilePath, Done]

  @StackrateApi
  def requestPreview(assembly: UUID): ServiceCall[NotUsed, Done]

  def _requestPreview(team: String, assembly: UUID): ServiceCall[NotUsed, Done]

  @StackrateApi
  def newFileLifecycle(
      assembly: String,
      version: Option[UUID],
      file: String,
      time: Option[Long]
  ): ServiceCall[FileLifecycleStage, Done]

  @StackrateApi
  def getFileLifecycles(
      assembly: String,
      version: Option[UUID],
      file: String
  ): ServiceCall[NotUsed, Seq[FileLifecycleStage]]

  @StackrateApi
  def updateFileLifecycle(
      assembly: String,
      version: Option[UUID],
      file: String,
      lcname: String,
      time: Option[Long]
  ): ServiceCall[LifecycleStageStatus, Done]

  def _updateFileLifecycle(
      team: String,
      assembly: UUID,
      version: Option[UUID],
      file: String,
      lcname: String,
      time: Option[Long]
  ): ServiceCall[LifecycleStageStatus, Done]

  @StackrateApi
  def getFileLifecycle(
      assembly: String,
      version: Option[UUID],
      file: String,
      lcname: String
  ): ServiceCall[NotUsed, FileLifecycleStage]

  @StackrateApi
  def newVersionLifecycle(
      assembly: String,
      version: Option[UUID],
      time: Option[Long]
  ): ServiceCall[IndividualAssemblyLifecycleStage, Done]

  @StackrateApi
  def getVersionLifecycles(
      assembly: String,
      version: Option[UUID] = None,
      detail: Option[Boolean]
  ): ServiceCall[NotUsed, Map[String, ApiFileLifecycle]]

  @StackrateApi
  def getLifecycleSummary(assembly: String, version: Option[UUID]): ServiceCall[NotUsed, LifecycleSummary]

  def getLifecycleSummaryImage(assembly: String, version: Option[UUID]): ServiceCall[NotUsed, Array[Byte]]
  def getLifecycleSummaryGraph(
      assembly: String,
      version: Option[UUID],
      compact: Option[Boolean],
      length: Option[Long]
  ): ServiceCall[NotUsed, String]

  @StackrateApi
  def updateVersionLifecycle(
      assembly: String,
      version: Option[UUID],
      lcname: String,
      time: Option[Long]
  ): ServiceCall[LifecycleStageStatus, Done]

  def _updateVersionLifecycle(
      team: String,
      assembly: UUID,
      version: Option[UUID],
      lcname: String,
      time: Option[Long]
  ): ServiceCall[LifecycleStageStatus, Done]

  def _getVersionLifecycle(
      team: String,
      assembly: UUID,
      version: Option[UUID],
      lcname: String
  ): ServiceCall[NotUsed, IndividualAssemblyLifecycleStage]

  @StackrateApi
  def getVersionLifecycle(
      assembly: String,
      version: Option[UUID],
      lcname: String
  ): ServiceCall[NotUsed, IndividualAssemblyLifecycleStage]

  def getSharesForAssembly(
      assembly: String,
      version: UUID
  ): ServiceCall[NotUsed, Seq[SharedAssembly]]
  def shareAssembly(
      assembly: String,
      version: UUID
  ): ServiceCall[ShareAssemblyTo, SharedAssembly]
  def _shareAssembly(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[ShareAssemblyTo, SharedAssembly]

  def getSharedAssemblies(
      @Parameter(
        description = "Page for Pagination"
      )
      page: Option[Int],
      @Parameter(
        description = "Pagesize for Pagination"
      )
      pagesize: Option[Int],
      @Parameter(
        description = "The filter as described above"
      )
      filter: Option[String],
      @Parameter(
        description = "a sort direction (ASC od DESC). This is only preliminary for now"
      )
      sort: Option[String]
  ): ServiceCall[NotUsed, PaginationListResult[SharedAssembly]]

  def getFullSharedAssemblies(
      @Parameter(
        description = "Page for Pagination"
      )
      page: Option[Int],
      @Parameter(
        description = "Pagesize for Pagination"
      )
      pagesize: Option[Int],
      @Parameter(
        description = "The filter as described above"
      )
      filter: Option[String],
      @Parameter(
        description = "a sort direction (ASC od DESC). This is only preliminary for now"
      )
      sort: Option[String]
  ): ServiceCall[NotUsed, PaginationListResult[AssemblyWithShare]]

  def getSharedAssembly(share: UUID): ServiceCall[NotUsed, AssemblyWithShare]

  def _getAssemblyShare(
      team: String,
      share: UUID
  ): ServiceCall[NotUsed, AssemblyWithShare]

  // internal API

  import Service._

  override def descriptor: Descriptor =
    withDocumentation(
      named("assembly")
        .withCalls(createCallDescriptors: _*)
        .withTopics(
          topic(AssemblyService.EVENTS, assemblyTopic()),
          topic(AssemblyService.SHARES, shareTopic()),
          topic(AssemblyService.TIMELINE, timelineTopic()),
          topic(AssemblyService.LIFECYCLES, lifecycleTopic())
        )
        .withAcls(
          ServiceAcl.forPathRegex(s"/files/assembly/.*"),
          ServiceAcl(pathRegex = Some(s"/api/assembly/.*"))
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )

  private def createCallDescriptors: Seq[Call[_, _]] =
    Seq(
      restCall(Method.POST, s"$basePath?team", createNewAssembly _),
      restCall(Method.POST, s"$internalBasePath/assemblies/:creator", _createNewAssembly _),
      restCall(Method.GET, s"$basePath?page&pagesize&filter&sort&flat", getTeamAssemblies _),
      restCall(Method.GET, s"$basePath/stream?k", streamAssemblies _),
      restCall(Method.PUT, s"$basePath/:assembly", updateAssembly _),
      restCall(Method.PUT, s"$basePath/:assembly/uiStatus", setUIStatus _),
      restCall(Method.GET, s"$basePath/:assembly", getAssembly _),
      restCall(Method.POST, s"$basePath/:assembly/clone", cloneAssembly _),
      restCall(Method.POST, s"$basePath/:assembly/duplicate?team", duplicateAssemblyToTeam _),
      restCall(Method.GET, s"$internalBasePath/assemblies/:assembly", _getAssembly _),
      restCall(Method.PUT, s"$internalBasePath/assemblies/:assembly", _updateAssembly _),
      restCall(Method.GET, s"$internalBasePath/assembliesByVersion/:version", _getAssemblyByVersion _),
      restCall(Method.GET, s"$internalBasePath/assembliesByVersion/:version/lockstate", _getAssemblyLockState _),
      restCall(Method.GET, s"$internalBasePath/assembliesByVersion/:version/id", _getAssemblyIdByVersion _),
      restCall(
        Method.GET,
        s"$internalBasePath/assembliesByVersion/:version/files/working",
        _getAssemblyFilesByVersion _
      ),
      restCall(
        Method.GET,
        s"$internalBasePath/assembliesByVersion/:version/files/original",
        _getAssemblyOriginalFilesByVersion _
      ),
      restCall(Method.GET, s"$internalBasePath/find/:someId", _findAssembly _),
      restCall(Method.GET, s"$internalBasePath/findByOrder/:someId", _findAssembliesByOrderId _),
      restCall(Method.DELETE, s"$basePath/:assembly?keepFiles", deleteAssembly _),
      restCall(Method.DELETE, s"$internalBasePath/assemblies/:assembly?keepFiles&externalDelete", _deleteAssembly _),
      restCall(Method.POST, s"$basePath/:assembly/preview", requestPreview _),
      restCall(Method.POST, s"$basePath/:assembly/versions", createAssemblyVersion _),
      restCall(Method.PATCH, s"$basePath/:assembly/versions", createVersionIfMissing _),
      restCall(Method.GET, s"$basePath/:assembly/versions", getAssemblyVersions _),
      restCall(Method.GET, s"$basePath/:assembly/versions/:version", getVersion _),
      restCall(Method.GET, s"$internalBasePath/assemblies/:assembly/versions/:version", _getVersion _),
      restCall(Method.PUT, s"$basePath/:assembly/versions/:version", updateVersion _),
      restCall(Method.PUT, s"$basePath/:assembly/versions/:version/status", updateVersionStatus _),
      restCall(Method.DELETE, s"$basePath/:assembly/versions/:version", deleteVersion _),
      restCall(Method.PUT, s"$internalSpecificBasePath/lifecycles/:lcname?time", _updateVersionLifecycle _),
      restCall(Method.GET, s"$internalSpecificBasePath/lifecycles/:lcname", _getVersionLifecycle _),
      restCall(Method.PUT, s"$internalSpecificBasePath/files/:name", _updateFile _),
      restCall(Method.DELETE, s"$internalSpecificBasePath/files/:file", _deleteFile _),
      restCall(Method.PUT, s"$internalSpecificBasePath/fileapproval/status?initial", _approveFileMatchesWithState _),
      restCall(Method.PUT, s"$internalSpecificBasePath/filetypes", _updateFileTypes _),
      restCall(Method.PUT, s"$internalSpecificBasePath/hashes", _updateFileHashes _),
      restCall(Method.PUT, s"$internalSpecificBasePath/files/:file/lifecycle/:lcname?time", _updateFileLifecycle _),
      restCall(Method.POST, s"$internalSpecificBasePath/files?fullProject&original", _addFiles _)
        .withCircuitBreaker(CircuitBreaker.None),
      restCall(Method.PUT, s"$internalSpecificBasePath/preview", _setPreview _),
      restCall(Method.GET, s"$internalSpecificBasePath/shares", _getAssemblyShares _),
      restCall(Method.POST, s"$internalBasePath/assemblies/:assembly/preview", _requestPreview _),
      restCall(Method.GET, s"/internal/$asubPath/assemblies", _cloneAssembly _),

      // provisional
      restCall(Method.GET, s"$basePath/:assembly/lifecycles?time&version", getVersionLifecycles _),

      // shares
      restCall(Method.GET, s"/api/$asubPath/shares?page&pagesize&filter&sort", getSharedAssemblies _),
      restCall(Method.GET, s"/api/$asubPath/fullshares?page&pagesize&filter&sort", getFullSharedAssemblies _),
      restCall(Method.GET, s"$internalBasePath/shares/:share", _getAssemblyShare _),
      restCall(Method.GET, s"/api/$asubPath/shares/:share", getSharedAssembly _),
      restCall(Method.PUT, s"/api/$asubPath/shares/:share", updateSharedAssembly _)
    ) ++ createCallsWithVersion(specificBasePath)

  private def createCallsWithVersion(base: String) =
    Seq(
      restCall(Method.GET, s"$base/files", getFiles _),
      restCall(Method.PUT, s"$base/files", approveFileMatches _),

      // deprecated
      restCall(Method.GET, s"$base/fileapproval", approveFileMatches _),

      // deprecated
      restCall(Method.PUT, s"$base/fileapproval/status", approveFileMatchesWithState _),
      restCall(Method.GET, s"$base/fileapproval/status", getFilesState _),
      restCall(Method.DELETE, s"$base/files", deleteFiles _),
      restCall(Method.DELETE, s"$base/files/:file", deleteFile _),
      restCall(Method.POST, s"$base/files/:file/lifecycles?time", newFileLifecycle _),
      restCall(Method.GET, s"$base/files/:file/lifecycles", getFileLifecycles _),
      restCall(Method.GET, s"$base/files/:file/lifecycles/:lcname", getFileLifecycle _),
      restCall(Method.PUT, s"$base/files/:file/lifecycles/:lcname?time", updateFileLifecycle _),
      restCall(Method.GET, s"$base/lifecyclestream", getFileLifecycles _),
      restCall(Method.GET, s"$base/stream?k", streamAssembly _),
      restCall(Method.POST, s"$base/lifecycles?time", newVersionLifecycle _),
      restCall(Method.GET, s"$base/lifecycles?detail", getVersionLifecycles _),
      restCall(Method.GET, s"$base/lifecyclesummary", getLifecycleSummary _),
      restCall(Method.GET, s"$base/lifecyclesummary/image", getLifecycleSummaryImage _)
        .withResponseSerializer(new BinaryMessageSerializer()),
      restCall(Method.GET, s"$base/lifecyclesummary/graph?compact&length", getLifecycleSummaryGraph _)
        .withResponseSerializer(new HtmlPageSerializer()),
      restCall(Method.GET, s"$base/lifecycles/:lcname", getVersionLifecycle _),
      restCall(Method.PUT, s"$base/lifecycles/:lcname?time", updateVersionLifecycle _),

      //        restCall(Method.POST, s"$base/files/:name", postFile _),
      restCall(Method.PUT, s"$base/files/:name", updateFile _),
      restCall(Method.GET, s"$base/files/:name", getFile _),
      restCall(Method.GET, s"$base/fileStream?k", streamFileUpdates _),
      restCall(Method.GET, s"$base/hints", getAllHints _),
      restCall(Method.PUT, s"$base/hints/:hint", updateHint _),
      restCall(Method.GET, s"$base/hints/:hint", getHint _),
      restCall(Method.DELETE, s"$base/hints/:hint", deleteHint _),
      restCall(Method.GET, s"$base/hintStream?k", streamHints _),
      // shares
      restCall(Method.PUT, s"$base/shares", shareAssembly _),
      restCall(Method.PUT, s"$internalSpecificBasePath/shares", _shareAssembly _),
      restCall(Method.GET, s"$base/shares", getSharesForAssembly _),
      restCall(Method.GET, s"$internalSpecificBasePath/stream", _streamAssemblyV2 _)
    )

  def assemblyTopic(): Topic[streams.StreamMessage[AssemblyStreamMessage]]

  def shareTopic(): Topic[streams.StreamMessage[AssemblyStreamMessage]]

  def timelineTopic(): Topic[TimelineEvent]

  def lifecycleTopic(): Topic[LifecycleMessage]

  //  def previewRequestTopic(): Topic[PreviewRequestMessage]
}

object AssemblyService {

  val V = s"v1.6"

  val FILE             = s"domain.assembly.file-$V"
  val EVENTS           = s"domain.assembly.events-$V"
  val SHARES           = s"domain.assembly.shares-$V"
  val TIMELINE         = s"domain.assembly.timeline-$V"
  val FILETYPE         = s"domain.assembly.filetypes-$V"
  val FILEMATCHING     = s"domain.assembly.filematching-$V"
  val ASSEMBLY_CREATED = s"domain.assembly.ass.created-$V"
  val VERSION_CREATED  = s"domain.assembly.version.created-$V"
  val HINT             = s"domain.assembly.hint-$V"
  val VERSION          = s"domain.assembly.version-$V"
  val LIFECYCLES       = s"domain.assembly.lifecycles-$V"
  val PREVIEW_REQUEST  = s"domain.assembly.preview-requested-v1.7"
}
