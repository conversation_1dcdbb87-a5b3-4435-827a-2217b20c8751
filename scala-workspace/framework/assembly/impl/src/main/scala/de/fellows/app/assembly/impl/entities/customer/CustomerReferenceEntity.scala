package de.fellows.app.assembly.impl.entities.customer

import java.util.UUID

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity

class CustomerReferenceEntity extends PersistentEntity {
  override type Command = CustomerReferenceCommand
  override type Event   = CustomerReferenceEvent
  override type State   = Option[UUID]

  override def initialState: State = None

  override def behavior: Behavior =
    Actions()
      .onCommand[CreateCustomerReference, Done] {
        case (x: CreateCustomerReference, ctx, s) =>
          ctx.thenPersist(CustomerReferenceCreated(x.id))(_ => ctx.reply(Done))
      }
      .onCommand[RemoveCustomerReference, Done] {
        case (x: RemoveCustomerReference, ctx, s) =>
          ctx.thenPersist(CustomerReferenceRemoved(x.id))(_ => ctx.reply(Done))
      }
      .onEvent {
        case (x: CreateCustomerReference, s) => Some(x.id)
        case (x: RemoveCustomerReference, s) => None
      }
}
