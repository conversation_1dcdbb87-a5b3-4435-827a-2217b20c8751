package de.fellows.app.assembly.impl

import akka.actor.typed.scaladsl.adapter.ClassicActorSystemOps
import akka.cluster.typed.ClusterSingleton
import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.api.LagomConfigComponent
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.persistence.ReadSidePersistenceComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.assembly.impl.entities.share.SharedAssemblyEntity
import de.fellows.app.assembly.impl.entities.{AssemblyEntity, AssemblyServiceSerializerRegistry}
import de.fellows.app.assembly.impl.lifecycles.TimeoutActorSetup
import de.fellows.app.assembly.impl.queue.AssemblyEventQueue
import de.fellows.app.assembly.impl.read._
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.inbox.api.InboxService
import de.fellows.app.quotation.QuotationService

import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.redislog.RedisLogComponents
import de.fellows.utils.svix.SvixClient
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import scala.concurrent.duration._
import de.fellows.luminovo.client.customparts.alerts.CustomPartsAlertsService
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import scala.concurrent.Future
import akka.actor.ActorSystem
import akka.event.Logging
import akka.actor.typed.scaladsl.AskPattern._
import akka.util.Timeout
import akka.pattern.AskTimeoutException
import scala.util.control.NonFatal
import de.fellows.utils.health.HealthCheckComponents

class AssemblyServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext) =
    new AssemblyServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new AssemblyServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[AssemblyService])
}

trait AssemblyServiceComponents
    extends LagomServerComponents
    with LagomConfigComponent
    with CassandraPersistenceComponents
    with ReadSidePersistenceComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("assembly")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = AssemblyServiceSerializerRegistry

  lazy val notificationRepository = wire[AssemblyRepository]
  lazy val assemblyIndex          = wire[AssemblyIndex]
  lazy val lcIndex                = wire[LifecycleIndex]
  lazy val assStatusIndex         = wire[AssemblyStatusRepository]
  lazy val lcTimeoutRepo          = wire[LifecycleTimeoutRepo]
  lazy val versionIndex           = wire[VersionIndex]
  lazy val sharesRepo             = wire[AssemblyShareRepository]
  lazy val sharesByVersionRepo    = wire[AssemblyShareByVersionRepository]

  val userEventProcessor = wire[AssemblyEventProcessor]
  readSide.register(userEventProcessor)

  val assIndexProcessor = wire[AssemblyIndexProcessor]
  readSide.register(assIndexProcessor)

  val lcIndexProcessor = wire[LifecycleIndexProcessor]
  readSide.register(lcIndexProcessor)

  val assemblyStatusProcessor = wire[AssemblyStatusIndex]
  readSide.register(assemblyStatusProcessor)

  val lcTimeoutProcessor = wire[LifecycleTimeoutsProcessor]
  readSide.register(lcTimeoutProcessor)

  val versionProcessor = wire[VersionIndexProcessor]
  readSide.register(versionProcessor)

  val sharesProcessor = wire[AssemblyShareReadSideProcessor]
  readSide.register(sharesProcessor)

  val versionSharesProcessor = wire[AssemblyShareByVersionProcessor]
  readSide.register(versionSharesProcessor)

  persistentEntityRegistry.register({
    implicit val conf = configuration.underlying
    wire[AssemblyEntity]
  })

  persistentEntityRegistry.register({
    implicit val conf = configuration.underlying
    wire[SharedAssemblyEntity]
  })
}

abstract class AssemblyServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with AssemblyServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with StackrateAPITestComponents
    with LagomKafkaComponents
    with RedisLogComponents
    with HealthCheckComponents {
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)

  lazy val fileRouter: AssemblyFileUploadService = wire[AssemblyFileUploadService].withApp(this)

  lazy val customerService  = serviceClient.implement[CustomerService]
  lazy val inboxService     = serviceClient.implement[InboxService]
  lazy val quotationService = serviceClient.implement[QuotationService]

  lazy val luminovoCustomPartAlerts = serviceClient.implement[CustomPartsAlertsService]

  def singletonManager: ClusterSingleton = ClusterSingleton(actorSystem.toTyped)

  lazy val impl: AssemblyServiceImpl = wire[AssemblyServiceImpl]

  override lazy val lagomServer = serverFor[AssemblyService](impl)
    .additionalRouter(fileRouter.router)

  private val queue: AssemblyEventQueue = wire[AssemblyEventQueue]
  queue.withApp(impl)
  readSide.register(queue)

  val assemblyListener = {
    val svixClient: SvixClient = SvixClient.fromConfig(config)
    wire[AssemblyLifecycleListener]
  }

  val inboxListener     = wire[InboxListener]
  val quotationListener = wire[QuotationListener]

  wire[TimeoutActorSetup].setup()
}
