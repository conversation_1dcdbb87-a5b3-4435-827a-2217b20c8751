package de.fellows.app.assembly.impl.read

import akka.Done
import akka.stream.Materializer
import akka.stream.scaladsl.Sink
import com.datastax.driver.core._
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, EventStreamElement, ReadSideProcessor}
import de.fellows.app.assembly.impl.entities._
import de.fellows.app.assemby.api.enums.{AssemblyArchived, AssemblyDeleted, UIStatus}
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.{PaginationListResult, UUIDUtils}
import org.slf4j.{Logger, LoggerFactory}

import java.util.{Date, UUID}
import scala.concurrent.{ExecutionContext, Future}

class AssemblyIndex(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer
) {
  implicit val log: Logger = LoggerFactory.getLogger(classOf[AssemblyRepository])
  AssemblyIndex.registerCodecs(session)

  def getAssembliesByTeam(
      team: String,
      page: Option[Int],
      pagesize: Option[Int],
      filter: (BaseAssemblyReference => Boolean),
      sort: Option[(String, String)]
  ): Future[PaginationListResult[BaseAssemblyReference]] =
    pagination(page, pagesize, filter) {
      val q = (sort match {
        case Some(("name", "asc"))     => s"SELECT ref FROM assemblyByNameAsc WHERE team = ?"
        case Some(("name", "desc"))    => s"SELECT ref FROM assemblyByNameDesc WHERE team = ?"
        case Some(("created", "asc"))  => s"SELECT ref FROM assemblyByDateAsc WHERE team = ?"
        case Some(("created", "desc")) => s"SELECT ref FROM assemblyByDateDesc WHERE team = ?"
        case _                         => s"SELECT ref FROM assemblyByNameAsc WHERE team = ?"
      })

      new SimpleStatement(q, team)
    }

  def getAssembliesByUIStatus(
      team: String,
      uiStatus: String,
      page: Option[Int],
      pagesize: Option[Int],
      filter: (BaseAssemblyReference => Boolean),
      sort: Option[(String, String)]
  ): Future[PaginationListResult[BaseAssemblyReference]] =
    pagination(page, pagesize, filter) {
      val q = (sort match {
        case Some(("name", "asc"))     => s"SELECT ref FROM assemblyByUIStateNameAsc WHERE team = ? AND uiState = ? "
        case Some(("name", "desc"))    => s"SELECT ref FROM assemblyByUIStateNameDesc WHERE team = ? AND uiState = ?"
        case Some(("created", "asc"))  => s"SELECT ref FROM assemblyByUIStateDateAsc WHERE team = ? AND uiState = ?"
        case Some(("created", "desc")) => s"SELECT ref FROM assemblyByUIStateDateDesc WHERE team = ? AND uiState = ?"
        case _                         => s"SELECT ref FROM assemblyByUIStateNameAsc WHERE team = ? AND uiState = ?"
      })

      new SimpleStatement(q, team, uiStatus)
    }

  def getAssembliesByName(
      team: String,
      name: String,
      page: Option[Int],
      pagesize: Option[Int],
      filter: (BaseAssemblyReference => Boolean),
      sort: Option[(String, String)]
  ): Future[PaginationListResult[BaseAssemblyReference]] =
    pagination(page, pagesize, filter) {
      val q = (sort match {
        case Some(("name", "asc"))     => s"SELECT ref FROM assemblyByNameAsc WHERE team = ? AND name = ?"
        case Some(("name", "desc"))    => s"SELECT ref FROM assemblyByNameDesc WHERE team = ? AND name = ?"
        case Some(("created", "asc"))  => s"SELECT ref FROM assemblyByNameDateAsc WHERE team = ? AND name = ?"
        case Some(("created", "desc")) => s"SELECT ref FROM assemblyByNameDateDesc WHERE team = ? AND name = ?"
        case _                         => s"SELECT ref FROM assemblyByNameAsc WHERE team = ? AND name = ?"
      })

      new SimpleStatement(q, team, name)
    }

  def getAssembliesByGid(
      team: String,
      gid: String,
      page: Option[Int],
      pagesize: Option[Int],
      filter: (BaseAssemblyReference => Boolean),
      sort: Option[(String, String)]
  ): Future[PaginationListResult[BaseAssemblyReference]] =
    pagination(page, pagesize, filter) {
      val q = (sort match {
        case Some(("name", "asc"))     => s"SELECT ref FROM assemblyByGidNameAsc WHERE team = ? AND gid = ?"
        case Some(("name", "desc"))    => s"SELECT ref FROM assemblyByGidNameDesc WHERE team = ? AND gid = ?"
        case Some(("created", "asc"))  => s"SELECT ref FROM assemblyByGidDateAsc WHERE team = ? AND gid = ?"
        case Some(("created", "desc")) => s"SELECT ref FROM assemblyByGidDateDesc WHERE team = ? AND gid = ?"
        case _                         => s"SELECT ref FROM assemblyByGidNameAsc WHERE team = ? AND gid = ?"
      })

      new SimpleStatement(q, team, gid)
    }

  def getFirstAssemblyByOrderId(team: String, orderId: String): Future[Option[BaseAssemblyReference]] =
    getAssembliesByOrderId(team, orderId, None, None, _ => true).map(_.results.headOption)

  def getAssembliesByOrderId(
      team: String,
      orderId: String,
      page: Option[Int],
      pagesize: Option[Int],
      filter: (BaseAssemblyReference => Boolean)
  ): Future[PaginationListResult[BaseAssemblyReference]] =
    Option(orderId).map { cust =>
      pagination(page, pagesize, filter) {
        val q = s"SELECT ref FROM assemblyByOrderIDDateDesc WHERE team = ? AND orderid = ?"
        new SimpleStatement(q, team, cust)
      }
    }.getOrElse(Future.successful(PaginationListResult(Seq(), 0)))

  def getAssembliesByCustomer(
      team: String,
      customer: UUID,
      page: Option[Int],
      pagesize: Option[Int],
      filter: (BaseAssemblyReference => Boolean),
      sort: Option[(String, String)]
  ): Future[PaginationListResult[BaseAssemblyReference]] =
    Option(customer).map { cust =>
      pagination(page, pagesize, filter) {
        val q = (sort match {
          case Some(("name", "asc"))    => s"SELECT ref FROM assemblyByCustomerNameAsc WHERE team = ? AND customer = ?"
          case Some(("name", "desc"))   => s"SELECT ref FROM assemblyByCustomerNameDesc WHERE team = ? AND customer = ?"
          case Some(("created", "asc")) => s"SELECT ref FROM assemblyByCustomerDateAsc WHERE team = ? AND customer = ?"
          case Some(("created", "desc")) =>
            s"SELECT ref FROM assemblyByCustomerDateDesc WHERE team = ? AND customer = ?"
          case _ => s"SELECT ref FROM assemblyByCustomerNameAsc WHERE team = ? AND customer = ?"
        })

        new SimpleStatement(q, team, cust)
      }
    }.getOrElse(Future.successful(PaginationListResult(Seq(), 0)))

  def getAssembliesByAssignee(
      team: String,
      assignee: UUID,
      page: Option[Int],
      pagesize: Option[Int],
      filter: (BaseAssemblyReference => Boolean),
      sort: Option[(String, String)]
  ): Future[PaginationListResult[BaseAssemblyReference]] =
    Option(assignee).map { assgn =>
      pagination(page, pagesize, filter) {
        val q = (sort match {
          case Some(("name", "asc"))    => s"SELECT ref FROM assemblyByAssigneeNameAsc WHERE team = ? AND assignee = ?"
          case Some(("name", "desc"))   => s"SELECT ref FROM assemblyByAssigneeNameDesc WHERE team = ? AND assignee = ?"
          case Some(("created", "asc")) => s"SELECT ref FROM assemblyByAssigneeDateAsc WHERE team = ? AND assignee = ?"
          case Some(("created", "desc")) =>
            s"SELECT ref FROM assemblyByAssigneeDateDesc WHERE team = ? AND assignee = ?"
          case _ => s"SELECT ref FROM assemblyByAssigneeNameAsc WHERE team = ? AND assignee = ?"
        })

        new SimpleStatement(q, team, assgn)
      }
    }.getOrElse(Future.successful(PaginationListResult(Seq(), 0)))

  def pagination(
      page: Option[Int],
      pagesize: Option[Int],
      filter: (BaseAssemblyReference => Boolean)
  )(stmt: => Statement): Future[PaginationListResult[BaseAssemblyReference]] =
    if (pagesize.isDefined) {
      val ps = pagesize.getOrElse(100)
      val p  = page.getOrElse(1) - 1

      val statement = stmt

      statement.setFetchSize(ps)
      val source = session.select(
        statement
      )

      val result = source.map(toRef).collect {
        case Some(x) => x
      }.filter(filter).runWith(Sink.seq)
      for {
        list <- result.map { res =>
          res
            .drop(p * ps) // pagination: page
            .take(ps)     // pagination: pagesize
        }
        resultCount <- result.map(_.length)
      } yield PaginationListResult(list, resultCount)

    } else {
      session.selectAll(stmt).map(_.flatMap(toRef)).map(x => PaginationListResult(x, x.length))
    }

  def toRef(x: Row): Option[BaseAssemblyReference] =
    // Backwards compatibility. should ideally never be null, but may be for old rows (azure-cassandra delete behaviour)
    Option(x.get("ref", classOf[BaseAssemblyReference]))

}

object AssemblyIndex {
  def registerCodecs(session: CassandraSession)(implicit ctx: ExecutionContext): Future[CassandraSession] =
    for {
      s <- session.underlying()
      _ <- AssemblyRepository.registerCodecs(session)
    } yield {
      PCBCodecHelper.registerUDTCodec("assemblyfeature", tc => new AssemblyFeatureCodec(tc))(s)
      PCBCodecHelper.registerUDTCodec("external_reference", tc => new LqReferenceCodec(tc))(s)
      PCBCodecHelper.registerUDTCodec("baseassembly", tc => new BaseAssemblyReferenceCodec(tc))(s)
      session
    }
}

private[impl] class AssemblyIndexProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[AssemblyEvent] with StackrateLogging {

  var setRefByOrderId: Seq[PreparedStatement]    = Seq()
  var deleteRefByOrderId: Seq[PreparedStatement] = Seq()

  var setRefByCustomer: Seq[PreparedStatement]    = Seq()
  var deleteRefByCustomer: Seq[PreparedStatement] = Seq()

  var setRefByAssignee: Seq[PreparedStatement]    = Seq()
  var deleteRefByAssignee: Seq[PreparedStatement] = Seq()

  var setRefByName: Seq[PreparedStatement]    = Seq()
  var deleteRefByName: Seq[PreparedStatement] = Seq()

  var setRefByGid: Seq[PreparedStatement]    = Seq()
  var deleteRefByGid: Seq[PreparedStatement] = Seq()

  var setRefByUIState: Seq[PreparedStatement]    = Seq()
  var deleteRefByUIState: Seq[PreparedStatement] = Seq()

  // language=SQL
  private def prepareStatements() = {
    for {
      setRefByOrderIdDateDesc <- session.prepare(
        "UPDATE assemblyByOrderIDDateDesc SET ref = :ref WHERE team = :team AND orderid = :orderid AND id = :id AND name = :name AND created = :created"
      )

      setRefByAssigneeDateAsc <- session.prepare(
        "UPDATE assemblyByAssigneeDateAsc SET ref = :ref WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created"
      )
      setRefByAssigneeDateDesc <- session.prepare(
        "UPDATE assemblyByAssigneeDateDesc SET ref = :ref WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created"
      )
      setRefByAssigneeNameAsc <- session.prepare(
        "UPDATE assemblyByAssigneeNameAsc SET ref = :ref WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created"
      )
      setRefByAssigneeNameDesc <- session.prepare(
        "UPDATE assemblyByAssigneeNameDesc SET ref = :ref WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created"
      )

      deleteRefByAssigneeDateAsc <- session.prepare(
        "DELETE FROM assemblyByAssigneeDateAsc WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created "
      )
      deleteRefByAssigneeDateDesc <- session.prepare(
        "DELETE FROM assemblyByAssigneeDateDesc WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created "
      )
      deleteRefByAssigneeNameAsc <- session.prepare(
        "DELETE FROM assemblyByAssigneeNameAsc WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created "
      )
      deleteRefByAssigneeNameDesc <- session.prepare(
        "DELETE FROM assemblyByAssigneeNameDesc WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created "
      )

      setRefByCustomerDateAsc <- session.prepare(
        "UPDATE assemblyByCustomerDateAsc SET ref = :ref WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )
      setRefByCustomerDateDesc <- session.prepare(
        "UPDATE assemblyByCustomerDateDesc SET ref = :ref WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )
      setRefByCustomerNameAsc <- session.prepare(
        "UPDATE assemblyByCustomerNameAsc SET ref = :ref WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )
      setRefByCustomerNameDesc <- session.prepare(
        "UPDATE assemblyByCustomerNameDesc SET ref = :ref WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )

      deleteRefByCustomerDateAsc <- session.prepare(
        "DELETE FROM assemblyByCustomerDateAsc WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByCustomerDateDesc <- session.prepare(
        "DELETE FROM assemblyByCustomerDateDesc WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByCustomerNameAsc <- session.prepare(
        "DELETE FROM assemblyByCustomerNameAsc WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByCustomerNameDesc <- session.prepare(
        "DELETE FROM assemblyByCustomerNameDesc WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )

      setRefByNameAsc <- session.prepare(
        "UPDATE assemblyByNameAsc SET ref = :ref WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      setRefByNameDesc <- session.prepare(
        "UPDATE assemblyByNameDesc SET ref = :ref WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      setRefByDateAsc <- session.prepare(
        "UPDATE assemblyByDateAsc SET ref = :ref WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      setRefByDateDesc <- session.prepare(
        "UPDATE assemblyByDateDesc SET ref = :ref WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      setRefByNameDateAsc <- session.prepare(
        "UPDATE assemblyByNameDateAsc SET ref = :ref WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      setRefByNameDateDesc <- session.prepare(
        "UPDATE assemblyByNameDateDesc SET ref = :ref WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )

      deleteRefByGidDateAsc <- session.prepare(
        "DELETE FROM assemblyByGidDateAsc WHERE team = :team AND gid = :gid AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByAssigneeNameDesc <- session.prepare(
        "DELETE FROM assemblyByAssigneeNameDesc WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByUiStateNameAsc <- session.prepare(
        "DELETE FROM assemblyByUiStateNameAsc WHERE team = :team AND uistate = :uistate AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByAssigneeDateDesc <- session.prepare(
        "DELETE FROM assemblyByAssigneeDateDesc WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByGidDateDesc <- session.prepare(
        "DELETE FROM assemblyByGidDateDesc WHERE team = :team AND gid = :gid AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByUiStateDateDesc <- session.prepare(
        "DELETE FROM assemblyByUiStateDateDesc WHERE team = :team AND uistate = :uistate AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByAssigneeNameAsc <- session.prepare(
        "DELETE FROM assemblyByAssigneeNameAsc WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByCustomerNameAsc <- session.prepare(
        "DELETE FROM assemblyByCustomerNameAsc WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByCustomerDateAsc <- session.prepare(
        "DELETE FROM assemblyByCustomerDateAsc WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByUiStateDateAsc <- session.prepare(
        "DELETE FROM assemblyByUiStateDateAsc WHERE team = :team AND uistate = :uistate AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByGidNameAsc <- session.prepare(
        "DELETE FROM assemblyByGidNameAsc WHERE team = :team AND gid = :gid AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByCustomerNameDesc <- session.prepare(
        "DELETE FROM assemblyByCustomerNameDesc WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByUiStateNameDesc <- session.prepare(
        "DELETE FROM assemblyByUiStateNameDesc WHERE team = :team AND uistate = :uistate AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByOrderIdDateDesc <- session.prepare(
        "DELETE FROM assemblyByOrderIdDateDesc WHERE team = :team AND orderid = :orderid AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByAssigneeDateAsc <- session.prepare(
        "DELETE FROM assemblyByAssigneeDateAsc WHERE team = :team AND assignee = :assignee AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByGidNameDesc <- session.prepare(
        "DELETE FROM assemblyByGidNameDesc WHERE team = :team AND gid = :gid AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByCustomerDateDesc <- session.prepare(
        "DELETE FROM assemblyByCustomerDateDesc WHERE team = :team AND customer = :customer AND id = :id AND name = :name AND created = :created"
      )

      deleteRefByNameAsc <- session.prepare(
        "DELETE FROM assemblyByNameAsc WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByNameDesc <- session.prepare(
        "DELETE FROM assemblyByNameDesc WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByDateAsc <- session.prepare(
        "DELETE FROM assemblyByDateAsc WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByDateDesc <- session.prepare(
        "DELETE FROM assemblyByDateDesc WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByNameDateAsc <- session.prepare(
        "DELETE FROM assemblyByNameDateAsc WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )
      deleteRefByNameDateDesc <- session.prepare(
        "DELETE FROM assemblyByNameDateDesc WHERE team = :team  AND id = :id AND name = :name AND created = :created"
      )

      setRefByGidNameAsc <- session.prepare(
        "UPDATE assemblyByGidNameAsc SET ref = :ref WHERE team = :team AND  gid = :gid AND id = :id AND name = :name AND created = :created"
      )
      setRefByGidNameDesc <- session.prepare(
        "UPDATE assemblyByGidNameDesc SET ref = :ref WHERE team = :team AND  gid = :gid AND id = :id AND name = :name AND created = :created"
      )
      setRefByGidDateAsc <- session.prepare(
        "UPDATE assemblyByGidDateAsc SET ref = :ref WHERE team = :team AND  gid = :gid AND id = :id AND name = :name AND created = :created"
      )
      setRefByGidDateDesc <- session.prepare(
        "UPDATE assemblyByGidDateDesc SET ref = :ref WHERE team = :team AND  gid = :gid AND id = :id AND name = :name AND created = :created"
      )

      setRefByUiStateNameAsc <- session.prepare(
        "UPDATE assemblyByUIStateNameAsc SET ref = :ref WHERE uiState = :msg AND team = :team  AND id = :id AND name = :name AND created = :created"
      )
      setRefByUiStateNameDesc <- session.prepare(
        "UPDATE assemblyByUIStateNameDesc SET ref = :ref WHERE uiState = :msg  AND team = :team  AND id = :id AND name = :name AND created = :created"
      )
      setRefByUiStateDateAsc <- session.prepare(
        "UPDATE assemblyByUIStateDateAsc SET ref = :ref WHERE uiState = :msg AND team = :team  AND id = :id AND name = :name AND created = :created"
      )
      setRefByUiStateDateDesc <- session.prepare(
        "UPDATE assemblyByUIStateDateDesc SET ref = :ref WHERE uiState = :msg AND team = :team  AND id = :id AND name = :name AND created = :created"
      )

    } yield {
      AssemblyIndex.registerCodecs(session)

      this.setRefByOrderId = Seq(
        setRefByOrderIdDateDesc
      )
      this.deleteRefByOrderId = Seq(
        deleteRefByOrderIdDateDesc
      )

      this.setRefByCustomer = Seq(
        setRefByCustomerDateAsc,
        setRefByCustomerDateDesc,
        setRefByCustomerNameAsc,
        setRefByCustomerNameDesc
      )

      this.deleteRefByCustomer = Seq(
        deleteRefByCustomerDateAsc,
        deleteRefByCustomerDateDesc,
        deleteRefByCustomerNameAsc,
        deleteRefByCustomerNameDesc
      )

      this.setRefByAssignee = Seq(
        setRefByAssigneeDateAsc,
        setRefByAssigneeDateDesc,
        setRefByAssigneeNameAsc,
        setRefByAssigneeNameDesc
      )

      this.deleteRefByAssignee = Seq(
        deleteRefByAssigneeDateAsc,
        deleteRefByAssigneeDateDesc,
        deleteRefByAssigneeNameAsc,
        deleteRefByAssigneeNameDesc
      )

      this.setRefByName = Seq(
        setRefByNameAsc,
        setRefByNameDesc,
        setRefByDateAsc,
        setRefByDateDesc,
        setRefByNameDateAsc,
        setRefByNameDateDesc
      )

      this.deleteRefByName = Seq(
        deleteRefByNameAsc,
        deleteRefByNameDesc,
        deleteRefByDateAsc,
        deleteRefByDateDesc,
        deleteRefByNameDateAsc,
        deleteRefByNameDateDesc,
        deleteRefByGidDateAsc,
        deleteRefByGidDateDesc,
        deleteRefByAssigneeNameAsc,
        deleteRefByAssigneeNameDesc,
        deleteRefByUiStateNameAsc,
        deleteRefByUiStateNameDesc,
        deleteRefByAssigneeDateAsc,
        deleteRefByAssigneeDateDesc,
        deleteRefByUiStateDateAsc,
        deleteRefByUiStateDateDesc,
        deleteRefByCustomerNameAsc,
        deleteRefByCustomerNameDesc,
        deleteRefByCustomerDateAsc,
        deleteRefByCustomerDateDesc,
        deleteRefByGidNameAsc,
        deleteRefByGidNameDesc,
        deleteRefByOrderIdDateDesc
      )

      this.setRefByGid = Seq(
        setRefByGidNameAsc,
        setRefByGidNameDesc,
        setRefByGidDateAsc,
        setRefByGidDateDesc
      )
      this.deleteRefByGid = Seq(
        deleteRefByGidNameAsc,
        deleteRefByGidNameDesc,
        deleteRefByGidDateAsc,
        deleteRefByGidDateDesc
      )

      this.setRefByUIState = Seq(
        setRefByUiStateNameAsc,
        setRefByUiStateNameDesc,
        setRefByUiStateDateAsc,
        setRefByUiStateDateDesc
      )

      this.deleteRefByUIState = Seq(
        deleteRefByUiStateNameAsc,
        deleteRefByUiStateNameDesc,
        deleteRefByUiStateDateAsc,
        deleteRefByUiStateDateDesc
      )

      Done
    }
  }

  // language=SQL
  private def createTables() = {
    for {
      _ <- PCBCodecHelper.loadTypes(session)
      _ <- session.executeCreateTable(
        """
          |CREATE TYPE IF NOT EXISTS assemblyfeature (
          | service text,
          | feature text,
          |)
          |""".stripMargin
      )

      _ <- session.executeCreateTable(
        """
          |CREATE TYPE IF NOT EXISTS external_reference (
          | rfqId uuid,
          | assemblyId uuid,
          | type text,
          |)
          |""".stripMargin
      )

      _ <- session.executeCreateTable(
        """
          |CREATE TYPE IF NOT EXISTS baseassembly (
          | eid uuid,
          | gid text,
          | team text,
          | creator uuid,
          | created timestamp,
          | orderid text,
          | customer uuid,
          | assignee uuid,
          | itemNo text,
          | name text,
          | description text,
          | project uuid,
          | status text,
          | uiStatus text,
          | uiStatusProgress int,
          | preview frozen<filepath>,
          | currentVersion uuid,
          | mailId text,
          | mailboxUser text,
          | mailboxFolder text,
          | features list<frozen<assemblyfeature>>,
          | externalReference frozen<external_reference>,
          |)
          |""".stripMargin
      )

      // OrderID

      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByOrderIDDateDesc (
          |   team text,
          |   orderid text,
          |   id uuid,
          |   created timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), orderid, created, name, id)
          | )WITH CLUSTERING ORDER BY (orderid DESC, created DESC)
          |""".stripMargin
      )

      // Assignee
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByAssigneeDateAsc (
          |   team text,
          |   assignee uuid,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team),assignee, created, name, id)
          | )WITH CLUSTERING ORDER BY (assignee ASC, created ASC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByAssigneeDateDesc (
          |   team text,
          |   assignee uuid,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team),assignee, created, name, id)
          | )WITH CLUSTERING ORDER BY (assignee ASC, created DESC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByAssigneeNameAsc (
          |   team text,
          |   assignee uuid,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team),assignee, name, created, id)
          | )WITH CLUSTERING ORDER BY (assignee ASC, name ASC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByAssigneeNameDesc (
          |   team text,
          |   assignee uuid,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team),assignee, name, created, id)
          | )WITH CLUSTERING ORDER BY (assignee ASC, name DESC)
          |""".stripMargin
      )

      // CUSTOMER
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByCustomerDateAsc (
          |   team text,
          |   customer uuid,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team),customer, created, name, id)
          | )WITH CLUSTERING ORDER BY (customer ASC, created ASC)
          |""".stripMargin
      )

      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByCustomerDateDesc (
          |   team text,
          |   customer uuid,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), customer, created, name, id)
          | )WITH CLUSTERING ORDER BY (customer DESC, created DESC)
          |""".stripMargin
      )

      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByCustomerNameAsc (
          |   team text,
          |   customer uuid,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), customer, name,  created, id)
          | )WITH CLUSTERING ORDER BY (customer ASC, name ASC)
          |""".stripMargin
      )

      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByCustomerNameDesc (
          |   team text,
          |   customer uuid,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), customer, name, created, id)
          | )WITH CLUSTERING ORDER BY (customer ASC, name DESC)
          |""".stripMargin
      )

      // NAME
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByNameDesc (
          |   team text,
          |   name text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), name, created, id)
          | )WITH CLUSTERING ORDER BY (name DESC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByNameAsc (
          |   team text,
          |   name text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), name, created, id)
          | )WITH CLUSTERING ORDER BY (name ASC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByDateDesc (
          |   team text,
          |   name text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), created, name, id)
          | )WITH CLUSTERING ORDER BY (created DESC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByDateAsc (
          |   team text,
          |   name text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team),  created,name, id)
          | )WITH CLUSTERING ORDER BY (created ASC)
          |""".stripMargin
      )

      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByNameDateDesc (
          |   team text,
          |   name text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), name, created, id)
          | )WITH CLUSTERING ORDER BY (name ASC, created DESC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByNameDateAsc (
          |   team text,
          |   name text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), name, created, id)
          | )WITH CLUSTERING ORDER BY (name ASC, created ASC)
          |""".stripMargin
      )

      // GID
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByGidDateAsc (
          |   team text,
          |   gid text,
          |   id uuid,
          |   name text,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), gid, created, name, id)
          | )WITH CLUSTERING ORDER BY (gid ASC, created ASC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByGidDateDesc (
          |   team text,
          |   gid text,
          |   id uuid,
          |   name text,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), gid, created, name, id)
          | )WITH CLUSTERING ORDER BY (gid ASC, created DESC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByGidNameDesc (
          |   team text,
          |   gid text,
          |   id uuid,
          |   name text,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), gid, name, created, id)
          | )WITH CLUSTERING ORDER BY (gid ASC, name DESC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByGidNameASC (
          |   team text,
          |   gid text,
          |   id uuid,
          |   name text,
          |   created timestamp,
          |   changed timestamp,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), gid, name,  created, id)
          | )WITH CLUSTERING ORDER BY (gid ASC, name ASC)
          |""".stripMargin
      )

      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByUIStateNameAsc (
          |   team text,
          |   uiState text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), uiState, name, created, id)
          | )WITH CLUSTERING ORDER BY (uiState ASC, name ASC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByUIStateNameDesc (
          |   team text,
          |   uiState text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), uiState, name, created, id)
          | )WITH CLUSTERING ORDER BY (uiState ASC,name DESC )
          |""".stripMargin
      )

      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByUIStateDateAsc (
          |   team text,
          |   uiState text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), uiState, created, name, id)
          | )WITH CLUSTERING ORDER BY (uiState ASC,created ASC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS assemblyByUIStateDateDesc (
          |   team text,
          |   uiState text,
          |   id uuid,
          |   created timestamp,
          |   changed timestamp,
          |   name text,
          |   ref frozen<baseassembly>,
          |   PRIMARY KEY ((team), uiState, created, name, id)
          | )WITH CLUSTERING ORDER BY (uiState ASC,created DESC)
          |""".stripMargin
      )

    } yield {
      AssemblyIndex.registerCodecs(session)
      Done
    }
  }

  def updateRefs(assembly: BaseAssemblyReference): Future[Seq[BoundStatement]] =
    if (assembly.status.s != AssemblyDeleted.s && assembly.status.s != AssemblyArchived.s) {
      Future.successful(
        List(
          Some(this.setRefByCustomer.map(_.bind(
            assembly,
            assembly.team,
            assembly.customer.getOrElse(UUIDUtils.nil),
            assembly.eid,
            assembly.name,
            assembly.created
          ))),
          Some(this.setRefByAssignee.map(_.bind(
            assembly,
            assembly.team,
            assembly.assignee.getOrElse(UUIDUtils.nil),
            assembly.eid,
            assembly.name,
            assembly.created
          ))),
          Option(assembly.name).map(name =>
            this.setRefByName.map(_.bind(assembly, assembly.team, assembly.eid, assembly.name, assembly.created))
          ),
          Option(assembly.gid).map(x =>
            this.setRefByGid.map(_.bind(assembly, assembly.team, assembly.gid, assembly.eid, x, assembly.created))
          ),
          Option(assembly.uiStatus).map(x =>
            this.setRefByUIState.map(_.bind()
              .set("ref", assembly, classOf[BaseAssemblyReference])
              .setString("msg", x.status)
              .setString("team", assembly.team)
              .setUUID("id", assembly.eid)
              .setString("name", assembly.name)
              .setTimestamp("created", Date.from(assembly.created)))
          ),
          Some(this.setRefByOrderId.map(_.bind(
            assembly,
            assembly.team,
            assembly.orderId.getOrElse(""),
            assembly.eid,
            assembly.name,
            assembly.created
          )))
        ).flatten.flatten
      )
    } else {
      Future.successful(List())
    }

  def removeRefs(assembly: BaseAssemblyReference): Future[Seq[BoundStatement]] =
    for {
      byName     <- removeByName(assembly, assembly.name)
      byAssignee <- removeByAssignee(assembly, assembly.assignee, true)
      byCustomer <- removeByCustomer(assembly, assembly.customer, true)
      byUIState  <- removeByUIState(assembly, assembly.uiStatus, true)

      a <- Future.successful(
        List(
          Option(assembly.gid).map(x =>
            this.deleteRefByGid.map(r =>
              r.bind()
                .setString("team", assembly.team)
                .setString("gid", assembly.gid)
                .setUUID("id", assembly.eid)
                .setString("name", assembly.name)
                .setTimestamp("created", Date.from(assembly.created))
            )
          ),
          assembly.orderId.map(orderid =>
            this.deleteRefByOrderId.map(r =>
              r.bind()
                .setString("team", assembly.team)
                .setString("orderid", orderid)
                .setUUID("id", assembly.eid)
                .setString("name", assembly.name)
                .setTimestamp("created", Date.from(assembly.created))
            )
          )
        ).flatten.flatten
      )
    } yield byName ++ byAssignee ++ byCustomer ++ byUIState ++ a // ++ b

  def removeByUIState(assembly: BaseAssemblyReference, oldStatus: UIStatus, force: Boolean) = {
    val x = oldStatus
    if (force || x.status != assembly.uiStatus.status) {
      Future.successful(
        this.deleteRefByUIState.map(_.bind()
          .setString("team", assembly.team)
          .setString("uistate", x.status)
          .setUUID("id", assembly.eid)
          .setString("name", assembly.name)
          .setTimestamp("created", Date.from(assembly.created)))
      )
    } else {
      Future.successful(Seq())
    }
  }

  def tryBind(s: BoundStatement)(bind: BoundStatement => BoundStatement) =
    try
      bind(s)
    catch {
      case e: IllegalArgumentException => logger.warn(s"skip binding field: ${e.getLocalizedMessage}")
    }

  def removeByName(assembly: BaseAssemblyReference, oldName: String): Future[Seq[BoundStatement]] =
    Future.successful(
      //      this.deleteRefByName.map(_.bind(assembly.team, assembly.eid, oldName, assembly.created)),
      this.deleteRefByName.map { statement =>
        val boundStatement = statement.bind()
        tryBind(boundStatement)(_.setString("team", assembly.team))
        tryBind(boundStatement)(_.setUUID("id", assembly.eid))
        tryBind(boundStatement)(_.setString("name", oldName))
        tryBind(boundStatement)(_.setString("uistate", assembly.uiStatus.status))
        tryBind(boundStatement)(_.setString("gid", assembly.gid))
        tryBind(boundStatement)(_.setTimestamp("created", Date.from(assembly.created)))

        tryBind(boundStatement)(_.setUUID("assignee", assembly.assignee.getOrElse(UUIDUtils.nil)))
        tryBind(boundStatement)(_.setUUID("customer", assembly.customer.getOrElse(UUIDUtils.nil)))

        tryBind(boundStatement)(_.setString("orderId", assembly.orderId.getOrElse("")))
        boundStatement
      }
    )

  def removeByAssignee(
      assembly: BaseAssemblyReference,
      oldAssignee: Option[UUID],
      force: Boolean
  ): Future[Seq[BoundStatement]] =
    if (force || oldAssignee != assembly.assignee) {
      Future.successful(
        this.deleteRefByAssignee.map(_.bind()
          .setString("team", assembly.team)
          .setUUID("assignee", oldAssignee.getOrElse(UUIDUtils.nil))
          .setUUID("id", assembly.eid)
          .setString("name", assembly.name)
          .setTimestamp("created", Date.from(assembly.created)))
      )
    } else {
      Future.successful(Seq())
    }

  def removeByCustomer(
      assembly: BaseAssemblyReference,
      oldCustomer: Option[UUID],
      force: Boolean
  ): Future[Seq[BoundStatement]] =
    if (force || oldCustomer != assembly.customer) {
      Future.successful(
        this.deleteRefByCustomer.map(_.bind()
          .setString("team", assembly.team)
          .setUUID("customer", oldCustomer.getOrElse(UUIDUtils.nil))
          .setUUID("id", assembly.eid)
          .setString("name", assembly.name)
          .setTimestamp("created", Date.from(assembly.created)))
      )
    } else {
      Future.successful(Seq())
    }

  def updateStatus(event: AssemblyStatusChanged): Future[Seq[BoundStatement]] =
    if (event.status.s == AssemblyDeleted.s || event.status.s == AssemblyArchived.s) {
      removeRefs(event.assembly)
    } else {
      updateRefs(event.assembly)
    }

  private def assemblyCreated(e: EventStreamElement[AssemblyCreated]): Future[Seq[BoundStatement]] =
    updateRefs(e.event.assembly)

  private def assemblyCloned(e: EventStreamElement[AssemblyCloned]): Future[Seq[BoundStatement]] =
    updateRefs(BaseAssemblyReference.of(e.event.newAssembly))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[AssemblyEvent] =
    readSide.builder[AssemblyEvent]("assemblyIndexEventOffset-v1.3")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[AssemblyCreated](e => assemblyCreated(e))
      .setEventHandler[AssemblyCloned](e => assemblyCloned(e))
      .setEventHandler[AssemblyUIStatusChanged] { e =>
        for {
          a <- removeByUIState(e.event.assembly, e.event.oldStatus, false)
          b <- updateRefs(e.event.assembly)
        } yield a ++ b // ++ c
      }
      .setEventHandler[NameChanged] { e =>
        if (e.event.assembly.name != e.event.oldName) {
          for {
            a <- removeByName(e.event.assembly, e.event.oldName)
            b <- updateRefs(e.event.assembly)
          } yield a ++ b
        } else {
          Future.successful(Seq())
        }
      }
      .setEventHandler[UserAssigned] { e =>
        for {
          a <- removeByAssignee(e.event.assembly, e.event.oldUser, false)
          b <- updateRefs(e.event.assembly)
        } yield a ++ b
      }
      .setEventHandler[AssemblyStatusChanged](e => updateStatus(e.event))
      .setEventHandler[VersionCreated](e => updateRefs(e.event.assembly))
      .setEventHandler[PreviewUpdated](e => updateRefs(e.event.assembly.copy(preview = Some(e.event.preview))))
      .setEventHandler[DescriptionChanged](e => updateRefs(e.event.assembly))
      .setEventHandler[CustomerAssigned] { e =>
        removeByCustomer(e.event.assembly, e.event.oldCustomer, false)
        updateRefs(e.event.assembly)
      }
      .setEventHandler[FileMatchingApproved](e => updateRefs(e.event.assembly))
      .build()

  override def aggregateTags: Set[AggregateEventTag[AssemblyEvent]] = AssemblyEvent.Tag.allTags
}
