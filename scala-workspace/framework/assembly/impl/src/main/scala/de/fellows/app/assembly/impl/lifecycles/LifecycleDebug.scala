package de.fellows.app.assembly.impl.lifecycles

import de.fellows.app.assemby.api
import de.fellows.app.assemby.api.LifecycleSummary
import de.fellows.utils.UUIDUtils
import de.fellows.utils.internal.{HistoricLifecycleState, LifecycleStage, LifecycleStageStatus, StageStatusName}

import java.awt.Color
import java.awt.image.BufferedImage

object LifecycleDebug {

  case class DebugLifecycle(
      val name: String,
      val status: LifecycleStageStatus,
      val history: Option[Seq[HistoricLifecycleState]]
  )

  def color(name: StageStatusName): Color = {
    import StageStatusName._
    name match {
      case Unknown  => Color.GRAY
      case Waiting  => Color.BLUE
      case Success  => Color.GREEN
      case Progress => Color.YELLOW
      case Error    => Color.RED
      case Timeout  => Color.RED
    }
  }

  def graph(sum: LifecycleSummary, compact: Boolean, maxLength: Option[Long]): String = {
    val versionGraph: String = createAssemblyGraph(Seq(sum.version, sum.aggregates), compact, maxLength)

    val files = sum.files.flatMap { f =>
      if (f._2.isEmpty || f._2.flatMap(_._2.history.getOrElse(Seq())).isEmpty) {
        None
      } else {
        Some(f._1 -> createFileGraph(f._1, Seq(f._2), compact, maxLength))
      }
    }.map { x =>
      s"""<pre class="mermaid">
        |${x._2}
        |</pre>""".stripMargin
    }.mkString("\n")

    s"""<html>
       |<head>
       |
       |</head>
       |
       |<body>
       |
       |<pre class="mermaid">
       |${versionGraph}
       |</pre>
       |
       |${files}
       |
       |    <script type="module">
       |      import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
       |      mermaid.initialize({
       |        startOnLoad: true,
       |   		gantt: {
       |		  numberSectionStyles: 2,
       |		}
       |      });
       |    </script>
       |
       |  </body>
       |
       |</html>
       |""".stripMargin

  }

  private def createAssemblyGraph(
      lifecycleMap: Seq[Map[String, api.AssemblyLifecycleStage]],
      compact: Boolean,
      maxLength: Option[Long]
  ) = {
    val x = lifecycleMap.map { x =>
      x.map { x =>
        x._1 -> DebugLifecycle(
          x._2.name,
          x._2.status,
          x._2.history
        )
      }
    }

    doCreateGraph(x, "Version Lifecycles", compact, maxLength)
  }

  private def createFileGraph(
      file: String,
      lifecycleMap: Seq[Map[String, LifecycleStage]],
      compact: Boolean,
      maxLength: Option[Long]
  ) = {
    val x = lifecycleMap.map { x =>
      x.map { x =>
        x._1 -> DebugLifecycle(
          x._2.name.value,
          x._2.status,
          x._2.history
        )
      }
    }

    doCreateGraph(x, s"File ${file}", compact, maxLength)
  }

  private def doCreateGraph(
      lifecycleMaps: Seq[Map[String, DebugLifecycle]],
      title: String,
      compact: Boolean,
      maxLength: Option[Long]
  ) = {
    val mermaidGraph = new StringBuilder()
    mermaidGraph.append("\n")
    if (compact) {
      mermaidGraph.append("---\ndisplayMode: compact\n---\n\n")
    }
    mermaidGraph.append("gantt\n")
    mermaidGraph.append(s"    title ${title}\n")
    mermaidGraph.append("    dateFormat x\n")
    mermaidGraph.append("    axisFormat %s.%L\n")

    var tickInterval = 1000

    val allLifecycles = lifecycleMaps.reduce(_ ++ _)

    val min       = allLifecycles.values.flatMap(_.history.map(_.map(_.start))).toSeq.flatten.min
    val cappedEnd = maxLength.map(e => min + e)

    val max = cappedEnd.getOrElse(allLifecycles.values.flatMap(x =>
      Seq(x.history.map(_.flatMap(_.end)), x.history.map(_.map(_.start))).flatten
    ).toSeq.flatten.max)

    tickInterval = Math.max(((max - min) / 20).toInt, tickInterval)

    mermaidGraph.append(s"    tickInterval ${tickInterval}millisecond\n")

    lifecycleMaps.foreach { lifecycleMap =>
      lifecycleMap.toSeq.sortBy { x =>
        x._2.history.map(_.map(_.start)).getOrElse(Seq()).minOption
      }.foreach { case (name, lc) =>
        mermaidGraph.append(s"  section $name\n")
        lc.history
          .getOrElse(Seq())
          .filter { x =>
            cappedEnd.forall(e => x.start <= e)
          }
          .foreach { state =>
            val start    = state.start - min
            val end      = state.`end`.map(end => Math.min(max, end - min))
            val duration = end.map(e => e - start)

            val tag = state.name match {
              case StageStatusName.Unknown  => Seq()
              case StageStatusName.Waiting  => Seq()
              case StageStatusName.Success  => Seq("done")
              case StageStatusName.Progress => Seq("active")
              case StageStatusName.Error    => Seq("crit")
              case StageStatusName.Timeout  => Seq("crit")
            }

            val tagString =
              if (tag.isEmpty) {
                ""
              } else {
                tag.mkString(", ") + ", "
              }

            duration match {
              case Some(value) =>
                mermaidGraph.append(
                  s"    ${state.name} :${tagString}${UUIDUtils.createTiny()}, $start, ${value}ms\n"
                )
              case None => mermaidGraph.append(s"    ${state.name} :milestone,${tagString} $start\n")
            }

          }
      }
    }
    mermaidGraph.result()
  }

  def draw(sum: LifecycleSummary): BufferedImage = {

    val min = sum.version.values.flatMap(_.history.map(_.map(_.start))).toSeq.flatten.min
    val max =
      sum.version.values.flatMap(_.history.map(_.map(_.`end`))).toSeq.flatten.max.getOrElse(System.currentTimeMillis())

    val width      = 1000.0
    val wmargin    = 80.0
    val hmargin    = 10.0
    val barHeight  = 30.0
    val rightPoint = max - min

    val legend = 30

    val widthWithMargin  = (width + 2 * wmargin).toInt
    val heightWithMargin = (sum.version.size * (barHeight + (2 * hmargin)) + hmargin).toInt
    val img              = new BufferedImage(widthWithMargin, legend + heightWithMargin, BufferedImage.TYPE_INT_RGB)
    val gr               = img.createGraphics()
    gr.setPaint(Color.WHITE)
    gr.fillRect(0, 0, img.getWidth(), img.getHeight())

    def toX(point: Long): Int = {
      val off = point - min

      val dreisatz = 100.0 / rightPoint.doubleValue() * off.doubleValue()
      val res      = (dreisatz / 100 * width + wmargin)

      res.toInt
    }

    val fontHeight = gr.getFontMetrics.getAscent.doubleValue()

    gr.setPaint(new Color(200, 200, 200))
    var ix         = 0
    var itime      = 0
    val graphsteps = 50
    Range.apply(0, rightPoint.toInt, graphsteps).foreach { i =>
      gr.drawLine(wmargin.toInt + i, 0, wmargin.toInt + i, (heightWithMargin.toInt))

      if (ix == 0) {
        val text      = s"${itime}ms"
        val textwidth = gr.getFontMetrics.stringWidth(text).doubleValue()
        gr.drawString(
          text,
          (wmargin.toInt + i - (textwidth / 2)).intValue(),
          (heightWithMargin + fontHeight).intValue()
        )
      }
      ix = (ix + 1) % 2
      itime += (rightPoint / graphsteps).toInt
    }

    var bar  = 0
    var barY = 0.0
    sum.version.values.foreach { lc =>
      gr.setPaint(Color.BLACK)

      barY += hmargin

      gr.drawString(lc.name, 0, ((barY + barHeight / 2) + fontHeight / 2.0).intValue())
      lc.history.getOrElse(Seq()).foreach { state =>
        gr.setPaint(color(state.name))
        val startX = toX(state.start)
        val endX   = state.`end`.map(toX).map(x => x - startX)

        gr.fillRect(startX, barY.toInt, endX.getOrElse(2), barHeight.toInt)
      }

      gr.setPaint(Color.BLACK)

      barY += barHeight + hmargin
      gr.drawLine(0, barY.toInt, widthWithMargin, (barY).toInt)
      bar += 1
    }

    gr.setPaint(Color.BLACK)
    gr.drawLine(wmargin.toInt, 0, wmargin.toInt, (heightWithMargin.toInt))

    img
  }
}
