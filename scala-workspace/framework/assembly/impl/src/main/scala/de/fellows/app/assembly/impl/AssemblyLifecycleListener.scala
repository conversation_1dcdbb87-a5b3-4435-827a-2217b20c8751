package de.fellows.app.assembly.impl

import akka.Done
import akka.NotUsed
import akka.persistence.query.Offset
import akka.persistence.query.TimeBasedUUID
import akka.stream.Materializer
import akka.stream.scaladsl.Flow
import akka.stream.scaladsl.{Flow, Sink}
import com.datastax.driver.core.PreparedStatement
import com.datastax.driver.core.utils.UUIDs
import com.lightbend.lagom.scaladsl.persistence.AggregateEventTag
import com.lightbend.lagom.scaladsl.persistence.ReadSideProcessor
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRegistry}
import de.fellows.app.assembly.impl.AssemblyLifecycleSvixEventListener.toSvixEvent
import de.fellows.app.assembly.impl.entities.VersionLifecyclesUpdated
import de.fellows.app.assembly.impl.entities.{AssemblyEvent, VersionLifecycleUpdated}
import de.fellows.app.assemby.api.LifecycleMessage
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, IndividualAssemblyLifecycleStage}
import de.fellows.utils.DebugUtils
import de.fellows.utils.internal.StageStatusName
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.OffsetStore
import de.fellows.utils.redislog.ReadSideHandlerBuilder
import de.fellows.utils.redislog.RedisLogEventHandler
import de.fellows.utils.redislog.RedisLogReadSideHandler
import de.fellows.utils.redislog.RedisLogReadSideHandler.Handler
import de.fellows.utils.redislog.jobs.UUID1Converter
import de.fellows.utils.streams.ValidMessage
import de.fellows.utils.svix.SvixClient.ApplicationName
import de.fellows.utils.svix._
import play.api.Logging

import java.time.Instant

import scala.concurrent.ExecutionContext
import scala.concurrent.Future
import scala.concurrent.blocking
import scala.reflect.ClassTag
import scala.util.{Failure, Success, Try}
import de.fellows.app.assembly.impl.queue.CassandraOffsetStore
import akka.persistence.query.NoOffset
import de.fellows.utils.TimeBasedUUIDUtils
import com.typesafe.config.Config

class AssemblyLifecycleListener(
    svixClient: SvixClient,
    cassandraSession: CassandraSession,
    config: Config
)(implicit val exc: ExecutionContext) extends ReadSideProcessor[AssemblyEvent] with StackrateLogging {

  override def aggregateTags: Set[AggregateEventTag[AssemblyEvent]] = AssemblyEvent.Tag.allTags

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[AssemblyEvent] = {
    val processorID = "svix-lifecycle-queue-v1.0"
    var offsetStore = new CassandraOffsetStore(processorID, cassandraSession)
    val cutOff      = TimeBasedUUIDUtils.getCuttOff(introducedAt = 1748269324000L, config)

    new ReadSideProcessor.ReadSideHandler[AssemblyEvent] {
      var tag: AggregateEventTag[AssemblyEvent] = _

      override def handle(): Flow[EventStreamElement[AssemblyEvent], Done, NotUsed] =
        Flow[EventStreamElement[AssemblyEvent]]
          .mapAsync(parallelism = 1) { elem =>
            val eventClass = elem.event.getClass
            val eventEpoch = UUID1Converter.getEpoch(elem.offset.asInstanceOf[TimeBasedUUID].value)

            if (eventEpoch < UUID1Converter.getEpoch(cutOff.value)) {
              logger.info(
                s"Skipping event ${eventClass} with timestamp ${eventEpoch}"
              )
              offsetStore.setOffset(elem.offset.asInstanceOf[TimeBasedUUID], tag)
            } else {

              (convertStreamElement(elem.event) match {
                case Some((event, pcbId, tenant)) => sendEvent(event, pcbId, tenant)
                case None                         => Future.successful(Done)
              }).flatMap { _ =>
                offsetStore.setOffset(elem.offset.asInstanceOf[TimeBasedUUID], tag)
              }
            }
          }

      override def prepare(tag: AggregateEventTag[AssemblyEvent]): Future[Offset] = {
        this.tag = tag
        offsetStore.prepare(tag) map {
          case NoOffset         => cutOff
          case x: TimeBasedUUID => x
        }
      }
    }
  }

  private def sendEvent(
      event: AssemblySvixEvent,
      tenant: TenantId,
      idempotency: String
  ): Future[Done] =
    Future {
      blocking {
        svixClient.send(
          tenant = ApplicationName(tenant.value),
          eventType = event.eventType,
          payload = event,
          idempotency = Some(idempotency)
        )
        Done
      }
    }

  private def convertStreamElement(element: AssemblyEvent): Option[(AssemblySvixEvent, TenantId, String)] =
    Try(toSvixEvent(element, Instant.now())) match {
      case Failure(exception) =>
        logger.error(s"Failed to convert event to Svix event, error=${exception.getMessage}")
        None
      case Success(value) =>
        value.map {
          case (event, pcbId, tenant) =>
            (
              event,
              tenant,
              s"${pcbId}|${event.eventType}"
            )
        }
    }
}

object AssemblyLifecycleSvixEventListener extends Logging {

  /** Creates an Svix event from an AssemblyEvent.
    *
    * Currently only Render and DfmAnalysis lifecycle events are supported.
    */
  def toSvixEvent(event: AssemblyEvent, timestamp: Instant): Option[(AssemblySvixEvent, PcbId, TenantId)] =
    event match {
      case e: VersionLifecycleUpdated =>
        val tenant = TenantId(e.assembly.team)
        val pcbId = PcbId(e.assembly.currentVersion.getOrElse(
          throw new IllegalStateException(s"No version found for assembly ${e.assembly.eid}")
        ))
        val isInProgress = !e.oldState.forall(_.status.name == StageStatusName.Waiting)

        val svixEvent: Option[AssemblySvixEvent] = e.lc match {
          case Some(IndividualAssemblyLifecycleStage(AssemblyLifecycleStageName.Render, status, _)) =>
            status.name match {
              case StageStatusName.Progress if isInProgress =>
                Some(RenderUpdatedEvent(pcbId, Percentage(status.percent.getOrElse(0)), timestamp))

              case StageStatusName.Progress => Some(RenderStartedEvent(pcbId, timestamp))
              case StageStatusName.Success =>
                Some(RenderFinishedEvent(pcbId, Result.success, timestamp))
              case StageStatusName.Error =>
                Some(
                  RenderFinishedEvent(
                    pcbId,
                    Result.error(status.messages.headOption.getOrElse("Error while rendering.")),
                    timestamp
                  )
                )

              case _ => None
            }

          case Some(IndividualAssemblyLifecycleStage(AssemblyLifecycleStageName.Dfm, status, _)) =>
            status.name match {
              case StageStatusName.Progress if isInProgress => None
              case StageStatusName.Progress                 => Some(AnalysisStartedEvent(pcbId, timestamp))
              case StageStatusName.Success => Some(AnalysisFinishedEvent(pcbId, Result.success, timestamp))
              case StageStatusName.Error =>
                Some(
                  AnalysisFinishedEvent(
                    pcbId,
                    Result.error(status.messages.headOption.getOrElse("Error while processing.")),
                    timestamp
                  )
                )
              case _ => None
            }

          case _ => None
        }

        svixEvent.map(e => (e, pcbId, tenant))

      case e: VersionLifecyclesUpdated => None // TODO
      case _                           => None
    }

}
