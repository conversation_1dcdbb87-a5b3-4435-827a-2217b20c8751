package de.fellows.app.assembly.impl

import java.util.UUID

import akka.Done
import com.datastax.driver.core.{ BoundStatement, PreparedStatement }
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEventTag, EventStreamElement, ReadSideProcessor }
import com.lightbend.lagom.scaladsl.persistence.cassandra.{ CassandraReadSide, CassandraSession }
import de.fellows.app.assembly.impl.entities.AssemblyEvent
import de.fellows.app.assembly.impl.entities.customer.{
  CustomerReferenceCreated,
  CustomerReferenceEvent,
  CustomerReferenceRemoved
}
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.communication.ServiceDefinition

import scala.collection.immutable
import scala.concurrent.{ ExecutionContext, Future }

class CustomerRepository(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[CustomerReferenceEvent] {
  var stmtCustomerReference: PreparedStatement       = _
  var stmtRemoveCustomerReference: PreparedStatement = _

  def exists(id: UUID): Future[Boolean] =
    session.selectOne("SELECT * FROM customers WHERE cid = ?", id).map(_.isDefined)

  def createRef(e: EventStreamElement[CustomerReferenceCreated]): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      stmtCustomerReference.bind().setUUID("cid", e.event.id)
    ))

  def removeRef(e: EventStreamElement[CustomerReferenceRemoved]): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      stmtRemoveCustomerReference.bind().setUUID("cid", e.event.id)
    ))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[CustomerReferenceEvent] =
    readSide.builder[CustomerReferenceEvent]("customerReferenceEventOffset-v1.0")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[CustomerReferenceCreated](e => createRef(e))
      .setEventHandler[CustomerReferenceRemoved](e => removeRef(e))
      .build()

  // language=SQL
  def prepareStatements(): Future[Done] =
    for {
      setCustomerReference <- session.prepare(
        """INSERT INTO customers(cid) VALUES(:cid)"""
      )
      removeCustomerReference <- session.prepare(
        """DELETE FROM customers WHERE cid = :cid """
      )
    } yield {
      stmtCustomerReference = setCustomerReference
      stmtRemoveCustomerReference = setCustomerReference
      Done
    }

  // language=SQL
  private def createTables() =
    for {
      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS customers (
            cid uuid,
            PRIMARY KEY (cid)
          )
          """
      )
    } yield Done

  override def aggregateTags: Set[AggregateEventTag[CustomerReferenceEvent]] = CustomerReferenceEvent.Tag.allTags
}
