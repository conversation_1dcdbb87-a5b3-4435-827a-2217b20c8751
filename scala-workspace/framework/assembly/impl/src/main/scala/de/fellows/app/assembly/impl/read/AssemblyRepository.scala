package de.fellows.app.assembly.impl.read

import akka.Done
import com.datastax.driver.core._
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.assembly.commons.AssemblyFeature
import de.fellows.app.assembly.impl.AssemblyNotFound
import de.fellows.app.assembly.impl.entities._
import de.fellows.app.assemby.api.enums._
import de.fellows.app.inbox.commons.{MailBox, MailMessageRef}
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.communication.{ServiceDefinition, ServiceException}
import de.fellows.utils.internal.{File, FileType}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.{FilePath, UUIDUtils}
import org.slf4j.{Logger, LoggerFactory}

import java.time.Instant
import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._
import scala.util.control.NonFatal

class AssemblyRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) {
  implicit val log: Logger = LoggerFactory.getLogger(classOf[AssemblyRepository])

  import AssemblyRepository._

  AssemblyRepository.registerCodecs(session)

  private def convertUUID(id: Option[UUID]): Option[UUID] =
    id match {
      case Some(EMPTY_UUID) => None
      case x                => x
    }

  private def convertFeatures(features: Option[Row]): Seq[AssemblyFeature] =
    features match {
      case Some(r) =>
        r.getList(
          "features",
          classOf[TupleValue]
        ).asScala.toSeq.map(tv => AssemblyFeature(tv.getString(0), tv.getString(1)))

      case None => Seq()
    }

  private def convertAssembly(r: Row): Future[Option[AssemblyDTO]] = {
    val id = r.getUUID("id")
    if (id == null) {
      Future.successful(None)
    } else {
      session.selectOne(
        """
          | SELECT * FROM features WHERE assembly = ?
      """.stripMargin,
        id
      ).map {

        features =>
          if (r.getUUID("creator") == null) {
            None
          } else {
            Some(AssemblyDTO(
              r.getUUID("id"),
              r.getString("gid"),
              r.getString("team"),
              r.getString("name"),
              Option(r.getString("description")),
              r.getUUID("creator"),
              r.get("created", classOf[Instant]),
              Option(r.getUUID("customer")),
              Option(r.getString("orderid")),
              Option(r.getUUID("assignee")),
              Option(r.getString("itemNo")),
              AssemblyStatus(r.getString("status")),
              UIStatus(r.getString("uiStatus"), Option(r.get("uiStatusProgress", classOf[Integer])).map(_.intValue())),
              Option(r.getUUID("currentVersion")),
              convertUUID(Option(r.getUUID("project"))),
              convertFeatures(features),
              Option(r.get("preview", classOf[FilePath])),
              (Option(r.getString("mailID")), Option(r.getString("mailUser")), Option(r.getString("mailBox"))) match {
                case (Some(id), Some(user), Some(box)) => Some(MailMessageRef(id, MailBox(user, box)))
                case _                                 => None
              }
            ))
          }

      }
    }

  }

  private def convertVersion(files: Seq[File])(r: Row): VersionDTO =
    VersionDTO(
      id = r.getUUID("id"),
      name = Option(r.getString("name")),
      created = r.get[Instant]("created", classOf[Instant]),
      released = Option(r.get[Instant]("releaseDate", classOf[Instant])),
      files = files,
      approved = r.getBool("approved"),
      projectType = AssemblyEntity.projectTypeFromFileList(files)
    )

  private def convertFile(r: Row): File =
    File(
      id = r.getUUID("id"),
      name = r.getString("name"),
      path = r.get("path", classOf[FilePath]),
      fType = r.get("ftype", classOf[FileType]),
      detectedTypes = r.getList("detectedTypes", classOf[FileType]).asScala.toSeq,
      created = r.get[Instant]("created", classOf[Instant]),
      preview = Option(r.get("preview", classOf[FilePath])),
      hash = Option(r.getString("hash"))
    )

  private def convertHint(r: Row): Hint =
    Hint(
      r.getUUID("id"),
      r.getString("service"),
      r.getString("title"),
      Option(r.getString("message")),
      HintStatus(r.getString("status")),
      HintLevel(r.getString("level"))
    )

  def getAssembly(team: String, assembly: UUID): Future[Option[AssemblyDTO]] =
    session.selectOne(
      """
        | SELECT * FROM assembliesById WHERE team = ? AND id = ?
""".stripMargin,
      team,
      assembly
    ).flatMap(x => invert(x.map(convertAssembly)).map(_.flatten))

  def getAssembly(team: String, gid: String): Future[Option[AssemblyDTO]] =
    session.selectOne(
      """
        | SELECT * FROM assembliesByGid WHERE team = ? AND gid = ?
""".stripMargin,
      team,
      gid
    ).flatMap(x => invert(x.map(convertAssembly)).map(_.flatten))

  private def invert[A](x: Option[Future[A]])(implicit ec: ExecutionContext): Future[Option[A]] =
    x match {
      case Some(f) => f.map(Some(_))
      case None    => Future.successful(None)
    }

  def getCurrentVersionsForAssembly(team: String, assembly: UUID): Future[Option[VersionDTO]] =
    getAssembly(team, assembly).flatMap {
      case Some(a) => getVersion(team, assembly, a.currentVersion)
      case None    => throw new ServiceException(AssemblyNotFound)
    }

  def getVersion(team: String, assembly: UUID, version: Option[UUID]): Future[Option[VersionDTO]] =
    version match {
      case Some(v) => getVersion(team, assembly, v)
      case None    => Future.successful(None)
    }

  def getVersion(team: String, assembly: UUID, version: UUID): Future[Option[VersionDTO]] =
    for {
      files <- getFilesForVersion(team, assembly, version)
      versions <- session.selectOne(
        """
          | SELECT * FROM versions WHERE team = ? AND assembly = ? and id = ?
""".stripMargin,
        team,
        assembly,
        version
      ).map(_.map(convertVersion(files)))
    } yield versions

  def getFile(team: String, assembly: UUID, version: UUID, name: String): Future[Option[File]] =
    session.selectOne(
      """
        | SELECT * FROM files WHERE version = ? AND name = ?
""".stripMargin,
      version,
      name
    ).map(_.map(convertFile))

  def getFilesForVersion(team: String, assembly: UUID, version: UUID): Future[Seq[File]] =
    session.selectAll(
      """
        | SELECT * FROM files WHERE version = ?
""".stripMargin,
      version
    ).map(_.map(convertFile))

  def getHintsForVersion(team: String, assembly: UUID, version: UUID): Future[Seq[Hint]] =
    session.selectAll(
      """
        | SELECT * FROM hints WHERE version = ?
""".stripMargin,
      version
    ).map(_.map(convertHint))

  def getHint(team: String, assembly: UUID, version: UUID, hint: UUID): Future[Option[Hint]] =
    session.selectOne(
      """
        | SELECT * FROM hints WHERE version = ? AND hint = ?
""".stripMargin,
      version,
      hint
    ).map(_.map(convertHint))
}

object AssemblyRepository {
  val EMPTY_UUID = new UUID(0, 0)

  def registerCodecs(s: CassandraSession)(implicit ctx: ExecutionContext) =
    PCBCodecHelper.registerPCBCodecs(s)
}

private[impl] class AssemblyEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[AssemblyEvent] with StackrateLogging {

  import AssemblyRepository._

  def bindDelete(s: PreparedStatement) = {
    val ps = s.bind()
    ps.setToNull("eid")
      .setToNull("gid")
      .setToNull("team")
      .setToNull("name")
      .setToNull("description")
      .setToNull("status")
      .setToNull("currentversion")
      .setToNull("project")
      .setToNull("customer")
      .setToNull("orderid")
      .setToNull("assignee")
      .setToNull("itemNo")
      .setToNull("creator")
      .setToNull("created")
      .setToNull("preview")
      .setToNull("mailID")
      .setToNull("mailUser")
      .setToNull("mailBox")
      .setToNull("uistatusprogress")
      .setToNull("uistatus")
  }

  def bind(s: PreparedStatement, a: BaseAssemblyReference): BoundStatement = {
    var ps = s.bind()
    ps.set("eid", a.eid, classOf[UUID])
      .set("gid", a.gid, classOf[String])
      .set("team", a.team, classOf[String])
      .set("name", a.name, classOf[String])
      .set("description", a.description.orNull, classOf[String])
      .set("status", a.status.s, classOf[String])
      .set("uiStatus", a.uiStatus.status, classOf[String])
      .set("currentversion", a.currentVersion.orNull, classOf[UUID])
      .set("project", a.project.getOrElse(EMPTY_UUID), classOf[UUID])
      .set("assignee", a.assignee.orNull, classOf[UUID])
      .set("itemNo", a.itemNo.orNull, classOf[String])
      .set("customer", a.customer.orNull, classOf[UUID])
      .setString("orderid", a.orderId.orNull)
      .set("creator", UUIDUtils.fromString(a.creator).getOrElse(UUIDUtils.nil), classOf[UUID])
      .set("created", a.created, classOf[Instant])
      .set("preview", a.preview.orNull, classOf[FilePath])
      .setString("mailID", a.mail.map(_.id).orNull)
      .setString("mailUser", a.mail.map(_.mailbox.team).orNull)
      .setString("mailBox", a.mail.map(_.mailbox.inboxId).orNull)

    a.uiStatus.progress match {
      case Some(p) => ps = ps.setInt("uiStatusProgress", p)
      case _       => ps = ps.setToNull("uiStatusProgress")
    }
    ps
  }

  def insertAssembly(a: AssemblyCreated): Future[immutable.Seq[BoundStatement]] =
    insertAssembly(a.assembly)

  def insertAssembly(a: AssemblyCloned): Future[immutable.Seq[BoundStatement]] = {
    val ref = BaseAssemblyReference.of(a.newAssembly)

    val fInsertAssembly = insertAssembly(ref)
    val fCreateVersion  = createVersion(ref, a.newAssembly.currentVersion.get)

    (for {
      r1 <- fInsertAssembly
      r2 <- fCreateVersion
    } yield r1 ++ r2).map { statements =>
      logger.info(s"[ASSEMBLYREPO] insert assembly: ${statements.size}: ${statements}")
      statements
    }
  }

  def insertAssembly(assembly: BaseAssemblyReference): Future[immutable.Seq[BoundStatement]] =
    session.underlying().map { s =>
      val ttype = s.getCluster.getMetadata.newTupleType(DataType.text, DataType.text)

      List(
        bind(stmUpdateAssembly, assembly),
        bind(stmUpdateAssemblyById, assembly),
        bind(stmUpdateAssemblyByGid, assembly),
        bind(stmUpdateAssemblyByProject, assembly),
        stmUpdateFeatures.bind()
          .setList("features", assembly.features.map(f => ttype.newValue(f.service, f.feature)).asJava)
          .set("assembly", assembly.eid, classOf[UUID])
      )
    }

  def releaseAssembly(a: AssemblyReleased): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      bindVersion(a.assembly, a.version)
    ))

  // UPDATE versions SET released = true, releaseDate = ?, name = ? WHERE id = ? AND owner = ? AND assembly = ?

  private def bindVersion(a: BaseAssemblyReference, v: Version) =
    stmUpdateVersion.bind()
      .set("team", a.team, classOf[String])
      .set("assembly", a.eid, classOf[UUID])
      .set("eid", v.id, classOf[UUID])
      .set("name", v.name.orNull, classOf[String])
      .setBool("approved", v.filesLocked)
      .set("created", v.created, classOf[Instant])
      .set("releasedate", v.released.orNull, classOf[Instant])
      .set("released", v.released.isDefined, classOf[Boolean])

  def changeDescription(event: DescriptionChanged): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      bind(stmUpdateAssembly, event.assembly),
      bind(stmUpdateAssemblyById, event.assembly),
      bind(stmUpdateAssemblyByGid, event.assembly),
      bind(stmUpdateAssemblyByProject, event.assembly)
    ))

  def updateFileType(a: FileTypeUpdated): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(bindFile(a.file, a.version)))

  def updateFileTypes(a: FileTypesUpdated): Future[immutable.Seq[BoundStatement]] =
    Future.successful(
      a.file.map(f => bindFile(f, a.version))
    )

  private def updateFilePreview(a: FilePreviewUpdated): Future[immutable.Seq[BoundStatement]] =
    Future.successful(
      List(
        bindFilePreview(a.file, a.version),
        bindFile(a.file, a.version)
      )
    )

  private def bindFilePreview(file: File, version: UUID): BoundStatement =
    stmUpdateFilePreview.bind()
      .set("name", file.name, classOf[String])
      .set("version", version, classOf[UUID])
      .set("preview", file.preview.orNull, classOf[FilePath])

  def addFile(a: FileAdded): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(bindFile(a.file, a.version)))

  def addFiles(a: FilesAdded): Future[immutable.Seq[BoundStatement]] =
    Future.successful(
      a.file.map(f => bindFile(f, a.version))
    )

  private def bindFile(file: File, version: UUID): BoundStatement =
    stmUpdateFile.bind()
      .set("name", file.name, classOf[String])
      .set("version", version, classOf[UUID])
      .set("eid", file.id, classOf[UUID])
      .set("path", file.path, classOf[FilePath])
      .set("preview", file.preview.orNull, classOf[FilePath])
      .set("created", file.created, classOf[Instant])
      .set("ftype", file.fType, classOf[FileType])
      .setList("detected", file.detectedTypes.asJava, classOf[FileType])
      .setString("hash", file.hash.orNull)

  def deleteFile(a: FileDeleted): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      stmDeleteFile.bind()
        .set("name", a.fileName, classOf[String])
        .set("version", a.version, classOf[UUID])
    ))

  def changeFileMatchingApproval(ref: BaseAssemblyReference, version: UUID, approved: Boolean, manually: Boolean) =
    Future.successful(List(
      stmUpdateVersionFileApproval.bind()
        .setBool("approved", approved)
        .setString("team", ref.team)
        .setUUID("assembly", ref.eid)
        .setUUID("eid", version)
    ))

  private def createVersion(event: VersionCreated): Future[immutable.Seq[BoundStatement]] =
    createVersion(event.assembly, event.version)

  private def createVersion(
      assembly: BaseAssemblyReference,
      version: Version
  ): Future[immutable.Seq[BoundStatement]] = {
    logger.info(s"[ASSEMBLYREPO] create version: ${version.id}")
    for {
      base <- {
        logger.info(s"[ASSEMBLYREPO] add base assembly: ${version.id}")
        session.executeWriteBatch(new BatchStatement()
          .addAll(
            List(
              bindVersion(assembly, version),
              bind(stmUpdateAssembly, assembly.copy(currentVersion = Some(version.id))),
              bind(stmUpdateAssemblyById, assembly.copy(currentVersion = Some(version.id))),
              bind(stmUpdateAssemblyByGid, assembly.copy(currentVersion = Some(version.id))),
              bind(stmUpdateAssemblyByProject, assembly.copy(currentVersion = Some(version.id)))
            ).asJava
          ))
      }

      files <- Future.sequence(version.files.map { f =>
        logger.info(s"[ASSEMBLYREPO] add file batch: ${version.id} ${f.name}")
        session.executeWriteBatch(new BatchStatement()
          .add(bindFile(f, version.id)))
      })
    } yield Seq()
  }

  def deleteStatements(id: UUID, gid: String, project: Option[UUID], team: String, name: String) = List(
    stmDeleteAssembly.bind()
      .set("team", team, classOf[String])
      .set("name", name, classOf[String])
      .set("eid", id, classOf[UUID]),
    stmDeleteAssemblyById.bind()
      .set("team", team, classOf[String])
      .set("name", name, classOf[String])
      .set("eid", id, classOf[UUID]),
    stmDeleteAssemblyByGid.bind()
      .set("team", team, classOf[String])
      .set("name", name, classOf[String])
      .set("gid", gid, classOf[String]),
    stmDeleteAssemblyByProject.bind()
      .set("team", team, classOf[String])
      .set("project", project.getOrElse(EMPTY_UUID), classOf[UUID])
      .set("eid", id, classOf[UUID])
  )

  def changeName(event: NameChanged): Future[immutable.Seq[BoundStatement]] =
    Future.successful(
      if (event.oldName == event.newName) {
        List()
      } else {
        deleteStatements(
          event.assembly.eid,
          event.assembly.gid,
          event.assembly.project,
          event.assembly.team,
          event.oldName
        )
      } ++
        List(
          bind(stmUpdateAssembly, event.assembly.copy(name = event.newName)),
          bind(stmUpdateAssemblyById, event.assembly.copy(name = event.newName)),
          bind(stmUpdateAssemblyByGid, event.assembly.copy(name = event.newName)),
          bind(stmUpdateAssemblyByProject, event.assembly.copy(name = event.newName))
        )
    )

  def addHint(event: HintAdded): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      bindHint(event.version, event.hint)
    ))

  //  def assignProject(event: UserAssigned): Future[immutable.Seq[BoundStatement]] = {
  //
  //    Future.successful(List(
  //      bindDelete(stmUpdateAssemblyByProject)
  //        .set("team", event.assembly.team, classOf[String])
  //        .set("project", event.oldUser.getOrElse(EMPTY_UUID), classOf[UUID])
  //        .set("eid", event.assembly.eid, classOf[UUID]),
  //
  //      bind(stmUpdateAssembly, event.assembly)
  //        .set("project", event.user.getOrElse(EMPTY_UUID), classOf[UUID]),
  //      bind(stmUpdateAssemblyById, event.assembly)
  //        .set("project", event.user.getOrElse(EMPTY_UUID), classOf[UUID]),
  //      bind(stmUpdateAssemblyByGid, event.assembly)
  //        .set("project", event.user.getOrElse(EMPTY_UUID), classOf[UUID]),
  //      bind(stmUpdateAssemblyByProject, event.assembly)
  //        .set("project", event.user.getOrElse(EMPTY_UUID), classOf[UUID]),
  //    ))
  //  }

  def changeHint(event: HintStatusChanged): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      bindHint(event.version, event.hint)
        .set("status", event.status.s, classOf[String])
    ))

  private def bindHint(version: UUID, hint: Hint) =
    stmUpdateHints.bind()
      .set("version", version, classOf[UUID])
      .set("eid", hint.id, classOf[UUID])
      .set("level", hint.level.s, classOf[String])
      .set("message", hint.message.orNull, classOf[String])
      .set("service", hint.service, classOf[String])
      .set("status", hint.status.s, classOf[String])
      .set("title", hint.title, classOf[String])

  private def bindPreview(s: PreparedStatement, fp: FilePath) =
    s.bind()
      .set("preview", fp, classOf[FilePath])

  private def setPreview(event: PreviewUpdated): Future[immutable.Seq[BoundStatement]] =
    Future.successful(
      List(
        bindPreview(stmUpdateAssemblyPreview, event.preview)
          .set("team", event.assembly.team, classOf[String])
          .set("name", event.assembly.name, classOf[String])
          .set("eid", event.assembly.eid, classOf[UUID]),
        bindPreview(stmUpdateAssemblyPreviewById, event.preview)
          .set("team", event.assembly.team, classOf[String])
          .set("name", event.assembly.name, classOf[String])
          .set("eid", event.assembly.eid, classOf[UUID]),
        bindPreview(stmUpdateAssemblyPreviewByGid, event.preview)
          .set("team", event.assembly.team, classOf[String])
          .set("name", event.assembly.name, classOf[String])
          .set("gid", event.assembly.gid, classOf[String]),
        bindPreview(stmUpdateAssemblyPreviewByProject, event.preview)
          .set("team", event.assembly.team, classOf[String])
          .set("project", event.assembly.project.getOrElse(EMPTY_UUID), classOf[UUID])
          .set("eid", event.assembly.eid, classOf[UUID])
      )
    )

  def setStatus(event: AssemblyStatusChanged): Future[immutable.Seq[BoundStatement]] =
    Future.successful(event.status match {
      case AssemblyDeleted =>
        val assembly = event.assembly
        deleteStatements(assembly.eid, assembly.gid, assembly.project, assembly.team, assembly.name)

      case _ => List()
    })

  def updateCustomer(event: CustomerAssigned): Future[Seq[BoundStatement]] =
    Future.successful(List(
      bind(stmUpdateAssembly, event.assembly),
      bind(stmUpdateAssemblyById, event.assembly),
      bind(stmUpdateAssemblyByGid, event.assembly),
      bind(stmUpdateAssemblyByProject, event.assembly)
    ).map(_.set("customer", event.customer.getOrElse(EMPTY_UUID), classOf[UUID])))

  //  val unset:
  override def buildHandler(): ReadSideProcessor.ReadSideHandler[AssemblyEvent] =
    readSide.builder[AssemblyEvent]("assemblyEventOffset-v1.4")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[AssemblyCreated] { e =>
        logger.info("[ASSEMBLYREPO] AssemblyCreated")
        insertAssembly(e.event)
      }
      .setEventHandler[AssemblyCloned] { e =>
        logger.info("[ASSEMBLYREPO] AssemblyCloned")
        insertAssembly(e.event)
      }
      .setEventHandler[CustomerAssigned] { e =>
        logger.info("[ASSEMBLYREPO] CustomerAssigned")
        updateCustomer(e.event)
      }
      .setEventHandler[AssemblyReleased] { e =>
        logger.info("[ASSEMBLYREPO] AssemblyReleased")
        releaseAssembly(e.event)
      }
      .setEventHandler[DescriptionChanged] { e =>
        logger.info("[ASSEMBLYREPO] DescriptionChanged")
        changeDescription(e.event)
      }
      .setEventHandler[FilesAdded] { e =>
        logger.info("[ASSEMBLYREPO] FilesAdded")
        addFiles(e.event)
      }
      .setEventHandler[FileAdded] { e =>
        logger.info("[ASSEMBLYREPO] FileAdded")
        addFile(e.event)
      }
      .setEventHandler[FileTypeUpdated] { e =>
        logger.info("[ASSEMBLYREPO] FileTypeUpdated")
        updateFileType(e.event)
      }
      .setEventHandler[FileTypesUpdated] { e =>
        logger.info("[ASSEMBLYREPO] FileTypesUpdated")
        updateFileTypes(e.event)
      }
      .setEventHandler[FilePreviewUpdated] { e =>
        logger.info("[ASSEMBLYREPO] FilePreviewUpdated")
        updateFilePreview(e.event)
      }
      .setEventHandler[FileDeleted] { e =>
        logger.info("[ASSEMBLYREPO] FileDeleted")
        deleteFile(e.event)
      }
      .setEventHandler[VersionCreated] { e =>
        logger.info("[ASSEMBLYREPO] VersionCreated")
        createVersion(e.event)
      }
      .setEventHandler[NameChanged] { e =>
        logger.info("[ASSEMBLYREPO] NameChanged")
        changeName(e.event)
      }
      .setEventHandler[HintAdded] { e =>
        logger.info("[ASSEMBLYREPO] HintAdded")
        addHint(e.event)
      }
      .setEventHandler[HintStatusChanged] { e =>
        logger.info("[ASSEMBLYREPO] HintStatusChanged")
        changeHint(e.event)
      }
      //    .setEventHandler[UserAssigned](e => assignProject(e.event))
      .setEventHandler[PreviewUpdated] { e =>
        logger.info("[ASSEMBLYREPO] PreviewUpdated")
        setPreview(e.event)
      }
      .setEventHandler[AssemblyStatusChanged] { e =>
        logger.info("[ASSEMBLYREPO] AssemblyStatusChanged")
        setStatus(e.event)
      }
      .setEventHandler[FileMatchingApproved] { e =>
        logger.info("[ASSEMBLYREPO] FileMatchingApproved")
        changeFileMatchingApproval(e.event.assembly, e.event.version, true, e.event.manually)
      }
      .setEventHandler[FileMatchingUnlocked] { e =>
        logger.info("[ASSEMBLYREPO] FileMatchingUnlocked")
        changeFileMatchingApproval(e.event.assembly, e.event.version, false, e.event.manually)
      }
      .build()

  override def aggregateTags: Set[AggregateEventTag[AssemblyEvent]] = AssemblyEvent.Tag.allTags

  var stmUpdateAssembly: PreparedStatement          = _
  var stmUpdateAssemblyById: PreparedStatement      = _
  var stmUpdateAssemblyByGid: PreparedStatement     = _
  var stmUpdateAssemblyByProject: PreparedStatement = _

  var stmDeleteAssembly: PreparedStatement          = _
  var stmDeleteAssemblyById: PreparedStatement      = _
  var stmDeleteAssemblyByGid: PreparedStatement     = _
  var stmDeleteAssemblyByProject: PreparedStatement = _

  var stmUpdateAssemblyPreview: PreparedStatement          = _
  var stmUpdateAssemblyPreviewById: PreparedStatement      = _
  var stmUpdateAssemblyPreviewByGid: PreparedStatement     = _
  var stmUpdateAssemblyPreviewByProject: PreparedStatement = _

  var stmUpdateFeatures: PreparedStatement = _
  var stmUpdateHints: PreparedStatement    = _

  var stmUpdateFile: PreparedStatement        = _
  var stmDeleteFile: PreparedStatement        = _
  var stmUpdateFilePreview: PreparedStatement = _

  var stmUpdateVersion: PreparedStatement             = _
  var stmUpdateVersionFileApproval: PreparedStatement = _

  // language=SQL
  private def prepareStatements() = {
    for {

      updateAssembly <- session.prepare(
        """UPDATE assemblies SET
          |gid = :gid,
          |description = :description,
          |status = :status,
          |uiStatus = :uiStatus,
          |uiStatusProgress = :uiStatusProgress,
          |currentversion = :currentversion,
          |project = :project,
          |customer = :customer,
          |orderid = :orderid,
          |assignee = :assignee,
          |itemNo = :itemNo,
          |creator = :creator,
          |created = :created,
          |preview = :preview,
          |mailID = : mailID,
          |mailUser = : mailUser,
          |mailBox = : mailBox
          |WHERE team = :team AND name = :name AND id = :eid""".stripMargin
      )
      deleteAssembly <- session.prepare("DELETE FROM assemblies WHERE team = :team AND name = :name AND id = :eid")
      updateAssemblyByID <- session.prepare(
        """UPDATE assembliesbyid SET
          |gid = :gid,
          |description = :description,
          |status = :status,
          |uiStatus = :uiStatus,
          |uiStatusProgress = :uiStatusProgress,
          |
          |currentversion = :currentversion,
          |project = :project,
          |customer = :customer,
          |orderid = :orderid,
          |assignee = :assignee,
          |itemNo = :itemNo,
          |creator = :creator,
          |created = :created,
          |preview = :preview,
          |mailID = : mailID,
          |mailUser = : mailUser,
          |mailBox = : mailBox
          |WHERE team =:team  AND name = :name AND id = :eid""".stripMargin
      )
      deleteAssemblyByID <-
        session.prepare("DELETE FROM assembliesbyid WHERE team =:team  AND name = :name AND id = :eid")

      updateAssemblyByGid <- session.prepare(
        """UPDATE assembliesbygid SET
          |id = :eid,
          |description = :description,
          |status = :status,
          |uiStatus = :uiStatus,
          |uiStatusProgress = :uiStatusProgress,
          |
          |currentversion = :currentversion,
          |project = :project,
          |customer = :customer,
          |orderid = :orderid,
          |assignee = :assignee,
          |itemNo = :itemNo,
          |creator = :creator,
          |created = :created,
          |preview = :preview,
          |mailID = : mailID,
          |mailUser = : mailUser,
          |mailBox = : mailBox
          | WHERE team = :team AND name = :name AND gid = :gid""".stripMargin
      )
      deleteAssemblyByGid <-
        session.prepare("DELETE FROM assembliesbygid WHERE team = :team AND name = :name AND gid = :gid")

      updateAssemblyByProject <- session.prepare(
        """UPDATE assembliesbyproject SET
          |gid = :gid,
          |description = :description,
          |status =  :status,
          |uiStatus = :uiStatus,
          |uiStatusProgress = :uiStatusProgress,
          |
          |currentversion = :currentversion,
          |name = :name,
          |customer = :customer,
          |orderid = :orderid,
          |assignee = :assignee,
          |itemNo = :itemNo,
          |creator = :creator,
          |created = :created,
          |preview = :preview,
          |mailID = : mailID,
          |mailUser = : mailUser,
          |mailBox = : mailBox
          |WHERE team = :team  AND project = :project AND id = :eid""".stripMargin
      )

      deleteAssemblyByProject <-
        session.prepare("DELETE FROM assembliesbyproject WHERE team = :team  AND project = :project AND id = :eid")

      updateAssemblyPreview <- session.prepare(
        "UPDATE assemblies SET preview = :preview WHERE team = :team AND name = :name AND id = :eid"
      )
      updateAssemblyPreviewByID <- session.prepare(
        "UPDATE assembliesbyid SET preview = :preview WHERE team =:team  AND name = :name AND id = :eid"
      )
      updateAssemblyPreviewByGid <- session.prepare(
        "UPDATE assembliesbygid SET preview = :preview WHERE team = :team AND name = :name AND gid = :gid"
      )
      updateAssemblyPreviewByProject <- session.prepare(
        "UPDATE assembliesbyproject SET preview = :preview WHERE team = :team  AND project = :project AND id = :eid"
      )

      updateFeatures <- session.prepare(
        "UPDATE features SET features = :features WHERE assembly = :assembly"
      )

      updateHints <- session.prepare(
        "UPDATE hints SET level = :level, message = :message, service = :service, status = :status, title = :title WHERE id = :eid AND version = :version"
      )

      updateVersion <- session.prepare(
        //            id uuid,
        //            assembly uuid,
        //            team uuid,
        //            name text,
        //            created timestamp,
        //            releaseDate timestamp,
        //            released boolean,
        //
        //            PRIMARY KEY (team, assembly, id)
        "UPDATE versions SET name = :name, created = :created, releaseDate = :releasedate, released = :released, approved = :approved WHERE team = :team AND assembly = :assembly AND id = :eid"
      )

      updateVersionFileApproval <- session.prepare(
        "UPDATE versions SET approved = :approved WHERE team = :team AND assembly = :assembly AND id = :eid"
      )

      //          CREATE TABLE IF NOT EXISTS files (
      //            id uuid,
      //            version uuid,
      //            name text,
      //            pathRoot text,
      //            pathBase text,
      //            pathRel text,
      //
      //            previewRoot text,
      //            previewBase text,
      //            previewRel text,
      //            created timestamp,
      //
      //            ftype filetype,
      //            detectedTypes list<FROZEN<filetype>>,
      //
      //            PRIMARY KEY (version, name)
      //          )

      updateFile <- session.prepare(
        """UPDATE files
            SET
              id = :eid,
              path = :path,

              preview = :preview,

              created = :created,
              ftype = :ftype,
              detectedTypes = :detected,
              hash = :hash

              WHERE version = :version AND name = :name
        """
      )
      deleteFile <- session.prepare("DELETE FROM files WHERE version = :version AND name = :name")

      updateFilePreview <- session.prepare(
        """UPDATE files
            SET
              preview = :preview
              WHERE version = :version AND name = :name
        """
      )
    } yield {
      AssemblyRepository.registerCodecs(session)
      stmUpdateAssembly = updateAssembly
      stmUpdateAssemblyById = updateAssemblyByID
      stmUpdateAssemblyByGid = updateAssemblyByGid
      stmUpdateAssemblyByProject = updateAssemblyByProject

      stmDeleteAssembly = deleteAssembly
      stmDeleteAssemblyById = deleteAssemblyByID
      stmDeleteAssemblyByGid = deleteAssemblyByGid
      stmDeleteAssemblyByProject = deleteAssemblyByProject

      stmUpdateAssemblyPreview = updateAssemblyPreview
      stmUpdateAssemblyPreviewById = updateAssemblyPreviewByID
      stmUpdateAssemblyPreviewByGid = updateAssemblyPreviewByGid
      stmUpdateAssemblyPreviewByProject = updateAssemblyPreviewByProject

      stmUpdateFeatures = updateFeatures
      stmUpdateHints = updateHints

      stmUpdateFile = updateFile
      stmDeleteFile = deleteFile
      stmUpdateFilePreview = updateFilePreview

      stmUpdateVersion = updateVersion
      stmUpdateVersionFileApproval = updateVersionFileApproval

      Done
    }
  }

  // language=SQL
  private def createTables() = {
    for {
      _ <- PCBCodecHelper.loadTypes(session)

      _ <- session.executeCreateTable(
        """
          |CREATE TYPE IF NOT EXISTS filetype (
          |            typeService text,
          |            typeCategory text,
          |            typeType text,
          |            typeProd boolean,
          |            typeMime text,
          |            typeIndex decimal,
          |);
        """.stripMargin
      )

      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS assemblies (
            id uuid ,
            gid text ,
            team text,
            name text,
            description text,
            status text,
            uiStatus text,
            uiStatusProgress int,
            currentVersion uuid,
            project uuid,

            customer uuid,
            orderid text,
            assignee uuid,
            itemNo text,
            creator uuid,
            created timestamp,

            preview filepath,

            mailID text,
            mailUser text,
            mailBox text,

            PRIMARY KEY (team, name, id)
          )
          """
      )
      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS assembliesById (
            id uuid ,
            gid text ,
            team text,
            name text,
            description text,
            status text,
            uiStatus text,
            uiStatusProgress int,
            currentVersion uuid,
            project uuid,

            customer uuid,
            orderid text,
            assignee uuid,
            itemNo text,
            creator uuid,
            created timestamp,

            preview filepath,

            mailID text,
            mailUser text,
            mailBox text,

            PRIMARY KEY (team, id, name)
          )
          """
      )
      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS assembliesByGid (
            id uuid ,
            gid text,
            team text,
            name text,
            description text,
            status text,
            uiStatus text,
            uiStatusProgress int,
            currentVersion uuid,
            project uuid,

            customer uuid,
            orderid text,
            assignee uuid,
            itemNo text,
            creator uuid,
            created timestamp,

            preview filepath,

            mailID text,
            mailUser text,
            mailBox text,

            PRIMARY KEY (team, gid, name)
          )
          """
      )
      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS assembliesByProject (
            id uuid ,
            gid text ,
            team text,
            name text,
            description text,
            status text,
            uiStatus text,
            uiStatusProgress int,
            currentVersion uuid,
            project uuid,

            customer uuid,
            orderid text,
            assignee uuid,
            itemNo text,
            creator uuid,
            created timestamp,

            preview filepath,

            mailID text,
            mailUser text,
            mailBox text,

            PRIMARY KEY (team, project, id)
          )
          """
      )

      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS versions (
            id uuid,
            assembly uuid,
            team text,
            name text,
            approved boolean,
            created timestamp,
            releaseDate timestamp,
            released boolean,

            PRIMARY KEY (team, assembly, id)
          )
          """
      )
      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS hints (
            id uuid,
            service text,
            title text,
            message text,
            status text,
            level text,
            version uuid,

            PRIMARY KEY (version, id)
          )
          """
      )

      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS files (
            id uuid,
            version uuid,
            name text,

            path filepath,
            preview filepath,
            created timestamp,

            ftype filetype,
            detectedTypes list<FROZEN<filetype>>,
            hash text,

            PRIMARY KEY (version, name)
          )
        """
      )

      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS features (
            assembly uuid,
            features list<frozen<tuple<text,text>>>,

            PRIMARY KEY (assembly)
          )
        """
      )

      _ <- migrate()

    } yield {
      AssemblyRepository.registerCodecs(session)
      Done
    }
  }

  def migrate(): Future[Done] =
    //    session.underlying().map(casssession => {
    //
    //    })
    //    val database: DatabaseConnection = null
    //    val lq = new Liquibase(
    //      "test.xml",
    //      new ClassLoaderResourceAccessor(),
    //      database
    //    )
    Future.successful(Done)

}
