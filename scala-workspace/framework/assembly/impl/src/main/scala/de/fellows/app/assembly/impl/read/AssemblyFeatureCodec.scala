package de.fellows.app.assembly.impl.read

import com.datastax.driver.core.{ TypeCodec, UDTValue, UserType }
import de.fellows.app.assembly.commons.AssemblyFeature
import de.fellows.app.assembly.impl.entities.BaseAssemblyReference
import de.fellows.utils.codec.AbstractCodec

class AssemblyFeatureCodec(cdc: TypeCodec[UDTValue])
    extends AbstractCodec[AssemblyFeature](cdc, classOf[AssemblyFeature]) {
  override def toValue(value: AssemblyFeature): UDTValue =
    if (value == null) null
    else {
      cdc.getCqlType.asInstanceOf[UserType].newValue()
        .setString("service", value.service)
        .setString("feature", value.feature)
    }

  override def fromValue(value: UDTValue): AssemblyFeature = {
    implicit val c = value
    if (value == null) null
    else {
      val s = get(classOf[String]) _

      AssemblyFeature(
        s("service"),
        s("feature")
      )
    }
  }
}
