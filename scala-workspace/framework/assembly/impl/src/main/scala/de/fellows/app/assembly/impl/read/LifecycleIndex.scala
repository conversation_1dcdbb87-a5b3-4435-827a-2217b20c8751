package de.fellows.app.assembly.impl.read

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, EventStreamElement, ReadSideProcessor}
import de.fellows.app.assembly.impl.entities._
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, IndividualAssemblyLifecycleStage}
import de.fellows.app.assemby.api.enums.AssemblyDeleted
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.internal.{LifecycleStageStatus, StageStatusName}
import de.fellows.utils.logging.StackrateLogging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._
import scala.util.Try

class LifecycleIndex(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {
  def getLifecycles(team: String, version: UUID) = {
    val query = session.selectAll("""SELECT * FROM AssemblyLifecycles WHERE team = ? AND version = ? """, team, version)
    query.map(_.map { row =>
      val status = LifecycleStageStatus(
        name = StageStatusName.fromName(row.getString("status")),
        percent = Try(row.getInt("progress")).toOption,
        messages = row.getList("messages", classOf[String]).asScala.toSeq
      )
      AssemblyLifecycleStageName.fromName(row.getString("name")).map(name => IndividualAssemblyLifecycleStage(name, status))
    }.collect { case Some(stage) => stage }.toSeq)
  }
}

private[impl] class LifecycleIndexProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[AssemblyEvent] with StackrateLogging {

  def versionLifecycle(e: EventStreamElement[VersionLifecycleUpdated]): Future[Seq[BoundStatement]] =
    updateLC(e.event.assembly.team, e.event.versionID, e.event.lc.toSeq)

  def versionLifecycles(e: EventStreamElement[VersionLifecyclesUpdated]): Future[Seq[BoundStatement]] =
    updateLC(e.event.assembly.team, e.event.versionID, e.event.lc)

  private def updateLC(team: String, version: UUID, lcs: Seq[IndividualAssemblyLifecycleStage]) =
    Future.successful(
      lcs.map { lc =>
        val prog: Option[Int] = lc.status.percent

        val b = this.updateLifecycleStmt.bind()
          .setString("team", team)
          .setUUID("version", version)
          .setString("name", lc.name.value)
          .setString("status", lc.status.name.value)
          .setList("messages", lc.status.messages.asJava)

        prog match {
          case Some(p) => b.setInt("progress", p)
          case None    => b.setToNull("progress")
        }

        b

      }.toSeq
    )

  def fileLifecycle(e: EventStreamElement[LifecycleUpdated]): Future[Seq[BoundStatement]] = {

    val event             = e.event
    val lc                = event.lc
    val prog: Option[Int] = lc.status.percent
    Future.successful(List({
      val b = this.updateFileLifecycleStmt.bind()
        .setString("team", event.assembly.team)
        .setUUID("version", event.versionID)
        .setString("file", event.file)
        .setString("name", lc.name.value)
        .setString("status", lc.status.name.value)
        .setList("messages", lc.status.messages.asJava)

      prog match {
        case Some(p) => b.setInt("progress", p)
        case None    => b.setToNull("progress")
      }
      b
    }))
  }

  def statusChanged(e: EventStreamElement[AssemblyStatusChanged]): Future[Seq[BoundStatement]] =
    if (e.event.status == AssemblyDeleted && e.event.assembly.currentVersion.isDefined) {
      Future.successful(Seq(
        this.deleteLifecycleStmt.bind()
          .setString("team", e.event.assembly.team)
          .setUUID("version", e.event.assembly.currentVersion.get),
        this.deleteFileLifecycleStmt.bind()
          .setString("team", e.event.assembly.team)
          .setUUID("version", e.event.assembly.currentVersion.get)
      ))
    } else {
      Future.successful(Seq())
    }

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[AssemblyEvent] =
    readSide.builder[AssemblyEvent]("assemblyLifecycleIndexEventOffset-v1.0")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[AssemblyStatusChanged](e => statusChanged(e))
      .setEventHandler[VersionLifecycleUpdated](e => versionLifecycle(e))
      .setEventHandler[VersionLifecyclesUpdated](e => versionLifecycles(e))
      .setEventHandler[LifecycleUpdated](e => fileLifecycle(e))
      .build()

  override def aggregateTags: Set[AggregateEventTag[AssemblyEvent]] = AssemblyEvent.Tag.allTags

  var updateLifecycleStmt: PreparedStatement     = _
  var updateFileLifecycleStmt: PreparedStatement = _
  var deleteLifecycleStmt: PreparedStatement     = _
  var deleteFileLifecycleStmt: PreparedStatement = _

  private def prepareStatements() =
    for {
      updateLifecycle <- session.prepare(
        "UPDATE AssemblyLifecycles SET status = :status, progress = :progress, messages = :messages WHERE team = :team AND version = :version AND name = :name"
      )
      updateFileLifecycle <- session.prepare(
        "UPDATE AssemblyFileLifecycles SET status = :status, progress = :progress, messages = :messages WHERE team = :team AND version = :version AND file = :file AND name = :name"
      )

      deleteLifecycle <- session.prepare(
        "DELETE FROM AssemblyLifecycles WHERE team = :team AND version = :version"
      )
      deleteFileLifecycle <- session.prepare(
        "DELETE FROM AssemblyFileLifecycles  WHERE team = :team AND version = :version"
      )

    } yield {
      this.updateLifecycleStmt = updateLifecycle
      this.updateFileLifecycleStmt = updateFileLifecycle
      this.deleteLifecycleStmt = deleteLifecycle
      this.deleteFileLifecycleStmt = deleteFileLifecycle
      Done
    }

  private def createTables() =
    for {
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS AssemblyLifecycles (
          |   team text,
          |   version uuid,
          |   name text,
          |   status text,
          |   progress int,
          |   messages list<text>,
          |   PRIMARY KEY (team, version, name)
          | )
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS AssemblyFileLifecycles (
          |   team text,
          |   version uuid,
          |   file text,
          |   name text,
          |   status text,
          |   progress int,
          |   messages list<text>,
          |   PRIMARY KEY (team, version, file, name)
          | )
          |""".stripMargin
      )
    } yield Done

}
