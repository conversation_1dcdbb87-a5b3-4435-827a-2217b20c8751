package de.fellows.app.assembly.impl.queue

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assembly.impl.entities.{
  AssemblyEvent,
  FileMatchingApproved,
  VersionLifecycleUpdated,
  VersionLifecyclesUpdated
}
import de.fellows.app.assemby.api.AssemblyLifecycleStageName.{
  Analysis,
  CustomerPanel,
  FileAnalysis,
  LayerStack,
  Outline,
  Reconciliation
}
import de.fellows.app.assemby.api.{
  AssemblyLifecycleStage,
  AssemblyLifecycleStageName,
  AssemblyService,
  IndividualAssemblyLifecycleStage,
  SuccessAssemblyLifecycle
}
import de.fellows.ems.renderer.api.job.{
  BoardAnalysisJobEntry,
  OutlineJobEntry,
  ProductionAnalysisJobEntry,
  ReconciliationJobEntry,
  RenderSpecificationJobEntry
}
import de.fellows.utils.internal.{LifecycleDeadline, LifecycleStageStatus, StageStatusName}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.RedisLog
import de.fellows.utils.redislog.jobs.JobBuilder
import redis.clients.jedis.CommandObject

import scala.concurrent.duration.{Deadline, DurationInt}
import scala.concurrent.{ExecutionContext, Future}
import com.datastax.driver.core.utils.UUIDs
import de.fellows.utils.redislog.CutOff
import com.typesafe.config.Config
import java.time.format.DateTimeFormatter
import java.time.LocalDateTime
import java.time.ZoneOffset
import de.fellows.utils.TimeBasedUUIDUtils
import akka.persistence.query.TimeBasedUUID

class AssemblyEventQueue(
    readSide: RedisLog,
    jobBuilder: JobBuilder,
    cassandraSession: CassandraSession,
    config: Config
)(
    implicit val ctx: ExecutionContext
) extends ReadSideProcessor[AssemblyEvent] with StackrateLogging {

  private type CommandList = Seq[CommandObject[_]]

  var assemblyService: AssemblyService = _

  def withApp(app: AssemblyService): AssemblyEventQueue = {
    this.assemblyService = app
    this
  }

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[AssemblyEvent] = {
    val processorID = "assemblyqueue-v1.0"
    val cutOff      = TimeBasedUUIDUtils.getCuttOff(introducedAt = 1717167864000L, config)
    readSide.builder[AssemblyEvent](processorID, cutOff)
      .setEventHandler[VersionLifecycleUpdated](e => handleVersionLifecycleUpdate(e.event))
      .setEventHandler[VersionLifecyclesUpdated](e => handleVersionLifecyclesUpdate(e.event))
      .setEventHandler[FileMatchingApproved](e => fileMatchingApproved(e.event))
      .setOffsetStore(new CassandraOffsetStore(processorID, cassandraSession))
      .build()
  }

  override def aggregateTags: Set[AggregateEventTag[AssemblyEvent]] = AssemblyEvent.Tag.allTags

  private def fileMatchingApproved(e: FileMatchingApproved): Future[CommandList] = {
    val assRef = AssemblyReference(
      team = e.assembly.team,
      id = e.assembly.eid,
      gid = Some(e.assembly.gid),
      version = e.version
    )

    handleOutline(assRef)
  }

  private def handleVersionLifecycleUpdate(e: VersionLifecycleUpdated): Future[CommandList] =
    handleLifecycleUpdate(
      assemblyReference = AssemblyReference(
        team = e.assembly.team,
        id = e.assembly.eid,
        gid = Some(e.assembly.gid),
        version = e.versionID
      ),
      lifecycles = e.versionLifecycles,
      fileApproval = e.fileApproval,
      lifecycle = e.lc
    )

  private def handleVersionLifecyclesUpdate(e: VersionLifecyclesUpdated): Future[CommandList] =
    Future
      .traverse(e.lc) { lc =>
        handleLifecycleUpdate(
          assemblyReference = AssemblyReference(
            team = e.assembly.team,
            id = e.assembly.eid,
            gid = Some(e.assembly.gid),
            version = e.versionID
          ),
          lifecycles = e.versionLifecycles,
          fileApproval = e.fileApproval,
          lifecycle = Some(lc)
        )
      }
      .map(_.flatten)

  private def handleLifecycleUpdate(
      assemblyReference: AssemblyReference,
      lifecycles: Seq[IndividualAssemblyLifecycleStage],
      fileApproval: Boolean,
      lifecycle: Option[IndividualAssemblyLifecycleStage]
  ): Future[CommandList] =
    lifecycle match {
      case SuccessAssemblyLifecycle(LayerStack) =>
        Future.sequence(Seq(
          handleSpecificationRender(assemblyReference, lifecycles, fileApproval),
          handleReconciliation(assemblyReference, lifecycles, fileApproval)
        ))
          .map(_.flatten)

      case SuccessAssemblyLifecycle(Outline) =>
        Future.sequence(Seq(
          handleSpecificationRender(assemblyReference, lifecycles, fileApproval),
          handleReconciliation(assemblyReference, lifecycles, fileApproval)
        ))
          .map(_.flatten)

      case SuccessAssemblyLifecycle(Reconciliation) =>
        Future.sequence(Seq(
          handleSpecificationRender(assemblyReference, lifecycles, fileApproval),
          handleBoardAnalysis(assemblyReference, lifecycles, fileApproval)
        ))
          .map(_.flatten)

      case SuccessAssemblyLifecycle(Analysis) =>
        handlePanelWeightAnalysis(assemblyReference, lifecycles, fileApproval)

      case SuccessAssemblyLifecycle(CustomerPanel) =>
        handlePanelWeightAnalysis(assemblyReference, lifecycles, fileApproval)

      case _ => Future.successful(Seq.empty)
    }

  private def handleOutline(
      assRef: AssemblyReference
  ): Future[CommandList] =
    setAsyncLifecycleWaiting(assRef, AssemblyLifecycleStageName.Outline)
      .map { _ =>
        val (_, commands) = jobBuilder.buildCreateJobCommands(
          jobDescription = OutlineJobEntry(assembly = assRef),
          queueName = "rendering",
          jobIdOption = None
        )

        commands
      }

  private def handleSpecificationRender(
      assRef: AssemblyReference,
      lifecycles: Seq[IndividualAssemblyLifecycleStage],
      fileApproval: Boolean
  ): Future[CommandList] = {
    val ls =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, LayerStack, StageStatusName.Success)
    val outline =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, Outline, StageStatusName.Success)

    if (ls && outline && fileApproval) {
      val hasReconciliation =
        IndividualAssemblyLifecycleStage.hasState(lifecycles, Reconciliation, StageStatusName.Success)

      val lifecycle =
        if (hasReconciliation) {
          AssemblyLifecycleStageName.ReconciledSpecificationRender
        } else
          AssemblyLifecycleStageName.SpecificationRender

      val (_, commands) = jobBuilder.buildCreateJobCommands(
        jobDescription = RenderSpecificationJobEntry(
          team = assRef.team,
          assembly = assRef.id,
          lifecycle = Some(lifecycle)
        ),
        queueName = "rendering",
        jobIdOption = None
      )

      Future.successful(commands)
    } else {
      Future.successful(Seq.empty)
    }
  }

  private def handleReconciliation(
      assRef: AssemblyReference,
      lifecycles: Seq[IndividualAssemblyLifecycleStage],
      fileApproval: Boolean
  ): Future[CommandList] = {
    val ls =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, LayerStack, StageStatusName.Success)
    val fa =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, FileAnalysis, StageStatusName.Success)
    val outline =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, Outline, StageStatusName.Success)

    if (ls && fa && outline && fileApproval) {
      setAsyncLifecycleWaiting(assRef, AssemblyLifecycleStageName.Reconciliation)
        .map { _ =>
          val (_, commands) = jobBuilder.buildCreateJobCommands(
            jobDescription = ReconciliationJobEntry(assRef),
            queueName = "rendering",
            jobIdOption = None
          )

          commands
        }
    } else {
      Future.successful(Seq.empty)
    }
  }

  private def handleBoardAnalysis(
      assRef: AssemblyReference,
      lifecycles: Seq[IndividualAssemblyLifecycleStage],
      fileApproval: Boolean
  ): Future[CommandList] = {
    val ls =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, LayerStack, StageStatusName.Success)
    val fa =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, FileAnalysis, StageStatusName.Success)
    val outline =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, Outline, StageStatusName.Success)
    val reconciliation =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, Reconciliation, StageStatusName.Success)

    if (ls && fa && outline && reconciliation && fileApproval) {
      setAsyncLifecycleWaiting(assRef, AssemblyLifecycleStageName.Analysis)
        .map { _ =>
          val (_, commands) = jobBuilder.buildCreateJobCommands(
            jobDescription = BoardAnalysisJobEntry(assembly = assRef),
            queueName = "rendering",
            jobIdOption = None
          )

          commands
        }
    } else {
      Future.successful(Seq.empty)
    }
  }

  private def handlePanelWeightAnalysis(
      assRef: AssemblyReference,
      lifecycles: Seq[IndividualAssemblyLifecycleStage],
      fileApproval: Boolean
  ): Future[CommandList] = {
    val customerPanelIsSuccessful =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, CustomerPanel, StageStatusName.Success)
    val analysisIsSuccessful =
      IndividualAssemblyLifecycleStage.hasState(lifecycles, Analysis, StageStatusName.Success)

    if (customerPanelIsSuccessful && analysisIsSuccessful) {
      logger.info(
        s"run panelweightcalc because: customer panel is successful: $customerPanelIsSuccessful, analysis is successful: $analysisIsSuccessful"
      )
      setAsyncLifecycleWaiting(assRef, AssemblyLifecycleStageName.ProductionAnalysis)
        .map { _ =>
          val (_, commands) = jobBuilder.buildCreateJobCommands(
            jobDescription = ProductionAnalysisJobEntry(assRef),
            queueName = "rendering",
            jobIdOption = None
          )

          commands
        }
    } else {
      Future.successful(Seq.empty)
    }
  }

  private def defaultDeadline: Deadline = LifecycleDeadline.in(10 minutes)

  private def setAsyncLifecycleWaiting(
      assemblyReference: AssemblyReference,
      lcName: AssemblyLifecycleStageName
  ): Future[Done] =
    assemblyService
      ._updateVersionLifecycle(
        team = assemblyReference.team,
        assembly = assemblyReference.id,
        version = Some(assemblyReference.version),
        lcname = lcName.value,
        time = Some(System.currentTimeMillis())
      )
      .invoke(
        LifecycleStageStatus.emptyWaiting.copy(deadline = Some(defaultDeadline))
      )

}
