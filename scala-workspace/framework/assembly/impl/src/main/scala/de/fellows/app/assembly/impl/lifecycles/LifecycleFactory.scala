package de.fellows.app.assembly.impl.lifecycles

import de.fellows.app.assembly.impl.entities.AssemblyEntity
import de.fellows.app.assembly.impl.lifecycles.AggregateLifecycleStageName.{Composition, DfmAnalysis, Main}
import de.fellows.app.assembly.impl.lifecycles.LifecycleFactory.flattenHistory
import de.fellows.app.assemby.api
import de.fellows.app.assemby.api.AssemblyLifecycleStageName.SpecificationRender
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, IndividualAssemblyLifecycleStage, LifecycleSummary}
import de.fellows.utils.internal._
import play.api.Logging

import java.awt.Color
import java.awt.image.BufferedImage
import scala.collection.mutable

case class AggregatePart(name: AssemblyLifecycleStageName, required: Boolean = true, weight: Int = 1)

class LifecycleFactory(v: Seq[IndividualAssemblyLifecycleStage]) extends Logging {

  def create(changedBase: Option[Seq[IndividualAssemblyLifecycleStage]])
      : Map[AggregateLifecycleStageName, AggregateLifecycleStage] = {

    val changedNames = changedBase.map(_.map(_.name))

    val res = Seq.newBuilder[AggregateLifecycleStage]
    if (changedNames.isEmpty || Composition.parts.map(_.name).exists(changedNames.contains)) {
      res += createAggregate(Composition, v, AggregateLifecycleStageName.Composition.parts)
    }

    if (changedNames.isEmpty || DfmAnalysis.parts.map(_.name).exists(changedNames.contains)) {
      res += createAggregate(DfmAnalysis, v, AggregateLifecycleStageName.DfmAnalysis.parts)
    }

    if (changedNames.isEmpty || Main.parts.map(_.name).exists(changedNames.contains)) {
      res += createAggregate(Main, v, AggregateLifecycleStageName.Main.parts)
    }
    res.result().map(x => x.name -> x).toMap
  }

  def create(): Map[AggregateLifecycleStageName, AggregateLifecycleStage] =
    create(None)

  def createForApi(): Map[String, ApiFileLifecycle] =
    create(None).map { x =>
      x._1.value -> toSimple(x._2)
    }

  private def calculatePercentage(
      aggregateName: AggregateLifecycleStageName,
      subjects: mutable.Buffer[IndividualAssemblyLifecycleStage],
      value: Seq[AggregatePart]
  ): Int = {
    val allPercentages    = calculatePercentages(subjects)
    val max               = value.map(_.weight).sum
    val normalizedWeights = value.map(x => (x.name -> x.weight.doubleValue() / max.doubleValue())).toMap

    allPercentages.map(p => p._2 * normalizedWeights(p._1)).sum.round.intValue()
  }

  private def calculatePercentages(
      subjects: mutable.Buffer[IndividualAssemblyLifecycleStage]
  ): mutable.Seq[(AssemblyLifecycleStageName, Int)] =
    subjects.map { s =>
      s.name -> s.status.calculatedPercent
    }

  private def createAggregate(
      aggregateName: AggregateLifecycleStageName,
      version: Seq[IndividualAssemblyLifecycleStage],
      value: Seq[AggregatePart]
  ): AggregateLifecycleStage = {
    // version lifecycles of this name
    val names = value.map(_.name).toSet
    val subjects: mutable.Buffer[IndividualAssemblyLifecycleStage] =
      version.filter(lc => names.contains(lc.name)).toBuffer

    value.foreach { v =>
      if (v.required && !subjects.exists(s => s.name == v.name)) {
        // add a "fake" lifecycle for required parts of the aggregation that are not available yet
        val stage = v.name match {
          case AssemblyLifecycleStageName.ReconciledSpecificationRender =>
            /*
             * A bit of a hack, but the specification render runs twice, before and after the reconciliation.
             * so, if we calculate based solely on the endState, the progress bar jumps around, because
             * the specification render appears as 100% and then 0% again, before the second render.
             *
             * To prevent that, we need to take the second execution into account, each end state needs to account for 50%
             *
             * That means that it's only 100% if the end state is finished AND there's another success state in
             * the history, one that has an end timestamp (because that means it went from success -> something else)
             */
            subjects
              .find(_.name == SpecificationRender)
              .flatMap { specificationRender =>
                val hasSecondSuccess = specificationRender
                  .history
                  .exists(_.exists(h => h.name == StageStatusName.Success && h.end.isDefined))

                Option.when(hasSecondSuccess) {
                  IndividualAssemblyLifecycleStage(
                    v.name,
                    LifecycleStageStatus.emptySuccess,
                    None
                  )
                }
              }.getOrElse {
                IndividualAssemblyLifecycleStage.waiting(v.name)
              }

          case _ => IndividualAssemblyLifecycleStage.waiting(v.name)
        }

        subjects += stage
      }
    }

    val percentage    = calculatePercentage(aggregateName, subjects, value)
    val combinedState = AssemblyEntity.calculateStatus(subjects.toSeq).copy(percent = Some(percentage))

    val hist = flattenHistory(subjects.flatMap(_.history.getOrElse(Seq())).toSeq)

    AggregateLifecycleStage(
      aggregateName,
      combinedState,
      Some(hist)
    )
  }

  private def toSimple(lc: AggregateLifecycleStage): ApiFileLifecycle = {
    val cycleName = lc.name
    val hist      = lc.history.getOrElse(Seq()) // all entries of one lifecycle

    val durations = hist.groupBy(_.name).flatMap { lcstategroup =>
      val stateName = lcstategroup._1
      val durationOptions = lcstategroup._2.map { state =>
        state.`end`.map { e =>
          e - state.start
        }
      }

      if (durationOptions.forall(_.isDefined)) {
        Some(s"$stateName" -> durationOptions.flatten.sum)
      } else {
        None
      }
    }

    ApiFileLifecycle(
      cycleName.value,
      lc.status,
      durations,
      None
    )
  }
}

object LifecycleFactory {



  def flattenHistory(value: Seq[HistoricLifecycleState]): Seq[HistoricLifecycleState] = {
    val sorted = value.sortBy(_.start)
    val breaks = sorted.flatMap(h => Seq(Some(h.start), h.end).flatten)

    val b = Seq.newBuilder[HistoricLifecycleState]

    var current: Option[HistoricLifecycleState] = None

    def applyCurrent(l: Long, name: StageStatusName): Unit =
      current match {
        case Some(value) =>
          if (value.name == name) {
            // extend the current history entry
            current = Some(value.copy(end = Some(l)))
          } else {
            b += value // .copy(end = Some(l))

            current = Some(HistoricLifecycleState(
              name = name,
              start = l,
              end = None
            ))
          }
        case None =>
          current = Some(HistoricLifecycleState(
            name = name,
            start = l,
            end = None
          ))
      }

    breaks.sorted.foreach { l =>
      val applies = sorted.filter { h =>
        h.end.isDefined && l >= h.start && l <= h.end.get
      }

      if (applies.map(_.name).contains(StageStatusName.Progress)) {
        applyCurrent(l, StageStatusName.Progress)
      } else if (applies.map(_.name).contains(StageStatusName.Success)) {
        applyCurrent(l, StageStatusName.Success)
      } else if (applies.map(_.name).contains(StageStatusName.Waiting)) {
        applyCurrent(l, StageStatusName.Waiting)
      } else {
        applyCurrent(l, StageStatusName.Unknown)
      }
    }

    current.foreach(b += _)

    b.result()
  }

}
