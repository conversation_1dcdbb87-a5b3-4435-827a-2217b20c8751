package de.fellows.app.assembly.impl

import java.io.File
import de.fellows.app.assembly.impl.lifecycles.{LifecycleDebug, LifecycleFactory}
import de.fellows.app.assemby.api.LifecycleSummary

import javax.imageio.ImageIO
import play.api.libs.json.Json

object DrawTest extends App {

  val j = loadj

  val sum = j.as[LifecycleSummary]

  ImageIO.write(LifecycleDebug.draw(sum), "png", new File("/tmp/cycle.png"))

  private def loadj = {
    Json.parse(
      """
        |{
        |    "version": {
        |        "dfm": {
        |            "name": "dfm",
        |            "status": {
        |                "name": "success",
        |                "messages": []
        |            },
        |            "history": [
        |                {
        |                    "name": "progress",
        |                    "start": 1605628434239,
        |                    "end": 1605628434795
        |                },
        |                {
        |                    "name": "success",
        |                    "start": 1605628434795
        |                }
        |            ]
        |        },
        |        "initialization": {
        |            "name": "initialization",
        |            "status": {
        |                "name": "success",
        |                "messages": []
        |            },
        |            "history": [
        |                {
        |                    "name": "progress",
        |                    "start": 1605628396504,
        |                    "end": 1605628397341
        |                },
        |                {
        |                    "name": "success",
        |                    "start": 1605628397341
        |                }
        |            ]
        |        },
        |        "fileanalysis": {
        |            "name": "fileanalysis",
        |            "status": {
        |                "name": "success",
        |                "messages": [
        |                    "[debug/analysis] persist 100 distances",
        |                    "[debug/analysis] persist 100 distances",
        |                    "[debug/analysis] persist 100 distances",
        |                    "[debug/analysis] persist 100 distances",
        |                    "[debug/analysis] persist 100 distances",
        |                    "[debug/analysis] persist 100 distances"
        |                ],
        |                "percent": 100
        |            },
        |            "history": [
        |                {
        |                    "name": "waiting",
        |                    "start": 1605628331322,
        |                    "end": 1605628334541
        |                },
        |                {
        |                    "name": "progress",
        |                    "start": 1605628334541,
        |                    "end": 1605628341398
        |                },
        |                {
        |                    "name": "waiting",
        |                    "start": 1605628341398,
        |                    "end": 1605628382887
        |                },
        |                {
        |                    "name": "progress",
        |                    "start": 1605628382887,
        |                    "end": 1605628432520
        |                },
        |                {
        |                    "name": "success",
        |                    "start": 1605628432520
        |                }
        |            ]
        |        },
        |        "analysis": {
        |            "name": "analysis",
        |            "status": {
        |                "name": "success",
        |                "messages": []
        |            },
        |            "history": [
        |                {
        |                    "name": "waiting",
        |                    "start": 1605628397367,
        |                    "end": 1605628397432
        |                },
        |                {
        |                    "name": "progress",
        |                    "start": 1605628397432,
        |                    "end": 1605628424459
        |                },
        |                {
        |                    "name": "success",
        |                    "start": 1605628424459
        |                }
        |            ]
        |        },
        |        "render": {
        |            "name": "render",
        |            "status": {
        |                "name": "success",
        |                "messages": [],
        |                "percent": 100
        |            },
        |            "history": [
        |                {
        |                    "name": "waiting",
        |                    "start": 1605628331322,
        |                    "end": 1605628331428
        |                },
        |                {
        |                    "name": "progress",
        |                    "start": 1605628331428,
        |                    "end": 1605628382844
        |                },
        |                {
        |                    "name": "success",
        |                    "start": 1605628382844
        |                }
        |            ]
        |        }
        |    },
        |    "files": {
        |        "908582B.GTO": {
        |            "fileanalysis": {
        |                "name": "fileanalysis",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331581,
        |                        "end": 1605628341251
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628341251
        |                    }
        |                ]
        |            },
        |            "render": {
        |                "name": "render",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331581,
        |                        "end": 1605628331644
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628331644,
        |                        "end": 1605628341230
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628341230
        |                    }
        |                ]
        |            }
        |        },
        |        "908582B.GM1": {
        |            "fileanalysis": {
        |                "name": "fileanalysis",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331492,
        |                        "end": 1605628331789
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628331789
        |                    }
        |                ]
        |            },
        |            "render": {
        |                "name": "render",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331492,
        |                        "end": 1605628331541
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628331541,
        |                        "end": 1605628331755
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628331755
        |                    }
        |                ]
        |            }
        |        },
        |        "908582B-RoundHoles-NonPlated.TXT": {},
        |        "908582B.GTL": {
        |            "fileanalysis": {
        |                "name": "fileanalysis",
        |                "status": {
        |                    "name": "success",
        |                    "messages": [
        |                        "[debug/analysis] persist 100 distances",
        |                        "[debug/analysis] persist 100 distances"
        |                    ]
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331765,
        |                        "end": 1605628382887
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628382887,
        |                        "end": 1605628432520
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628432520
        |                    }
        |                ]
        |            },
        |            "render": {
        |                "name": "render",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331765,
        |                        "end": 1605628331818
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628331818,
        |                        "end": 1605628382844
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628382844
        |                    }
        |                ]
        |            }
        |        },
        |        "908582b.PDF": {},
        |        "908582B.GP1": {
        |            "fileanalysis": {
        |                "name": "fileanalysis",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331643,
        |                        "end": 1605628332099
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628332099
        |                    }
        |                ]
        |            },
        |            "render": {
        |                "name": "render",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331643,
        |                        "end": 1605628331689
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628331689,
        |                        "end": 1605628332070
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628332070
        |                    }
        |                ]
        |            }
        |        },
        |        "908582B.G1": {
        |            "fileanalysis": {
        |                "name": "fileanalysis",
        |                "status": {
        |                    "name": "success",
        |                    "messages": [
        |                        "[debug/analysis] persist 100 distances",
        |                        "[debug/analysis] persist 100 distances"
        |                    ]
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331540,
        |                        "end": 1605628334541
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628334541,
        |                        "end": 1605628341398
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628341398
        |                    }
        |                ]
        |            },
        |            "render": {
        |                "name": "render",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331540,
        |                        "end": 1605628331584
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628331584,
        |                        "end": 1605628334507
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628334507
        |                    }
        |                ]
        |            }
        |        },
        |        "908582B.GBL": {
        |            "fileanalysis": {
        |                "name": "fileanalysis",
        |                "status": {
        |                    "name": "success",
        |                    "messages": [
        |                        "[debug/analysis] persist 100 distances",
        |                        "[debug/analysis] persist 100 distances"
        |                    ]
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331433,
        |                        "end": 1605628336906
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628336906,
        |                        "end": 1605628341377
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628341377
        |                    }
        |                ]
        |            },
        |            "render": {
        |                "name": "render",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331433,
        |                        "end": 1605628331491
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628331491,
        |                        "end": 1605628336864
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628336864
        |                    }
        |                ]
        |            }
        |        },
        |        "908582B-SlotHoles-NonPlated.TXT": {},
        |        "908582B.DRR": {},
        |        "908582B.GTS": {
        |            "fileanalysis": {
        |                "name": "fileanalysis",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331688,
        |                        "end": 1605628333590
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628333590
        |                    }
        |                ]
        |            },
        |            "render": {
        |                "name": "render",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331688,
        |                        "end": 1605628331719
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628331719,
        |                        "end": 1605628333573
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628333573
        |                    }
        |                ]
        |            }
        |        },
        |        "908582B-RoundHoles-Plated.TXT": {},
        |        "908582B.GBS": {
        |            "fileanalysis": {
        |                "name": "fileanalysis",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331322,
        |                        "end": 1605628331767
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628331767
        |                    }
        |                ]
        |            },
        |            "render": {
        |                "name": "render",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331322,
        |                        "end": 1605628331428
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628331428,
        |                        "end": 1605628331739
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628331739
        |                    }
        |                ]
        |            }
        |        },
        |        "908582B.GD1": {
        |            "fileanalysis": {
        |                "name": "fileanalysis",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331718,
        |                        "end": 1605628334458
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628334458
        |                    }
        |                ]
        |            },
        |            "render": {
        |                "name": "render",
        |                "status": {
        |                    "name": "success",
        |                    "messages": []
        |                },
        |                "history": [
        |                    {
        |                        "name": "waiting",
        |                        "start": 1605628331718,
        |                        "end": 1605628331765
        |                    },
        |                    {
        |                        "name": "progress",
        |                        "start": 1605628331765,
        |                        "end": 1605628334441
        |                    },
        |                    {
        |                        "name": "success",
        |                        "start": 1605628334441
        |                    }
        |                ]
        |            }
        |        }
        |    }
        |}
        |""".stripMargin
    )
  }
}
