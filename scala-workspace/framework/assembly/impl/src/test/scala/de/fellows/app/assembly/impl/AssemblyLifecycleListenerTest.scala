package de.fellows.app.assembly.impl

import de.fellows.app.assembly.impl.AssemblyLifecycleSvixEventListener.toSvixEvent
import de.fellows.app.assembly.impl.entities.{BaseAssemblyReference, VersionLifecycleUpdated}
import de.fellows.app.assemby.api.enums.{AssemblyStatus, UIStatus}
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, IndividualAssemblyLifecycleStage}
import de.fellows.ems.pcb.api.PCBFeatures
import de.fellows.utils.internal.{LifecycleStageStatus, StageStatusName}
import de.fellows.utils.svix.{PcbId, Percentage, RenderFinishedEvent, RenderStartedEvent, RenderUpdatedEvent, Result}
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec
import de.fellows.utils.svix.TenantId

import java.time.{Instant, LocalTime}
import java.util.UUID
import de.fellows.app.assemby.api.LifecycleMessage
import de.fellows.app.assembly.commons.AssemblyReference

class AssemblyLifecycleListenerTest extends AnyWordSpec with should.Matchers {

  private val versionId = UUID.randomUUID()
  private val pcbId     = PcbId(versionId)
  private val timestamp = Instant.now()
  private val tenantId  = TenantId("test-team")

  private val baseref = BaseAssemblyReference(
    eid = UUID.randomUUID(),
    gid = "testassembly",
    team = tenantId.value,
    creator = UUID.randomUUID().toString,
    Instant.now,
    customer = None,
    orderId = None,
    assignee = None,
    itemNo = None,
    name = "testassembly",
    description = Some("yo"),
    project = None,
    AssemblyStatus.INITIAL_STATUS,
    UIStatus.DEFAULT,
    preview = None,
    features = Seq(PCBFeatures.PCB_FEATURE),
    currentVersion = Some(versionId),
    mail = None,
    template = None,
    externalReference = None
  )

  val assemblyReference = AssemblyReference(
    team = baseref.team,
    id = baseref.eid,
    gid = Some(baseref.gid),
    version = baseref.currentVersion.get
  )

  "AssemblyLifecycleListener" should {
    "toSvixEvent" should {
      "send a RenderStartedEvent when a Render lifecycle stage is started" in {
        val event = VersionLifecycleUpdated(
          assembly = baseref,
          versionID = UUID.randomUUID(),
          lc = Some(
            IndividualAssemblyLifecycleStage(
              AssemblyLifecycleStageName.Render,
              LifecycleStageStatus(StageStatusName.Progress, Seq.empty, None),
              None
            )
          ),
          oldState = None,
          versionLifecycles = Seq.empty
        )

        val svixEvents = toSvixEvent(event, timestamp)

        svixEvents should contain((RenderStartedEvent(pcbId, timestamp), PcbId(baseref.currentVersion.get), tenantId))
      }

      "send a RenderUpdateEvent when a Render lifecycle stage is updated" in {
        val event = VersionLifecycleUpdated(
          assembly = baseref,
          versionID = UUID.randomUUID(),
          lc = Some(
            IndividualAssemblyLifecycleStage(
              AssemblyLifecycleStageName.Render,
              LifecycleStageStatus(StageStatusName.Progress, Seq.empty, Some(50)),
              None
            )
          ),
          oldState = Some(
            IndividualAssemblyLifecycleStage(
              AssemblyLifecycleStageName.Render,
              LifecycleStageStatus(StageStatusName.Progress, Seq.empty, Some(25)),
              None
            )
          )
        )

        val svixEvents = toSvixEvent(event, timestamp)

        svixEvents should contain((
          RenderUpdatedEvent(pcbId, Percentage(50), timestamp),
          PcbId(baseref.currentVersion.get),
          tenantId
        ))
      }

      "send a RenderFinishedEvent when a Render lifecycle stage is finished" in {
        val event = VersionLifecycleUpdated(
          assembly = baseref,
          versionID = UUID.randomUUID(),
          lc = Some(
            IndividualAssemblyLifecycleStage(
              AssemblyLifecycleStageName.Render,
              LifecycleStageStatus(StageStatusName.Success, Seq.empty, None),
              None
            )
          ),
          oldState = Some(
            IndividualAssemblyLifecycleStage(
              AssemblyLifecycleStageName.Render,
              LifecycleStageStatus(StageStatusName.Progress, Seq.empty, Some(25)),
              None
            )
          )
        )

        val svixEvents = toSvixEvent(event, timestamp)

        svixEvents should contain((
          RenderFinishedEvent(pcbId, Result.success, timestamp),
          PcbId(baseref.currentVersion.get),
          tenantId
        ))
      }

      "not return an event for render Waiting state" in {
        val event = VersionLifecycleUpdated(
          assembly = baseref,
          versionID = UUID.randomUUID(),
          lc = Some(
            IndividualAssemblyLifecycleStage(
              AssemblyLifecycleStageName.Render,
              LifecycleStageStatus(StageStatusName.Waiting, Seq.empty, None),
              None
            )
          ),
          oldState = None,
          versionLifecycles = Seq.empty
        )

        val svixEvent = toSvixEvent(event, timestamp)

        svixEvent shouldBe None
      }

      "does not return an event if the lifecycle stage is not Render nor DfmAnalysis" in {
        val event = VersionLifecycleUpdated(
          assembly = baseref,
          versionID = UUID.randomUUID(),
          lc = Some(
            IndividualAssemblyLifecycleStage(
              AssemblyLifecycleStageName.FileAnalysis,
              LifecycleStageStatus(StageStatusName.Progress, Seq.empty, None),
              None
            )
          ),
          oldState = None,
          versionLifecycles = Seq.empty
        )

        val svixEvent = toSvixEvent(event, timestamp)

        svixEvent shouldBe None
      }
    }
  }
}
