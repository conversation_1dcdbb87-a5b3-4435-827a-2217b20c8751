package de.fellows.app.assembly.commons

import de.fellows.utils.JsonFormats

import java.util.UUID
import io.swagger.v3.oas.annotations.Parameter
import play.api.libs.json
import play.api.libs.json.Json.MacroOptions
import play.api.libs.json.{Format, JsError, JsObject, Json, JsonConfiguration, Reads}

import scala.annotation.meta.field

sealed trait AbstractAssemblyReference {
  val team: String

  def getReference(): AssemblyReference

  /** gets the identifier that describes the version of this reference.
    * For AssemblyReferences, this is the version UUID.
    * Shared Assemblies are already created for a specific version (which is contained in its reference), so to we use the id of the share for referencing.
    * @return
    */
  def getReferenceIdentifier: UUID =
    this match {
      case AssemblyReference(team, id, gid, version)         => version
      case SharedAssemblyReference(team, id, sharedAssembly) => id
    }
}

case class AssemblyReference(
    @(Parameter @field)(
      description =
        """
                                    |The team of the Assembly
                                    |
                                    |Will always be set when retrieving data.
                                    |When Posting data, this is generally ignored in favor of the current logged-in team """
    )
    override val team: String,
    @(Parameter @field)(
      description =
        """
                                    | The Assembly UUID
                                    |
                                    | This ID is universally unique and will not change for any given assembly"""
    )
    val id: UUID,
    @(Parameter @field)(
      description =
        """
                                    | The Assembly GID (human readable ID)
                                    |
                                    | This ID is unique inside its team, and will not change for any given assembly"""
    )
    val gid: Option[String],
    @(Parameter @field)(
      description =
        """
                                    | The Version ID
                                    |
                                    |  This ID is universally unique and will not change for any given version"""
    )
    val version: UUID
) extends AbstractAssemblyReference {
  override def getReference(): AssemblyReference = this
}

case class SharedAssemblyReference(
    team: String,
    id: UUID,
    sharedAssembly: AssemblyReference
) extends AbstractAssemblyReference {
  override def getReference(): AssemblyReference = sharedAssembly
}

object SharedAssemblyReference {
  implicit val format: Format[SharedAssemblyReference] = Json.format
}

case class AssemblyFeature(service: String, feature: String)

object AssemblyFeature {
  implicit val format: Format[AssemblyFeature] = Json.format[AssemblyFeature]
}

object AssemblyReference {
  implicit val format: json.Format[AssemblyReference] = Json.format
}

object AbstractAssemblyReference {
  implicit val cfg: JsonConfiguration.Aux[MacroOptions] =
    JsonConfiguration(
      typeNaming = JsonFormats.JsonSealedTraitNaming
    )

  val baseFormat: Format[AbstractAssemblyReference] = Json.format

  /** If the _type field is not present, we assume that this is a AssemblyReference.
    * This is for backwards compatibility in the json parsing.
    */
  implicit val format: Format[AbstractAssemblyReference] = Format(
    Reads[AbstractAssemblyReference] {
      case js: JsObject if js.keys.contains("_type") =>
        baseFormat.reads(js)
      case js: JsObject =>
        AssemblyReference.format.reads(js)
      case _ =>
        JsError("Expected an object")
    },
    baseFormat
  )

}
