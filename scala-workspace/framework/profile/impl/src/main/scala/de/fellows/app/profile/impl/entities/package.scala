package de.fellows.app.profile.impl

import de.fellows.app.profile.api.{Address, ProfileData}
import de.fellows.utils.FilePath
import play.api.libs.json.{Format, Json}

import java.util.UUID

package object entities {

  case class InternalProfile(user: UUID, data: ProfileData, addresses: Seq[Address], avatar: Option[FilePath])

  object InternalProfile {
    implicit val format: Format[InternalProfile] = Json.format[InternalProfile]

    def getAddress(prof: InternalProfile, id: UUID): Option[Address] =
      prof.addresses.find(_.id.contains(id))
  }

}
