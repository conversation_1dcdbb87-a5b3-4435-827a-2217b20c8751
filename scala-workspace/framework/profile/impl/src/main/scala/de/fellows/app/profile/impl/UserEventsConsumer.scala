package de.fellows.app.profile.impl

import akka.Done
import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.profile.impl.entities._
import de.fellows.app.user.api.{TeamMessage, UserCreatedMessage, UserDeletedMessage, UserService}
import de.fellows.utils.TopicUtils
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.streams.{EmptyMessage, ValidMessage}

import scala.concurrent.{ExecutionContext, Future}

class UserEventsConsumer(userService: UserService, registry: PersistentEntityRegistry)(implicit
    ec: ExecutionContext,
    mat: Materializer
) extends StackrateLogging {

  val started = System.currentTimeMillis()
  import TopicUtils.defaultNaming
  TopicUtils.subscribeLatest(userService.userTopic(), started) { msg =>
    msg.payload match {
      case ValidMessage(x: UserCreatedMessage) =>
        logger.info(s"received user creation $msg")
        registry.refFor[ProfileEntity](x.id.toString).ask(CreateProfile(x.id)).map { _ =>
          logger.info(s"creating initial profile for user $msg")
          Done
        }.recover {
          case _: ProfileExistsException =>
            logger.warn(s"profile exists $msg")
            Done
        }
      case ValidMessage(x: UserDeletedMessage) =>
        registry.refFor[ProfileEntity](x.id.toString).ask(DeleteProfile(x.id)).map { _ =>
          logger.info(s"deleting profile for user $msg")
          Done
        }.recover {
          case _: ProfileExistsException => Done
        }
    }
  }

  TopicUtils.subscribeLatest(userService.teamTopic(), started) { msg =>
    msg.payload match {
      case ValidMessage(TeamMessage(team, TeamMessage.CREATED)) =>
        registry.refFor[TeamIDEntity](team.domain).ask(GetID(team.domain))
          .flatMap(id =>
            registry.refFor[ProfileEntity](id.toString).ask(CreateProfile(id.id))
              .map { _ =>
                logger.info(s"creating initial profile for team $msg")
                Done
              }
          )
          .recover { e =>
            logger.error("Failed to create Team Profile", e)
            Done
          }

    }
  }

}
