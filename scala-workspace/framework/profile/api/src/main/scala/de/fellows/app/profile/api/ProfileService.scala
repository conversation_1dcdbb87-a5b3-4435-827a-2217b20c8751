// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.profile.api

import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Service, ServiceAcl, ServiceCall}
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.communication.ServiceExceptionSerializer
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.{OpenAPIDefinition, Operation}

import java.util.UUID

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate Profile API"
  )
)
trait ProfileService extends Service with StackrateServiceAPI {

  @StackrateApi
  @Operation(
    summary = "Get User Profile"
  )
  @Tag(name = "User")
  def getProfile(name: UUID): ServiceCall[NotUsed, Profile]

  def _getProfile(name: UUID): ServiceCall[NotUsed, InternalProfileAPI]

  //  @StackrateApi
  def waitForProfile(id: String, name: String): ServiceCall[NotUsed, Source[ProfileMessage, NotUsed]]

  @StackrateApi
  @Operation(
    summary = "Get User Addresses"
  )
  @Tag(name = "User")
  def getAddresses(name: String): ServiceCall[NotUsed, Seq[Address]]

  @StackrateApi
  @Operation(
    summary = "Create a user address"
  )
  @Tag(name = "User")
  def createAddress(name: String): ServiceCall[Address, Done]

  @StackrateApi
  @Operation(
    summary = "Delete a user address"
  )
  @Tag(name = "User")
  def deleteAddress(name: String, id: UUID): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Operation(
    summary = "Update a user address"
  )
  @Tag(name = "User")
  def updateAddress(name: String, id: UUID): ServiceCall[Address, Done]

  @StackrateApi
  @Operation(
    summary = "Update a User Profile"
  )
  @Tag(name = "User")
  def updateProfile(name: String): ServiceCall[ProfileData, Done]

  @StackrateApi
  @Operation(
    summary = "Create a User Profile"
  )
  @Tag(name = "User")
  def createProfile(name: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Operation(
    summary = "Get the Team Profile"
  )
  @Tag(name = "Team")
  def getTeamProfile(team: Option[String]): ServiceCall[NotUsed, Profile]

  def _getTeamProfile(team: String): ServiceCall[NotUsed, InternalProfileAPI]

  @StackrateApi
  @Operation(
    summary = "Update the Team Profile"
  )
  @Tag(name = "Team")
  def updateTeamProfile(team: Option[String]): ServiceCall[ProfileData, Profile]

  @StackrateApi
  @Operation(
    summary = "Delete Team Address"
  )
  @Tag(name = "Team")
  def deleteTeamAddress(address: UUID, team: Option[String]): ServiceCall[NotUsed, Profile]

  @StackrateApi
  @Operation(
    summary = "Create/Update Team Address",
    description =
      """Creates or Updates a Team address.
        |if the `address` parameter is set (when using PUT, or POST with the query parameter), the corresponding Address
        |will be updated instead.
        |"""
  )
  @Tag(name = "Team")
  def updateTeamAddress(address: Option[UUID], team: Option[String]): ServiceCall[Address, Profile]

  override final def descriptor = {
    import Service._

    val userpath  = "/api/profile/users"
    val teamspath = "/api/profile/teams"

    withDocumentation(
      named("profile")
        .withCalls(
          restCall(Method.GET, userpath + "/:name", getProfile _),
          restCall(Method.GET, "/internal/profile/users/:name", _getProfile _),
          restCall(Method.POST, "/api/profile/:name", createProfile _),
          restCall(Method.PUT, userpath + "/:name", updateProfile _),
          restCall(Method.GET, userpath + "/:id/:name/stream", waitForProfile _),
          restCall(Method.GET, userpath + "/:name/address", getAddresses _),
          restCall(Method.POST, userpath + "/:name/address", createAddress _),
          restCall(Method.PUT, userpath + "/:name/address/:id", updateAddress _),
          restCall(Method.DELETE, userpath + "/:name/address/:id", deleteAddress _),
          restCall(Method.GET, teamspath + "/profile?team", getTeamProfile _),
          restCall(Method.POST, teamspath + "/profile/addresses?team&address", updateTeamAddress _),
          restCall(Method.PUT, teamspath + "/profile/addresses/:address?team", updateTeamAddress _),
          restCall(Method.DELETE, teamspath + "/profile/addresses/:address?team", deleteTeamAddress _),
          restCall(Method.GET, "/internal/profile/teams/:name/profile", _getTeamProfile _),
          restCall(Method.PUT, teamspath + "/profile?team", updateTeamProfile _)
        )
        .withAcls(
          ServiceAcl(pathRegex = Some("/files/profile/.*")),
          ServiceAcl(pathRegex = Some(userpath + "/.*"))
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )
  }
}
//
