{"author": {"email": "<EMAIL>", "name": "S L"}, "description": {"EN": "EAGLE default 14 layer CAM job."}, "output_type": "directory", "outputs": [{"filename_prefix": "CAMOutputs/GerberFiles", "format_specifier": {"decimal": 4, "integer": 3}, "generate_job_file": true, "output_type": "gerber", "outputs": [{"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 1, "layer_details": "mixed", "layer_type": "top"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [1, 17, 18], "name": "Top Copper", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 2, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [2, 17, 18], "name": "Copper Layer 2", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 3, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [3, 17, 18], "name": "Copper Layer 3", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 4, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [4, 17, 18], "name": "Copper Layer 4", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 5, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [5, 17, 18], "name": "Copper Layer 5", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 6, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [6, 17, 18], "name": "Copper Layer 6", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 7, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [7, 17, 18], "name": "Copper Layer 7", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 8, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [10, 17, 18], "name": "Copper Layer 10", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 9, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [11, 17, 18], "name": "Copper Layer 11", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 10, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [12, 17, 18], "name": "Copper Layer 12", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 11, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [13, 17, 18], "name": "Copper Layer 13", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 12, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [14, 17, 18], "name": "Copper Layer 14", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 13, "layer_details": "mixed", "layer_type": "Inner"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [15, 17, 18], "name": "Copper Layer 15", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 14, "layer_details": "mixed", "layer_type": "bottom"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [16, 17, 18], "name": "Bottom Copper", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": true, "config": {"file_function": "Profile", "plating": "non-plated"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [], "milling": true, "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Soldermask", "index": 1, "layer_type": "top"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [29], "name": "Soldermask Top", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Soldermask", "index": 1, "layer_type": "bottom"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [30], "name": "Soldermask Bottom", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Paste", "layer_type": "top"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [31], "milling": false, "name": "Solderpaste Top", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Paste", "layer_type": "bottom"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [32], "milling": false, "name": "Solderpaste Bottom", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Legend", "index": 1, "layer_type": "top"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [21, 25], "milling": false, "name": "Silkscreen Top", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Legend", "index": 1, "layer_type": "bottom"}, "filename_format": "%PREFIX/%FF.gbr", "layers": [22, 26], "milling": false, "name": "Silkscreen Bottom", "polarity": "positive", "type": "gerber_layer"}], "version": "X2"}, {"filename_prefix": "CAMOutputs/DrillFiles", "format_specifier": {"decimal": 3, "integer": 3}, "output_type": "drill", "outputs": [{"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "filename_format": "%DRILLPREFIX/drill_%FROM_%TO.xln", "name": "Auto Drill", "type": "autodrills"}]}, {"filename_prefix": "CAMOutputs/Assembly", "output_type": "assembly", "outputs": [{"filename_format": "%ASSEMBLYPREFIX/%N", "list_attribute": true, "list_type": "values", "name": "Bill of Material", "output_format": "csv", "type": "bom"}, {"filename_format": "%ASSEMBLYPREFIX/PnP_%N_%BOARDSIDE", "name": "Pick and Place", "output_format": "csv", "type": "pick_and_place"}]}, {"filename_prefix": "CAMOutputs/DrawingFiles", "output_type": "drawing", "outputs": []}], "timestamp": "2021-10-21T14:50:38", "type": "EAGLE CAM job", "units": "metric", "version": "9.2.0"}