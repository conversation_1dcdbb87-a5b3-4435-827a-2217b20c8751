// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.security

import akka.stream.Materializer
import com.datastax.driver.core._
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.security.SecurityApi.PermissionBinding
import de.fellows.utils.security.Permission
import org.slf4j.LoggerFactory

import java.util.UUID
import scala.jdk.CollectionConverters._
import scala.concurrent.{ExecutionContext, Future}

class SecurityRepository(session: CassandraSession)(implicit ec: ExecutionContext, mat: Materializer) {
  lazy final val logger = LoggerFactory.getLogger(classOf[SecurityRepository])
  val conf: Config      = ConfigFactory.load

  def getDefaultPermissions(team: String, user: UUID) =
    Future.successful(
      //      (conf.getConfigList("fellows.security.initial")
      //        .asScala.find(p => p.getString("name") == "default-group") match {
      //        case None => Seq()
      //        case Some(c) => {
      //
      //          val perms = c.getStringList("permissions")
      //          val binding = PermissionBinding(
      //            resourceClass = None,
      //            team = Some(Seq(team)),
      //            owner = None, //Some(Seq(user.toString, team)),
      //            //            owner = None, //TODO
      //            resource = None,
      //            attribute = None,
      //            action = None
      //          )
      //
      //          println(s"bind default permission ${perms.asScala} with ${binding}")
      //          val r = bindWithBinding(perms.asScala.map(Permission.create).toSeq, Some(binding))
      //          println(s"binding is $r")
      //
      //          r
      //        }
      //      }) ++
      (conf.getStringList("fellows.security.admins").asScala.map(UUID.fromString).find(_ == user) match {
        case Some(u) => Seq(Permission(Seq(), Seq(), Seq(), Seq(), Seq(), Seq()))
        case None    => Seq()
      })
    )

  private def convertBoundPermissions(item: Row): Seq[Permission] =
    bindWithPermission(
      item.getList("permissions", classOf[Permission]).asScala.toSeq,
      Option(item.get[Permission]("binding", classOf[Permission]))
    )

  def bindWithPermission(perms: Seq[Permission], binding: Option[Permission]): Seq[Permission] =
    bindWithBinding(
      perms,
      binding.map(b =>
        PermissionBinding(
          resourceClass = if (b.resourceClass.isEmpty) None else Some(b.resourceClass),
          team = if (b.team.isEmpty) None else Some(b.team),
          owner = if (b.owner.isEmpty) None else Some(b.owner),
          resource = if (b.resource.isEmpty) None else Some(b.resource),
          attribute = if (b.attribute.isEmpty) None else Some(b.attribute),
          action = if (b.action.isEmpty) None else Some(b.action)
        )
      )
    )

  /** binds permission p to bnd. if bnd is None, return p. if bnd is Some, return p ++ bnd. a special char "_" can be
    * used in p, that will be removed.
    *
    * @param p
    * @param bnd
    * @tparam T
    * @return
    */
  def bind[T](p: Seq[T], bnd: Option[Seq[T]]): Seq[T] =
    p flatMap {
      case "_" => bnd.getOrElse(Seq())
      case x   => Seq(x)
    }
  //    val r = bnd match {
  //      case Some(b) => b ++ p.filterNot(_.toString == "_")
  //      case None => p
  //    }
  //    r

  def bindWithBinding(perms: Seq[Permission], binding: Option[PermissionBinding]): Seq[Permission] =
    if (binding.isDefined) {
      val bnd = binding.get
      perms.map { p =>
        p.copy(
          resourceClass = bind(p.resourceClass, bnd.resourceClass),
          team = bind(p.team, bnd.team),
          owner = bind(p.owner, bnd.owner),
          resource = bind(p.resource, bnd.resource),
          attribute = bind(p.attribute, bnd.attribute),
          action = bind(p.action, bnd.action)
        )
      }
    } else {
      perms
    }

}
