package de.fellows.app.security

import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.persistence.{ PersistentEntityRef, PersistentEntityRegistry }
import de.fellows.app.security.entities.user._
import de.fellows.app.user.api.UserService
import org.slf4j.{ Logger, LoggerFactory }

import scala.concurrent.ExecutionContext

class UserEventsConsumer(
    userService: UserService,
    registry: PersistentEntityRegistry,
    securityRepository: SecurityRepository
)(implicit ec: ExecutionContext, mat: Materializer) {

  private final val log: Logger =
    LoggerFactory.getLogger(classOf[UserEventsConsumer])

  def addUserToGroup(u: PersistentEntityRef[SecurityUserCommand], msg: String) = {
    //    securityRepository.getGroup("default-user").flatMap(g => {
    //      log.info(s"set initial permissions for ${msg}")
    //      val binding = Some(PermissionBinding(
    //        resourceClass = None,
    //        owner = Some(Seq(msg)),
    //        resource = None,
    //        attribute = None,
    //        action = None
    //      ))
    //
    //      u.ask(AddUserToGroup(g.get.id, binding)).map(_ => {
    //        Done
    //      }).recover {
    //        case _: UserDoesNotExistException => Done
    //        case _: UserIsPartOfGroupException => Done
    //      }
    //    })
  }
}
