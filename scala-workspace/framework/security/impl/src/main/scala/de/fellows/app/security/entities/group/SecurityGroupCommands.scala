package de.fellows.app.security.entities.group

import java.util.UUID

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.security.SecurityApi.Group
import de.fellows.utils.security.Permission
import play.api.libs.json.{ Format, Json }

sealed trait SecurityGroupCommand

case class GetGroup(id: UUID) extends SecurityGroupCommand with ReplyType[Group]

case class CreateSecurityGroup(id: UUID, name: String, perms: Option[Seq[Permission]]) extends SecurityGroupCommand
    with ReplyType[Group]

case class GrantGroupPermissions(perms: Seq[Permission]) extends SecurityGroupCommand with ReplyType[Done]

case class SetGroupPermissions(perms: Seq[Permission]) extends SecurityGroupCommand with ReplyType[Group]

case class RevokeGroupPermissions(perms: Seq[Permission]) extends SecurityGroupCommand with ReplyType[Done]

object GetGroup {
  implicit val format: Format[GetGroup] = Json.format[GetGroup]
}

object SetGroupPermissions {
  implicit val format: Format[SetGroupPermissions] = Json.format[SetGroupPermissions]
}

object CreateSecurityGroup {
  implicit val format: Format[CreateSecurityGroup] = Json.format[CreateSecurityGroup]
}

object RevokeGroupPermissions {
  implicit val format: Format[RevokeGroupPermissions] = Json.format[RevokeGroupPermissions]
}

object GrantGroupPermissions {
  implicit val format: Format[GrantGroupPermissions] = Json.format[GrantGroupPermissions]
}
