package de.fellows.app.security.entities.group

import java.util.UUID

import com.lightbend.lagom.scaladsl.persistence.{ AggregateEvent, AggregateEventTag }
import de.fellows.utils.security.Permission
import play.api.libs.json.{ Format, Json }

sealed trait SecurityGroupEvent extends AggregateEvent[SecurityGroupEvent] {
  override def aggregateTag = SecurityGroupEvent.Tag

}

object SecurityGroupEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[SecurityGroupEvent](NumShards)
}

case class SecurityGroupCreated(uid: UUID, name: String, perms: Seq[Permission]) extends SecurityGroupEvent

case class GroupPermissionsUpdated(name: String, perms: Seq[Permission]) extends SecurityGroupEvent

object SecurityGroupCreated {
  implicit val format: Format[SecurityGroupCreated] = Json.format[SecurityGroupCreated]

}

object GroupPermissionsUpdated {
  implicit val format: Format[GroupPermissionsUpdated] = Json.format[GroupPermissionsUpdated]

}
