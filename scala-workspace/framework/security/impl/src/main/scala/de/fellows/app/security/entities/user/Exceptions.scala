package de.fellows.app.security.entities.user

import play.api.libs.json.Format
import play.api.libs.json.Json
import de.fellows.utils.communication.ServiceError
import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode

final case class UserIsPartOfGroupException(message: String) extends IllegalArgumentException {
  def serviceError: ServiceError = ServiceError(1, message, TransportErrorCode.BadRequest)
}
