package de.fellows.app.security

import akka.actor.ActorSystem
import akka.stream.Materializer
import akka.{ Done, NotUsed }
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.transport.{ TransportErrorCode, TransportException }
import com.lightbend.lagom.scaladsl.persistence.{ PersistentEntity, PersistentEntityRegistry }
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.typesafe.config.ConfigFactory
import de.fellows.app.security.AccessControlServiceComposition._
import de.fellows.app.security.SecurityApi.{ Group, PermissionBinding }
import de.fellows.app.security.entities.group._
import de.fellows.app.security.entities.user._
import de.fellows.app.security.read.GroupRepository
import de.fellows.utils.UUIDUtils
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.security.Permission

import java.util.UUID
import scala.concurrent.{ ExecutionContext, Future }
import scala.reflect.ClassTag

class SecurityServiceImpl(
    registry: PersistentEntityRegistry,
    system: ActorSystem,
    secRep: SecurityRepository,
    grep: GroupRepository
)(implicit ec: ExecutionContext, mat: Materializer) extends SecurityService
    with StackrateAPIImpl {
  implicit val actorSystem: ActorSystem             = system
  implicit val secSrv: SecurityService = this

  val conf = ConfigFactory.load()

  override def _getAllPermissions(
      team: String,
      user: UUID,
      withGroups: Boolean = true
  ): ServerServiceCall[NotUsed, Seq[Permission]] =
    ServerServiceCall { _ =>
      _doGetAllPermissions(team, user, withGroups)
    }

  private def _doGetAllPermissions(team: String, user: UUID, withGroups: Boolean) =
    for {
      dp <- refFor[UserEntity](user).ask(GetUser(user))
      gp <-
        withGroups match {
          case false => Future.successful(Seq())
          case true =>
            Future.sequence(dp.groups.map { g =>
              refFor[GroupEntity](g.group).ask(GetGroup(g.group)).map { group =>
                (group.permissions, g.binding)
              }
            }).map(_.map(pwb =>
              secRep.bindWithBinding(
                pwb._1,
                pwb._2.map(bnd =>
                  bnd.copy(
                    team = Some(Seq(team)),
                    owner = Some(Seq(user.toString, team))
//              owner = None
                  )
                )
              )
            )).map(_.flatten)
        }

      defPerms <- secRep.getDefaultPermissions(team, user)
    } yield {
      println(s"found permissions: $dp \n $gp \n $defPerms")
      dp.permissions ++ gp ++ defPerms
    }

  override def _newUser(team: String, technical: Boolean): ServiceCall[UUID, Seq[Permission]] =
    ServerServiceCall { u =>
      conf.getString("fellows.security.default-group")
      (if (!technical) {
         _doAddUserToGroup(u, "default", team)
       } else {
         Future.successful(Done)
       }).flatMap { _ =>
        _doGetAllPermissions(team, u, true)
      }
    }

  override def getAllPermissions(user: UUID): ServiceCall[NotUsed, Seq[Permission]] =
    authorizedString(token => s"permissions:${token.team}:${user}:*:permissions:read") { (token, _) =>
      _getAllPermissions(token.team, user)
    }

  override def getPermissions(user: UUID, groups: Option[Boolean]): ServiceCall[NotUsed, Seq[Permission]] =
    authorizedString(token => s"permissions:${token.team}:${user}:*:permissions:read") { (token, _) =>
      _getAllPermissions(token.team, user, groups.getOrElse(false))
    }

  override def grantPermission(user: UUID): ServerServiceCall[Seq[String], Done] =
    authorizedString(token => s"permissions:${token.team}:${user}:*:permissions:write") { (token, _) =>
      ServerServiceCall { perm: Seq[String] =>
        refFor[UserEntity](user).ask(GrantPermissions(user, perm.map(Permission.create)))
      }
    }

  override def revokePermission(user: UUID): ServiceCall[Seq[String], Done] =
    authorizedString(token => s"permissions:${token.team}:${user}:*:permissions:write") { (token, _) =>
      ServerServiceCall { perm: Seq[String] =>
        refFor[UserEntity](user).ask(RevokePermissions(user, perm.map(Permission.create)))
      }
    }

  override def addUserToGroup(user: UUID, group: String): ServiceCall[NotUsed, Done] =
    authorizedString(
      token => s"permissions:${token.team}:${user}:*:permissions:write",
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _doAddUserToGroup(user, group, token.team)
      }
    }

  private def _doAddUserToGroup(user: UUID, group: String, team: String): Future[Done] = {
    val binding = PermissionBinding(
      resourceClass = None,
      team = Some(Seq(team)),
      //      owner = Some(Seq(user.toString, team)),
      owner = None, // TODO
      resource = None,
      attribute = None,
      action = None
    )

    groupByNameOrID(group, grid => refFor[UserEntity](user).ask(AddUserToGroup(user, grid, Some(binding))))
  }

  override def removeUserFromGroup(user: UUID, group: UUID): ServiceCall[NotUsed, Done] =
    authorizedString(token => s"permissions:${token.team}:${user}:*:permissions:write") { (token, _) =>
      ServerServiceCall { _ =>
        refFor[UserEntity](user).ask(RemoveUserFromGroup(user, group))
      }
    }

  override def grantPermissionsToGroup(group: UUID): ServiceCall[Seq[String], Done] =
    authorizedString(token => s"permissions:${token.team}:${group}:*:permissions:write") { (token, _) =>
      ServerServiceCall { permString: Seq[String] =>
        val perms = permString.map(Permission.create)
        registry.refFor[GroupEntity](group.toString).ask(GrantGroupPermissions(perms))
      }
    }

  override def setPermissionsToGroup(group: UUID): ServiceCall[Seq[String], Group] =
    authorizedString(token => s"permissions:${token.team}:${group}:*:permissions:write") { (token, _) =>
      ServerServiceCall { permString: Seq[String] =>
        val perms = permString.map(Permission.create)
        registry.refFor[GroupEntity](group.toString).ask(SetGroupPermissions(perms))
      }
    }

  override def revokePermissionFromGroup(group: UUID): ServiceCall[Seq[String], Done] =
    authorizedString(token => s"permissions:${token.team}:${group}:*:permissions:write") { (token, _) =>
      ServerServiceCall { permString: Seq[String] =>
        val perms = permString.map(Permission.create)
        registry.refFor[GroupEntity](group.toString).ask(RevokeGroupPermissions(perms))
      }
    }

  override def createGroup(name: String): ServiceCall[Seq[String], SecurityApi.Group] =
    authorizedString(token => s"permissions:${token.team}:*:*:permissions:write") { (token, _) =>
      ServerServiceCall { permStrings: Seq[String] =>
        val perms = permStrings.map(Permission.create)

        (this.grep.getId(name).flatMap {
          case Some(id) =>
            registry.refFor[GroupEntity](id.toString).ask(SetGroupPermissions(perms))
          case None =>
            val id = UUID.randomUUID()
            registry.refFor[GroupEntity](id.toString).ask(CreateSecurityGroup(id, name, Some(perms)))
        })
      }
    }

  def groupByNameOrID[T](group: String, cb: UUID => Future[T]): Future[T] =
    (UUIDUtils.fromString(group) match {
      case Some(g) => Future.successful(Some(g))
      case None    => grep.getId(group)
    }).flatMap {
      case Some(grid) => cb(grid)
      case None       => throw new TransportException(TransportErrorCode.NotFound, s"group $group not found")
    }

  override def getGroup(group: String): ServiceCall[NotUsed, Group] =
    authorizedString(token => s"permissions:${token.team}:${token.userId}:*:permissions:read") { (token, _) =>
      ServerServiceCall { _ =>
        groupByNameOrID(group, grid => registry.refFor[GroupEntity](grid.toString).ask(GetGroup(grid)))
      }
    }

  override def getGroups(user: Option[UUID]): ServiceCall[NotUsed, Seq[Group]] =
    authorizedString(token => s"permissions:${token.team}:${user.getOrElse(token.userId)}:*:permissions:read") {
      (token, _) =>
        ServerServiceCall { _ =>
          val groupIds = user match {
            case Some(uid) => refFor[UserEntity](uid).ask(GetUser(uid)).map(_.groups.map(_.group))
            case None      => grep.getAllGroupIds()
          }

          groupIds.flatMap { gids =>
            Future.sequence(gids.map(gid => refFor[GroupEntity](gid).ask(GetGroup(gid))))
          }
        }
    }

  private def refFor[T <: PersistentEntity: ClassTag](id: UUID) = registry.refFor[T](id.toString)

}

object SecurityServiceImpl {}
