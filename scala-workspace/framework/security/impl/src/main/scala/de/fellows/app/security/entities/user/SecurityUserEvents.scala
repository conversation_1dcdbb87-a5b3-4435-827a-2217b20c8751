package de.fellows.app.security.entities.user

import java.util.UUID

import com.lightbend.lagom.scaladsl.persistence.{ AggregateEvent, AggregateEventTag }
import de.fellows.app.security.SecurityApi.GroupMembership
import de.fellows.utils.security.Permission
import play.api.libs.json.{ Format, Json }

sealed trait SecurityUserEvent extends AggregateEvent[SecurityUserEvent] {
  override def aggregateTag = SecurityUserEvent.Tag

}

object SecurityUserEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[SecurityUserEvent](NumShards)
}

case class PermissionsUpdated(id: UUID, perms: Seq[Permission]) extends SecurityUserEvent

case class UserGroupAdded(id: UUID, group: GroupMembership) extends SecurityUserEvent

case class UserGroupRemoved(id: UUID, group: GroupMembership) extends SecurityUserEvent

object PermissionsUpdated {
  implicit val format: Format[PermissionsUpdated] = Json.format
}

object UserGroupAdded {
  implicit val format: Format[UserGroupAdded] = Json.format
}

object UserGroupRemoved {
  implicit val format: Format[UserGroupRemoved] = Json.format
}
