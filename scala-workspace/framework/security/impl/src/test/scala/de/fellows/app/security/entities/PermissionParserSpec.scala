package de.fellows.app.security.entities

import de.fellows.utils.security.Permission
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.util.UUID

class PermissionParserSpec extends AnyWordSpec with BeforeAndAfterAll with Matchers {
  "Permission parser" should {
    "parse correctly" in {
      val id   = UUID.randomUUID().toString
      val perm = Permission.create(s"profile:team:sam,flo:$id:address:read")

      val expected = Permission(
        resourceClass = Seq("profile"),
        team = Seq("team"),
        owner = Seq("sam", "flo"),
        resource = Seq(id),
        attribute = Seq("address"),
        action = Seq("read")
      )

      perm should equal(expected)
    }

    "parse missing parts as wildcards" in {
      val perm = Permission.create(s"profile:team:sam,flo")

      val expected = Permission(
        resourceClass = Seq("profile"),
        team = Seq("team"),
        owner = Seq("sam", "flo"),
        resource = Seq(),
        attribute = Seq(),
        action = Seq()
      )

      perm should equal(expected)
    }
  }
}
