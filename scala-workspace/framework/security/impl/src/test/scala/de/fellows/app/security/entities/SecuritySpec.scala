package de.fellows.app.security.entities

import de.fellows.app.security.AccessControl
import de.fellows.app.security.SecurityApi.{PermissionRequest, PermissionResponse}
import de.fellows.utils.security.Permission
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.{BeforeAndAfterAll, Inside}

import java.util.UUID

class SecuritySpec extends AnyWordSpec with Matchers with BeforeAndAfterAll with Inside {

  //  private val server = ServiceTest.startServer(ServiceTest.defaultSetup.withCassandra(true)) { ctx =>
  //    new LagomApplication(ctx) with SecurityServiceComponents with AhcWSComponents with LagomKafkaComponents {
  //      override def serviceLocator = NoServiceLocator
  //
  //      override lazy val readSide: ReadSideTestDriver = new ReadSideTestDriver
  //    }
  //  }
  //
  //  val securityService = server.serviceClient.implement[SecurityService]
  //
  //  override def afterAll = server.stop()

  val fakeResource  = UUID.randomUUID().toString
  val fakeResource2 = UUID.randomUUID().toString

  "Permission Service" should {
    "allow equal permission" in {

      val perm = Permission(
        Seq("profile"),
        team = Seq("team"),
        owner = Seq("owner"),
        resource = Seq(fakeResource),
        attribute = Seq("attr"),
        action = Seq("read")
      )

      val req = PermissionRequest(Seq(Seq(perm)))
      val res = AccessControl.checkPermissionsRequest(req, Seq(perm))

      res should equal(PermissionResponse(true))
    }

    "disallow unequal permission" in {

      val perms = Seq(
        Permission(
          Seq("profile2"),
          team = Seq("team"),
          owner = Seq("owner"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("read")
        ),
        Permission(
          Seq("profile"),
          team = Seq("team"),
          owner = Seq("owner2"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("read")
        ),
        Permission(
          Seq("profile"),
          team = Seq("team"),
          owner = Seq("owner"),
          resource = Seq(fakeResource2),
          attribute = Seq("attr"),
          action = Seq("read")
        ),
        Permission(
          Seq("profile"),
          team = Seq("team"),
          owner = Seq("owner"),
          resource = Seq(fakeResource),
          attribute = Seq("attr2"),
          action = Seq("read")
        ),
        Permission(
          Seq("profile"),
          team = Seq("team"),
          owner = Seq("owner"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("read2")
        )
      )

      perms.map { p =>
        val req = PermissionRequest(Seq(Seq(p)))
        AccessControl.checkPermissionsRequest(
          req,
          Seq(Permission(
            Seq("profile"),
            team = Seq("team"),
            owner = Seq("owner"),
            resource = Seq(fakeResource),
            attribute = Seq("attr"),
            action = Seq("read")
          ))
        )
      } should equal(
        Seq(
          PermissionResponse(false),
          PermissionResponse(false),
          PermissionResponse(false),
          PermissionResponse(false),
          PermissionResponse(false)
        )
      )
    }

    "AND  allows if all is granted" in {
      val perm = Permission(
        Seq("profile"),
        owner = Seq("owner"),
        team = Seq("team"),
        resource = Seq(fakeResource),
        attribute = Seq("attr"),
        action = Seq("write", "read")
      )

      val req = PermissionRequest(Seq(Seq(
        Permission(
          Seq("profile"),
          owner = Seq("owner"),
          team = Seq("team"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("read")
        ),
        Permission(
          Seq("profile"),
          owner = Seq("owner"),
          team = Seq("team"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("write")
        )
      )))

      AccessControl.checkPermissionsRequest(req, Seq(perm)) should equal(PermissionResponse(true))
    }

    "AND denies if some are revoked" in {
      val perm = Permission(
        Seq("profile"),
        owner = Seq("owner"),
        team = Seq("team"),
        resource = Seq(fakeResource),
        attribute = Seq("attr"),
        action = Seq("write", "read")
      )

      val req = PermissionRequest(Seq(Seq(
        Permission(
          Seq("profile"),
          owner = Seq("owner"),
          team = Seq("team"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("read")
        ),
        Permission(
          Seq("profile"),
          owner = Seq("owner2"),
          team = Seq("team2"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("write")
        )
      )))

      AccessControl.checkPermissionsRequest(req, Seq(perm)) should equal(PermissionResponse(false))
    }

    "OR allows if one is granted" in {
      val perm = Permission(
        Seq("profile"),
        owner = Seq("owner"),
        team = Seq("team"),
        resource = Seq(fakeResource),
        attribute = Seq("attr"),
        action = Seq("write", "read")
      )

      val req = PermissionRequest(Seq(
        Seq(
          Permission(
            Seq("profile"),
            owner = Seq("owner"),
            team = Seq("team"),
            resource = Seq(fakeResource),
            attribute = Seq("attr"),
            action = Seq("read")
          )
        ),
        Seq(Permission(
          Seq("profile"),
          owner = Seq("owner2"),
          team = Seq("team2"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("write")
        ))
      ))

      AccessControl.checkPermissionsRequest(req, Seq(perm)) should equal(PermissionResponse(true))
    }

    "OR denies if none are granted" in {
      val perm = Permission(
        Seq("profile"),
        owner = Seq("owner"),
        team = Seq("team"),
        resource = Seq(fakeResource),
        attribute = Seq("attr"),
        action = Seq("write", "read")
      )

      val req = PermissionRequest(Seq(
        Seq(
          Permission(
            Seq("profile"),
            owner = Seq("owner3"),
            team = Seq("team3"),
            resource = Seq(fakeResource),
            attribute = Seq("attr"),
            action = Seq("read")
          )
        ),
        Seq(Permission(
          Seq("profile"),
          owner = Seq("owner2"),
          team = Seq("team2"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("write")
        ))
      ))

      AccessControl.checkPermissionsRequest(req, Seq(perm)) should equal(PermissionResponse(false))
    }

    "WILDCARD Grant should allow all" in {
      val perm = Permission(
        Seq("profile"),
        owner = Seq("owner"),
        team = Seq("team"),
        resource = Seq(fakeResource),
        attribute = Seq("attr"),
        action = Seq()
      )

      val req = PermissionRequest(Seq(Seq(
        Permission(
          Seq("profile"),
          owner = Seq("owner"),
          team = Seq("team"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("write")
        ),
        Permission(
          Seq("profile"),
          owner = Seq("owner"),
          team = Seq("team"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("read")
        ),
        Permission(
          Seq("profile"),
          owner = Seq("owner"),
          team = Seq("team"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq()
        ),
        Permission(
          Seq("profile"),
          owner = Seq("owner"),
          team = Seq("team"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("*")
        )
      )))

      AccessControl.checkPermissionsRequest(req, Seq(perm)) should equal(PermissionResponse(true))

    }

    "WILDCARD Check should deny all" in {
      val perm = Permission(
        Seq("profile"),
        owner = Seq("owner"),
        team = Seq("team"),
        resource = Seq(fakeResource),
        attribute = Seq("attr"),
        action = Seq("read")
      )

      val req = PermissionRequest(Seq(Seq(
        Permission(
          Seq("profile"),
          owner = Seq("owner"),
          team = Seq("team"),
          resource = Seq(fakeResource),
          attribute = Seq("attr"),
          action = Seq("*")
        )
      )))

      AccessControl.checkPermissionsRequest(req, Seq(perm)) should equal(PermissionResponse(false))
    }

    "ALLOW Parsed" in {
      val profileId = UUID.randomUUID()
      val grant     = Permission.create(s"profile:sam:$profileId")
      val check     = PermissionRequest(Seq(Seq(Permission.create(s"profile:sam:$profileId:address:read"))))

      AccessControl.checkPermissionsRequest(check, Seq(grant)) should equal(PermissionResponse(true))

    }

    "DENY Parsed" in {
      val profileId = UUID.randomUUID()
      val grant     = Permission.create(s"profile:flo:$profileId")
      val check     = PermissionRequest(Seq(Seq(Permission.create(s"profile:sam:$profileId:address:read"))))

      AccessControl.checkPermissionsRequest(check, Seq(grant)) should equal(PermissionResponse(false))

    }
  }

}
