FROM lumiquote.azurecr.io/credentials:2023-09-12 as creds

FROM lumiquote.azurecr.io/cached/openjdk:11
ARG SBT_VERSION=1.7.1

# Install sbt
RUN \
  mkdir /working/ && \
  cd /working/ && \
  curl -L -o sbt-$SBT_VERSION.deb https://repo.scala-sbt.org/scalasbt/debian/sbt-$SBT_VERSION.deb && \
  dpkg -i sbt-$SBT_VERSION.deb && \
  rm sbt-$SBT_VERSION.deb && \
  apt-get update && \
  apt-get install -y sbt webp --no-install-recommends && \
  cd && \
  rm -r /working/ && \
  sbt sbtVersion
WORKDIR /app
COPY ./docker/_.sbtopts_ ./.sbtopts
# create and specify USER to patch container escape vulnerability
ENV USER=non-root-sbt
ENV USER_ID=1000
RUN useradd -m -u ${USER_ID} ${USER}
RUN chown -R ${USER}:${USER} /app
USER ${USER_ID}
ENV XDG_RUNTIME_DIR=/home/<USER>
RUN sbt version || true

# Pass in the docker credentials so that sbt has access in the building stage, not in the task
RUN mkdir -p /home/<USER>/.docker
COPY --from=creds /lumiquote/config.json /home/<USER>/.docker/config.json
