package de.fellows.utils.redislog

import akka.persistence.query.{NoOffset, Offset, Sequence, TimeBasedUUID}
import akka.stream.scaladsl.Flow
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.persistence.ReadSideProcessor.ReadSideHandler
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag, EventStreamElement}
import de.fellows.utils.redislog.jobs.UUID1Converter
import play.api.Logging
import redis.clients.jedis.{CommandObject, UnifiedJedis}

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import com.typesafe.config.Config

trait OffsetStore[Event <: AggregateEvent[Event]] {
  def prepare(tag: AggregateEventTag[Event]): Future[Offset]

  def setOffset(o: TimeBasedUUID, tag: AggregateEventTag[Event]): Future[Done]
}

class RedisOffsetStore[Event <: AggregateEvent[Event]](id: String, jedis: UnifiedJedis) extends OffsetStore[Event] {

  def offsetstore(tag: AggregateEventTag[Event]) =
    s"offsetstore-${id}-${tag.tag}"
  override def prepare(tag: AggregateEventTag[Event]): Future[Offset] =
    Option(jedis.get(offsetstore(tag))).map(UUID.fromString) match {
      case Some(value) => Future.successful(TimeBasedUUID(value))
      case None =>
        Future.successful(Offset.noOffset)
    }

  override def setOffset(o: TimeBasedUUID, tag: AggregateEventTag[Event]): Future[Done] = {
    jedis.set(offsetstore(tag), o.value.toString)
    Future.successful(Done)
  }
}

class RedisLogReadSideHandler[Event <: AggregateEvent[Event], Handler](
    jedis: UnifiedJedis,
    handlers: Map[Class[_ <: Event], RedisLogEventHandler],
    id: String,
    offsetStore: OffsetStore[Event],
    // TODO: remove this when everything is on cassandra
    fallbackOffsetStore: OffsetStore[Event],
    cutOff: TimeBasedUUID
)(implicit val ctx: ExecutionContext) extends ReadSideHandler[Event] with Logging {

  var tag: AggregateEventTag[Event] = _

  override def handle(): Flow[EventStreamElement[Event], Done, NotUsed] =
    Flow[EventStreamElement[Event]]
      .mapAsync(parallelism = 1) { elem =>
        val eventClass = elem.event.getClass
        val eventEpoch = UUID1Converter.getEpoch(elem.offset.asInstanceOf[TimeBasedUUID].value)

        // Drop all events before this time. THIS WILL BE REMOVED AFTER PROMOTION
        if (eventEpoch < UUID1Converter.getEpoch(cutOff.value)) {
          logger.info(
            s"[REDIS] skipping event ${eventClass} with timestamp ${eventEpoch}"
          )
          offsetStore.setOffset(elem.offset.asInstanceOf[TimeBasedUUID], tag)
        } else {
          handlers.get(eventClass) match {
            case Some(handler) =>
              handler.handle(elem)
                .flatMap { cmds =>
                  try {

                    val responses = cmds.map { cmd =>
                      jedis.executeCommand(cmd.getArguments)
                    }

                    offsetStore.setOffset(elem.offset.asInstanceOf[TimeBasedUUID], tag)
                  } catch {
                    case e: Exception =>
                      logger.error(s"Error while executing redis actions: ${e.getMessage}")
                      throw e;
                  }
                }
            case None =>
              offsetStore.setOffset(elem.offset.asInstanceOf[TimeBasedUUID], tag)
          }
        }
      }

  override def globalPrepare(): Future[Done] =
    Future.successful(Done)

  override def prepare(tag: AggregateEventTag[Event]): Future[Offset] = {
    this.tag = tag
    offsetStore.prepare(tag).flatMap {
      case NoOffset => fallbackOffsetStore.prepare(tag).map {
          case NoOffset         => cutOff
          case x: TimeBasedUUID => x
        }
      case x: TimeBasedUUID => Future.successful(x)
    }
  }

}

object RedisLogReadSideHandler {
  type Handler[Event] = (EventStreamElement[Event]) => Future[Seq[CommandObject[_]]]

  def emptyHandler[E]: RedisLogEventHandler = (elem: EventStreamElement[_]) => Future.successful(Seq.empty)
}

trait RedisLogEventHandler {
  def handle(elem: EventStreamElement[_]): Future[Seq[CommandObject[_]]]
}

case class CutOff(time: Long, uuid: UUID)
