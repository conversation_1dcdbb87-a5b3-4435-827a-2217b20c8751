package de.fellows.utils.redislog.jobs

import enumeratum._

sealed abstract class JobType(override val entryName: String) extends EnumEntry

object JobType extends Enum[JobType] with PlayJsonEnum[JobType] {
  val values: IndexedSeq[JobType] = findValues

  case object <PERSON>Type<PERSON><PERSON>               extends JobType("render")
  case object JobTypeRenderPostProcessing extends JobType("render-postprocessing")
  case object JobTypeRenderSpecification  extends JobType("render-specification")
  case object JobTypeReconciliation       extends JobType("reconciliation")
  case object JobTypeOutline              extends JobType("outline")
  case object JobTypeBoardAnalysis        extends JobType("board-analysis")
  case object JobTypeProductionAnalysis   extends JobType("production-analysis")
  case object JobTypeConvertNative        extends JobType("convert-native")
}

trait JobEntry {
  val jobType: JobType
}
