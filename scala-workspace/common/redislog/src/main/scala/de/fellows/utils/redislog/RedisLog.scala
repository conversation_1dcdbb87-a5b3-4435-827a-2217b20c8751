package de.fellows.utils.redislog

import com.lightbend.lagom.scaladsl.persistence.ReadSideProcessor.ReadSideHandler
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, EventStreamElement}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.RedisLogReadSideHandler.Handler
import redis.clients.jedis.UnifiedJedis

import scala.concurrent.ExecutionContext
import scala.reflect.ClassTag
import java.util.UUID
import akka.persistence.query.TimeBasedUUID

class RedisLog(jedis: UnifiedJedis) extends StackrateLogging {

  def builder[Event <: AggregateEvent[Event]](
      eventProcessorId: String,
      cutOff: TimeBasedUUID
  )(implicit
      ctx: ExecutionContext
  ): ReadSideHandlerBuilder[Event] =
    new ReadSideHandlerBuilder[Event] {
      logger.info("[REDIS] Building RedisLog ReadSideHandlerBuilder")
      val handlers = Map.newBuilder[Class[_ <: Event], RedisLogEventHandler]

      var offsetStore: OffsetStore[Event] = new RedisOffsetStore[Event](eventProcessorId, jedis)

      override def setEventHandler[E <: Event: ClassTag](handler: Handler[E]): ReadSideHandlerBuilder[Event] = {
        val eventClass = implicitly[ClassTag[E]].runtimeClass.asInstanceOf[Class[Event]]
        logger.info(s"[REDIS] Set handler for event ${eventClass.getSimpleName}")

        this.handlers += eventClass -> ((elem: EventStreamElement[_]) =>
          handler(elem.asInstanceOf[EventStreamElement[E]])
        )

        this
      }

      override def build(): ReadSideHandler[Event] =
        new RedisLogReadSideHandler[Event, Handler[Event]](
          jedis = jedis,
          handlers = handlers.result(),
          id = eventProcessorId,
          offsetStore = this.offsetStore,
          fallbackOffsetStore = new RedisOffsetStore[Event](eventProcessorId, jedis),
          cutOff = cutOff
        )

      override def setOffsetStore(offsetStore: OffsetStore[Event]): ReadSideHandlerBuilder[Event] = {
        this.offsetStore = offsetStore
        this
      }
    }
}

trait ReadSideHandlerBuilder[Event <: AggregateEvent[Event]] {

  /** Define the event handler that will be used for events of a given class.
    *
    * This variant allows for offsets to be consumed as well as their events.
    *
    * @tparam E The event type to handle.
    * @param handler The function to handle the events.
    * @return This builder for fluent invocation
    */
  def setEventHandler[E <: Event: ClassTag](handler: Handler[E]): ReadSideHandlerBuilder[Event]

  /** Build the read side handler.
    *
    * @return The read side handler.
    */
  def build(): ReadSideHandler[Event]

  def setOffsetStore(offsetStore: OffsetStore[Event]): ReadSideHandlerBuilder[Event]
}
