package de.fellows.utils.docs;

import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.tika.detect.DefaultDetector;
import org.apache.tika.detect.Detector;
import org.apache.tika.extractor.EmbeddedDocumentExtractor;
import org.apache.tika.io.IOUtils;
import org.apache.tika.io.TikaInputStream;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.mime.MediaType;
import org.xml.sax.ContentHandler;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class FileEmbeddedDocumentEtractor implements EmbeddedDocumentExtractor {
    private int count = 0;

    public boolean shouldParseEmbedded(Metadata m) {
        return true;
    }

    public void parseEmbedded(InputStream inputStream, Content<PERSON><PERSON>ler contentHandler, Metadata metadata, boolean outputHtml) throws IOException {
        System.out.println("parse embedded " + metadata);
        Detector detector = new DefaultDetector();
        String name = metadata.get("resourceName");
        MediaType contentType = detector.detect(inputStream, metadata);
        if (!contentType.getType().equals("image")) return;
        new File("/tmp/wtf/").mkdirs();
        File outputFile = new File("/tmp/wtf/", name);
        try {
            try (FileOutputStream os = new FileOutputStream(outputFile)) {
                TikaInputStream tin = (TikaInputStream) inputStream;
                if (tin != null) {
                    if (tin.getOpenContainer() != null && tin.getOpenContainer() instanceof DirectoryEntry) {
                        POIFSFileSystem fs = new POIFSFileSystem();

                        fs.writeFilesystem(os);
                    } else {
                        IOUtils.copy(inputStream, os);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
