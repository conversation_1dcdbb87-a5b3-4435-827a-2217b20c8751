<html>
<head>
    <link rel="stylesheet"
          href="templates/default/test/bootstrap.min.css"/>
    <link rel="stylesheet"
          href="templates/default/test/quotation.css"/>


    <style>
        @page {
            size: A4;
        }

        .print-format {
            background-color: white;
            box-shadow: 0px 0px 9px rgba(0, 0, 0, 0.5);
            max-width: 8.3in;
            min-height: 11.69in;
            padding: 0.75in;
            margin: auto;
        }

        .page-break {
            padding: 30px 0px;
            border-bottom: 1px dashed #888;
        }
    </style>
</head>
<body>


</body>
<div class="print-format">
    <div class="page-break" style="display: flex; flex-direction: column;">
        <div id="header-html" class="hidden-pdf">

            <div class="letter-head">
                <!--            <img src="/files/header21.png" style="width: 100%;"/>-->
            </div>


            <div class="print-heading">
                <h2>Angebot</h2>

                <div class="doc-details" style="text-align:right">
                    <table align="right" class="header-table">
                        <tr>
                            <td class="detail">Angebotsnummer:</td>
                            <td class="detail2">ANG-2019002</td>
                        </tr>
                        <tr>
                            <td class="detail">Datum:</td>
                            <td class="detail2">09.10.2019</td>
                        </tr>
                        <tr>
                            <td class="detail">Gültig bis:</td>
                            <td class="detail2">09.11.2019</td>
                        </tr>
                        <tr>
                            <td class="detail">Ansprechpartner:</td>
                            <td class="detail2">Florian Herborn</td>
                        </tr>

                    </table>

                </div>

            </div>
            <div class="text-center" document-status="draft">
                <h4 style="margin: 0px;">DRAFT</h4>
            </div>
        </div>

        <div class="row section-break">
            <div class="col-xs-12 column-break">

                <div>
                    <div class="address-block">

                        <div class="sender-address">
                            <span class="sender-address-text">Electronic Fellows GmbH | Äppelallee 27 | 65203 Wiesbaden</span>
                        </div>

                        <div><b>{{quotation.shipping.alias}}</b></div>
                        <div>{{quotation.shipping.name}}</div>
                        <div>{{quotation.shipping.street}} {{quotation.shipping.no}}<br/>
                            {{quotation.shipping.postal}} {{quotation.shipping.city}}<br/>
                            {{quotation.shipping.country}}<br/><br/>
                        </div>
                    </div>
                </div>

            </div>

        </div>


        <div class="row section-break">
            <div class="col-xs-12 column-break">

                <div data-fieldname="items" data-fieldtype="Table">
                    <table class="table table-bordered table-condensed">
                        <thead>
                        <tr>
                            <th style="width: 40px" class="table-sr">Nr</th>


                            <th style="width: 200px;" class="" data-fieldname="items" data-fieldtype="Table">
                                Artikelname
                            </th>


                            <th style="width: 300px;" class="" data-fieldname="items" data-fieldtype="Table">
                                Beschreibung
                            </th>


                            <th style="width: 100px;" class="text-right" data-fieldname="items" data-fieldtype="Table">
                                Menge
                            </th>


                            <th style="width: 100px;" class="text-right" data-fieldname="items" data-fieldtype="Table">
                                Preis
                            </th>


                            <th style="width: 100px;" class="text-right" data-fieldname="items" data-fieldtype="Table">
                                Betrag
                            </th>


                        </tr>
                        </thead>
                        <tbody>

                        {% for quotItem in items %}
                        <tr>

                            <td class="" data-fieldname="items" data-fieldtype="Table">
                                <div class="value">
                                    {{quotItem.assembly.name}}
                                </div>
                            </td>

                            <td class="" data-fieldname="items" data-fieldtype="Table">
                                <div class="value">
                                    {{quotItem.version.created}}
                                </div>
                            </td>

                            <td class="text-right" data-fieldname="items"
                                data-fieldtype="Table">
                                <div class="value">

                                    <small class="pull-left">days</small>
                                    {{quotItem.item.info.delivery}}

                                </div>
                            </td>

                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">
                                <div class="value">
                                    € {{quotItem.item.info.unitPrice}}
                                </div>
                            </td>


                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">
                                <div class="value">
                                    € {{quotItem.item.info.unitPrice}}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                        <!--                        <tr>-->
                        <!--                            <td class="table-sr">1</td>-->


                        <!--                            <td class="" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    Erstellung des Lastenhefts-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    Software Entwicklung-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->

                        <!--                                    <small class="pull-left">days</small>-->
                        <!--                                    9-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    € 900,00-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    € 8.100,00-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                        </tr>-->

                        <!--                        <tr>-->
                        <!--                            <td class="table-sr">2</td>-->


                        <!--                            <td class="" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    Kernmodule-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    <div>Entwicklung der benötigten Kernmodule für die geplante PCB-Quotation Plattform-->
                        <!--                                    </div>-->
                        <!--                                    <ol>-->
                        <!--                                        <li data-list="ordered">Platform Framework Module</li>-->
                        <!--                                        <li data-list="ordered">Assembly / Project Module</li>-->
                        <!--                                        <li data-list="ordered">Calculation Engine</li>-->
                        <!--                                        <li data-list="ordered">PCB Analyzer</li>-->
                        <!--                                        <li data-list="ordered">Team Module</li>-->
                        <!--                                    </ol>-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->

                        <!--                                    <small class="pull-left">days</small>-->
                        <!--                                    294-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    € 900,00-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    € 264.600,00-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                        </tr>-->

                        <!--                        <tr>-->
                        <!--                            <td class="table-sr">3</td>-->


                        <!--                            <td class="" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    Funktionalitäten mit Priorität Hoch-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    <div>Diese werden für die Produktivstellung der ersten funktionierenden Version der-->
                        <!--                                        geplanten Plattform benötigt und umfassen rund 65% des skizzierten-->
                        <!--                                        Funktionsumfangs-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->

                        <!--                                    <small class="pull-left">days</small>-->
                        <!--                                    360-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    € 900,00-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    € 324.000,00-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                        </tr>-->

                        <!--                        <tr>-->
                        <!--                            <td class="table-sr">4</td>-->


                        <!--                            <td class="" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    Software Entwicklung-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    Software Entwicklung-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->

                        <!--                                    <small class="pull-left">days</small>-->
                        <!--                                    1-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    € 125,00-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                            <td class="text-right" data-fieldname="items" data-fieldtype="Table">-->
                        <!--                                <div class="value">-->
                        <!--                                    € 125,00-->
                        <!--                                </div>-->
                        <!--                            </td>-->


                        <!--                        </tr>-->

                        </tbody>
                    </table>
                </div>

            </div>

        </div>

        <div class="row section-break">
            <div class="col-xs-6 column-break">

            </div>

            <div class="col-xs-6 column-break">

                <div class="row">

                    <div class="col-xs-5">
                        <label>Summe</label></div>
                    <div class="col-xs-7 text-right">
                        € 596.825,00
                    </div>

                </div>

            </div>

        </div>

        <div class="row section-break">
            <div class="col-xs-12 column-break">

                <div class="row">
                    <div class="col-xs-6"></div>
                    <div class="col-xs-6">
                        <div class="row">
                            <div class="col-xs-5">
                                <label>Zusätzlicher Rabatt</label></div>
                            <div class="col-xs-7 text-right">
                                - € 264.375,00
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-5">
                                <label>19% MwSt.</label></div>
                            <div class="col-xs-7 text-right">
                                € 63.165,50
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>

        <div class="row section-break">
            <div class="col-xs-6 column-break">

            </div>

            <div class="col-xs-6 column-break">

                <div class="row  data-field" data-fieldname="grand_total" data-fieldtype="Currency">
                    <div class="col-xs-5">


                        <label>Gesamtbetrag</label>


                    </div>
                    <div class="col-xs-7
			text-right value">

                        € 395.615,50

                    </div>
                </div>

            </div>

        </div>

        <div class="row section-break">
            <div class="col-xs-6 column-break">

            </div>

            <div class="col-xs-6 column-break">

            </div>

        </div>


        <div id="footer-html" class="visible-pdf" style="">

            <div class="letter-head-footer">
                <div class="row" style="font-size: 7.5pt; letter-spacing: 0.2mm !important;">
                    <div class="col-xs-4">
                        <div>Electronic Fellows GmbH</div>
                        <div>Äppelallee 27 | 65203 Wiesbaden</div>
                        <br/>
                        <div>T : +49 (0) 611 949 170 40</div>
                        <div>E: <EMAIL></div>
                    </div>
                    <div class="col-xs-4" style="text-align: center;">
                        <div>Amtsgericht Wiesbaden</div>
                        <div>Registernummer: HRB 31132</div>
                        <br/>
                        <div>Geschäftsführer: Florian Herborn,</div>
                        <div>Samuel Leisering, Nils Minor</div>
                    </div>
                    <div class="col-xs-4" style="text-align: right;">
                        <div>vrBank Untertaunus</div>
                        <div>BIC: VRBUDE51</div>
                        <br/>
                        <div>DE06 5109 1700 0011 7993 02</div>
                        <div>Ust.-ID-Nr: DE325337856</div>
                    </div>
                </div>
                <br/><br/>
            </div>

            <p class="text-center small page-number visible-pdf">
                Seite <span class="page"></span> von <span class="topage"></span>
            </p>
        </div>


    </div>
</div>
</html>
