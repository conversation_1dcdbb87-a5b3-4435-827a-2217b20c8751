package de.fellows.utils.templating

import play.api.Logging

import java.nio.file.{Files, Path, StandardCopyOption}
import scala.jdk.CollectionConverters._
import scala.util.{Failure, Success, Try}

object HTMLtoPDF extends Logging {
  def convert(
      html: Path,
      workingDir: Path,
      resources: Seq[Path] = Seq(),
      deleteWorkingDir: Boolean = true,
      header: Option[Path],
      footer: Option[Path]
  ): Array[Byte] = {

    val dir    = workingDir
    val result = dir.resolve("__result__.pdf")

    resources.foreach { f =>
      Try(Files.copy(f, dir.resolve(f.toFile.getName), StandardCopyOption.REPLACE_EXISTING)) match {
        case Failure(exception) => logger.error(s"Failed to copy resource $f", exception)
        case Success(value)     =>
      }
    }

    try {
      // xvfb-run -a -s "-screen 0 640x480x16" wkhtmltopdf "$@"
      //      val pb = new ProcessBuilder("wkhtmltopdf", "--allow", dir.toAbsolutePath.toString, tmp.toAbsolutePath.toString, result.toAbsolutePath.toString)

      // run wkhtml in a headless, virtual framebuffer

      val headerParam = header.map(f => Seq("--header-html", f.toAbsolutePath.toString))
      val footerParam = footer.map(f => Seq("--footer-html", f.toAbsolutePath.toString))

      val args = Seq(
        "xvfb-run",
        "-a",
        "-s",
        "-screen 0 1920x1080x32"
      ) ++ Seq(
        "wkhtmltopdf",
        "--disable-javascript",
        "--page-size",
        "A4",
        "--viewport-size",
        "1920x1080"
//        "--print-media-type"
      ) ++
        headerParam.getOrElse(Seq()) ++
        footerParam.getOrElse(Seq()) ++
        resources.flatMap(p => Seq("--allow", p.toAbsolutePath.toString)) ++
        Seq(
          "--allow",
          dir.toAbsolutePath.toString,
          html.toAbsolutePath.toString,
          result.toAbsolutePath.toString
        )

      val pb = new ProcessBuilder(args: _*)
      println(s"start ${pb.command().asScala.mkString(" ")}")
      pb.redirectErrorStream(true)
      val process = pb.start()
      val is      = process.getInputStream
      val s       = scala.io.Source.fromInputStream(is).mkString

      println(s)

      is.close()
      process.waitFor()

      val res = Files.readAllBytes(result)
      res
    } finally
      if (deleteWorkingDir) {
        try {
          val dirFile          = dir.toFile
          val dirChildrenFiles = dirFile.listFiles()
          if (dirChildrenFiles != null) {
            dirChildrenFiles.foreach { f =>
              f.delete()
            }
          }

          dirFile.delete()

        } catch {
          case e: Throwable => logger.error("Failed to delete temp dir", e)
        }
      }
  }

  def convert(
      html: String,
      base: Path,
      deleteWorkingDir: Boolean,
      header: Option[Path],
      footer: Option[Path]
  ): Array[Byte] = {
    val f = Files.createTempFile("convert", ".html")
    try {
      Files.write(f, Seq(html).asJava)
      HTMLtoPDF.convert(f, base, Seq(), deleteWorkingDir = deleteWorkingDir, header, footer)
    } finally
      f.toFile.delete()
  }
}
