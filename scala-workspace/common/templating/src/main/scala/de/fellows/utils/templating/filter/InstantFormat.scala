package de.fellows.utils.templating.filter

import com.hubspot.jinjava.interpret.JinjavaInterpreter
import com.hubspot.jinjava.lib.filter.Filter

import java.time.format.DateTimeFormatter
import java.time.{Instant, LocalDateTime, ZoneId}

class InstantFormat extends Filter {
  override def filter(o: Any, interpreter: JinjavaInterpreter, args: String*): AnyRef = {
    val i = (if (o.isInstanceOf[String]) {
      Some(Instant.parse(o.asInstanceOf[String]))
    } else if (o.isInstanceOf[Instant]) {
      Some(o.asInstanceOf[Instant])
    } else {
      None
    })


    i.map(ta => {
      var zone = ZoneId.systemDefault()
      if (args.length > 1) {
        zone = ZoneId.of(args(1))
      }
      val ldt = LocalDateTime.ofInstant(ta, zone)
      val df = args.headOption.map(DateTimeFormatter.ofPattern).getOrElse(DateTimeFormatter.ISO_INSTANT)
      df.format(ldt)
    }).getOrElse("")
  }

  override def getName: String = "instant"
}
