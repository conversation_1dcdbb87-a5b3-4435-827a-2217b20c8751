fellows {
  storage.base = /opt/stackrate

  maintanence = false
  topic-initialization = false
}

play.http.errorHandler = "de.fellows.utils.communication.StackrateErrorHandler"
play.http.parser.maxMemoryBuffer = 1024000
//play.filters.cors.pathPrefixes = "/api/"

play {
  temporaryFile {
    dir = ${fellows.storage.base}/tmp/
  }
}

# configuration used by the Lagom Kafka consumer
lagom.broker.kafka.consumer {
  batching-interval = 500 ms
}

akka.http.server.parsing {
    max-header-value-length = 16k
}

akka.http {
  parsing {
    max-uri-length = 32k
    max-header-value-length = 16k
  }
}

//akka.kafka.producer.kafka-clients {
//  max.request.size = 10000000
//}

akka.kafka.consumer.kafka-clients {
  auto.offset.reset = "latest"
}


akka.persistence.cassandra.events-by-tag.first-time-bucket = "20190315T00:00"
cassandra-query-journal.first-time-bucket = "20190315T00:00"
//akka.persistence.cassandra.events-by-tag.pubsub-notification = on
//akka.persistence.cassandra.query.refresh-interval = 30s

akka.cluster.downing-provider-class = "akka.cluster.sbr.SplitBrainResolverProvider"
akka.coordinated-shutdown.exit-jvm = off

akka.actor {
  serializers {
    stackrate-exception-serializer = "de.fellows.utils.common.EntityExceptionSerializer"
  }
  serialization-bindings {
    "de.fellows.utils.communication.ServiceException" = stackrate-exception-serializer
    "de.fellows.utils.common.EntityException" = stackrate-exception-serializer
  }

  serialization-identifiers {
    "de.fellows.utils.common.EntityExceptionSerializer" = 7000001
  }
}


//cassandra-journal.pubsub-minimum-interval = off

# Enable the serializer provided in Akka 2.5.8+ for akka.Done and other internal
# messages to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done" = akka-misc
  "akka.actor.Address" = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

play.http.secret.key = "changeme"
play.http.secret.key = ${?APPLICATION_SECRET}

akka.management {
  health-checks {
    readiness-checks {
      stackrate-cassandra = "de.fellows.utils.health.HealthCheck"
    }
  }
}


lagom.circuit-breaker {
  # Default configuration that is used if a configuration section
  # with the circuit breaker identifier is not defined.
  default {
    # Enable/Disable circuit breaker.
    enabled = off
    # Number of failures before opening the circuit.
    max-failures = 10
    //    max-failures = ${?CIRCUIT_BREAKER_MAX_FAILURES}
    # Duration of time in open state after which to attempt to close
    # the circuit, by first entering the half-open state.
    reset-timeout = 15s
    //    reset-timeout = ${?CIRCUIT_BREAKER_RESET_TIMEOUT}
    # Duration of time after which to consider a call a failure.
    call-timeout = 10s
    //    call-timeout = ${?CIRCUIT_BREAKER_CALL_TIMEOUT}
  }

  camunda {
    # Possibility to disable a given circuit breaker.
    enabled = off

    # Number of failures before opening the circuit.
    max-failures = 10

    # Duration of time after which to consider a call a failure.
    call-timeout = 20s

    # Duration of time in open state after which to attempt to close
    # the circuit, by first entering the half-open state.
    reset-timeout = 25s

    # A whitelist of fqcn of Exceptions that the CircuitBreaker
    # should not consider failures. By default all exceptions are
    # considered failures.
    exception-whitelist = ["com.lightbend.lagom.scaladsl.api.transport.TransportException"]
  }

  render {
    # Possibility to disable a given circuit breaker.
    enabled = off

    # Number of failures before opening the circuit.
    max-failures = 10

    # Duration of time after which to consider a call a failure.
    call-timeout = 30s

    # Duration of time in open state after which to attempt to close
    # the circuit, by first entering the half-open state.
    reset-timeout = 120s

    # A whitelist of fqcn of Exceptions that the CircuitBreaker
    # should not consider failures. By default all exceptions are
    # considered failures.
    exception-whitelist = ["com.lightbend.lagom.scaladsl.api.transport.TransportException"]
  }
}


play {
  server {
    pidfile.path = "/dev/null"
    akka.max-header-value-length = 16k
  }
}

lagom.services {
 luminovo-custom-parts-alerts = "http://backend-core:5000"
}

lagom.projection.auto-start.enabled = true


// cassandra specific config

lagom.persistence.read-side {
  cassandra {
    connection-pool {
      pool-timeout-millis = 5000
    }
    log-queries: true
  }
  global-prepare-timeout = 1 minute
}


cassandra-query-journal.eventual-consistency-delay = 1s
cassandra-query-journal.delayed-event-timeout = 30s
cassandra-query-journal.refresh-interval = 10s

cassandra-journal {
  log-queries = true
}

cassandra-journal.pubsub-notification = on
cassandra-query-journal.refresh-interval = 10s

cassandra-query-journal {
  log-queries = true
  events-by-tag {
    eventual-consistency-delay = 1s
    back-track {
      interval = 5s
    }
  }
}

//datastax-java-driver.advanced.reconnect-on-init = true
//datastax-java-driver.advanced.connection.init-query-timeout = 10 seconds
//datastax-java-driver.advanced.connection.init-query-timeout = 10 seconds
//datastax-java-driver.basic.request.timeout = 10 seconds

kamon.otel {
  # default to support the ENV:s as described at
  # https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/protocol/exporter.md
  endpoint = "http://localhost:4317"
  endpoint = ${?OTEL_EXPORTER_OTLP_ENDPOINT}

  # Supports empty string or gzip
  compression = ""
  compression = ${?OTEL_EXPORTER_OTLP_COMPRESSION}

  # Supports comma-separated pairs (i.e "api-key=supersecret,data-type=application-traces")
  headers = ""
  headers = ${?OTEL_EXPORTER_OTLP_HEADERS}

  timeout = 10s
  timeout = ${?OTEL_EXPORTER_OTLP_TIMEOUT}

  # Supports grpc and http/protobuf
  protocol = "grpc"
  protocol = ${?OTEL_EXPORTER_OTLP_PROTOCOL}

  # Support for OTEL_RESOURCE_ATTRIBUTES env var (cf https://opentelemetry.io/docs/reference/specification/resource/sdk/)
  attributes = ""
  attributes = ${?OTEL_RESOURCE_ATTRIBUTES}

  trace {
    endpoint = ${kamon.otel.endpoint}
    full-endpoint = ${?OTEL_EXPORTER_OTLP_TRACES_ENDPOINT}

    compression = ${kamon.otel.compression}
    compression = ${?OTEL_EXPORTER_OTLP_TRACES_COMPRESSION}

    headers = ${kamon.otel.headers}
    headers = ${?OTEL_EXPORTER_OTLP_TRACES_HEADERS}

    timeout = ${kamon.otel.timeout}
    timeout = ${?OTEL_EXPORTER_OTLP_TRACES_TIMEOUT}

    protocol = ${kamon.otel.protocol}
    protocol = ${?OTEL_EXPORTER_OTLP_TRACES_PROTOCOL}

    # If set to true, any error (message and stacktrace) on a span will be included as an event on the span with
    # standard attribute names; enable for 'more full' compliance with otel standard
    include-error-event = true
  }
}

kamon {
  propagation.http.default.entries {
    incoming.span = "w3c"
    outgoing.span = "w3c"
  }

  trace {
    sampler = "always"
    # Ignore traces to the /ready healthcheck endpoint
    ignored-operations = [
      "/ready",
    ]
    identifier-scheme = double
  }
}

kamon.instrumentation {
  akka.filters.actors {
    track {
      includes = []
    }

    # Filter akka message tracing, many traces are generated are noisy and do not add much
    trace {
      includes = []
    }
  }

  akka.http {
    server.metrics.enabled = false
  }

  play.http {
    client.tracing.operations.name-generator = "de.fellows.utils.metrics.LagomOperationNameGenerator"
  }

  cassandra {
    tracing.enabled = no
  }

  okhttp {
    http-client.tracing.enabled = no
  }

}


kanela.modules.annotation {
  within += "de.fellows.*"
}

kanela.modules {
  kafka-clients {
    name = "Apache Kafka Client Instrumentation"
    description = "Provides distributed context propagation for the Apache Kafka Producer and Consumer"
    instrumentations = [
      "de.fellows.utils.telemetry.ProducerInstrumentation",
      "kamon.instrumentation.kafka.client.ConsumerInstrumentation"
    ]

    within = [
      "org.apache.kafka.clients..*",
    ]
  }
}

kamon.modules {
  otel-trace-reporter {
    enabled = false
    enabled = ${?ENABLE_OTLP_EXPORTER}
    name = "OpenTelemetry Trace Reporter"
    description = "Sends trace data to a OpenTelemetry server via gRPC"
    factory = "kamon.otel.OpenTelemetryTraceReporter$Factory"
  }
}

