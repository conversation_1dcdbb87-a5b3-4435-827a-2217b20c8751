fellows.services.seedip = "s-c-srsv01.we-group.com"
fellows.services.seed = "http://"${fellows.services.seedip}

lagom.services {
  security = [
    "http://s-c-srsv01.we-group.com:"${fellows.services.security.http},
  ]
  user = ["http://s-c-srsv01.we-group.com:"${fellows.services.user.http}]
  assembly = ["http://s-c-srsv01.we-group.com:"${fellows.services.assembly.http}]
  camunda-bridge = ["http://s-c-srsv01.we-group.com:"${fellows.services.camunda-bridge.http}]
  camunda = ${fellows.services.camunda-bridge.endpoint}":8080/rest"
  notification = ["http://s-c-srsv01.we-group.com:"${fellows.services.notification.http}]
  profile = ["http://s-c-srsv01.we-group.com:"${fellows.services.profile.http}]
  renderer = [
    "http://s-c-srsv02.we-group.com:"${fellows.services.renderer.http}
  ]
  pcb = ["http://s-c-srsv01.we-group.com:"${fellows.services.pcb.http}]
  pcb-supplier = ["http://s-c-srsv01.we-group.com:"${fellows.services.pcb-supplier.http}]
  quotation = ["http://s-c-srsv01.we-group.com:"${fellows.services.quotation.http}]
  dfm = ["http://s-c-srsv01.we-group.com:"${fellows.services.dfm.http}]
  customer = ["http://s-c-srsv01.we-group.com:"${fellows.services.customer.http}]
  inbox = ["http://s-c-srsv01.we-group.com:"${fellows.services.inbox.http}]
  layerstack = ["http://s-c-srsv01.we-group.com:"${fellows.services.layerstack.http}]
  erpnext-bridge = ["http://s-c-srsv01.we-group.com:"${fellows.services.erpnext-bridge.http}]
  panel = ["http://s-c-srsv01.we-group.com:"${fellows.services.panel.http}]
  price = ["http://s-c-srsv01.we-group.com:"${fellows.services.price.http}]
  analysis = ["http://s-c-srsv01.we-group.com:"${fellows.services.analysis.http}]

  converter = ["http://s-c-srsv02.we-group.com:"${fellows.services.converter.http}]
}

akka.discovery.config.services {
  security = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.security.remoting}
    }]
  }
  user = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.user.remoting}
    }]
  }
  assembly = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.assembly.remoting}
    }]
  }
  camunda-bridge = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.camunda-bridge.remoting}
    }]
  }
  notification = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.notification.remoting}
    }]
  }
  profile = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.profile.remoting}
    }]
  }
  renderer = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.renderer.remoting}
    }, {
      host = "http://s-c-srsv02.we-group.com"
      port = ${fellows.services.renderer.remoting}
    }]
  }
  pcb = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.pcb.remoting}
    }]
  }
  pcb-supplier = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.pcb-supplier.remoting}
    }]
  }
  quotation = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.quotation.remoting}
    }]
  }
  dfm = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.dfm.remoting}
    }]
  }
  customer = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.customer.remoting}
    }]
  }
  inbox = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.inbox.remoting}
    }]
  }
  layerstack = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.layerstack.remoting}
    }]
  }
  erpnext-bridge = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.erpnext-bridge.remoting}
    }]
  }
  panel = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.panel.remoting}
    }]
  }
  price = {
    endpoints = [{
      host = "http://s-c-srsv01.we-group.com"
      port = ${fellows.services.price.remoting}
    }]
  }
}

fellows.services {
  security {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2552
    management = 8558
    http = 9000
  }
  user {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2553
    management = 8559
    http = 9001
  }
  assembly {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2554
    management = 8560
    http = 9002
  }
  camunda-bridge {
    seed = "s-c-srsv01.we-group.com"
    token = "3aee1012-7e61-4c07-8978-1d526915e33b"
    teamtoken.online = "9d6c0927-8ea6-4234-91ba-d2460eec608f"
    teamtoken.demo = "3aee1012-7e61-4c07-8978-1d526915e33b"
    teamtoken.jmeter = "81d8858b-5421-4393-b3a8-1dbc11ddac0e"
    endpoint = "http://s-c-srbu01.we-group.com"
    remoting = 2555
    management = 8561
    http = 9003
  }
  notification {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2556
    management = 8562
    http = 9004
  }
  profile {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2557
    management = 8563
    http = 9005
  }

  renderer {
    seed = "s-c-srsv02.we-group.com"
    remoting = 2558
    management = 8564
    http = 9006
  }
  pcb {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2559
    management = 8565
    http = 9007
  }
  pcb-supplier {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2560
    management = 8566
    http = 9008
  }
  quotation {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2561
    management = 8567
    http = 9009
  }
  dfm {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2562
    management = 8568
    http = 9010
  }
  customer {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2563
    management = 8569
    http = 9011
  }
  inbox {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2564
    management = 8570
    http = 9012
  }
  layerstack {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2565
    management = 8571
    http = 9013
  }
  erpnext-bridge {
    endpoint = "http://s-c-srbu01.we-group.com"
    secret = "0b2d550cf3335c0"
    token = "bd3baef58ba4094"
    remoting = 2566
    management = 8572
    http = 9014
  }
  panel {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2567
    management = 8573
    http = 9015
  }
  price {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2568
    management = 8574
    http = 9016
    endpoint {
      demo = "http://s-c-srbu01.we-group.com:8080/rest"
    }
  }

  analysis {
    seed = "s-c-srsv01.we-group.com"
    remoting = 2569
    management = 8575
    http = 9017
  }

  converter {
    seed = "s-c-srsv02.we-group.com"
    remoting = 2570
    management = 8576
    http = 9018
  }
}


//akka {
//  remote {
//    netty.tcp {
//      port = 2553
//      bind-port = 2553
//    }
//  }
//  management.http.port = 8559
//}
//
//play.server.http.port = 9003
