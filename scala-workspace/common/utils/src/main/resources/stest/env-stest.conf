jwt {
  secret = "stest-jwt-secret"

  token {
    auth.expirationInSeconds = 86400,
    file.expirationInSeconds = 86400,
    refresh.expirationInSeconds = 86400
  }
}

fellows.baseURL = "http://localhost:4200"
fellows.backend = "http://********/"


fellows.env.registration.open = false


fellows.inbox = [
  {
    name = "stest"
    user = "<EMAIL>"
    folder = "INBOX"
    imap = "imap.electronic-fellows.de"
    pass = "r*-&Fcz]L9D[74pxgTTp>P3?:hnph+xW37i+"
    team = "demo"
  }
]

fellows.security {
  admins = [
    "d719543e-035f-4c0e-88e8-2303e4023435"
    "294a7cb8-7418-407f-aab6-3414e0073af4"
    "d719543e-035f-4c0e-88e8-2303e4023435"
  ]
}

//
fellows {
  metrics {
    elasticsearch.protocol = "http"
    elasticsearch.host = "************"
    elasticsearch.port = "9200"
  }
}



