
include "../common-application.conf"
include "../debian-application.conf"
##                                                                        ##
# This file will be replaced by the CI. Use this file for local dev only.  #
# Put relevant {prod,test}-config in main-application-{prod,test}.conf     #
##                                                                        ##

cassandra-query-journal.eventual-consistency-delay = 5s
cassandra-query-journal.delayed-event-timeout = 30s
cassandra-journal.pubsub-minimum-interval = 5s

# Enable the serializer provided in Akka 2.5.8+ for akka.Done and oth1er internal
# messages to avoid the use of Java serialization.
akka.actor.serialization-bindings {
  "akka.Done" = akka-misc
  "akka.actor.Address" = akka-misc
  "akka.remote.UniqueAddress" = akka-misc
}

play.http.secret.key="changeme"
play.http.secret.key=${?APPLICATION_SECRET}

jwt {
  secret = "wow"

  token {
    auth.expirationInSeconds = 86400,
    file.expirationInSeconds = 86400,
    refresh.expirationInSeconds = 86400
  }
}

mailgun {
  key = "**************************************************"
  endoint = "https://api.eu.mailgun.net/v3/mail.electronic-fellows.de"
}

lagom.persistence.ask-timeout = 20 seconds

lagom.services {
  mailgun = "https://api.eu.mailgun.net/"
}

fellows {
  baseURL = "http://localhost:9000"
  environment = "local"
  metrics {
    elasticsearch.host = "http://elastic.electronic-fellows.de:9200"
  }
}

//akka.persistence {
//  journal {
//    circuit-breaker {
//      max-failures = 10
//      call-timeout = 60s
//      reset-timeout = 4 minutes
//    }
//  }
//  snapshot-store {
//    circuit-breaker {
//      max-failures = 10
//      call-timeout = 60s
//      reset-timeout = 4 minutes
//    }
//  }
//}


lagom.akka.discovery {

  # The timeout for a successful lookup.
  lookup-timeout = 20 seconds
}
