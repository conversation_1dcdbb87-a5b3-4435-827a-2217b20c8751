package de.fellows.utils.graphics

import java.awt.geom.PathIterator

object PathPrinter {
  def toString(i: PathIterator): String = {
    val sb = new StringBuffer()
    print(i, sb)
    sb.toString
  }

  def print(i: PathIterator): Unit = {
    val sb = new StringBuffer()
    print(i, sb)
    println(sb.toString)
  }

  def print(i: PathIterator, s: StringBuffer, invertY: Boolean = false, shift: Option[(Double, Double)] = None): Unit =
    walk(i, (t, seq) => s.append(s"$t ${seq.mkString(" ")} "), invertY, shift)

  def walk(
      i: PathIterator,
      c: (String, Seq[Number]) => Unit,
      invertY: Boolean = false,
      shift: Option[(Double, Double)] = None
  ): Unit = {
    val (sx, sy) = shift.getOrElse((0.0, 0.0))
    val inversion =
      if (invertY) {
        -1
      } else {
        1
      }
    val seg = new Array[Double](6)
    while (!i.isDone) {
      val t = i.currentSegment(seg)
      t match {
        case PathIterator.SEG_MOVETO => c(
            "M",
            Seq(
              seg(0) + sx,
              inversion * seg(1) + sy
            )
          )
        case PathIterator.SEG_CLOSE => c(
            "Z",
            Seq(
            )
          )
        case PathIterator.SEG_CUBICTO => c(
            "C",
            Seq(
              seg(0) + sx,
              inversion * seg(1) + sy,
              seg(2) + sx,
              inversion * seg(3) + sy,
              seg(4) + sx,
              inversion * seg(5) + sy
            )
          )
        case PathIterator.SEG_QUADTO => c(
            "Q",
            Seq(
              seg(0) + sx,
              inversion * seg(1) + sy,
              seg(2) + sx,
              inversion * seg(3) + sy
            )
          )
        case PathIterator.SEG_LINETO => c(
            "L",
            Seq(
              seg(0) + sx,
              inversion * seg(1) + sy
            )
          )
      }

      i.next()
    }
  }
  def walkInt(
      i: PathIterator,
      c: (String, Seq[Number]) => Unit,
      invertY: Boolean = false,
      shift: Option[(Double, Double)] = None
  ): Unit = {
    val (sx, sy) = shift.getOrElse((0.0, 0.0))
    val inversion =
      if (invertY) {
        -1
      } else {
        1
      }
    val seg = new Array[Double](6)
    while (!i.isDone) {
      val t = i.currentSegment(seg)
      t match {
        case PathIterator.SEG_MOVETO => c(
            "M",
            Seq(
              seg(0).intValue + sx,
              inversion * seg(1).intValue + sy
            )
          )
        case PathIterator.SEG_CLOSE => c(
            "Z",
            Seq(
            )
          )
        case PathIterator.SEG_CUBICTO =>
          c(
            "C",
            Seq(
              seg(0).intValue + sx,
              inversion * seg(1).intValue + sy,
              seg(2).intValue + sx,
              inversion * seg(3).intValue + sy,
              seg(4).intValue + sx,
              inversion * seg(5).intValue + sy
            )
          )
        case PathIterator.SEG_QUADTO =>
          c(
            "Q",
            Seq(
              seg(0).intValue + sx,
              inversion * seg(1).intValue + sy,
              seg(2).intValue + sx,
              inversion * seg(3).intValue + sy
            )
          )
        case PathIterator.SEG_LINETO => c(
            "L",
            Seq(
              seg(0).intValue + sx,
              inversion * seg(1).intValue + sy
            )
          )
      }

      i.next()
    }
  }
}
