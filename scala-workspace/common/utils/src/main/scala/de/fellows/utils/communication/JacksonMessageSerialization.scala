package de.fellows.utils.communication

import com.fasterxml.jackson.databind.ObjectMapper
import com.lightbend.lagom.scaladsl.api.deser.MessageSerializer
import com.lightbend.lagom.scaladsl.api.transport.MessageProtocol

class JacksonMessageSerialization[T] extends MessageSerializer[T, String] {
  val mapper = new ObjectMapper()

  //  override def serializerForRequest: NegotiatedSerializer[Array[Byte], ByteString] = (message: Array[Byte]) => ByteString.fromArray(message)
  //
  //  override def deserializer(protocol: MessageProtocol): MessageSerializer.NegotiatedDeserializer[Array[Byte], ByteString] = (wire: ByteString) => wire.toArray
  //
  //  override def serializerForResponse(acceptedMessageProtocols: immutable.Seq[MessageProtocol]): NegotiatedSerializer[Array[Byte], ByteString] = new NegotiatedSerializer[Array[Byte], ByteString] {
  //    override def serialize(message: Array[Byte]): ByteString = ByteString.fromArray(message)
  //  }

  override def serializerForRequest: MessageSerializer.NegotiatedSerializer[T, String] = (message: T) => mapper.writeValueAsString(message)

  override def deserializer(protocol: MessageProtocol): MessageSerializer.NegotiatedDeserializer[T, String] = ???//o => mapper.readValue[T](o, classOf[T])

  override def serializerForResponse(acceptedMessageProtocols: Seq[MessageProtocol]): MessageSerializer.NegotiatedSerializer[T, String] = ???
}
