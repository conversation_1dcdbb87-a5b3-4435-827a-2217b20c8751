package de.fellows.utils

class TimeoutException() extends RuntimeException

object ThreadUtils {

  def interruptCheck(t: Long): Unit =
    if (System.currentTimeMillis() > t) {
      throw new TimeoutException()
    }

  def interruptCheck(t: Thread): Unit =
    if (t.isInterrupted) {
      throw new TimeoutException()
    }

  def interruptCheck(): Unit =
    if (Thread.currentThread().isInterrupted) {
      throw new TimeoutException()
    }
}
