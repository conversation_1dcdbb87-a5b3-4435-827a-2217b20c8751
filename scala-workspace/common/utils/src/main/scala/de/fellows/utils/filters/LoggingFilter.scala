package de.fellows.utils.filters

import akka.stream.Materializer
import play.api.Logging
import play.api.http.Status.INTERNAL_SERVER_ERROR
import play.api.mvc.{Filter, RequestHeader, Result}

import scala.concurrent.Future

// Unused, but will keep in case we want to log requests for debugging
class LoggingFilter(val mat: Materializer) extends Filter with Logging {
  def apply(nextFilter: RequestHeader => Future[Result])(requestHeader: RequestHeader): Future[Result] = {
    val start       = System.currentTimeMillis()
    val fullAddress = s"${requestHeader.host}${requestHeader.uri}"

    logger.info(s"=======> ${requestHeader.method} $fullAddress")

    nextFilter(requestHeader).transform { result =>
      val method     = requestHeader.method
      val dt         = s"${System.currentTimeMillis() - start}ms"
      val statusCode = result.map(_.header.status).getOrElse(INTERNAL_SERVER_ERROR)

      logger.info(s"<======= $method $fullAddress dt=$dt status=$statusCode")

      result
    }(mat.executionContext)
  }
}
