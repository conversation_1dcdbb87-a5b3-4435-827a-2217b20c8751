package de.fellows.utils.communication

import com.lightbend.lagom.scaladsl.api.transport.{ExceptionMessage, TransportErrorCode, TransportException}

object TransportExceptionHelper {
  def apply(code: TransportErrorCode, s: String): TransportException =
    new TransportException(code, s)

  def apply(errorCode: TransportErrorCode, message: String, internalMessage: String): TransportException =
    apply(errorCode, message, new RuntimeException(internalMessage))

  def apply(errorCode: TransportErrorCode, message: String, cause: Throwable): TransportException =
    new TransportException(
      errorCode,
      new ExceptionMessage(classOf[TransportException].getSimpleName, message),
      cause
    )
}
