package de.fellows.utils.service

import com.lightbend.lagom.scaladsl.api.deser.PathParamSerializer

class StackratePathParamSerializer[T](serialize: T => String, deserialize: String => T, name: String)
    extends PathParamSerializer[T] {
  override def serialize(parameter: T): Seq[String] = Seq(serialize(parameter))

  override def deserialize(parameters: Seq[String]): T = parameters.headOption.map(s =>
    try
      deserialize(s)
    catch {
      case _: Throwable =>
        throw WrongTypeClientPathParameterException(s, name)
    }
  ).getOrElse(throw MissingClientPathParameterException(name))
}

object StackratePathParamSerializer {
  def apply[T](serialize: T => String, deserialize: String => T, name: String): StackratePathParamSerializer[T] =
    new StackratePathParamSerializer(serialize, deserialize, name)

}
