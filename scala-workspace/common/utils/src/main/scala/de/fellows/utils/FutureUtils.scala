package de.fellows.utils

import java.util.{Timer, TimerTask}
import java.util.concurrent.atomic.AtomicReference
import scala.concurrent.duration.{Duration, FiniteDuration}
import scala.concurrent.{Await, ExecutionContext, Future, Promise}
import scala.util.{Failure, Success, Try}

object FutureUtils {

  def option[T](f: Option[Future[T]])(implicit c: ExecutionContext): Future[Option[T]] =
    f match {
      case Some(x) => x.map(a => Some(a))
      case None    => Future.successful(None)
    }

  /** Run futures in sequence.
    */
  def sequentialTraverse[A, B](seq: Seq[A])(f: A => Future[B])(implicit ec: ExecutionContext): Future[Seq[B]] =
    seq.foldLeft(Future.successful(Seq.empty[B])) {
      case (acc, next) => acc.flatMap(bs => f(next).map(b => bs :+ b))
    }

  def delay[A](duration: FiniteDuration)(f: => Future[A])(implicit ec: ExecutionContext): Future[A] = {
    val promise = Promise[Unit]()
    val timer   = new Timer()

    timer.schedule(
      new TimerTask {
        override def run(): Unit =
          promise.complete(Try(()))
      },
      duration.toMillis
    )

    promise.future.flatMap(_ => f)
  }

  /** Run futures in sequence, X at at time where X is the parallelism.
    */
  def sequentialTraverse[A, B](seq: Seq[A], parallelism: Int)(f: A => Future[B])(implicit
      ec: ExecutionContext
  ): Future[Seq[B]] =
    seq
      .grouped(parallelism)
      .foldLeft(Future.successful(Seq.empty[B])) {
        case (acc, next) =>
          acc
            .flatMap(bs => Future.traverse(next)(f).map(b => bs ++ b))
      }

  def timeout[T](n: Duration)(f: => T)(implicit ectx: ExecutionContext): Try[T] = {
    val thread = new AtomicReference[Thread](null)
    val fut = Future {
      thread.set(Thread.currentThread())
      f
    }

    val result = Try {
      Await.result(fut, n)
    }

    result match {
      case Failure(_: java.util.concurrent.TimeoutException) =>
        Option(thread.get()).foreach(_.interrupt())
      case _ =>
    }

    result
  }

  implicit class FutureExOps[T](private val s: Future[T]) extends AnyVal {
    def ?(implicit e: ExecutionContext): Future[Try[T]] = s.map(Success(_)).recover(Failure(_))

    def ??(implicit e: ExecutionContext): Future[Option[T]] = s.map(Some(_)).recover(x => None)
  }
}
