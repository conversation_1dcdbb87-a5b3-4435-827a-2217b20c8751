package de.fellows.utils.collaboration

object TimelineUtils {
  val SUMMARY = "summary"
  val DESCRIPTION = "description"

  def summaryId(cat: String, x: AnyRef): String = {
    id(cat, x, SUMMARY)
  }

  def descriptionId(cat: String, x: AnyRef): String = {
    id(cat, x, DESCRIPTION)
  }

  def id(cat: String, x: AnyRef, idType: String): String = {
    idString(cat, x.getClass.getSimpleName, idType)
  }

  def idString(cat: String, x: String, idType: String): String = {
    s"i18n.timeline.$cat.${x.toLowerCase}.$idType"
  }
}
