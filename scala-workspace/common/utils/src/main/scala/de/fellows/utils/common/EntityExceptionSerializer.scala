package de.fellows.utils.common

import akka.actor.ExtendedActorSystem
import akka.serialization.{BaseSerializer, SerializerWithStringManifest}
import akka.util.ByteString
import de.fellows.utils.communication.{ServiceDefinition, ServiceError, ServiceException, ServiceExceptionSerializer}
import play.api.Logging
import play.api.libs.json.{JsNumber, Json}

class EntityExceptionSerializer(val system: ExtendedActorSystem)
    extends SerializerWithStringManifest
    with BaseSerializer with Logging {

  override def manifest(o: AnyRef): String =
    o match {
      case x: ServiceException => "ServiceException"
      case x: EntityException  => "EntityException"
    }

  override def toBinary(o: AnyRef): Array[Byte] =
    o match {
      case x: ServiceException =>
        ByteString.fromString(Json.stringify(ServiceExceptionSerializer.createJson(x))).toArray

      case x: EntityException =>
        var o = Json.obj(
          "message" -> x.msg
        )

        if (x.code.isDefined) {
          o = o + ("code" -> JsNumber(x.code.get))
        }

        ByteString.fromString(Json.stringify(o)).toArray
    }

  override def fromBinary(bytes: Array[Byte], manifest: String): AnyRef = {
    val str = ByteString.fromArray(bytes).utf8String
    val js  = Json.parse(str)

    // We should not return an `Option` here, otherwise lagom cannot do anything with it
    // and a ClassCastException will be thrown (without a meaningful error or stack trace...)
    val exception: Throwable = manifest match {
      case "ServiceException" =>
        ServiceExceptionSerializer
          .fromJson(str, js)
          .getOrElse {
            val msg = s"Unable to deserialize ServiceException message: '$str'"
            logger.error(msg)
            new ServiceException(new ServiceError(0, str), msg)(ServiceDefinition("unknown"))
          }
      case "EntityException" =>
        EntityException((js \ "message").as[String], (js \ "code").toOption.map(_.as[Int]))
    }

    exception
  }
}
