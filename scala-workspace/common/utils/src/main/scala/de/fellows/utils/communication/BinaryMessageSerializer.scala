package de.fellows.utils.communication

import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.deser.MessageSerializer
import com.lightbend.lagom.scaladsl.api.deser.MessageSerializer.{NegotiatedDeserializer, NegotiatedSerializer}
import com.lightbend.lagom.scaladsl.api.transport.MessageProtocol

import scala.collection.immutable

class BinaryMessageSerializer extends MessageSerializer[Array[Byte], ByteString] {
  //  override def serialize(message: Array[Byte]): ByteString = ByteString.fromArray(message)
  override def serializerForRequest: NegotiatedSerializer[Array[Byte], ByteString] = (message: Array[Byte]) => ByteString.fromArray(message)

  override def deserializer(protocol: MessageProtocol): MessageSerializer.NegotiatedDeserializer[Array[Byte], ByteString] = (wire: ByteString) => wire.toArray

  override def serializerForResponse(acceptedMessageProtocols: immutable.Seq[MessageProtocol]): NegotiatedSerializer[Array[Byte], ByteString] = new NegotiatedSerializer[Array[Byte], ByteString] {
    override def serialize(message: Array[Byte]): ByteString = ByteString.fromArray(message)
  }
}
