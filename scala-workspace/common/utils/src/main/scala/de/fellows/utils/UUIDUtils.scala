package de.fellows.utils

import java.nio.ByteBuffer
import java.security.SecureRandom
import java.util.{Base64, Random, UUID}
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, Future}

object UUIDUtils {

  type Base64EncodedString = String

  val encoder = Base64.getEncoder.withoutPadding()
  val decoder = Base64.getDecoder

  private val tinyGenerator = new SecureRandom;

  val nil = new UUID(0, 0)

  implicit class Base64String(string: String) {
    def encode: Base64EncodedString = encoder.encodeToString(string.getBytes)

    def decode: String = decoder.decode(string).map(_.toChar).mkString
  }

  def ofShort(os: String): UUID = {
    val s = os.replace("_", "/")
      .replace("-", "+")
    val bytes  = decoder.decode(s)
    val buffer = ByteBuffer.wrap(bytes)
    val most   = buffer.getLong()
    val least  = buffer.getLong()
    new UUID(most, least)
  }

  def createShort(): String =
    UUID.randomUUID().short()

  private def toByteString(b: Array[Byte]): String = {
    val x = encoder.encodeToString(b)
    x.replace("/", "_")
      .replace("+", "-")
  }

  def createTiny(): String = {
    val bb = ByteBuffer.allocate(8)
    toByteString(bb.putLong(tinyGenerator.nextLong()).array())
  }

  implicit class UUIDImprovements(val c: UUID) {
    def short(): String = {
      val bb = ByteBuffer.allocate(16)
      bb.putLong(c.getMostSignificantBits)
      bb.putLong(c.getLeastSignificantBits)
      toByteString(bb.array())
    }

    def tiny(): String = {
      val bb = ByteBuffer.allocate(8)
      bb.putLong(c.getMostSignificantBits)
      toByteString(bb.array())
    }
  }

  def fromString(s: String): Option[UUID] =
    try
      Some(UUID.fromString(s))
    catch {
      case _: IllegalArgumentException => None
    }

  val chars   = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
  val r       = new Random()
  val charlen = chars.length

  def randomString(len: Int = 10): String =
    (0 to len).map(_ => chars.charAt(r.nextInt(charlen))).mkString("")

  def maybe(nameOrID: String, resolver: () => Future[Option[UUID]]): Future[Option[UUID]] =
    try
      Future.successful(Some(UUID.fromString(nameOrID)))
    catch {
      case x: IllegalArgumentException =>
        resolver()
    }

  def maybeInstant(nameOrID: String, resolver: () => Future[Option[UUID]]): Option[UUID] =
    Await.result(maybe(nameOrID, resolver), 20 seconds)
}

// a
