package de.fellows.utils.communication

import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}

object TransportErrors {
  implicit class StrExOps(private val s: String) extends AnyVal {
    def !(code: TransportErrorCode) = throw new TransportException(code, s)
  }

  implicit class CodeExOps(private val code: TransportErrorCode) extends AnyVal {
    def !(s: String) = throw new TransportException(code, s)
  }
}
