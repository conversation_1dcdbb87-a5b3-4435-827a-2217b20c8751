package de.fellows.utils.collaboration

import com.fasterxml.jackson.annotation.{JsonSubTypes, JsonTypeInfo}
import de.fellows.utils.UUIDUtils
import de.fellows.utils.security.{Auth0Token, Auth0TokenContent, FileTokenContent, GenericTokenContent, TokenContent}
import play.api.libs.json.{Format, Json}

import java.time.Instant

case class TimelineCommand(
    creator: TimelineUser,
    time: Instant,
    params: Map[String, String] = Map()
)

object TimelineCommand {
  def of(token: GenericTokenContent, params: Map[String, String] = Map()) = {

    val user = TimelineUser.of(token)

    TimelineCommand(user, Instant.now(), params)
  }

  def system: TimelineCommand = system(Map())

  def system(params: Map[String, String]) =
    TimelineCommand(TimelineUser.system, Instant.now(), params)

  implicit val format: Format[TimelineCommand] = Json.using[Json.WithDefaultValues].format
}

case class TimelineEvent(
    team: String,
    creator: TimelineUser,
    reference: TimelineReference,
    change: TimelineChange,
    time: Instant
) {
  def description = s"${reference.entityType}/${reference.entity}: ${change.changeCategory}${change.changeType} ${time}"

}

object TimelineEvent {
  implicit val format: Format[TimelineEvent] = Json.format

  def of(team: String, cmd: TimelineCommand, ref: TimelineReference, change: TimelineChange): TimelineEvent =
    TimelineEvent(
      team = team,
      creator = cmd.creator,
      reference = ref,
      change = change,
      time = cmd.time
    )
}

case class TimelineReference(entity: String, entityType: String)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes(
  Array(
    new JsonSubTypes.Type(value = classOf[SpecificTimelineChange], name = "specific"),
    new JsonSubTypes.Type(value = classOf[PredefinedTimelineChange], name = "predef")
  )
)
sealed trait TimelineChange {
  val changeCategory: String
  val changeType: String
}

case class SpecificTimelineChange(
    override val changeCategory: String,
    override val changeType: String,
    summary: String,
    description: String // markdown
) extends TimelineChange

case class PredefinedTimelineChange(
    override val changeCategory: String,
    override val changeType: String,
    summaryIdentifier: String,
    descriptionIdentifier: String,
    params: Map[String, String]
) extends TimelineChange

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes(
  Array(
    new JsonSubTypes.Type(value = classOf[TimelineGuest], name = "guest"),
    new JsonSubTypes.Type(value = classOf[TimelineSystemUser], name = "system")
  )
)
sealed trait TimelineUser

case class TimelineSystemUser(id: String) extends TimelineUser

case class TimelineGuest(
    share: String,
    grantingUser: String,
    token: Option[TokenContent]
) extends TimelineUser

object SpecificTimelineChange {
  implicit val format: Format[SpecificTimelineChange] = Json.format
}

object PredefinedTimelineChange {
  implicit val format: Format[PredefinedTimelineChange] = Json.format
}

object TimelineChange {
  implicit val format: Format[TimelineChange] = Json.format
}

object TimelineReference {
  implicit val format: Format[TimelineReference] = Json.format
}

object TimelineSystemUser {
  implicit val format: Format[TimelineSystemUser] = Json.format
}

object TimelineGuest {
  implicit val format: Format[TimelineGuest] = Json.format
}

object TimelineUser {
  def of(token: GenericTokenContent): TimelineUser =
    token match {
      case c: TokenContent => c.share match {
          case Some(shareID) => TimelineGuest(shareID, c.userId.toString, Some(c))
          case None          => TimelineSystemUser(c.userId.toString)
        }
      case c: Auth0Token       => TimelineSystemUser(c.getUserId)
      case c: FileTokenContent => ??? // TODO: handle this case
    }

  val systemUserId               = UUIDUtils.nil
  def system: TimelineSystemUser = TimelineSystemUser(systemUserId.toString)

  implicit val format: Format[TimelineUser] = Json.format
}
