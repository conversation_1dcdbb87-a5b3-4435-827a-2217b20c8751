package de.fellows.utils

import de.fellows.utils.JsonFormats.{BasicResolver, EnumResolver}
import play.api.libs.json.{Format, JsValue, Json}
import play.api.libs.json.{Js<PERSON><PERSON>ber, JsString}
sealed abstract class Region {
  val id: Int
  val name: String
}

object Region {
  case object Unknown extends Region {
    override val id   = 0
    override val name = "Unknown"
  }
  case object World extends Region {
    override val id   = 1
    override val name = "World"
  }
  case object Africa extends Region {
    override val id   = 2
    override val name = "Africa"
  }
  case object NorthernAfrica extends Region {
    override val id   = 15
    override val name = "NorthernAfrica"
  }
  case object SubSaharanAfrica extends Region {
    override val id   = 202
    override val name = "SubSaharanAfrica"
  }
  case object WesternAfrica extends Region {
    override val id   = 11
    override val name = "WesternAfrica"
  }
  case object EasternAfrica extends Region {
    override val id   = 14
    override val name = "EasternAfrica"
  }
  case object MiddleAfrica extends Region {
    override val id   = 17
    override val name = "MiddleAfrica"
  }
  case object SouthernAfrica extends Region {
    override val id   = 18
    override val name = "SouthernAfrica"
  }
  case object Americas extends Region {
    override val id   = 19
    override val name = "Americas"
  }
  case object NorthernAmerica extends Region {
    override val id   = 21
    override val name = "NorthernAmerica"
  }
  case object LatinAmericaAndTheCaribbean extends Region {
    override val id   = 419
    override val name = "LatinAmericaAndTheCaribbean"
  }
  case object SouthAmerica extends Region {
    override val id   = 5
    override val name = "SouthAmerica"
  }
  case object CentralAmerica extends Region {
    override val id   = 13
    override val name = "CentralAmerica"
  }
  case object Caribbean extends Region {
    override val id   = 29
    override val name = "Caribbean"
  }
  case object Asia extends Region {
    override val id   = 142
    override val name = "Asia"
  }
  case object EasternAsia extends Region {
    override val id   = 30
    override val name = "EasternAsia"
  }
  case object SouthernAsia extends Region {
    override val id   = 34
    override val name = "SouthernAsia"
  }
  case object SouthEasternAsia extends Region {
    override val id   = 35
    override val name = "SouthEasternAsia"
  }
  case object CentralAsia extends Region {
    override val id   = 143
    override val name = "CentralAsia"
  }
  case object WesternAsia extends Region {
    override val id   = 145
    override val name = "WesternAsia"
  }
  case object Europe extends Region {
    override val id   = 150
    override val name = "Europe"
  }
  case object SouthernEurope extends Region {
    override val id   = 39
    override val name = "SouthernEurope"
  }
  case object EasternEurope extends Region {
    override val id   = 151
    override val name = "EasternEurope"
  }
  case object NorthernEurope extends Region {
    override val id   = 154
    override val name = "NorthernEurope"
  }
  case object ChannelIslands extends Region {
    override val id   = 830
    override val name = "ChannelIslands"
  }
  case object WesternEurope extends Region {
    override val id   = 155
    override val name = "WesternEurope"
  }
  case object Oceania extends Region {
    override val id   = 9
    override val name = "Oceania"
  }
  case object AustraliaAndNewZealand extends Region {
    override val id   = 53
    override val name = "AustraliaAndNewZealand"
  }
  case object Melanesia extends Region {
    override val id   = 54
    override val name = "Melanesia"
  }
  case object Micronesia extends Region {
    override val id   = 57
    override val name = "Micronesia"
  }
  case object Polynesia extends Region {
    override val id   = 61
    override val name = "Polynesia"
  }
  case object Algeria extends Region {
    override val id   = 12
    override val name = "Algeria"
  }
  case object Egypt extends Region {
    override val id   = 818
    override val name = "Egypt"
  }
  case object Libya extends Region {
    override val id   = 434
    override val name = "Libya"
  }
  case object Morocco extends Region {
    override val id   = 504
    override val name = "Morocco"
  }
  case object Sudan extends Region {
    override val id   = 729
    override val name = "Sudan"
  }
  case object Tunisia extends Region {
    override val id   = 788
    override val name = "Tunisia"
  }
  case object WesternSahara extends Region {
    override val id   = 732
    override val name = "WesternSahara"
  }
  case object BritishIndianOceanTerritory extends Region {
    override val id   = 86
    override val name = "BritishIndianOceanTerritory"
  }
  case object Burundi extends Region {
    override val id   = 108
    override val name = "Burundi"
  }
  case object Comoros extends Region {
    override val id   = 174
    override val name = "Comoros"
  }
  case object Djibouti extends Region {
    override val id   = 262
    override val name = "Djibouti"
  }
  case object Eritrea extends Region {
    override val id   = 232
    override val name = "Eritrea"
  }
  case object Ethiopia extends Region {
    override val id   = 231
    override val name = "Ethiopia"
  }
  case object FrenchSouthernTerritories extends Region {
    override val id   = 260
    override val name = "FrenchSouthernTerritories"
  }
  case object Kenya extends Region {
    override val id   = 404
    override val name = "Kenya"
  }
  case object Madagascar extends Region {
    override val id   = 450
    override val name = "Madagascar"
  }
  case object Malawi extends Region {
    override val id   = 454
    override val name = "Malawi"
  }
  case object Mauritius extends Region {
    override val id   = 480
    override val name = "Mauritius"
  }
  case object Mayotte extends Region {
    override val id   = 175
    override val name = "Mayotte"
  }
  case object Mozambique extends Region {
    override val id   = 508
    override val name = "Mozambique"
  }
  case object Reunion extends Region {
    override val id   = 638
    override val name = "Reunion"
  }
  case object Rwanda extends Region {
    override val id   = 646
    override val name = "Rwanda"
  }
  case object Seychelles extends Region {
    override val id   = 690
    override val name = "Seychelles"
  }
  case object Somalia extends Region {
    override val id   = 706
    override val name = "Somalia"
  }
  case object SouthSudan extends Region {
    override val id   = 728
    override val name = "SouthSudan"
  }
  case object Uganda extends Region {
    override val id   = 800
    override val name = "Uganda"
  }
  case object UnitedRepublicOfTanzania extends Region {
    override val id   = 834
    override val name = "UnitedRepublicOfTanzania"
  }
  case object Zambia extends Region {
    override val id   = 894
    override val name = "Zambia"
  }
  case object Zimbabwe extends Region {
    override val id   = 716
    override val name = "Zimbabwe"
  }
  case object Angola extends Region {
    override val id   = 24
    override val name = "Angola"
  }
  case object Cameroon extends Region {
    override val id   = 120
    override val name = "Cameroon"
  }
  case object CentralAfricanRepublic extends Region {
    override val id   = 140
    override val name = "CentralAfricanRepublic"
  }
  case object Chad extends Region {
    override val id   = 148
    override val name = "Chad"
  }
  case object Congo extends Region {
    override val id   = 178
    override val name = "Congo"
  }
  case object DemocraticRepublicOfTheCongo extends Region {
    override val id   = 180
    override val name = "DemocraticRepublicOfTheCongo"
  }
  case object EquatorialGuinea extends Region {
    override val id   = 226
    override val name = "EquatorialGuinea"
  }
  case object Gabon extends Region {
    override val id   = 266
    override val name = "Gabon"
  }
  case object SaoTomeAndPrincipe extends Region {
    override val id   = 678
    override val name = "SaoTomeAndPrincipe"
  }
  case object Botswana extends Region {
    override val id   = 72
    override val name = "Botswana"
  }
  case object Eswatini extends Region {
    override val id   = 748
    override val name = "Eswatini"
  }
  case object Lesotho extends Region {
    override val id   = 426
    override val name = "Lesotho"
  }
  case object Namibia extends Region {
    override val id   = 516
    override val name = "Namibia"
  }
  case object SouthAfrica extends Region {
    override val id   = 710
    override val name = "SouthAfrica"
  }
  case object Benin extends Region {
    override val id   = 204
    override val name = "Benin"
  }
  case object BurkinaFaso extends Region {
    override val id   = 854
    override val name = "BurkinaFaso"
  }
  case object CaboVerde extends Region {
    override val id   = 132
    override val name = "CaboVerde"
  }
  case object CoteDIvore extends Region {
    override val id   = 384
    override val name = "CoteDIvore"
  }
  case object Gambia extends Region {
    override val id   = 270
    override val name = "Gambia"
  }
  case object Ghana extends Region {
    override val id   = 288
    override val name = "Ghana"
  }
  case object Guinea extends Region {
    override val id   = 324
    override val name = "Guinea"
  }
  case object GuineaBissau extends Region {
    override val id   = 624
    override val name = "GuineaBissau"
  }
  case object Liberia extends Region {
    override val id   = 430
    override val name = "Liberia"
  }
  case object Mali extends Region {
    override val id   = 466
    override val name = "Mali"
  }
  case object Mauritania extends Region {
    override val id   = 478
    override val name = "Mauritania"
  }
  case object Niger extends Region {
    override val id   = 562
    override val name = "Niger"
  }
  case object Nigeria extends Region {
    override val id   = 566
    override val name = "Nigeria"
  }
  case object SaintHelena extends Region {
    override val id   = 654
    override val name = "SaintHelena"
  }
  case object Senegal extends Region {
    override val id   = 686
    override val name = "Senegal"
  }
  case object SierraLeone extends Region {
    override val id   = 694
    override val name = "SierraLeone"
  }
  case object Togo extends Region {
    override val id   = 768
    override val name = "Togo"
  }
  case object Anguilla extends Region {
    override val id   = 660
    override val name = "Anguilla"
  }
  case object AntiguaAndBarbuda extends Region {
    override val id   = 28
    override val name = "AntiguaAndBarbuda"
  }
  case object Aruba extends Region {
    override val id   = 533
    override val name = "Aruba"
  }
  case object Bahamas extends Region {
    override val id   = 44
    override val name = "Bahamas"
  }
  case object Barbados extends Region {
    override val id   = 52
    override val name = "Barbados"
  }
  case object BonaireAndSintEustatiusAndSaba extends Region {
    override val id   = 535
    override val name = "BonaireAndSintEustatiusAndSaba"
  }
  case object BritishVirginIslands extends Region {
    override val id   = 92
    override val name = "BritishVirginIslands"
  }
  case object CaymanIslands extends Region {
    override val id   = 136
    override val name = "CaymanIslands"
  }
  case object Cuba extends Region {
    override val id   = 192
    override val name = "Cuba"
  }
  case object Curacao extends Region {
    override val id   = 531
    override val name = "Curacao"
  }
  case object Dominica extends Region {
    override val id   = 212
    override val name = "Dominica"
  }
  case object DominicanRepublic extends Region {
    override val id   = 214
    override val name = "DominicanRepublic"
  }
  case object Grenada extends Region {
    override val id   = 308
    override val name = "Grenada"
  }
  case object Guadeloupe extends Region {
    override val id   = 312
    override val name = "Guadeloupe"
  }
  case object Haiti extends Region {
    override val id   = 332
    override val name = "Haiti"
  }
  case object Jamaica extends Region {
    override val id   = 388
    override val name = "Jamaica"
  }
  case object Martinique extends Region {
    override val id   = 474
    override val name = "Martinique"
  }
  case object Montserrat extends Region {
    override val id   = 500
    override val name = "Montserrat"
  }
  case object PuertoRico extends Region {
    override val id   = 630
    override val name = "PuertoRico"
  }
  case object SaintBarthelemy extends Region {
    override val id   = 652
    override val name = "SaintBarthelemy"
  }
  case object SaintKittsAndNevis extends Region {
    override val id   = 659
    override val name = "SaintKittsAndNevis"
  }
  case object SaintLucia extends Region {
    override val id   = 662
    override val name = "SaintLucia"
  }
  case object SaintMartin extends Region {
    override val id   = 663
    override val name = "SaintMartin"
  }
  case object SaintVincentAndTheGrenadines extends Region {
    override val id   = 670
    override val name = "SaintVincentAndTheGrenadines"
  }
  case object SintMaarten extends Region {
    override val id   = 534
    override val name = "SintMaarten"
  }
  case object TrinidadAndTobago extends Region {
    override val id   = 780
    override val name = "TrinidadAndTobago"
  }
  case object TurksAndCaicosIslands extends Region {
    override val id   = 796
    override val name = "TurksAndCaicosIslands"
  }
  case object UnitedStatesVirginIslands extends Region {
    override val id   = 850
    override val name = "UnitedStatesVirginIslands"
  }
  case object Belize extends Region {
    override val id   = 84
    override val name = "Belize"
  }
  case object CostaRica extends Region {
    override val id   = 188
    override val name = "CostaRica"
  }
  case object ElSalvador extends Region {
    override val id   = 222
    override val name = "ElSalvador"
  }
  case object Guatemala extends Region {
    override val id   = 320
    override val name = "Guatemala"
  }
  case object Honduras extends Region {
    override val id   = 340
    override val name = "Honduras"
  }
  case object Mexico extends Region {
    override val id   = 484
    override val name = "Mexico"
  }
  case object Nicaragua extends Region {
    override val id   = 558
    override val name = "Nicaragua"
  }
  case object Panama extends Region {
    override val id   = 591
    override val name = "Panama"
  }
  case object Argentina extends Region {
    override val id   = 32
    override val name = "Argentina"
  }
  case object Bolivia extends Region {
    override val id   = 68
    override val name = "Bolivia"
  }
  case object BouvetIsland extends Region {
    override val id   = 74
    override val name = "BouvetIsland"
  }
  case object Brazil extends Region {
    override val id   = 76
    override val name = "Brazil"
  }
  case object Chile extends Region {
    override val id   = 152
    override val name = "Chile"
  }
  case object Colombia extends Region {
    override val id   = 170
    override val name = "Colombia"
  }
  case object Ecuador extends Region {
    override val id   = 218
    override val name = "Ecuador"
  }
  case object FalklandIslands extends Region {
    override val id   = 238
    override val name = "FalklandIslands"
  }
  case object FrenchGuiana extends Region {
    override val id   = 254
    override val name = "FrenchGuiana"
  }
  case object Guyana extends Region {
    override val id   = 328
    override val name = "Guyana"
  }
  case object Paraguay extends Region {
    override val id   = 600
    override val name = "Paraguay"
  }
  case object Peru extends Region {
    override val id   = 604
    override val name = "Peru"
  }
  case object SouthGeorgiaAndTheSouthSandwichIslands extends Region {
    override val id   = 239
    override val name = "SouthGeorgiaAndTheSouthSandwichIslands"
  }
  case object Suriname extends Region {
    override val id   = 740
    override val name = "Suriname"
  }
  case object Uruguay extends Region {
    override val id   = 858
    override val name = "Uruguay"
  }
  case object Venezuela extends Region {
    override val id   = 862
    override val name = "Venezuela"
  }
  case object Bermuda extends Region {
    override val id   = 60
    override val name = "Bermuda"
  }
  case object Canada extends Region {
    override val id   = 124
    override val name = "Canada"
  }
  case object Greenland extends Region {
    override val id   = 304
    override val name = "Greenland"
  }
  case object SaintPierreAndMiquelon extends Region {
    override val id   = 666
    override val name = "SaintPierreAndMiquelon"
  }
  case object UnitedStatesOfAmerica extends Region {
    override val id   = 840
    override val name = "UnitedStatesOfAmerica"
  }
  case object Antarctica extends Region {
    override val id   = 10
    override val name = "Antarctica"
  }
  case object Kazakhstan extends Region {
    override val id   = 398
    override val name = "Kazakhstan"
  }
  case object Kyrgyzstan extends Region {
    override val id   = 417
    override val name = "Kyrgyzstan"
  }
  case object Tajikistan extends Region {
    override val id   = 762
    override val name = "Tajikistan"
  }
  case object Turkmenistan extends Region {
    override val id   = 795
    override val name = "Turkmenistan"
  }
  case object Uzbekistan extends Region {
    override val id   = 860
    override val name = "Uzbekistan"
  }
  case object China extends Region {
    override val id   = 156
    override val name = "China"
  }
  case object Taiwan extends Region {
    override val id   = 158
    override val name = "Taiwan"
  }
  case object HongKong extends Region {
    override val id   = 344
    override val name = "HongKong"
  }
  case object Macao extends Region {
    override val id   = 446
    override val name = "Macao"
  }
  case object DemocraticPeoplesRepublicOfKorea extends Region {
    override val id   = 408
    override val name = "DemocraticPeoplesRepublicOfKorea"
  }
  case object Japan extends Region {
    override val id   = 392
    override val name = "Japan"
  }
  case object Mongolia extends Region {
    override val id   = 496
    override val name = "Mongolia"
  }
  case object RepublicOfKorea extends Region {
    override val id   = 410
    override val name = "RepublicOfKorea"
  }
  case object BruneiDarussalam extends Region {
    override val id   = 96
    override val name = "BruneiDarussalam"
  }
  case object Cambodia extends Region {
    override val id   = 116
    override val name = "Cambodia"
  }
  case object Indonesia extends Region {
    override val id   = 360
    override val name = "Indonesia"
  }
  case object LaoPeoplesDemocraticRepublic extends Region {
    override val id   = 418
    override val name = "LaoPeoplesDemocraticRepublic"
  }
  case object Malaysia extends Region {
    override val id   = 458
    override val name = "Malaysia"
  }
  case object Myanmar extends Region {
    override val id   = 104
    override val name = "Myanmar"
  }
  case object Philippines extends Region {
    override val id   = 608
    override val name = "Philippines"
  }
  case object Singapore extends Region {
    override val id   = 702
    override val name = "Singapore"
  }
  case object Thailand extends Region {
    override val id   = 764
    override val name = "Thailand"
  }
  case object TimorLeste extends Region {
    override val id   = 626
    override val name = "TimorLeste"
  }
  case object VietNam extends Region {
    override val id   = 704
    override val name = "VietNam"
  }
  case object Afghanistan extends Region {
    override val id   = 4
    override val name = "Afghanistan"
  }
  case object Bangladesh extends Region {
    override val id   = 50
    override val name = "Bangladesh"
  }
  case object Bhutan extends Region {
    override val id   = 64
    override val name = "Bhutan"
  }
  case object India extends Region {
    override val id   = 356
    override val name = "India"
  }
  case object Iran extends Region {
    override val id   = 364
    override val name = "Iran"
  }
  case object Maldives extends Region {
    override val id   = 462
    override val name = "Maldives"
  }
  case object Nepal extends Region {
    override val id   = 524
    override val name = "Nepal"
  }
  case object Pakistan extends Region {
    override val id   = 586
    override val name = "Pakistan"
  }
  case object SriLanka extends Region {
    override val id   = 144
    override val name = "SriLanka"
  }
  case object Armenia extends Region {
    override val id   = 51
    override val name = "Armenia"
  }
  case object Azerbaijan extends Region {
    override val id   = 31
    override val name = "Azerbaijan"
  }
  case object Bahrain extends Region {
    override val id   = 48
    override val name = "Bahrain"
  }
  case object Cyprus extends Region {
    override val id   = 196
    override val name = "Cyprus"
  }
  case object Georgia extends Region {
    override val id   = 268
    override val name = "Georgia"
  }
  case object Iraq extends Region {
    override val id   = 368
    override val name = "Iraq"
  }
  case object Israel extends Region {
    override val id   = 376
    override val name = "Israel"
  }
  case object Jordan extends Region {
    override val id   = 400
    override val name = "Jordan"
  }
  case object Kuwait extends Region {
    override val id   = 414
    override val name = "Kuwait"
  }
  case object Lebanon extends Region {
    override val id   = 422
    override val name = "Lebanon"
  }
  case object Oman extends Region {
    override val id   = 512
    override val name = "Oman"
  }
  case object Qatar extends Region {
    override val id   = 634
    override val name = "Qatar"
  }
  case object SaudiArabia extends Region {
    override val id   = 682
    override val name = "SaudiArabia"
  }
  case object StateOfPalestine extends Region {
    override val id   = 275
    override val name = "StateOfPalestine"
  }
  case object SyrianArabRepublic extends Region {
    override val id   = 760
    override val name = "SyrianArabRepublic"
  }
  case object Turkey extends Region {
    override val id   = 792
    override val name = "Turkey"
  }
  case object UnitedArabEmirates extends Region {
    override val id   = 784
    override val name = "UnitedArabEmirates"
  }
  case object Yemen extends Region {
    override val id   = 887
    override val name = "Yemen"
  }
  case object Belarus extends Region {
    override val id   = 112
    override val name = "Belarus"
  }
  case object Bulgaria extends Region {
    override val id   = 100
    override val name = "Bulgaria"
  }
  case object Czechia extends Region {
    override val id   = 203
    override val name = "Czechia"
  }
  case object Hungary extends Region {
    override val id   = 348
    override val name = "Hungary"
  }
  case object Poland extends Region {
    override val id   = 616
    override val name = "Poland"
  }
  case object RepublicOfMoldova extends Region {
    override val id   = 498
    override val name = "RepublicOfMoldova"
  }
  case object Romania extends Region {
    override val id   = 642
    override val name = "Romania"
  }
  case object RussianFederation extends Region {
    override val id   = 643
    override val name = "RussianFederation"
  }
  case object Slovakia extends Region {
    override val id   = 703
    override val name = "Slovakia"
  }
  case object Ukraine extends Region {
    override val id   = 804
    override val name = "Ukraine"
  }
  case object AlandIslands extends Region {
    override val id   = 248
    override val name = "AlandIslands"
  }
  case object Guernsey extends Region {
    override val id   = 831
    override val name = "Guernsey"
  }
  case object Jersey extends Region {
    override val id   = 832
    override val name = "Jersey"
  }
  case object Sark extends Region {
    override val id   = 680
    override val name = "Sark"
  }
  case object Denmark extends Region {
    override val id   = 208
    override val name = "Denmark"
  }
  case object Estonia extends Region {
    override val id   = 233
    override val name = "Estonia"
  }
  case object FaroeIslands extends Region {
    override val id   = 234
    override val name = "FaroeIslands"
  }
  case object Finland extends Region {
    override val id   = 246
    override val name = "Finland"
  }
  case object Iceland extends Region {
    override val id   = 352
    override val name = "Iceland"
  }
  case object Ireland extends Region {
    override val id   = 372
    override val name = "Ireland"
  }
  case object IsleOfMan extends Region {
    override val id   = 833
    override val name = "IsleOfMan"
  }
  case object Latvia extends Region {
    override val id   = 428
    override val name = "Latvia"
  }
  case object Lithuania extends Region {
    override val id   = 440
    override val name = "Lithuania"
  }
  case object Norway extends Region {
    override val id   = 578
    override val name = "Norway"
  }
  case object SvalbardAndJanMayenIslands extends Region {
    override val id   = 744
    override val name = "SvalbardAndJanMayenIslands"
  }
  case object Sweden extends Region {
    override val id   = 752
    override val name = "Sweden"
  }
  case object UnitedKingdomOfGreatBritainAndNorthernIreland extends Region {
    override val id   = 826
    override val name = "UnitedKingdomOfGreatBritainAndNorthernIreland"
  }
  case object Albania extends Region {
    override val id   = 8
    override val name = "Albania"
  }
  case object Andorra extends Region {
    override val id   = 20
    override val name = "Andorra"
  }
  case object BosniaAndHerzegovina extends Region {
    override val id   = 70
    override val name = "BosniaAndHerzegovina"
  }
  case object Croatia extends Region {
    override val id   = 191
    override val name = "Croatia"
  }
  case object Gibraltar extends Region {
    override val id   = 292
    override val name = "Gibraltar"
  }
  case object Greece extends Region {
    override val id   = 300
    override val name = "Greece"
  }
  case object HolySee extends Region {
    override val id   = 336
    override val name = "HolySee"
  }
  case object Italy extends Region {
    override val id   = 380
    override val name = "Italy"
  }
  case object Malta extends Region {
    override val id   = 470
    override val name = "Malta"
  }
  case object Montenegro extends Region {
    override val id   = 499
    override val name = "Montenegro"
  }
  case object NorthMacedonia extends Region {
    override val id   = 807
    override val name = "NorthMacedonia"
  }
  case object Portugal extends Region {
    override val id   = 620
    override val name = "Portugal"
  }
  case object SanMarino extends Region {
    override val id   = 674
    override val name = "SanMarino"
  }
  case object Serbia extends Region {
    override val id   = 688
    override val name = "Serbia"
  }
  case object Slovenia extends Region {
    override val id   = 705
    override val name = "Slovenia"
  }
  case object Spain extends Region {
    override val id   = 724
    override val name = "Spain"
  }
  case object Austria extends Region {
    override val id   = 40
    override val name = "Austria"
  }
  case object Belgium extends Region {
    override val id   = 56
    override val name = "Belgium"
  }
  case object France extends Region {
    override val id   = 250
    override val name = "France"
  }
  case object Germany extends Region {
    override val id   = 276
    override val name = "Germany"
  }
  case object Liechtenstein extends Region {
    override val id   = 438
    override val name = "Liechtenstein"
  }
  case object Luxembourg extends Region {
    override val id   = 442
    override val name = "Luxembourg"
  }
  case object Monaco extends Region {
    override val id   = 492
    override val name = "Monaco"
  }
  case object Netherlands extends Region {
    override val id   = 528
    override val name = "Netherlands"
  }
  case object Switzerland extends Region {
    override val id   = 756
    override val name = "Switzerland"
  }
  case object Australia extends Region {
    override val id   = 36
    override val name = "Australia"
  }
  case object ChristmasIsland extends Region {
    override val id   = 162
    override val name = "ChristmasIsland"
  }
  case object CocosKeelingIslands extends Region {
    override val id   = 166
    override val name = "CocosKeelingIslands"
  }
  case object HeardIslandAndMcDonaldIslands extends Region {
    override val id   = 334
    override val name = "HeardIslandAndMcDonaldIslands"
  }
  case object NewZealand extends Region {
    override val id   = 554
    override val name = "NewZealand"
  }
  case object NorfolkIsland extends Region {
    override val id   = 574
    override val name = "NorfolkIsland"
  }
  case object Fiji extends Region {
    override val id   = 242
    override val name = "Fiji"
  }
  case object NewCaledonia extends Region {
    override val id   = 540
    override val name = "NewCaledonia"
  }
  case object PapuaNewGuinea extends Region {
    override val id   = 598
    override val name = "PapuaNewGuinea"
  }
  case object SolomonIslands extends Region {
    override val id   = 90
    override val name = "SolomonIslands"
  }
  case object Vanuatu extends Region {
    override val id   = 548
    override val name = "Vanuatu"
  }
  case object Guam extends Region {
    override val id   = 316
    override val name = "Guam"
  }
  case object Kiribati extends Region {
    override val id   = 296
    override val name = "Kiribati"
  }
  case object MarshallIslands extends Region {
    override val id   = 584
    override val name = "MarshallIslands"
  }
  case object FederatedStatesOfMicronesia extends Region {
    override val id   = 583
    override val name = "FederatedStatesOfMicronesia"
  }
  case object Nauru extends Region {
    override val id   = 520
    override val name = "Nauru"
  }
  case object NorthernMarianaIslands extends Region {
    override val id   = 580
    override val name = "NorthernMarianaIslands"
  }
  case object Palau extends Region {
    override val id   = 585
    override val name = "Palau"
  }
  case object UnitedStatesMinorOutlyingIslands extends Region {
    override val id   = 581
    override val name = "UnitedStatesMinorOutlyingIslands"
  }
  case object AmericanSamoa extends Region {
    override val id   = 16
    override val name = "AmericanSamoa"
  }
  case object CookIslands extends Region {
    override val id   = 184
    override val name = "CookIslands"
  }
  case object FrenchPolynesia extends Region {
    override val id   = 258
    override val name = "FrenchPolynesia"
  }
  case object Niue extends Region {
    override val id   = 570
    override val name = "Niue"
  }
  case object Pitcairn extends Region {
    override val id   = 612
    override val name = "Pitcairn"
  }
  case object Samoa extends Region {
    override val id   = 882
    override val name = "Samoa"
  }
  case object Tokelau extends Region {
    override val id   = 772
    override val name = "Tokelau"
  }
  case object Tonga extends Region {
    override val id   = 776
    override val name = "Tonga"
  }
  case object Tuvalu extends Region {
    override val id   = 798
    override val name = "Tuvalu"
  }
  case object WallisAndFutunaIslands extends Region {
    override val id   = 876
    override val name = "WallisAndFutunaIslands"
  }

  val ALL: Seq[Region] = Seq(
    Region.Unknown,
    Region.World,
    Region.Africa,
    Region.NorthernAfrica,
    Region.SubSaharanAfrica,
    Region.WesternAfrica,
    Region.EasternAfrica,
    Region.MiddleAfrica,
    Region.SouthernAfrica,
    Region.Americas,
    Region.NorthernAmerica,
    Region.LatinAmericaAndTheCaribbean,
    Region.SouthAmerica,
    Region.CentralAmerica,
    Region.Caribbean,
    Region.Asia,
    Region.EasternAsia,
    Region.SouthernAsia,
    Region.SouthEasternAsia,
    Region.CentralAsia,
    Region.WesternAsia,
    Region.Europe,
    Region.SouthernEurope,
    Region.EasternEurope,
    Region.NorthernEurope,
    Region.ChannelIslands,
    Region.WesternEurope,
    Region.Oceania,
    Region.AustraliaAndNewZealand,
    Region.Melanesia,
    Region.Micronesia,
    Region.Polynesia,
    Region.Algeria,
    Region.Egypt,
    Region.Libya,
    Region.Morocco,
    Region.Sudan,
    Region.Tunisia,
    Region.WesternSahara,
    Region.BritishIndianOceanTerritory,
    Region.Burundi,
    Region.Comoros,
    Region.Djibouti,
    Region.Eritrea,
    Region.Ethiopia,
    Region.FrenchSouthernTerritories,
    Region.Kenya,
    Region.Madagascar,
    Region.Malawi,
    Region.Mauritius,
    Region.Mayotte,
    Region.Mozambique,
    Region.Reunion,
    Region.Rwanda,
    Region.Seychelles,
    Region.Somalia,
    Region.SouthSudan,
    Region.Uganda,
    Region.UnitedRepublicOfTanzania,
    Region.Zambia,
    Region.Zimbabwe,
    Region.Angola,
    Region.Cameroon,
    Region.CentralAfricanRepublic,
    Region.Chad,
    Region.Congo,
    Region.DemocraticRepublicOfTheCongo,
    Region.EquatorialGuinea,
    Region.Gabon,
    Region.SaoTomeAndPrincipe,
    Region.Botswana,
    Region.Eswatini,
    Region.Lesotho,
    Region.Namibia,
    Region.SouthAfrica,
    Region.Benin,
    Region.BurkinaFaso,
    Region.CaboVerde,
    Region.CoteDIvore,
    Region.Gambia,
    Region.Ghana,
    Region.Guinea,
    Region.GuineaBissau,
    Region.Liberia,
    Region.Mali,
    Region.Mauritania,
    Region.Niger,
    Region.Nigeria,
    Region.SaintHelena,
    Region.Senegal,
    Region.SierraLeone,
    Region.Togo,
    Region.Anguilla,
    Region.AntiguaAndBarbuda,
    Region.Aruba,
    Region.Bahamas,
    Region.Barbados,
    Region.BonaireAndSintEustatiusAndSaba,
    Region.BritishVirginIslands,
    Region.CaymanIslands,
    Region.Cuba,
    Region.Curacao,
    Region.Dominica,
    Region.DominicanRepublic,
    Region.Grenada,
    Region.Guadeloupe,
    Region.Haiti,
    Region.Jamaica,
    Region.Martinique,
    Region.Montserrat,
    Region.PuertoRico,
    Region.SaintBarthelemy,
    Region.SaintKittsAndNevis,
    Region.SaintLucia,
    Region.SaintMartin,
    Region.SaintVincentAndTheGrenadines,
    Region.SintMaarten,
    Region.TrinidadAndTobago,
    Region.TurksAndCaicosIslands,
    Region.UnitedStatesVirginIslands,
    Region.Belize,
    Region.CostaRica,
    Region.ElSalvador,
    Region.Guatemala,
    Region.Honduras,
    Region.Mexico,
    Region.Nicaragua,
    Region.Panama,
    Region.Argentina,
    Region.Bolivia,
    Region.BouvetIsland,
    Region.Brazil,
    Region.Chile,
    Region.Colombia,
    Region.Ecuador,
    Region.FalklandIslands,
    Region.FrenchGuiana,
    Region.Guyana,
    Region.Paraguay,
    Region.Peru,
    Region.SouthGeorgiaAndTheSouthSandwichIslands,
    Region.Suriname,
    Region.Uruguay,
    Region.Venezuela,
    Region.Bermuda,
    Region.Canada,
    Region.Greenland,
    Region.SaintPierreAndMiquelon,
    Region.UnitedStatesOfAmerica,
    Region.Antarctica,
    Region.Kazakhstan,
    Region.Kyrgyzstan,
    Region.Tajikistan,
    Region.Turkmenistan,
    Region.Uzbekistan,
    Region.China,
    Region.Taiwan,
    Region.HongKong,
    Region.Macao,
    Region.DemocraticPeoplesRepublicOfKorea,
    Region.Japan,
    Region.Mongolia,
    Region.RepublicOfKorea,
    Region.BruneiDarussalam,
    Region.Cambodia,
    Region.Indonesia,
    Region.LaoPeoplesDemocraticRepublic,
    Region.Malaysia,
    Region.Myanmar,
    Region.Philippines,
    Region.Singapore,
    Region.Thailand,
    Region.TimorLeste,
    Region.VietNam,
    Region.Afghanistan,
    Region.Bangladesh,
    Region.Bhutan,
    Region.India,
    Region.Iran,
    Region.Maldives,
    Region.Nepal,
    Region.Pakistan,
    Region.SriLanka,
    Region.Armenia,
    Region.Azerbaijan,
    Region.Bahrain,
    Region.Cyprus,
    Region.Georgia,
    Region.Iraq,
    Region.Israel,
    Region.Jordan,
    Region.Kuwait,
    Region.Lebanon,
    Region.Oman,
    Region.Qatar,
    Region.SaudiArabia,
    Region.StateOfPalestine,
    Region.SyrianArabRepublic,
    Region.Turkey,
    Region.UnitedArabEmirates,
    Region.Yemen,
    Region.Belarus,
    Region.Bulgaria,
    Region.Czechia,
    Region.Hungary,
    Region.Poland,
    Region.RepublicOfMoldova,
    Region.Romania,
    Region.RussianFederation,
    Region.Slovakia,
    Region.Ukraine,
    Region.AlandIslands,
    Region.Guernsey,
    Region.Jersey,
    Region.Sark,
    Region.Denmark,
    Region.Estonia,
    Region.FaroeIslands,
    Region.Finland,
    Region.Iceland,
    Region.Ireland,
    Region.IsleOfMan,
    Region.Latvia,
    Region.Lithuania,
    Region.Norway,
    Region.SvalbardAndJanMayenIslands,
    Region.Sweden,
    Region.UnitedKingdomOfGreatBritainAndNorthernIreland,
    Region.Albania,
    Region.Andorra,
    Region.BosniaAndHerzegovina,
    Region.Croatia,
    Region.Gibraltar,
    Region.Greece,
    Region.HolySee,
    Region.Italy,
    Region.Malta,
    Region.Montenegro,
    Region.NorthMacedonia,
    Region.Portugal,
    Region.SanMarino,
    Region.Serbia,
    Region.Slovenia,
    Region.Spain,
    Region.Austria,
    Region.Belgium,
    Region.France,
    Region.Germany,
    Region.Liechtenstein,
    Region.Luxembourg,
    Region.Monaco,
    Region.Netherlands,
    Region.Switzerland,
    Region.Australia,
    Region.ChristmasIsland,
    Region.CocosKeelingIslands,
    Region.HeardIslandAndMcDonaldIslands,
    Region.NewZealand,
    Region.NorfolkIsland,
    Region.Fiji,
    Region.NewCaledonia,
    Region.PapuaNewGuinea,
    Region.SolomonIslands,
    Region.Vanuatu,
    Region.Guam,
    Region.Kiribati,
    Region.MarshallIslands,
    Region.FederatedStatesOfMicronesia,
    Region.Nauru,
    Region.NorthernMarianaIslands,
    Region.Palau,
    Region.UnitedStatesMinorOutlyingIslands,
    Region.AmericanSamoa,
    Region.CookIslands,
    Region.FrenchPolynesia,
    Region.Niue,
    Region.Pitcairn,
    Region.Samoa,
    Region.Tokelau,
    Region.Tonga,
    Region.Tuvalu,
    Region.WallisAndFutunaIslands
  )

  lazy val regionMapName: Map[Region, Seq[String]] = ALL.map(r => r -> Seq(r.name)).toMap

  implicit val resolver: BasicResolver[Region]                   = new BasicResolver[Region](regionMapName, caseSensitive = true)
  implicit val nameFormat: Format[Region] = JsonFormats.enumFormat[Region](resolver)

}
