package de.fellows.utils.collaboration

import java.util.UUID

trait TimelineFeatures[CHANGE] {

  def timelineRef(spec: UUID): TimelineReference = {
    timelineRef(spec.toString)
  }

  def timelineRef(spec: String): TimelineReference = {
    TimelineReference(
      entity = spec.toString,
      entityType = entityType
    )
  }


  def timelineEvent(team: String, evt: AnyRef, x: TimelineCommand, entityId: String, params: Map[String, String] = Map()): CHANGE = {
    timelineEvent(team, evt, x, timelineRef(entityId), params)
  }

  def timelineEventString(team: String, evt: String, x: TimelineCommand, entityId: String, params: Map[String, String] = Map()): CHANGE = {
    timelineEventString(team, evt, x, timelineRef(entityId), params)
  }

  def timelineEvent(team: String, evt: AnyRef, x: TimelineCommand, reference: TimelineReference, params: Map[String, String]): CHANGE = {
    val prms = params ++ x.params
    val tlEvent = TimelineEvent.of(team, x, reference, PredefinedTimelineChange(
      changeCategory = changeCategory, changeType = evt.getClass.getSimpleName, TimelineUtils.summaryId(changeCategory, evt), TimelineUtils.descriptionId(changeCategory, evt), params = prms
    ))
    event(tlEvent)
  }

  def timelineEventString(team: String, evt: String, x: TimelineCommand, reference: TimelineReference, params: Map[String, String]): CHANGE = {
    val prms = params ++ x.params
    val tlEvent = TimelineEvent.of(team, x, reference, PredefinedTimelineChange(
      changeCategory = changeCategory, changeType = evt, TimelineUtils.idString(changeCategory, evt, TimelineUtils.SUMMARY), TimelineUtils.idString(changeCategory, evt, TimelineUtils.DESCRIPTION), params = prms
    ))
    event(tlEvent)
  }

  def entityType: String

  def changeCategory: String

  def event(tl: TimelineEvent): CHANGE
}
