package de.fellows.utils.security

import akka.actor.ActorSystem
import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.deser.MessageSerializer
import com.lightbend.lagom.scaladsl.api.transport.{Forbidden, RequestHeader, TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.utils.metrics.Metrics._
import pdi.jwt.JwtJson
import play.api.libs.json._
import play.api.libs.streams.Accumulator
import play.api.mvc.Results.{Unauthorized => PlayUnauthorized}
import play.api.mvc._

import java.util.Base64
import scala.util.{Failure, Success, Try}

object AuthenticationServiceComposition {

  def authenticatedPlay[T, A](header: play.api.mvc.RequestHeader)(call: TokenContent => Accumulator[
    ByteString,
    Either[Result, A]
  ]): Accumulator[ByteString, Either[Result, A]] = {
    val tokenContent = extractTokenContent(header)
    tokenContent match {
      case Success(tkn) =>
        call(tkn)
      case Failure(e) => Accumulator.done(Left(PlayUnauthorized(s"Authorization token is invalid: ${e.getMessage}")))
    }
  }

  private def invalidToken[A, T](headers: Map[String, Seq[String]]) = {
    logger.error(s"INVALID TOKEN: ${headers}")
    throw new TransportException(
      TransportErrorCode(401, 4401, "Unauthorized/Invalid Token"),
      "Authorization token is invalid"
    )
  }

  def authenticated[T](header: Request[T])(call: TokenContent => Result): Result = {
    val tokenContent = extractTokenContent(header.headers)
    tokenContent match {
      case Success(tkn) =>
        call(tkn)
      case _ => invalidToken(header.headers.toMap)
    }
  }

  def authenticatedWithToken[Request, Response](
      token: TokenContent,
      serviceCall: (TokenContent, ServiceCallContext) => ServerServiceCall[Request, Response]
  )(implicit ac: ActorSystem, f: MessageSerializer[Request, _]) =
    metric(token) { ctx =>
      ServerServiceCall.compose { requestHeader =>
        matchTokenContent(serviceCall, ctx, token, requestHeader)
      }
    }

  def forbidden[X](exception: Throwable): X = {
    logger.error("invalid token", exception)
    throw Forbidden(s"Invalid token: ${exception}")
  }

  def authenticatedDirect[Request, Response](serviceCall: (
      TokenContent,
      ServiceCallContext
  ) => ServerServiceCall[Request, Response])(implicit ac: ActorSystem, f: MessageSerializer[Request, _]) =
    metric { ctx =>
      ServerServiceCall.compose { requestHeader =>
        val tokenContent = extractTokenContent(requestHeader).filter(tokenContent => isAuthToken(tokenContent))
        tokenContent match {
          case Failure(exception) => forbidden(exception)
          case Success(token)     => matchTokenContent(serviceCall, ctx, token, requestHeader)
        }

      }
    }

  private def matchTokenContent[Response, Request](
      serviceCall: (TokenContent, ServiceCallContext) => ServerServiceCall[Request, Response],
      ctx: ServiceCallContext,
      tokenContent: TokenContent,
      header: RequestHeader
  )(implicit ac: ActorSystem): ServerServiceCall[Request, Response] = {
    ctx.metricsTracker.setToken(tokenContent)
    serviceCall(tokenContent, ctx)
  }

  def authenticatedWithRefreshToken[Request, Response](serviceCall: (
      TokenContent,
      ServiceCallContext
  ) => ServerServiceCall[Request, Response])(implicit ac: ActorSystem, f: MessageSerializer[Request, _]) =
    metric { ctx =>
      ServerServiceCall.compose { requestHeader =>
        val tokenContent = extractTokenContent(requestHeader).filter(tokenContent => isRefreshToken(tokenContent))

        tokenContent match {
          case Success(tkn) =>
            serviceCall(tkn, ctx)
          case Failure(e) => throw Forbidden(s"Refresh token is invalid: ${e.getMessage}")
        }
      }
    }

  private def asTry[T](o: Option[T], e: => Throwable): Try[T] = o match {
    case Some(value) => Success(value)
    case None        => Failure(e)
  }

  def extractTokenHeader(requestHeader: RequestHeader): Try[String] =
    asTry(requestHeader.getHeader("validated-token"), new IllegalArgumentException("empty token"))

  def extractTokenHeader(requestHeader: Headers): Try[String] =
    asTry(requestHeader.get("validated-token"), new IllegalArgumentException("empty token"))

  def extractTokenHeader(requestHeader: play.api.mvc.RequestHeader): Try[String] =
    extractTokenHeader(requestHeader.headers)

  private def extractTokenContent(requestHeader: Headers): Try[TokenContent] =
    extractTokenHeader(requestHeader)
      .flatMap(rawToken => decodeToken(rawToken))

  def extractTokenContent[Response, Request](requestHeader: RequestHeader): Try[TokenContent] =
    extractTokenHeader(requestHeader)
      .flatMap(rawToken => decodeToken(rawToken))
  def extractFileTokenContent[Response, Request](requestHeader: RequestHeader): Try[FileTokenContent] =
    extractTokenHeader(requestHeader)
      .flatMap(rawToken => decodeFileToken(rawToken))

  def extractTokenContent[Response, Request](requestHeader: play.api.mvc.RequestHeader): Try[TokenContent] =
    extractTokenHeader(requestHeader)
      .flatMap(rawToken => decodeToken(rawToken))
  def extractFileTokenContent[Response, Request](requestHeader: play.api.mvc.RequestHeader): Try[FileTokenContent] =
    extractTokenHeader(requestHeader)
      .flatMap(rawToken => decodeFileToken(rawToken))

  def decodeAnyTokenWithResponse(token: String): GenericTokenContent =
    decodeAnyToken(token) match {
      case Failure(exception) => forbidden(exception)
      case Success(value)     => value
    }

  def decodeAnyToken(token: String): Try[GenericTokenContent] =
    (
      decode(token).map(_.asOpt[TokenContent]),
      decode(token).map(_.asOpt[FileTokenContent]),
      decode(token).map(_.asOpt[Auth0TokenContent])
    ) match {
      case (Success(Some(x)), _, _) => Success(x)
      case (_, Success(Some(x)), _) => Success(x)
      case (_, _, Success(Some(x))) => Success(x)
      case (_, _, _) => Failure(new IllegalStateException(s"token ${token} was neither bearer nor file token"))
    }

  def decodeAnyToken(request: Option[Headers], token: Option[String]): Try[GenericTokenContent] =
    (
      request.map(extractTokenHeader).flatMap(_.toOption).map(decodeAnyToken),
      token.map(decodeAnyToken)
    ) match {
      case (Some(Success(v)), _) => Success(v) // prefer already validated token
      case (_, Some(Success(v))) => Success(v)
      case x                     => x._1.orElse(x._2).getOrElse(Failure(new IllegalArgumentException("no token found")))
    }

  def decode(token: String): Try[JsValue] = {

    val t1 =
      JwtTokenUtil.jwtConfig.flatMap { jwtConf =>
        JwtJson.decode(token, jwtConf.secret, Seq(jwtConf.algorithm, jwtConf.fileAlgorithm)).map(_.content)
          .map(Json.parse)
      }

    val t2 =
      try
        Success(Json.parse(new String(Base64.getDecoder.decode(token))))
      catch {
        case e: IllegalArgumentException =>
          Failure(e)
      }

    t1.orElse(t2) match {
      case x: Failure[_] =>
        logger.error(s"failed to decode token ${token}", x.exception)
        x
      case x: Success[_] => x
    }
  }

  def decodeTyped[T](token: String)(implicit jsReads: Reads[T]): Try[T] = decode(token).map(_.as[T])

  def decodeTypedWithResponse[T](token: String)(implicit jsReads: Reads[T]): T = decodeTyped(token) match {
    case Failure(exception) => forbidden(exception)
    case Success(value)     => value
  }

  def decodeToken(token: String): Try[TokenContent] = decodeTyped[TokenContent](token)

  def decodeFileToken(token: String): Try[FileTokenContent] = decodeTyped[FileTokenContent](token)

  def decodeTokenWithResponse(token: String): TokenContent = decodeTyped[TokenContent](token) match {
    case Failure(exception) => forbidden(exception)
    case Success(value)     => value
  }

  def isAuthToken(tokenContent: TokenContent) = !tokenContent.isRefreshToken

  def isRefreshToken(tokenContent: TokenContent) = tokenContent.isRefreshToken

}
