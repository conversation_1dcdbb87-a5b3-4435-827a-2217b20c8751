package de.fellows.utils.common

import java.util.UUID
import play.api.libs.json.{Format, Json}

case class Address(
    id: Option[UUID],
    alias: Option[String],
    name: Option[String],
    phone: Option[String],
    company: Option[String],
    street: String,
    no: String,
    apartment: Option[String],
    district: Option[String],
    city: String,
    postal: String,
    country: Option[String],
    continent: Option[String],
    billing: Boolean,
    shipping: Boolean,
    notes: Option[String]
)

object Address {
  implicit val format: Format[Address] = Json.format[Address]
}
