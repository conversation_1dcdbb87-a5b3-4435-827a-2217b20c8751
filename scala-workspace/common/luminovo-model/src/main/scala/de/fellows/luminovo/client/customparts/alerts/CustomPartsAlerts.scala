package de.fellows.luminovo.client.customparts.alerts

import java.util.UUID
import scala.concurrent.Future
import de.fellows.luminovo.client.customparts.alerts.model.UpsertAlertRequest
import akka.Done
import de.fellows.luminovo.client.customparts.alerts.model.CustomPartAlertType
import de.fellows.luminovo.client.customparts.alerts.model.CustomPartAlertStatus
import scala.concurrent.ExecutionContext
import scala.util.Success
import scala.util.Failure
import scala.util.Try
import org.slf4j.Logger
import de.fellows.utils.logging.StackrateLogger
import de.fellows.luminovo.client.customparts.alerts.model.CustomPartAlertUpsertResult
import play.api.libs.json.Json

class CustomPartsAlerts(
    service: CustomPartsAlertsService
) {

  /** Upsert an alert for a PCB
    * this will fail safely. Instead of failing the future, it will wrap the result in a Try
    *
    * TODO: We need to handle edgecases like:
    * - Luminovo backend is not available (like in dev or previews)
    * @param team
    * @param version
    * @param alert
    * @param status
    * @param ec
    * @param logger
    * @return
    */
  def upsert(
      customPartReference: CustomPartReference,
      alert: CustomPartAlertType,
      status: CustomPartAlertStatus
  )(implicit ec: ExecutionContext, logger: StackrateLogger): Future[Try[CustomPartAlertUpsertResult]] =
    customPartReference.externalReference match {
      case Some(_) =>
        val req = UpsertAlertRequest(
          alert_type = alert,
          alert_status = status
        )
        service.upsertAlertForPcb(customPartReference.team, customPartReference.pcbId).invoke(req).map { result =>
          Success(result)
        }.recoverWith {
          case e: Exception =>
            logger.error(s"error setting alert ${alert} to ${status}", e)
            Future.successful(Failure(e))
        }
      case None =>
        Future.successful(Success(CustomPartAlertUpsertResult(
          changed = false,
          alert = None
        )))
    }

}

case class CustomPartReference(
    team: String,
    pcbId: UUID,
    externalReference: Option[CustomPartLumiquoteReference]
)

case class CustomPartLumiquoteReference(
    rfqId: UUID,
    assemblyId: UUID
)
