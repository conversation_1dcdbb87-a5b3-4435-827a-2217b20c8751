package de.fellows.luminovo

import play.api.libs.json.{__, Format, JsError, Json, JsonConfiguration, Reads, Writes}

import java.util.UUID
import scala.util.Try
import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.utils.model.PCBId
import enumeratum.{Enum, EnumEntry, PlayJsonEnum}

package object panel {

  final case class LuminovoPadding(
      topInMm: BigDecimal,
      rightInMm: BigDecimal,
      bottomInMm: BigDecimal,
      leftInMm: BigDecimal
  )

  object LuminovoPadding {
    def zero: LuminovoPadding                         = LuminovoPadding(0, 0, 0, 0)
    implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
    implicit val format: Format[LuminovoPadding]      = Json.format[LuminovoPadding]
  }

  case class PanelId(value: UUID) extends AnyVal {
    override def toString: String = value.toString
  }

  object PanelId {
    implicit val format: Format[PanelId] = Format(
      Reads.uuidReads.map(PanelId.apply),
      implicitly[Writes[UUID]].contramap[PanelId](_.value)
    )

    def fromStringOption(str: String): Option[PanelId] =
      Try(PanelId(UUID.fromString(str))).toOption

    def random: PanelId = PanelId(UUID.randomUUID())
  }

  final case class ExistingPanel(
      id: Option[PanelId],
      panelWidth: BigDecimal,
      panelHeight: BigDecimal,
      numberOfPcbs: Int,
      pcb: PCBId,
      depanelization: Depanelization,
      pcbIsRotated: Boolean
  )
  object ExistingPanel {
    implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
    implicit val format: Format[ExistingPanel]        = Json.format[ExistingPanel]
  }

  final case class PanelDetails(
      id: Option[PanelId],
      rowCount: Int,
      columnCount: Int,
      horizontalSpacingInMm: BigDecimal,
      verticalSpacingInMm: BigDecimal,
      minMillingDistanceInMm: BigDecimal,
      padding: LuminovoPadding,
      depanelization: Depanelization,
      pcbIsRotated: Boolean
  ) {
    def quantity: Int = rowCount * columnCount
  }

  object PanelDetails {
    implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
    implicit val format: Format[PanelDetails]         = Json.format[PanelDetails]
  }

  sealed trait GenericPanel

  object GenericPanel {
    implicit val reads: Reads[GenericPanel] = Reads { json =>
      (json \ "type").as[String] match {
        case "PerSourcingScenario" => (json \ "data").validate[PerSourcingScenario]
        case "PerPcb"              => (json \ "data").validate[PerPcb]
        case "Existing"            => (json \ "data").validate[ExistingWrapper]
        case jsType                => JsError(s"Unknown type: $jsType")
      }
    }
  }

  final case class PerSourcingScenario(
      sourcingScenario: SourcingScenarioId,
      pcb: PCBId,
      panelDetails: PanelDetails
  ) extends GenericPanel

  object PerSourcingScenario {
    implicit val jsonConfiguration: JsonConfiguration                 = LuminovoJson.configuration
    implicit val perSourcingScenarioReads: Reads[PerSourcingScenario] = Json.reads[PerSourcingScenario]
  }

  final case class PerPcb(
      pcb: PCBId,
      panelDetails: PanelDetails
  ) extends GenericPanel

  object PerPcb {
    implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
    implicit val perPcbReads: Reads[PerPcb]           = Json.reads[PerPcb]
  }

  case class ExistingWrapper(panel: ExistingPanel) extends GenericPanel
  object ExistingWrapper {
    implicit val existingReads: Reads[ExistingWrapper] = Reads { js =>
      js.validate[ExistingPanel].map(ExistingWrapper.apply)
    }
  }

  sealed trait Depanelization extends EnumEntry
  object Depanelization extends Enum[Depanelization] with PlayJsonEnum[Depanelization] {
    override val values: IndexedSeq[Depanelization] = findValues

    case object VCut           extends Depanelization
    case object Milling        extends Depanelization
    case object MillingAndVCut extends Depanelization
  }

}
