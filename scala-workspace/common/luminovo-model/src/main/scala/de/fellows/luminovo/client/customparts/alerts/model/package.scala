package de.fellows.luminovo.client.customparts.alerts

import de.fellows.luminovo.LuminovoJson
import play.api.libs.json.{Format, Json, JsonConfiguration}

import play.api.libs.json.Writes
import play.api.libs.json.Reads
// import de.fellows.luminovo.client.RustEnum
import java.time.Instant
import java.util.UUID
import play.api.libs.json.JsError
import play.api.libs.json.JsString
import play.api.libs.json.JsObject
import de.fellows.luminovo.client.customparts.RustJsonUtils
import play.api.libs.json.JsSuccess
import play.api.libs.json.JsNull
import play.api.libs.json.JsNumber
import play.api.libs.json.JsFalse
import play.api.libs.json.JsTrue
import play.api.libs.json.JsArray
import play.api.libs.json.JsLookupResult
import play.api.libs.json.JsDefined

package object model {
  implicit val config: JsonConfiguration = LuminovoJson.configuration

  sealed trait CustomPartAlertType

  case class RenderingError(
      file: String
  ) extends CustomPartAlertType

  case class FileTimeout(file: String) extends CustomPartAlertType

  case class Pcb(pcb_alert_type: PcbAlertType) extends CustomPartAlertType

  sealed trait PcbAlertType

  case object GeneratedOutline extends PcbAlertType {
    implicit val format: Format[GeneratedOutline.type] = Json.format[GeneratedOutline.type]
  }

  case object NoOutlineFile extends PcbAlertType {
    implicit val format: Format[NoOutlineFile.type] = Json.format[NoOutlineFile.type]
  }

  case object UnusualBoardSize extends PcbAlertType {
    implicit val format: Format[UnusualBoardSize.type] = Json.format[UnusualBoardSize.type]
  }

  case object InconclusiveDimensions extends PcbAlertType {
    implicit val format: Format[InconclusiveDimensions.type] = Json.format[InconclusiveDimensions.type]
  }

  case object NoDrillFile extends PcbAlertType {
    implicit val format: Format[NoDrillFile.type] = Json.format[NoDrillFile.type]
  }

  case object NoSoldermask extends PcbAlertType {
    implicit val format: Format[NoSoldermask.type] = Json.format[NoSoldermask.type]
  }

  case object OddNumberOfCopperFiles extends PcbAlertType {
    implicit val format: Format[OddNumberOfCopperFiles.type] = Json.format[OddNumberOfCopperFiles.type]
  }

  case object NoSilkscreen extends PcbAlertType {
    implicit val format: Format[NoSilkscreen.type] = Json.format[NoSilkscreen.type]
  }

  case object NoPlatedVias extends PcbAlertType {
    implicit val format: Format[NoPlatedVias.type] = Json.format[NoPlatedVias.type]
  }

  object PcbAlertType {
    val baseFormat = Json.writes[PcbAlertType]
    implicit val format: Format[PcbAlertType] = Format(
      Reads { json =>
        json match {
          case x: JsObject =>
            x.value("type") match {
              case JsString("GeneratedOutline") =>
                JsSuccess(GeneratedOutline)
              case JsString("NoOutlineFile") =>
                JsSuccess(NoOutlineFile)
              case JsString("UnusualBoardSize") =>
                JsSuccess(UnusualBoardSize)
              case JsString("InconclusiveDimensions") =>
                JsSuccess(InconclusiveDimensions)
              case JsString("NoDrillFile") =>
                JsSuccess(NoDrillFile)
              case JsString("NoSoldermask") =>
                JsSuccess(NoSoldermask)
              case JsString("OddNumberOfCopperFiles") =>
                JsSuccess(OddNumberOfCopperFiles)
              case JsString("NoSilkscreen") =>
                JsSuccess(NoSilkscreen)
              case JsString("NoPlatedVias") =>
                JsSuccess(NoPlatedVias)
              case _ =>
                JsError("not a PcbAlertType")
            }
          case _ =>
            JsError("not a JsObject")
        }
      },
      Writes { ob =>
        RustJsonUtils.typed(ob, PcbAlertType.baseFormat)
      }
    )
  }

  case class CustomPartAlertUpsertResult(
      changed: Boolean,
      alert: Option[CustomPartAlert]
  )

  object CustomPartAlertUpsertResult {
    implicit val format: Format[CustomPartAlertUpsertResult] = Json.format[CustomPartAlertUpsertResult]
  }

  sealed trait CustomPartAlertStatus

  case object Active extends CustomPartAlertStatus {
    implicit val format: Format[Active.type] = Json.format[Active.type]
  }

  case object Dismissed extends CustomPartAlertStatus {
    implicit val format: Format[Dismissed.type] = Json.format[Dismissed.type]
  }

  case object Resolved extends CustomPartAlertStatus {
    implicit val format: Format[Resolved.type] = Json.format[Resolved.type]
  }

  case class UpsertAlertRequest(
      alert_type: CustomPartAlertType,
      alert_status: CustomPartAlertStatus = Active
  )

  case class CustomPartAlert(
      id: UUID,
      custom_part: UUID,
      status: CustomPartAlertStatus,
      alert_type: CustomPartAlertType,
      creation_date: Instant
  )

  object FileTimeout {
    implicit val format: Format[FileTimeout] = Json.format[FileTimeout]
  }
  object CustomPartAlertStatus {
    implicit val format: Format[CustomPartAlertStatus] = Format(
      Reads { json =>
        json.isInstanceOf[JsString] match {
          case true =>
            val s = json.as[String]

            s match {
              case "Active"    => JsSuccess(Active)
              case "Dismissed" => JsSuccess(Dismissed)
              case "Resolved"  => JsSuccess(Resolved)
              case _           => JsError(s"not a string")
            }

          case _ => JsError(s"not a string")
        }
      },
      Writes { ob =>
        JsString(RustJsonUtils.getClassName(ob))
      }
    )
  }

  object RenderingError {
    implicit val format: Format[RenderingError] = Json.format[RenderingError]
  }

  object Pcb {
    implicit val format: Format[Pcb] = Json.format[Pcb]
  }

  object CustomPartAlertType {
    val baseFormat = Json.format[CustomPartAlertType]

    implicit val format: Format[CustomPartAlertType] = Format(
      Reads { json =>
        json match {
          case x: JsObject =>
            x.value("type") match {
              case JsString("RenderingError") =>
                JsSuccess(RenderingError(x.value("file").as[String]))
              case JsString("FileTimeout") =>
                JsSuccess(FileTimeout(x.value("file").as[String]))
              case JsString("Pcb") =>
                JsSuccess(Pcb(x.value("pcb_alert_type").as[PcbAlertType]))
              case _ =>
                JsError("not a CustomPartAlertType")
            }

          case _ => JsError(s"not an object")
        }
      },
      Writes { ob =>
        RustJsonUtils.typed(ob, baseFormat)
      }
    )
  }

  object UpsertAlertRequest {
    implicit val format: Format[UpsertAlertRequest] = Json.format[UpsertAlertRequest]
  }

  object CustomPartAlert {
    implicit val format: Format[CustomPartAlert] = Json.format[CustomPartAlert]
  }
}
