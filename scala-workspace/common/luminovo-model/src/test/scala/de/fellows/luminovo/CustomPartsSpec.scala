package de.fellows.luminovo

import collection.mutable.Stack
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers._
import org.scalatest.matchers.should.Matchers
import de.fellows.luminovo.client.customparts.alerts.model._
import play.api.libs.json.Json
import play.api.libs.json.Format
import play.api.libs.json.JsonConfiguration
import java.util.UUID
import java.time.Instant

class CustomPartsSpec extends AnyFlatSpec with Matchers {

  "Luminovo model" should "be able to serialize CustomPartAlerts" in {

    val alertType = scala.reflect.runtime.universe.typeOf[CustomPartAlertType].typeSymbol

    val alertTypes: Seq[CustomPartAlertType] = Seq(
      Pcb(GeneratedOutline),
      Pcb(NoOutlineFile),
      Pcb(UnusualBoardSize),
      FileTimeout("file1"),
      RenderingError("file2")
    )

    val assertions = alertTypes.map { alertType =>
      val alert = CustomPartAlert(
        id = UUID.randomUUID(),
        custom_part = UUID.randomUUID(),
        status = Active,
        alert_type = alertType,
        creation_date = Instant.now()
      )

      val parsed = Json.parse(Json.toJson(alert).toString())

      parsed.as[CustomPartAlert] shouldEqual alert
    }

    assertions
  }

}
