package de.luminovo.odb.odbpp.model

import de.luminovo.odb.odbpp.model.ODBFeatures.SurfaceRecord
import de.luminovo.odb.odbpp.model.parse.ODBFeaturesParser
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers._

import scala.util.Using

class SurfaceSpec extends AnyFlatSpec {
  "Surface Record" should "parse correctly" in {

    Using.resource(getClass.getResourceAsStream("/record/surface")) { stream =>
      val source = ODBUtils.fromInputStream(stream)

      ODBLineRecord(source).foreach { lr =>
        val ll = new ODBFeaturesParser(MMUnit, MMUnit).parse(lr)
        ll.length should be(1)
        ll.head should be(a[SurfaceRecord])
      }

    }
  }
}
