package de.luminovo.odb.odbpp.model

import de.luminovo.odb.odbpp.model.ODBLineRecord.{
  CmdParam,
  LineRecordAssignment,
  LineRecordAttribute,
  LineRecordAttributeText,
  LineRecordHeader,
  LineRecordSymbol,
  RawLineRecordCommand
}
import org.scalatest.EitherValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.must.Matchers.be
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

import scala.io.Source

class LineRecordSpec extends AnyFlatSpec with EitherValues {
  "ODBLineRecord" should "parse simple" in {

    val src = ODBUtils.fromResource("record/features")

    (ODBLineRecord.parse(src.getLines()))
  }

  it should "parse SE" in {
    val file =
      """
        |S P 0 ;;ID=13904
        |OB 1.672352559055 1.26968503937 I
        |SE
        |""".stripMargin

    val state = ODBLineRecord(Source.fromString(file)) // ODBLineRecord.parse(file.split("\n").iterator)

    state.value should be(
      ODBLineRecord(
        Seq(),
        Seq(),
        Seq(),
        Seq(),
        Seq(
          RawLineRecordCommand(
            1,
            "S",
            Seq(
              CmdParam("P"),
              CmdParam("0")
            ),
            Seq(Seq(), Seq(LineRecordAssignment("ID", "13904")))
          ),
          RawLineRecordCommand(
            2,
            "OB",
            Seq(
              CmdParam("1.672352559055"),
              CmdParam("1.26968503937"),
              CmdParam("I")
            ),
            Seq()
          ),
          RawLineRecordCommand(
            3,
            "SE",
            Seq(),
            Seq()
          )
        )
      )
    )
  }

  it should "parse semicolon" in {
    val file =
      """
        |#
        |#Units
        |#
        |ID=57
        |UNITS=INCH
        |#
        |#Num Features
        |#
        |F 11499
        |#
        |#Feature symbol names
        |#
        |$0 r10.827
        |$1 donut_r78.74x27.559 I
        |$2 r5.906 I
        |#Feature attribute names
        |#
        |@0 .pad_usage
        |@1 .test_point
        |@2 .geometry
        |#
        |#Feature attribute text strings
        |#
        |&0 MICROVIA0.100_Round0.275
        |&1 VIA0.100_Round0.275
        |&2 _a1
        |P 2.385826771654 0.116141732283 0 P 0 0 ;0=1,2=0;ID=5265
        |""".stripMargin
    val triedState = ODBLineRecord(Source.fromString(file))
    triedState.isRight should be(true)
    triedState.foreach { state =>
      state.symbols should be(Seq(
        LineRecordSymbol(13, 0, "r10.827", None),
        LineRecordSymbol(14, 1, "donut_r78.74x27.559", Some(InchUnit)),
        LineRecordSymbol(15, 2, "r5.906", Some(InchUnit))
      ))

      state.attributes should be(Seq(
        LineRecordAttribute(18, 0, ".pad_usage"),
        LineRecordAttribute(19, 1, ".test_point"),
        LineRecordAttribute(20, 2, ".geometry")
      ))

      state.attributeTexts should be(Seq(
        LineRecordAttributeText(24, 0, "MICROVIA0.100_Round0.275"),
        LineRecordAttributeText(25, 1, "VIA0.100_Round0.275"),
        LineRecordAttributeText(26, 2, "_a1")
      ))

      state.header should be(Seq(
        LineRecordHeader(4, "ID"    -> "57"),
        LineRecordHeader(5, "UNITS" -> "INCH")
      ))

      state.commands should be(Seq(
        RawLineRecordCommand(
          9,
          "F",
          Seq(
            CmdParam("11499")
          ),
          Seq()
        ),
        RawLineRecordCommand(
          27,
          "P",
          Seq(
            CmdParam("2.385826771654"),
            CmdParam("0.116141732283"),
            CmdParam("0"),
            CmdParam("P"),
            CmdParam("0"),
            CmdParam("0")
          ),
          Seq(
            Seq(
              LineRecordAssignment("0" -> "1"),
              LineRecordAssignment("2" -> "0")
            ),
            Seq(
              LineRecordAssignment("ID" -> "5265")
            )
          )
        )
      ))
    }
  }
}
