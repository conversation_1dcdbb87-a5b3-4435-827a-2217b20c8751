package de.luminovo.odb.odbpp

import de.luminovo.odb.odbpp.model.error.NotFound

import java.awt.geom.{Point2D, Rectangle2D}
import java.nio.file.Path

object Utils {
  def fileGuard[X](p: Path)(body: Path => X): Either[NotFound.type, X] = {
    if(p.toFile.exists()){
      Right(body(p))
    }else{
      Left(NotFound)
    }
  }


  def rectangle(points: Seq[Point2D.Double]) = {
    val min = points.reduce{ (a,b) => {
      new Point2D.Double( Math.min(a.x, b.x), Math.min(a.y, b.y))
    }}
    val max = points.reduce{ (a,b) => {
      new Point2D.Double( Math.max(a.x, b.x), Math.max(a.y, b.y))
    }}

    new Rectangle2D.Double(min.x, min.y, max.x - min.x, max.y - min.y)
  }
}
