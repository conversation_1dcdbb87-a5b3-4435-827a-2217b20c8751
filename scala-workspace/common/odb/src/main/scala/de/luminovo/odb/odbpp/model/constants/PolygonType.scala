package de.luminovo.odb.odbpp.model.constants

import de.luminovo.odb.odbpp.model.validation.ODBParseError

trait PolygonType {
  val value: String
}

object PolygonType {
  def apply(s: String): PolygonType = s match {
    case "I" => PolygonIsland
    case "H" => PolygonHole
    case _   => throw new ODBParseError(s"Invalid polygon type: $s")
  }
}

case object PolygonIsland extends PolygonType {
  override val value: String = "I"
}
case object PolygonHole extends PolygonType {
  override val value: String = "I"

}
