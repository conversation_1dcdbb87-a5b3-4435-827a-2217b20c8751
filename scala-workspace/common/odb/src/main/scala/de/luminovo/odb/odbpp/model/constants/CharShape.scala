package de.luminovo.odb.odbpp.model.constants

import de.luminovo.odb.odbpp.model.validation.ODBParseError

trait CharShape {
  val odbString: String
}

case object Rounded<PERSON>har extends CharShape {
  override val odbString: String = "R"
}
case object <PERSON><PERSON>har extends Char<PERSON>hape {
  override val odbString: String = "S"
}

object CharShape {
  def apply(s: String): CharShape = s match {
    case "R" => RoundedChar
    case "S" => SquareChar
    case _   => throw new ODBParseError(s"Invalid char shape: $s")
  }

  def unapply(arg: CharShape): String =
    arg.odbString
}
