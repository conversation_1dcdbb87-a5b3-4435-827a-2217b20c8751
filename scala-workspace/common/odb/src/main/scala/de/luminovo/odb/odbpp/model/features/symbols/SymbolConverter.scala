package de.luminovo.odb.odbpp.model.features.symbols

import de.luminovo.odb.odbpp.model.{ODBLineRecord, ODBUnit}

import scala.util.{Failure, Success, Try}

class UnitConvertingSymbolConverter(originalUnit: ODBUnit, desiredUnit: ODBUnit) {
  def read(baseSymbol: ODBLineRecord.LineRecordSymbol): ODBSymbol = {
    val unitToUse = baseSymbol.unit.getOrElse(originalUnit)

    SymbolConverter.read(
      baseSymbol,
      conv = { d =>
        unitToUse.convert(d, desiredUnit) match {
          case Left(value)      => throw new IllegalStateException(value.message)
          case Right(converted) => converted
        }
      }
    )
  }

}

object SymbolConverter {


  implicit def toIntConversion(name: String): Int =
    Try {
      java.lang.Integer.parseInt(name)
    } match {
      case Failure(exception) =>
        throw exception
      case Success(value) => value
    }
  implicit def toDoubleConversion(name: String): Double =
    Try {
      java.lang.Double.parseDouble(name) / 1000
    } match {
      case Failure(exception) =>
        throw exception
      case Success(value) => value
    }
  implicit def toUnscaledDoubleConversion(name: String): UnscaledDouble =
    Try {
      java.lang.Double.parseDouble(name)
    } match {
      case Failure(exception) =>
        throw exception
      case Success(value) => UnscaledDouble(value)
    }

  def write(symbol: BasicStandardSymbol): String =
    symbol match {
      case RoundSymbol(symbolId, d)        => s"r${d}"
      case SquareSymbol(symbolId, dia)     => s"s${dia}"
      case RectangleSymbol(symbolId, w, h) => s"rect${w}x${h}"
      case RoundedRectangleSymbol(symbolId, w, h, rad, corners) =>
        s"rect${w}x${h}xr${rad}${corners.map(c => s"x$c").getOrElse("")}"
      case ChamferedRectangleSymbol(symbolId, w, h, rad, corners) =>
        s"rect${w}x${h}xc${rad}${corners.map(c => s"x$c").getOrElse("")}"
      case OvalSymbol(symbolId, w, h) =>
        s"oval${w}x${h}"
      case DiamondSymbol(symbolId, w, h) =>
        s"di${w}x${h}"
      case OctagonSymbol(symbolId, w, h, r) =>
        s"oct${w}x${h}x${r}"
      case RoundDonutSymbol(symbolId, od, id) =>
        s"donut_r${od}x${id}"
      case SquareDonutSymbol(symbolId, od, id) =>
        s"donut_s${od}x${id}"
      case SquareRoundDonutSymbol(symbolId, od, id) =>
        s"donut_sr${od}x${id}"
      case RoundedSquareDonutSymbol(symbolId, od, id, rad, corners) =>
        s"donut_s${od}x${id}xr${rad}${corners.map(c => s"x$c").getOrElse("")}"
      case RectangleDonutSymbol(symbolId, ow, oh, lw) =>
        s"donut_rc${ow}x${oh}x${lw}"
      case RoundedRectangleDonutSymbol(symbolId, ow, oh, lw, rad, corners) =>
        s"donut_rc${ow}x${oh}x${lw}xr${rad}${corners.map(c => s"x$c").getOrElse("")}"
      case OvalDonutSymbol(symbolId, ow, oh, lw) =>
        s"donut_o${ow}x${oh}x${lw}"
      case HorizontalHexagonSymbol(symbolId, w, h, r) =>
        s"hex_l${w}x${h}x${r}"
      case VerticalHexagonSymbol(symbolId, w, h, r) =>
        s"hex_s${w}x${h}x${r}"
      case ButterflySymbol(symbolId, d) =>
        s"bfr${d}"
      case SquareButterflySymbol(symbolId, s) =>
        s"bfs${s}"
      case TriangleSymbol(symbolId, base, h) =>
        s"tri${base}x${h}"
      case HalfOvalSymbol(symbolId, w, h) =>
        s"oval_h${w}x${h}"
      case RoundThermalRoundedSymbol(symbolId, od, id, angle, num_spokes, gap) =>
        s"thr${od}x${id}x${angle}x${num_spokes}x${gap}"
      case RoundThermalSquaredSymbol(symbolId, od, id, angle, num_spokes, gap) =>
        s"ths${od}x${id}x${angle}x${num_spokes}x${gap}"
      case SquareThermalSymbol(symbolId, os, is, angle, num_spokes, gap) =>
        s"s_ths${os}x${is}x${angle}x${num_spokes}x${gap}"
      case OpenSquareThermalSymbol(symbolId, od, id, angle, num_spokes, gap) =>
        s"s_tho${od}x${id}x${angle}x${num_spokes}x${gap}"
      case LineThermalSymbol(symbolId, os, is, angle, num_spokes, gap) =>
        s"s_thr${os}x${is}x${angle}x${num_spokes}x${gap}"
      case SquareRoundThermalSymbol(symbolId, os, id, angle, num_spokes, gap) =>
        s"sr_ths${os}x${id}x${angle}x${num_spokes}x${gap}"
      case RectangularThermalSymbol(symbolId, w, h, angle, num_spokes, gap, air_gap) =>
        s"rc_ths${w}x${h}x${angle}x${num_spokes}x${gap}x${air_gap}"
      case OpenRectangularThermalSymbol(symbolId, w, h, angle, num_spokes, gap, air_gap) =>
        s"rc_tho${w}x${h}x${angle}x${num_spokes}x${gap}x${air_gap}"
      case RoundedSquareThermalSymbol(symbolId, os, is, angle, num_spokes, gap, rad, corners) =>
        s"s_ths${os}x${is}x${angle}x${num_spokes}x${gap}xr${rad}${corners.map(c => s"x$c").getOrElse("")}"
      case OpenRoundedSquareThermalSymbol(symbolId) => ???
      case RoundedRectangleThermalSymbol(symbolId, ow, oh, angle, num_spokes, gap, lw, rad, corners) =>
        s"rc_ths${ow}x${oh}x${angle}x${num_spokes}x${gap}x${lw}xr${rad}${corners.map(c => s"x$c").getOrElse("")}"
      case OvalThermalSymbol(symbolId, ow, oh, angle, num_spokes, gap, lw) =>
        s"o_ths${ow}x${oh}x${angle}x${num_spokes}x${gap}x${lw}"
      case OblongThermalSymbol(symbolId, ow, oh, angle, num_spokes, gap, lw, r_s) =>
        s"oblong_ths${ow}x${oh}x${angle}x${num_spokes}x${gap}x${lw}x${r_s}"
      case HomePlateSymbol(symbolId, w, h, c, ra, ro) =>
        s"hplate${w}x${h}x${c}${ra.map(c => s"x$c").getOrElse("")}${ro.map(c => s"x$c").getOrElse("")}"
      case InvertedHomePlateSymbol(symbolId, w, h, c, ra, ro) =>
        s"rhplate${w}x${h}x${c}${ra.map(c => s"x$c").getOrElse("")}${ro.map(c => s"x$c").getOrElse("")}"
      case FlatHomePlateSymbol(symbolId, w, h, vc, hc, ra, ro) =>
        s"fhplate${w}x${h}x${vc}x${hc}${ra.map(c => s"x$c").getOrElse("")}${ro.map(c => s"x$c").getOrElse("")}"
      case RadiusedInvertedHomePlateSymbol(symbolId, w, h, ms, ra) =>
        s"radhplate${w}x${h}x${ms}${ra.map(c => s"x$c").getOrElse("")}"
      case RadiusedHomePlateSymbol(symbolId, w, h, r, ra) =>
        s"dshape${w}x${h}x${r}${ra.map(c => s"x$c").getOrElse("")}"
      case CrossSymbol(symbolId, w, h, hs, vs, hc, vc, r_s, ra) =>
        s"cross${w}x${h}x${hs}x${vs}x${hc}x${vc}x${r_s}${ra.map(c => s"x$c").getOrElse("")}"
      case DogboneSymbol(symbolId, w, h, hs, vs, hc, r_s, ra) =>
        s"dogbone${w}x${h}x${hs}x${vs}x${hc}x${r_s}${ra.map(c => s"x$c").getOrElse("")}"
      case DPackSymbol(symbolId, w, h, hg, vg, hn, vn, ra) =>
        s"dpack${w}x${h}x${hg}x${vg}x${hn}x${vn}${ra.map(c => s"x$c").getOrElse("")}"
      case EllipseSymbol(symbolId, w, h) =>
        s"el${w}x${h}"
      case MoireSymbol(symbolId, rw, rg, nr, lw, ll, la) =>
        s"moire${rw}x${rg}x${nr}x${lw}x${ll}x${la}"
      case HoleSymbol(symbolId, d, p, tp, tm) =>
        s"hole${d}x${p}x${tp}x${tm}"
      case NullSymbol(symbolId) =>
        s"ext"
    }

  def read(baseSymbol: ODBLineRecord.LineRecordSymbol, conv: Double => Double = identity): ODBSymbol = {
    val num = "([0-9.]*)"

    val RoundedRectangleSymbolRegexCorners   = s"rect${num}x${num}xr${num}x${num}".r
    val RoundedRectangleSymbolRegex          = s"rect${num}x${num}xr${num}.*".r
    val ChamferedRectangleSymbolRegexCorners = s"rect${num}x${num}xc${num}x${num}".r
    val ChamferedRectangleSymbolRegex        = s"rect${num}x${num}xc${num}".r
    val RectangleSymbolRegex                 = s"rect${num}x${num}".r

    val OvalSymbolRegex                           = s"oval${num}x${num}".r
    val DiamondSymbolRegex                        = s"di${num}x${num}".r
    val OctagonSymbolRegex                        = s"oct${num}x${num}x${num}".r
    val RoundDonutSymbolRegex                     = s"donut_r${num}x${num}".r
    val SquareRoundDonutSymbolRegex               = s"donut_sr${num}x${num}".r
    val RoundedSquareDonutSymbolRegexCorners      = s"donut_s${num}x${num}xr${num}x${num}".r
    val RoundedSquareDonutSymbolRegex             = s"donut_s${num}x${num}xr${num}".r
    val SquareDonutSymbolRegex                    = s"donut_s${num}x${num}".r
    val RoundedRectangleDonutSymbolRegexCorners   = s"donut_rc${num}x${num}x${num}xr${num}x${num}".r
    val RoundedRectangleDonutSymbolRegex          = s"donut_rc${num}x${num}x${num}xr${num}".r
    val RectangleDonutSymbolRegex                 = s"donut_rc${num}x${num}x${num}".r
    val OvalDonutSymbolRegex                      = s"donut_o${num}x${num}x${num}".r
    val HorizontalHexagonSymbolRegex              = s"hex_l${num}x${num}x${num}".r
    val VerticalHexagonSymbolRegex                = s"hex_s${num}x${num}x${num}".r
    val ButterflySymbolRegex                      = s"bfr${num}".r
    val SquareButterflySymbolRegex                = s"bfs${num}".r
    val TriangleSymbolRegex                       = s"tri${num}x${num}".r
    val HalfOvalSymbolRegex                       = s"oval_h${num}x${num}".r
    val RoundThermalRoundedSymbolRegex            = s"thr${num}x${num}x${num}x${num}x${num}".r
    val RoundThermalSquaredSymbolRegex            = s"ths${num}x${num}x${num}x${num}x${num}".r
    val RoundedSquareThermalSymbolRegexCorners    = s"s_ths${num}x${num}x${num}x${num}x${num}xr${num}x${num}".r
    val RoundedSquareThermalSymbolRegex           = s"s_ths${num}x${num}x${num}x${num}x${num}xr${num}".r
    val SquareThermalSymbolRegex                  = s"s_ths${num}x${num}x${num}x${num}x${num}".r
    val OpenSquareThermalSymbolRegex              = s"s_tho${num}x${num}x${num}x${num}x${num}".r
    val LineThermalSymbolRegex                    = s"s_thr${num}x${num}x${num}x${num}x${num}".r
    val SquareRoundThermalSymbolRegex             = s"sr_ths${num}x${num}x${num}x${num}x${num}".r
    val RoundedRectangleThermalSymbolRegexCorners = s"rc_ths${num}x${num}x${num}x${num}x${num}x${num}xr${num}x${num}".r
    val RoundedRectangleThermalSymbolRegex        = s"rc_ths${num}x${num}x${num}x${num}x${num}x${num}xr${num}".r
    val RectangularThermalSymbolRegex             = s"rc_ths${num}x${num}x${num}x${num}x${num}x${num}".r
    val OpenRectangularThermalSymbolRegex         = s"rc_tho${num}x${num}x${num}x${num}x${num}x${num}".r
    val OvalThermalSymbolRegex                    = s"o_ths${num}x${num}x${num}x${num}x${num}x${num}".r
    val OblongThermalSymbolRegex                  = s"oblong_ths${num}x${num}x${num}x${num}x${num}x${num}x${num}".r
    val HomePlateSymbolRegexCorner                = s"hplate${num}x${num}x${num}x${num}x${num}".r
    val HomePlateSymbolRegex                      = s"hplate${num}x${num}x${num}".r
    val InvertedHomePlateSymbolRegexCorners       = s"rhplate${num}x${num}x${num}x${num}x${num}".r
    val InvertedHomePlateSymbolRegex              = s"rhplate${num}x${num}x${num}".r
    val FlatHomePlateSymbolRegexCorners           = s"fhplate${num}x${num}x${num}x${num}x${num}x${num}".r
    val FlatHomePlateSymbolRegex                  = s"fhplate${num}x${num}x${num}x${num}".r
    val RadiusedInvertedHomePlateSymbolRegexCorners = s"radhplate${num}x${num}x${num}x${num}".r
    val RadiusedInvertedHomePlateSymbolRegex        = s"radhplate${num}x${num}x${num}".r
    val RadiusedHomePlateSymbolRegexCorners         = s"dshape${num}x${num}x${num}x${num}".r
    val RadiusedHomePlateSymbolRegex                = s"dshape${num}x${num}x${num}".r
    val CrossSymbolRegexCorners                     = s"cross${num}x${num}x${num}x${num}x${num}x${num}x${num}${num}".r
    val CrossSymbolRegex                            = s"cross${num}x${num}x${num}x${num}x${num}x${num}x${num}".r
    val DogboneSymbolRegexCorners                   = s"dogbone${num}x${num}x${num}x${num}x${num}x${num}x${num}".r
    val DogboneSymbolRegex                          = s"dogbone${num}x${num}x${num}x${num}x${num}x${num}".r
    val DPackSymbolRegexCorners                     = s"dpack${num}x${num}x${num}x${num}x${num}x${num}x${num}".r
    val DPackSymbolRegex                            = s"dpack${num}x${num}x${num}x${num}x${num}x${num}".r
    val EllipseSymbolRegex                          = s"el${num}x${num}".r
    val MoireSymbolRegex                            = s"moire${num}x${num}x${num}x${num}x${num}x${num}".r
    val HoleSymbolRegex                             = s"hole${num}x${num}x${num}x${num}".r
    val RoundSymbolRegex                            = s"r${num}".r
    val SquareSymbolRegex                           = s"s${num}".r
    val NullSymbolRegex                             = s"ext".r

    val symbolId = baseSymbol.id
    baseSymbol.name match {
      case RoundedRectangleSymbolRegexCorners(w, h, rad, corners) =>
        RoundedRectangleSymbol(symbolId, conv(w), conv(h), conv(rad), Some(corners))
      case RoundedRectangleSymbolRegex(w, h, rad) =>
        RoundedRectangleSymbol(symbolId, conv(w), conv(h), conv(rad), None)
      case ChamferedRectangleSymbolRegexCorners(w, h, rad, corners) =>
        ChamferedRectangleSymbol(symbolId, conv(w), conv(h), conv(rad), Some(corners))
      case ChamferedRectangleSymbolRegex(w, h, rad) =>
        ChamferedRectangleSymbol(symbolId, conv(w), conv(h), conv(rad), None)
      case RectangleSymbolRegex(w, h) =>
        RectangleSymbol(symbolId, conv(w), conv(h))
      case OvalSymbolRegex(w, h) =>
        OvalSymbol(symbolId, conv(w), conv(h))
      case DiamondSymbolRegex(w, h) =>
        DiamondSymbol(symbolId, conv(w), conv(h))
      case OctagonSymbolRegex(w, h, r) =>
        OctagonSymbol(symbolId, conv(w), conv(h), conv(r))
      case RoundDonutSymbolRegex(od, id) =>
        RoundDonutSymbol(symbolId, conv(od), conv(id))
      case SquareRoundDonutSymbolRegex(od, id) =>
        SquareRoundDonutSymbol(symbolId, conv(od), conv(id))
      case RoundedSquareDonutSymbolRegexCorners(od, id, rad, corners) =>
        RoundedSquareDonutSymbol(symbolId, conv(od), conv(id), conv(rad), Some(corners))
      case RoundedSquareDonutSymbolRegex(od, id, rad) =>
        RoundedSquareDonutSymbol(symbolId, conv(od), conv(id), conv(rad), None)
      case SquareDonutSymbolRegex(od, id) =>
        SquareDonutSymbol(symbolId, conv(od), conv(id))
      case RoundedRectangleDonutSymbolRegexCorners(ow, oh, lw, rad, corners) =>
        RoundedRectangleDonutSymbol(symbolId, conv(ow), conv(oh), conv(lw), conv(rad), Some(corners))
      case RoundedRectangleDonutSymbolRegex(ow, oh, lw, rad) =>
        RoundedRectangleDonutSymbol(symbolId, conv(ow), conv(oh), conv(lw), conv(rad), None)
      case RectangleDonutSymbolRegex(ow, oh, lw) =>
        RectangleDonutSymbol(symbolId, conv(ow), conv(oh), conv(lw))
      case OvalDonutSymbolRegex(ow, oh, lw) =>
        OvalDonutSymbol(symbolId, conv(ow), conv(oh), conv(lw))
      case HorizontalHexagonSymbolRegex(w, h, r) =>
        HorizontalHexagonSymbol(symbolId, conv(w), conv(h), conv(r))
      case VerticalHexagonSymbolRegex(w, h, r) =>
        VerticalHexagonSymbol(symbolId, conv(w), conv(h), conv(r))
      case ButterflySymbolRegex(d) =>
        ButterflySymbol(symbolId, conv(d))
      case SquareButterflySymbolRegex(s) =>
        SquareButterflySymbol(symbolId, conv(s))
      case TriangleSymbolRegex(base, h) =>
        TriangleSymbol(symbolId, conv(base), conv(h))
      case HalfOvalSymbolRegex(w, h) =>
        HalfOvalSymbol(symbolId, conv(w), conv(h))
      case RoundThermalRoundedSymbolRegex(od, id, angle, num_spokes, gap) =>
        RoundThermalRoundedSymbol(symbolId, conv(od), conv(id), angle, num_spokes, conv(gap))
      case RoundThermalSquaredSymbolRegex(od, id, angle, num_spokes, gap) =>
        RoundThermalSquaredSymbol(symbolId, conv(od), conv(id), angle, num_spokes, conv(gap))
      case RoundedSquareThermalSymbolRegex(os, is, angle, num_spokes, gap, rad, corners) =>
        RoundedSquareThermalSymbol(symbolId, conv(os), conv(is), angle, num_spokes, conv(gap), conv(rad), Some(corners))
      case RoundedSquareThermalSymbolRegex(os, is, angle, num_spokes, gap, rad) =>
        RoundedSquareThermalSymbol(symbolId, conv(os), conv(is), angle, num_spokes, conv(gap), conv(rad), None)
      case SquareThermalSymbolRegex(os, is, angle, num_spokes, gap) =>
        SquareThermalSymbol(symbolId, conv(os), conv(is), angle, num_spokes, conv(gap))
      case OpenSquareThermalSymbolRegex(od, id, angle, num_spokes, gap) =>
        OpenSquareThermalSymbol(symbolId, conv(od), conv(id), angle, num_spokes, conv(gap))
      case LineThermalSymbolRegex(os, is, angle, num_spokes, gap) =>
        LineThermalSymbol(symbolId, conv(os), conv(is), angle, num_spokes, conv(gap))
      case SquareRoundThermalSymbolRegex(os, id, angle, num_spokes, gap) =>
        SquareRoundThermalSymbol(symbolId, conv(os), conv(id), angle, num_spokes, conv(gap))
      case RoundedRectangleThermalSymbolRegexCorners(ow, oh, angle, num_spokes, gap, lw, rad, corners) =>
        RoundedRectangleThermalSymbol(
          symbolId,
          conv(ow),
          conv(oh),
          angle,
          num_spokes,
          conv(gap),
          conv(lw),
          conv(rad),
          Some(corners)
        )
      case RoundedRectangleThermalSymbolRegex(ow, oh, angle, num_spokes, gap, lw, rad) =>
        RoundedRectangleThermalSymbol(
          symbolId,
          conv(ow),
          conv(oh),
          angle,
          num_spokes,
          conv(gap),
          conv(lw),
          conv(rad),
          None
        )
      case RectangularThermalSymbolRegex(w, h, angle, num_spokes, gap, air_gap) =>
        RectangularThermalSymbol(symbolId, conv(w), conv(h), angle, num_spokes, conv(gap), conv(air_gap))
      case OpenRectangularThermalSymbolRegex(w, h, angle, num_spokes, gap, air_gap) =>
        OpenRectangularThermalSymbol(symbolId, conv(w), conv(h), angle, num_spokes, conv(gap), conv(air_gap))
      case OvalThermalSymbolRegex(ow, oh, angle, num_spokes, gap, lw) =>
        OvalThermalSymbol(symbolId, conv(ow), conv(oh), angle, num_spokes, conv(gap), conv(lw))
      case OblongThermalSymbolRegex(ow, oh, angle, num_spokes, gap, lw, r_s) =>
        OblongThermalSymbol(symbolId, conv(ow), conv(oh), angle, num_spokes, conv(gap), conv(lw), r_s)
      case HomePlateSymbolRegexCorner(w, h, c, ra, ro) =>
        HomePlateSymbol(symbolId, conv(w), conv(h), conv(c), Some(ra), Some(ro))
      case HomePlateSymbolRegex(w, h, c) =>
        HomePlateSymbol(symbolId, conv(w), conv(h), conv(c), None, None)
      case InvertedHomePlateSymbolRegexCorners(w, h, c, ra, ro) =>
        InvertedHomePlateSymbol(symbolId, conv(w), conv(h), conv(c), Some(ra), Some(ro))
      case InvertedHomePlateSymbolRegex(w, h, c) =>
        InvertedHomePlateSymbol(symbolId, conv(w), conv(h), conv(c), None, None)
      case FlatHomePlateSymbolRegexCorners(w, h, vc, hc, ra, ro) =>
        FlatHomePlateSymbol(symbolId, conv(w), conv(h), conv(vc), conv(hc), Some(ra), Some(ro))
      case FlatHomePlateSymbolRegex(w, h, vc, hc) =>
        FlatHomePlateSymbol(symbolId, conv(w), conv(h), conv(vc), conv(hc), None, None)
      case RadiusedInvertedHomePlateSymbolRegexCorners(w, h, ms, ra) =>
        RadiusedInvertedHomePlateSymbol(symbolId, conv(w), conv(h), conv(ms), Some(ra))
      case RadiusedInvertedHomePlateSymbolRegex(w, h, ms) =>
        RadiusedInvertedHomePlateSymbol(symbolId, conv(w), conv(h), conv(ms), None)
      case RadiusedHomePlateSymbolRegexCorners(w, h, r, ra) =>
        RadiusedHomePlateSymbol(symbolId, conv(w), conv(h), conv(r), Some(ra))
      case RadiusedHomePlateSymbolRegex(w, h, r) =>
        RadiusedHomePlateSymbol(symbolId, conv(w), conv(h), conv(r), None)
      case CrossSymbolRegexCorners(w, h, hs, vs, hc, vc, r_s, ra) =>
        CrossSymbol(symbolId, conv(w), conv(h), conv(hs), conv(vs), conv(hc), conv(vc), r_s, Some(ra))
      case CrossSymbolRegex(w, h, hs, vs, hc, vc, r_s) =>
        CrossSymbol(symbolId, conv(w), conv(h), conv(hs), conv(vs), conv(hc), conv(vc), r_s, None)
      case DogboneSymbolRegexCorners(w, h, hs, vs, hc, r_s, ra) =>
        DogboneSymbol(symbolId, conv(w), conv(h), conv(hs), conv(vs), conv(hc), r_s, Some(ra))
      case DogboneSymbolRegex(w, h, hs, vs, hc, r_s) =>
        DogboneSymbol(symbolId, conv(w), conv(h), conv(hs), conv(vs), conv(hc), r_s, None)
      case DPackSymbolRegexCorners(w, h, hg, vg, hn, vn, ra) =>
        DPackSymbol(symbolId, conv(w), conv(h), conv(hg), conv(vg), hn, vn, Some(ra))
      case DPackSymbolRegex(w, h, hg, vg, hn, vn) =>
        DPackSymbol(symbolId, conv(w), conv(h), conv(hg), conv(vg), hn, vn, None)
      case EllipseSymbolRegex(w, h) =>
        EllipseSymbol(symbolId, conv(w), conv(h))
      case MoireSymbolRegex(rw, rg, nr, lw, ll, la) =>
        MoireSymbol(symbolId, conv(rw), conv(rg), nr, conv(lw), conv(ll), la)
      case HoleSymbolRegex(d, p, tp, tm) =>
        HoleSymbol(symbolId, conv(d), p, tp, tm)
      case RoundSymbolRegex(dia) =>
        RoundSymbol(symbolId, conv(dia))
      case SquareSymbolRegex(size) =>
        SquareSymbol(symbolId, conv(size))
      case NullSymbolRegex() =>
        NullSymbol(symbolId)
      case something =>
        UserSymbol(symbolId, something)
    }
  }

}
