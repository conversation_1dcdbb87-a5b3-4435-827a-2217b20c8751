package de.luminovo.odb.odbpp.model

import de.luminovo.odb.odbpp.model.ODBFeatures.ODBFeatureRecord
import de.luminovo.odb.odbpp.model.ODBLineRecord.LineRecordAssignment
import de.luminovo.odb.odbpp.model.constants.{BarcodeAscii, BarcodeBackground, BarcodeChecksum, BarcodeTextOption, BarcodeTextPosition, Direction, Orientation, Polarity, PolygonType, TextVersion}
import de.luminovo.odb.odbpp.model.features.symbols.ODBSymbol
import de.luminovo.odb.odbpp.model.parse.ODBFeaturesParser
import de.luminovo.odb.odbpp.model.validation.{SemanticError, SyntaxError}
import de.luminovo.odb.odbpp.{Utils, model}

import java.awt.geom.{Point2D, Rectangle2D}
import scala.util.{Failure, Success, Try}

case class ODBFeatures(
    unit: ODBUnit,
    originalUnit: ODBUnit,
    symbols: Seq[ODBSymbol],
    records: Seq[ODBFeatureRecord]
) {

  def getBounds(): Rectangle2D =
    records.foldLeft(None: Option[Rectangle2D]) { (r, x) =>
      val next = x.getBounds
      Some(r.getOrElse(next).createUnion(next))
    }.getOrElse(new Rectangle2D.Double())
}

sealed trait SymbolUsage {
  val symNum: Int
  val polarity: Polarity

}

object ODBFeatures {
  sealed trait ODBFeatureRecord {
    val line: Int
    def getBounds: Rectangle2D

    def toODBString: String
  }

  case class LineRecord(
      override val line: Int,
      xs: Double,
      ys: Double,
      xe: Double,
      ye: Double,
      override val symNum: Int,
      override val polarity: Polarity,
      dcode: String
  ) extends ODBFeatureRecord with SymbolUsage {
    override def getBounds: Rectangle2D = {
      val r = new Rectangle2D.Double()

      val p1 = new Point2D.Double(xs.doubleValue, ys.doubleValue)
      val p2 = new Point2D.Double(xe.doubleValue, ye.doubleValue)
      r.setFrameFromDiagonal(p1, p2)
      r
    }

    override def toODBString: String = {
      val polarityString: String = polarity.odbString
      s"L ${xs} ${ys} ${xe} ${ye} ${symNum} ${polarityString} ${dcode}"
    }
  }

  case class PadRecord(
      override val line: Int,
      x: Double,
      y: Double,
      override val symNum: Int,
      override val polarity: Polarity,
      dcode: String,
      orientation: Orientation
  ) extends ODBFeatureRecord with SymbolUsage {
    override def getBounds: Rectangle2D = new Rectangle2D.Double(x.doubleValue, y.doubleValue, 0, 0)

    override def toODBString: String = {
      val polarityString: String = polarity.odbString
      s"P ${x} ${y} ${symNum} ${polarityString} ${dcode} ${orientation.odbString}"
    }
  }

  case class ArcRecord(
      override val line: Int,
      xs: Double,
      ys: Double,
      xe: Double,
      ye: Double,
      xc: Double,
      yc: Double,
      override val symNum: Int,
      override val polarity: Polarity,
      dCode: String,
      direction: Direction
  ) extends ODBFeatureRecord with SymbolUsage {

    override def getBounds: Rectangle2D = {
      val r = new Rectangle2D.Double()

      val p1 = new Point2D.Double(xs.doubleValue, ys.doubleValue)
      val p2 = new Point2D.Double(xe.doubleValue, ye.doubleValue)

      // TODO respect curve
      r.setFrameFromDiagonal(p1, p2)
      r
    }

    override def toODBString: String = {
      val polarityString: String = polarity.odbString
      s"A ${xs} ${ys} ${xe} ${ye} ${xc} ${yc} ${symNum} ${polarityString} ${dCode} ${direction.odbString}"
    }
  }

  case class TextRecord(
      override val line: Int,
      x: Double,
      y: Double,
      font: String,
      polarity: Polarity,
      orientation: Orientation,
      xSize: Double,
      ySize: Double,
      width: Double,
      text: String,
      version: TextVersion
  ) extends ODBFeatureRecord {
    override def getBounds: Rectangle2D = {
      // TODO actual text bounds
      val length = text.length * 0.2
      new Rectangle2D.Double(x, y, length, 2)
    }

    override def toODBString: String = {
      val polarityString: String = polarity.odbString
      s"T ${x} ${y} ${font} ${polarityString} ${orientation.odbString} ${xSize} ${ySize} ${width} ${text} ${version.odbString}"
    }

  }

  case class BarcodeRecord(
      override val line: Int,
      x: Double,
      y: Double,
      barcode: String,
      font: String,
      polarity: Polarity,
      orientation: Orientation,
      e: String,
      w: Double,
      h: Double,
      fasc: BarcodeAscii,
      cs: BarcodeChecksum,
      bg: BarcodeBackground,
      astr: BarcodeTextOption,
      astrPos: BarcodeTextPosition,
      text: String
  ) extends ODBFeatureRecord {
    override def getBounds: Rectangle2D =
      // TODO actual barcode bounds
      new Rectangle2D.Double(x.doubleValue, y.doubleValue, w.doubleValue, h.doubleValue)

    override def toODBString: String = {
      val polarityString: String = polarity.odbString
      s"B ${x} ${y} ${barcode} ${font} ${polarityString} ${orientation.odbString} ${e} ${w} ${h} ${fasc.odbString} ${cs.odbString} ${bg.odbString} ${astr.odbString} ${astrPos.odbString} ${text}"
    }
  }

  case class SurfaceRecord(
      override val line: Int,
      polygons: Seq[Polygon],
      polarity: Polarity,
      dCode: String,
      attributes: Seq[Seq[LineRecordAssignment]]
  ) extends ODBFeatureRecord {
    override def getBounds: Rectangle2D =
      // TODO actual surface bounds

      polygons.foldLeft(None: Option[Rectangle2D]) { (r, x) =>
        val s = x.curves.foldLeft(Seq(new Point2D.Double(x.xbs.doubleValue, x.ybs.doubleValue)): Seq[Point2D.Double]) {
          (r2, curve) =>
            curve match {
              case CurveLine(x, y, cw)          => r2 :+ new Point2D.Double(x.doubleValue, y.doubleValue)
              case CurveArc(xe, ye, xc, yc, cw) => r2 :+ new Point2D.Double(xe.doubleValue, ye.doubleValue)
            }
        }
        val next = Utils.rectangle(s)
        Some(r.getOrElse(next).createUnion(next))
      }.get

    override def toODBString: String = {
      val attributes = this.attributes.map(_.map(x => s"${x.name}=${x.value}").mkString(", ")).mkString("; ")
      s"S ${polarity.odbString} ${dCode} ${attributes}\n" + polygons.map(_.toODBString).mkString("\n") + "SE"
    }
  }

  sealed trait Curve {
    def toODBString: String
  }

  case class CurveLine(x: Double, y: Double, cw: Direction) extends Curve {
    override def toODBString: String = s"OS ${x} ${y} ${cw.odbString}"
  }

  case class CurveArc(xe: Double, ye: Double, xc: Double, yc: Double, cw: Direction) extends Curve {
    override def toODBString: String = s"OC ${xe} ${ye} ${xc} ${yc} ${cw.odbString}"
  }

  case class Polygon(
      xbs: Double,
      ybs: Double,
      polyType: PolygonType,
      curves: Seq[Curve]
  ) {
    def toODBString: String =
      s"""OB ${xbs} ${ybs} ${polyType.value}
         |\t${curves.map(_.toODBString).mkString("\n\t")}
         |OE
         |""".stripMargin

  }

  def apply(base: ODBLineRecord, desiredUnit: Option[ODBUnit]): Either[SemanticError, ODBFeatures] =
    Try {
      val originalUnit = base.header.find(h => h.name.toLowerCase == "units" || h.name.toLowerCase == "u") match {
        case Some(value) =>
          model.ODBUnit(value)
        case None =>
          model.UnknownUnit
      }

      val dunit   = desiredUnit.getOrElse(originalUnit)
      val parser  = new ODBFeaturesParser(originalUnit, dunit)
      val records = parser.parse(base)
      val symbols = parser.parseSymbols(base)

      new ODBFeatures(dunit, originalUnit, symbols, records)
    } match {
      case Failure(exception) =>
        Left(SyntaxError(exception.getMessage))
      case Success(value) => Right(value)
    }
}
