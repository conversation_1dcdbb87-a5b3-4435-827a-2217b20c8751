package de.fellows.utils

import scala.util.control.NonFatal
import com.typesafe.config.Config
import java.time.format.DateTimeFormatter
import java.time.LocalDateTime
import java.time.ZoneOffset
import akka.persistence.query.TimeBasedUUID
import com.datastax.driver.core.utils.UUIDs
import java.util.UUID

object TimeBasedUUIDUtils {

  def getCuttOff(introducedAt: Long, config: Config): TimeBasedUUID = {
    val cutOff = getFirstBucket(config)
      .fold(introducedAt)(value => Math.max(value, introducedAt))

    getTimeBasedUUID(cutOff)
  }

  private def getFirstBucket(config: Config): Option[Long] =
    try {
      val str = config.getString("akka.persistence.cassandra.events-by-tag.first-time-bucket")
      Some(getUnixTimestamp(str))
    } catch {
      case NonFatal(e) => None
    }

  private val formatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HH:mm")

  private def getUnixTimestamp(str: String): Long = {
    val localDateTime = LocalDateTime.parse(str, formatter)
    localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli()
  }

  private def getTimeBasedUUID(time: Long): TimeBasedUUID = {
    val uuid = UUIDs.startOf(time)
    TimeBasedUUID(uuid)
  }
}
