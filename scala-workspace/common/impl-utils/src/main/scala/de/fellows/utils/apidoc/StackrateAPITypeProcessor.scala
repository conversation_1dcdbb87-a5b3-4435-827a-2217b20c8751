package de.fellows.utils.apidoc

import com.google.common.reflect.TypeToken

import io.swagger.v3.oas.models.media.Schema
import io.swagger.v3.oas.models.{Components, OpenAPI}
import org.taymyr.lagom.internal.openapi.ClassLevelInfo

import java.lang.reflect.Type

class StackrateAPITypeProcessor(t: Type, classInfo: ClassLevelInfo, spec: OpenAPI, comps: Components)(implicit val loader: ClassLoader) {

  def processAnnotations(schema: Schema[Object]) = {
    //    schema
    Option(TypeToken.of(t).getRawType.getAnnotation(classOf[StackrateAPIObject]))
      .foreach(apiObject => {
        ApiGenUtils.emptyString(apiObject.description()).foreach(schema.description)
        ApiGenUtils.emptyString(apiObject.title()).foreach(schema.title)
      })

  }

  def process(): Set[Type] = {


    val schemaDef = new SchemaGenerator(t).generateDefinition()
    comps.addSchemas(SchemaGenerator.schemaName(t), schemaDef.schema)
    //    schemaDef.schema

    processAnnotations(schemaDef.schema)

    schemaDef.references.toSet
  }

}
