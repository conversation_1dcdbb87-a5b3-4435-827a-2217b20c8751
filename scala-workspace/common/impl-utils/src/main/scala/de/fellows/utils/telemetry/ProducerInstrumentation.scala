package de.fellows.utils.telemetry

import kamon.instrumentation.context.HasContext
import kanela.agent.api.instrumentation.InstrumentationBuilder

/** Producer instrumentation
  *
  * It's a copy of the original one bundled with Ka<PERSON> except it does not start a span
  * when the message is sent.
  *
  * It still propagates the context to the message headers.
  */
class ProducerInstrumentation extends InstrumentationBuilder {

  /** Instruments "org.apache.kafka.clients.producer.KafkaProducer::Send()
    */
  onType("org.apache.kafka.clients.producer.KafkaProducer")
    .advise(method("doSend").and(takesArguments(2)), classOf[SendMethodAdvisor])

  onType("org.apache.kafka.clients.producer.ProducerRecord")
    .mixin(classOf[HasContext.VolatileMixinWithInitializer])
}
