package de.fellows.utils.entities.secure

import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import de.fellows.utils.JsonFormats.singletonFormat
import de.fellows.utils.communication.{ServiceDefinition, ServiceError, ServiceException}
import de.fellows.utils.logging.StackrateLogging
import play.api.libs.json.Format

/** Wraps a Persistent entity in a Team-Owned container. This entity forces every command to be checked with the
  * #isAllowed mathod. A well designed Command carries all information to carry out the check.
  *
  * @param service
  * @tparam T
  */
abstract class SecureTeamEntity[T](implicit val service: ServiceDefinition) extends PersistentEntity
    with StackrateLogging {
  override final type State = T

  override final def behavior: Behavior = {
    state =>
      val x = entityBehavior(state)

      type CommandHandler = PartialFunction[(Command, CommandContext[Any], State), Persist]
      new Actions(
        x.eventHand<PERSON>,
        x.commandHandlers.map { (x: (Class[_], CommandHandler)) =>
          val delegate: CommandHandler = {
            case params @ (a, ctx, b) =>
              if (isAllowed(a, state)) {
                x._2(params)
              } else {
                logger.error(s"failed to apply command ${a} on ${state}")
                ctx.commandFailed(new ServiceException(EntityAccessDenied))
                ctx.done
              }
          }

          (x._1, delegate)
        }
      )
  }

  def entityBehavior(state: T): Actions

  def isAllowed(a: Command, s: T): Boolean
}

object EntityNotFound extends ServiceError(1, "Entity Does Not Exist", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[EntityNotFound.type] = singletonFormat(EntityNotFound)
}

object EntityAccessDenied
    extends ServiceError(1, "Access to this Entity is denied", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[EntityNotFound.type] = singletonFormat(EntityNotFound)
}
