package de.fellows.utils.apidoc

import com.google.common.reflect.TypeToken
import io.swagger.v3.core.util.AnnotationsUtils
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.{Schema => ASchema}
import io.swagger.v3.oas.models.media._
import play.api.libs.json.JsObject

import java.lang.reflect.{ParameterizedType, Type}
import java.time.Instant
import java.util.{Date, UUID}
import scala.reflect.runtime.universe
import scala.reflect.runtime.universe.typeOf

case class SchemaContainer[T](schema: Schema[T], references: Seq[Type], required: Boolean = true)

class SchemaGenerator(t: Type, symbol: Option[scala.reflect.runtime.universe.Type] = None)(implicit
    val loader: ClassLoader
) {

  val mirror = universe.runtimeMirror(loader)

  def generateDefinition(): SchemaContainer[Object] = {
    val mainSchema = new ObjectSchema()

    scala.reflect.runtime.universe.typeTag

    val clz = TypeToken.of(t).getRawType

    val checks =
      Option(clz.getAnnotation(classOf[StackrateAPIObject])).map(x => x.proxy().toSeq).filter(_.nonEmpty).getOrElse(Seq(
        clz
      ))

    val fields = checks.flatMap(_.getDeclaredFields)

    val newTypes = fields.flatMap { field =>
      val parameter = Option(field.getAnnotation(classOf[Parameter]))
      val m         = universe.runtimeMirror(loader)
      val tests     = checks.map(m.classSymbol)
      val ffield    = tests.flatMap(_.info.decls.toSeq).find(_.name.toString == field.getName)

      if (!parameter.exists(_.hidden())) {
        val schema = parameter.flatMap { param =>
          Option(param.array()).flatMap { sch =>
            val x = AnnotationsUtils.getArraySchema(sch, null).map(s =>
              SchemaContainer(s, Seq())
            )
            if (x.isEmpty) {
              None
            } else {
              Some(x.get)
            }

          } orElse
            Option(param.schema()).map { sch =>
              def v() = {
                val s = AnnotationsUtils.getSchemaFromAnnotation(sch, null).orElse(null)
                Option(s) match {
                  case Some(value) => SchemaContainer(value, Seq())
                  case None        => new SchemaGenerator(field.getGenericType, ffield.map(_.info)).generate(parameter)
                }

              }

              Option(sch.implementation()) match {
                case None =>
                  v()
                case Some(some) =>
                  if (some == classOf[Void]) {
                    v()
                  } else {
                    val m    = universe.runtimeMirror(loader)
                    val smbl = m.classSymbol(some)
                    val x    = new SchemaGenerator(some, Option(smbl.info)).generate(None)
                    // TODO merge parameters
                    x.copy(required = param.required())
                  }
              }

            }
        }
          .getOrElse({
            new SchemaGenerator(field.getGenericType, ffield.map(_.info)).generate(parameter)
          })

        parameter.foreach { aParam =>
          ApiGenUtils.emptyString(aParam.name()).foreach(schema.schema.name)
          ApiGenUtils.emptyString(aParam.description()).map(_.stripMargin).foreach(schema.schema.description)
          ApiGenUtils.emptyString(aParam.example()).foreach(schema.schema.example)

          val dep = aParam.deprecated()
          if (dep) {
            schema.schema.deprecated(dep)
          }
        }

        val deprecated =
          Option(field.getAnnotation(classOf[Deprecated]))

        deprecated.foreach(d => schema.schema.deprecated(true))

        mainSchema.addProperties(field.getName, schema.schema)

        if (schema.required) {
          mainSchema.addRequiredItem(field.getName)
        }

        schema.references

      } else {
        Seq()
      }

    }.distinct.toSeq

    new SchemaContainer[Object](mainSchema, newTypes)
  }

  def generate1(param: Option[Parameter])(objectHandler: Type => SchemaContainer[_]): SchemaContainer[_] = {
    val token = TypeToken.of(t)
    val generatedType = {
      val pSchema =
        param.map(_.schema()).flatMap(s => Option(AnnotationsUtils.getSchemaFromAnnotation(s, null).orElse(null)))
      if (pSchema.isDefined) {
        SchemaContainer(pSchema.get, Seq())
      } else if (token.isSubtypeOf(classOf[Seq[_]])) {

        def extractSuperType =
          token
            .asInstanceOf[TypeToken[Seq[_]]]
            .getSupertype(classOf[Seq[_]])
            .getType
            .asInstanceOf[ParameterizedType]
            .getActualTypeArguments()(0)

        val (nestedType, ex) = symbol.map(_.resultType).filter(t => t <:< typeOf[Seq[_]]) match {
          case Some(value) =>
            val t2 = value.typeArgs.head
            loadClass(t2) match {
              case Some(value) => (value, Some(t2))
              case None        => (extractSuperType, Some(t2))
            }

          case None =>
            (extractSuperType, None)
        }

        val s = new ArraySchema()

        val nestedSchema = new SchemaGenerator(nestedType, ex).generate(param)

        s.setItems(nestedSchema.schema)
        SchemaContainer(s, nestedSchema.references)
      } else if (token.isSubtypeOf(classOf[Map[_, _]])) {

        def extractType =
          token
            .asInstanceOf[TypeToken[Map[_, _]]]
            .getSupertype(classOf[Map[_, _]])
            .getType
            .asInstanceOf[ParameterizedType]
            .getActualTypeArguments()(1)

        val (nestedType, ex) = symbol.map(_.resultType).filter(t => t <:< typeOf[Map[_, _]]) match {
          case Some(value) =>
            val t2       = value.typeArgs(1)
            val resolved = loadClass(t2)
            resolved match {
              case Some(value) => (value, Some(t2))
              case None        => (extractType, Some(t2))
            }

          case None =>
            (extractType, None)
        }

        val s = new MapSchema()

        //        val nestedType = clazz.asInstanceOf[ParameterizedType].getActualTypeArguments()(1)
        val nestedSchema = new SchemaGenerator(nestedType).generate(None)

        s.additionalProperties(nestedSchema.schema)

        SchemaContainer(s, nestedSchema.references)
      } else if (token.isSubtypeOf(classOf[JsObject])) {
        val s = new MapSchema()
        s.additionalProperties(new StringSchema())

        SchemaContainer(s, Seq())
      } else if (token.isSubtypeOf(classOf[Instant]) || token.isSubtypeOf(classOf[Date])) {
        val s = new DateSchema()
        SchemaContainer(s, Seq())
      } else if (token.isSubtypeOf(classOf[Integer]) || token.getRawType == java.lang.Integer.TYPE) {
        val s = new IntegerSchema()
        SchemaContainer(s, Seq())
      } else if (token.getRawType == java.lang.Long.TYPE) {
        SchemaContainer(new Schema().`type`("number").format("long"), Seq())
      } else if (token.getRawType == java.lang.Double.TYPE) {
        SchemaContainer(new Schema().`type`("number").format("double"), Seq())
      } else if (token.isSubtypeOf(classOf[Number])) {
        val s = new NumberSchema()
        SchemaContainer(s, Seq())
      } else if (token.isSubtypeOf(classOf[Boolean]) || token.getRawType == java.lang.Boolean.TYPE) {
        val s = new BooleanSchema()
        SchemaContainer(s, Seq())
      } else if (token.isSubtypeOf(classOf[UUID])) {
        val s = new UUIDSchema()
        SchemaContainer[UUID](s, Seq())
      } else if (token.isSubtypeOf(classOf[String])) {
        val s = new StringSchema()
        SchemaContainer[String](s, Seq())
      } else if (token.isSubtypeOf(classOf[Option[_]])) {
        def extractType =
          token.asInstanceOf[TypeToken[Option[_]]]
            .getSupertype(classOf[Option[_]])
            .getType
            .asInstanceOf[ParameterizedType]
            .getActualTypeArguments()(0)

        val (paramType, ex) = symbol.map(_.resultType).filter(t => t <:< typeOf[Option[_]]) match {
          case Some(value) =>
            val t2       = value.typeArgs.head
            val resolved = loadClass(t2)

            resolved match {
              case Some(value) => (value, Some(t2))
              case None        => (extractType, Some(t2))
            }
          case None =>
            (extractType, None)
        }

        val sch = new SchemaGenerator(paramType, ex).generate(None).copy(required = false)
        sch
        //      val res = objectHandler(paramType)
        //      res
      } else {
        objectHandler(t)
        //      println(s"type is some object")
        //      val s = new ObjectSchema();
        //      s.set$ref(SchemaGenerator.ref(t))
        //
        //      SchemaContainer(s, Seq(t))
      }
    }

    param.foreach { aParam =>
      ApiGenUtils.emptyString(aParam.name()).foreach(generatedType.schema.name)
      ApiGenUtils.emptyString(aParam.description()).map(_.stripMargin).foreach(generatedType.schema.description)
      ApiGenUtils.emptyString(aParam.example()).foreach(generatedType.schema.example)
    }

    generatedType

  }

  val primitives: Map[String, Class[_]] = Seq(
    "boolean" -> classOf[Boolean],
    "byte"    -> classOf[Byte],
    "short"   -> classOf[Short],
    "int"     -> classOf[Integer],
    "long"    -> classOf[Long],
    "float"   -> classOf[Float],
    "double"  -> classOf[Double],
    "char"    -> classOf[Character]
  ).toMap

  private def loadClass(t2: universe.Type): Option[Class[_]] =
    try {
      val c = mirror.runtimeClass(t2)
      Some(if (c.isPrimitive) {
        primitives(c.getName)
      } else {
        loader.loadClass(c.getName)
      })
    } catch {
      case x: NoClassDefFoundError => None
    }

  def generate(param: Option[Parameter]): SchemaContainer[_] = {
    val sch = generate1(param) { t =>
      val clz = TypeToken.of(t).getRawType

      val schemaAnnotation = Option(clz.getAnnotation(classOf[ASchema]))
      val fromAnnotation =
        schemaAnnotation.flatMap(s => Option(AnnotationsUtils.getSchemaFromAnnotation(s, null).orElse(null)))
      if (fromAnnotation.isDefined) {
        val schema = fromAnnotation.get
        SchemaContainer(schema, Seq())
      } else {
        val clazz = schemaAnnotation
          .map(_.implementation()).getOrElse(t)

        val s = new ObjectSchema();
        s.set$ref(SchemaGenerator.ref(clazz))

        // create an allOf composition with only this element.
        // this trick allows further properties as siblings
        val composeHelper =
          new ComposedSchema()
            .addAllOfItem(s)

        SchemaContainer(composeHelper, Seq(clazz))
      }

    }

    sch
  }
}

object SchemaGenerator {
  def ref(t: Type): String =
    t match {
      case x: ParameterizedType => s"#/components/schemas/${schemaName(x.getRawType)}"
      case a                    => s"#/components/schemas/${schemaName(a)}"
    }

  def schemaName(t: Type): String = {

    val x = (t match {
      case x: Class[_]          => x.getSimpleName
      case x: ParameterizedType => schemaName(x.getRawType) // [${x.getActualTypeArguments.mkString(",")}]
      case x                    => x.getTypeName.split('.').last.replace("$", "")
    })
    x
  }

}
