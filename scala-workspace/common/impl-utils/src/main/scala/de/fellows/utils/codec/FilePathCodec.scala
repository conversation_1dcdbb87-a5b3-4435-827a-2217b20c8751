package de.fellows.utils.codec

import java.nio.ByteBuffer
import com.datastax.driver.core.{ProtocolVersion, TypeCodec, UDTValue, UserType}
import de.fellows.utils.FilePath

class FilePathCodec(cdc: TypeCodec[UDTValue]) extends TypeCodec[FilePath](cdc.getCqlType, classOf[FilePath]) {

  def to(value: FilePath): UDTValue =
    if (value == null) null
    else
      cdc.getCqlType.asInstanceOf[UserType].newValue()
        .setString("team", value.team)
        .setString("root", value.fsroot)
        .setString("resource", value.resource)
        .setString("base", value.base)
        .setString("subpath", value.subPath.getOrElse(Seq.empty).mkString("/"))
        .setString("name", value.filename)

  def from(value: UDTValue): FilePath =
    if (value == null) null
    else {
      FilePath(
        fsroot = value.getString("root"),
        team = value.getString("team"),
        resource = value.getString("resource"),
        base = value.getString("base"),
        subPath = Option(value.getString("subpath")).map(_.split("/")),
        filename = value.getString("name")
      )
    }

  override def serialize(value: FilePath, protocolVersion: ProtocolVersion): ByteBuffer =
    cdc.serialize(to(value), protocolVersion)

  override def deserialize(bytes: ByteBuffer, protocolVersion: ProtocolVersion): FilePath =
    from(cdc.deserialize(bytes, protocolVersion))

  override def parse(value: String): FilePath =
    if (value == null || value.isEmpty) null
    else from(cdc.parse(value))

  override def format(value: FilePath): String =
    if (value == null) null
    else cdc.format(to(value))
}
