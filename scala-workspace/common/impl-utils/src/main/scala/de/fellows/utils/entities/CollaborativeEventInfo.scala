package de.fellows.utils.entities

import de.fellows.utils.security.{GenericTokenContent, TokenContent}
import play.api.libs.json.{Format, Json}

import java.time.Instant

case class CollaborativeEventInfo(time: Instant, eventCreator: String)

object CollaborativeEventInfo {
  implicit val format: Format[CollaborativeEventInfo] = Json.format[CollaborativeEventInfo]

  def apply(t: GenericTokenContent): CollaborativeEventInfo =
    new CollaborativeEventInfo(Instant.now(), t.getUserId)

  def apply(user: String): CollaborativeEventInfo =
    new CollaborativeEventInfo(Instant.now(), user)
}
