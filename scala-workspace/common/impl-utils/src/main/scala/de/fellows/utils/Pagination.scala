package de.fellows.utils

import akka.stream.Materializer
import akka.stream.scaladsl.Sink
import com.datastax.driver.core.{Row, Statement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession

import scala.concurrent.{ExecutionContext, Future}

object Pagination {
  def paginationAsync[T](page: Option[Int], pagesize: Option[Int], filter: (T => Boolean), conv: ((Row) => Future[Option[T]]))(stmt: => Statement)
                        (implicit session: CassandraSession, materializer: Materializer, exc: ExecutionContext): Future[PaginationListResult[T]] = {

    paginationWithFilterAsync(page, pagesize, (x: T) => Future.successful(filter(x)), conv)(stmt)
  }

  def paginationWithClientSorting[T](page: Option[Int], pagesize: Option[Int], filter: (T => Future[Boolean]), conv: ((Row) => Future[Option[T]]), sort: Seq[T] => Seq[T])
                                    (stmt: => Statement)
                                    (implicit session: CassandraSession, materializer: Materializer, exc: ExecutionContext): Future[PaginationListResult[T]] = {
    val res = session.selectAll(stmt).flatMap(x => Future.sequence(x.map(_x => conv(_x))).map(_.flatten))

    res.flatMap(results => {
      if (page.isDefined) {
        val p = page.get - 1
        val ps = pagesize.getOrElse(100)

        Future.sequence(results.map(r => filter(r).map(b => r -> b)))
          .map(filtered => {
            PaginationListResult(sort(filtered.filter(_._2).map(_._1)).slice(p * ps, p * ps + ps), results.length)
          })
      } else {
        Future.sequence(results.map(r => filter(r).map(b => r -> b)))
          .map(filtered => {
            PaginationListResult(sort(filtered.filter(_._2).map(_._1)), results.length)
          })
      }
    })
  }

  def paginationWithFilterAsync[T](page: Option[Int], pagesize: Option[Int],
                                   filter: (T => Future[Boolean]),
                                   conv: ((Row) => Future[Option[T]]),
                                  )(stmt: => Statement)
                                  (implicit session: CassandraSession, materializer: Materializer, exc: ExecutionContext): Future[PaginationListResult[T]] = {


    if (pagesize.isDefined) {
      val ps = pagesize.getOrElse(100)
      val p = page.getOrElse(1) - 1

      val statement = stmt

      val source = session.select(
        statement
      )


      val result = source.mapAsync(1)(_x =>
        conv(_x)
          .flatMap{
            case None => Future.successful(false, None)
            case Some(item) => filter(item).map(_ -> Some(item))
          } // resolve the item and the filter flag asynchronously
      )
        .filter(_x => _x._1 && _x._2.isDefined) // filter by the (now available) filter flag
        .map(_._2.get) // drop the flag, use the item
        .runWith(Sink.seq)

      for {
        list <- result.map(res => {
          res
            .drop(p * ps) // pagination: page
            .take(ps) // pagination: pagesize
        })
        resultCount <- result.map(_.length)
      } yield {
        PaginationListResult(list, resultCount)
      }
    } else {
      session.selectAll(stmt).flatMap(x => Future.sequence(
        x.map(_x => {
          conv(_x).flatMap{
            case None => Future.successful(false, None)
            case Some(item) => filter(item).map(_ -> Some(item))
          } // resolve the item and the filter flag asynchronously
        })
      ).map(_.filter(x => x._1 && x._2.isDefined))
        .map(_.map(_._2.get)).map(rows => PaginationListResult(rows, rows.length)))
    }


  }

  def pagination[T](page: Option[Int], pagesize: Option[Int], filter: (T => Boolean), conv: ((Row) => T))(stmt: => Statement)
                   (implicit session: CassandraSession, materializer: Materializer, exc: ExecutionContext): Future[PaginationListResult[T]] = {
    if (pagesize.isDefined) {
      val ps = pagesize.getOrElse(100)
      val p = page.getOrElse(1) - 1

      val statement = stmt

      statement.setFetchSize(ps)
      val source = session.select(
        statement
      )
      val result = source.map(_x => conv(_x)).filter(_x => filter(_x)).runWith(Sink.seq)
      for {
        list <- result.map(res => {
          res
            .drop(p * ps) // pagination: page
            .take(ps) // pagination: pagesize
        })
        resultCount <- result.map(_.length)
      } yield {
        PaginationListResult(list, resultCount)
      }
    } else {
      session.selectAll(stmt).map(_.map(_x => conv(_x))).map(rows => PaginationListResult(rows, rows.length))
    }
  }

}
