package de.fellows.utils.internal

import java.nio.file.Path
import scala.io.Source

object CSVUtils {
  def readCSV(p: Path, separator: String = ","): Seq[Map[String, String]] =
    FileReader.withResource(Source.fromFile(p.toFile)) { s =>
      val lines  = s.getLines().toSeq
      val header = lines.head.split(separator)

      lines.tail.map { line =>
        val values = line.split(separator)

        (header zip values).toMap
      }
    }
}
