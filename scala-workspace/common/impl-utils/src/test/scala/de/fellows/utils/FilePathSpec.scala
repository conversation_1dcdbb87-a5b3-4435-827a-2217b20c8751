package de.fellows.utils

import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec

class FilePathSpec extends AsyncWordSpec with BeforeAndAfterAll with Matchers {
  "FilePath" should {
    "work " in {
      val parsed   = FilePath("/tmp/", "/team/re/sou/rce/base/f.png")
      val expected = FilePath("/tmp/", "team", "re/sou/rce", "base", None, "f.png")

      parsed shouldBe expected
      parsed.toPath shouldBe expected.toPath

    }
    "handle additional / " in {
      val parsed   = FilePath("/tmp/", "/team/re/sou//rce/base/f.png")
      val expected = FilePath("/tmp/", "/team/", "/re/sou/rce/", "/base/", None, "/f.png")

      //      parsed shouldBe expected
      parsed.toPath shouldBe expected.toPath

    }
    "handle missing / " in {
      val parsed   = FilePath("/tmp/", "/team/re/sou/rce/base/f.png")
      val expected = FilePath("tmp", "/team/", "/re/sou/rce/", "/base/", None, "/f.png")

      //      parsed shouldBe expected
      parsed.toPath shouldBe expected.toPath

    }
    "handle path " in {
      val parsed   = FilePath("/tmp/team/re/sou/rce/base/f.png")
      val expected = FilePath("/tmp", "team", "re/sou/rce", "base", None, "f.png")

      parsed shouldBe expected
      //      parsed.toPath shouldBe expected.toPath

    }

  }
}
