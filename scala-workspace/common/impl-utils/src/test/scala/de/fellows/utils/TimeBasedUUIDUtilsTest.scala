package de.fellows.utils

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import com.typesafe.config.ConfigFactory
import com.datastax.driver.core.utils.UUIDs

class TimeBasedUUIDUtilsTest extends AnyFlatSpec with should.Matchers {
  "getTimeBasedUUID" should "return a TimeBasedUUID" in {
    val timeBasedUUID = TimeBasedUUIDUtils.getCuttOff(1747759678000L, ConfigFactory.load())
    UUIDs.unixTimestamp(timeBasedUUID.value) shouldBe 1747759678000L
  }

  it should "return a TimeBasedUUID with the correct time" in {
    val timeBasedUUID = TimeBasedUUIDUtils.getCuttOff(1432136878L, ConfigFactory.load())
    UUIDs.unixTimestamp(timeBasedUUID.value) shouldBe 1552608000000L
  }
}
