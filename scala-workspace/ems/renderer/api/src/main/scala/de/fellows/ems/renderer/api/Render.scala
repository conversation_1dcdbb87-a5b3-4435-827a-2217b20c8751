package de.fellows.ems.renderer.api

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.{Format, GerberFile}
import de.fellows.utils.FilePath
import de.fellows.utils.meta._
import play.api.libs.json
import play.api.libs.json.Json

import java.util.UUID

case class Render(
    fileID: Option[GerberFile],
    graphics: Map[String, FilePath],
    preview: Option[FilePath],
    format: Option[Format],
    drills: Seq[FilePath] = Seq(),
    tree: Option[FilePath] = None,
    distances: Option[Seq[DistanceDescription]] = None,
    status: Option[RenderStatus] = Some(RenderStatus.STATUS_EMPTY),
    renderMessages: Option[Seq[RenderLogMessage]] = None,
    meta: Option[MetaInfo] = None
)

object Render {
  implicit val format: json.Format[Render] = Json.format

  def fileID(assRef: AssemblyReference, f: GerberFile): String = s"${assRef.version}|${f.name}"

  def fileID(version: UUID, fileName: String): String = s"$version|$fileName"

}
