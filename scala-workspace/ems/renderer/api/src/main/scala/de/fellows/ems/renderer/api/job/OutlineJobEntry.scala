package de.fellows.ems.renderer.api.job

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.utils.redislog.jobs.JobType.JobTypeOutline
import de.fellows.utils.redislog.jobs.{JobEntry, JobType}
import play.api.libs.json.{Format, Json}

case class OutlineJobEntry(assembly: AssemblyReference) extends JobEntry {
  override val jobType: JobType = JobTypeOutline
}

object OutlineJobEntry {
  implicit val format: Format[OutlineJobEntry] = Json.format[OutlineJobEntry]
}
