package de.fellows.ems.renderer.api

import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api._
import de.fellows.ems.pcb.model.graphics.tree.TraceWidthDescription
import de.fellows.ems.pcb.model.{Graphic, RenderedPreviewFile, SpecificationPreviews}
import de.fellows.ems.renderer.api.Streams.RenderStreamMessage
import de.fellows.utils.FilePath
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.communication.ServiceExceptionSerializer
import de.fellows.utils.internal.FileType
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.info.Info

import java.util.UUID

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate Renderer API"
  )
)
trait RendererService extends Service with StackrateServiceAPI {

  val subPath = "ems/renderer"

  val basePath                 = s"/api/$subPath"
  val internalBasePath         = s"/internal/$subPath"
  val specificBasePath         = s"$basePath/assemblies/:assembly/versions/:version"
  val specificSharedBasePath   = s"$basePath/shares/:share"
  val internalSpecificBasePath = s"$internalBasePath/:version"

  @StackrateApi
  def analyzeLayers(assembly: UUID, version: UUID, file: String): ServiceCall[NotUsed, Done]

  def _getRender(version: UUID, file: String): ServiceCall[NotUsed, Render]

  def getGraphics(assembly: UUID, version: UUID, file: String): ServiceCall[NotUsed, Map[String, Graphic]]

  def getSharedGraphics(share: UUID, file: String): ServiceCall[NotUsed, Map[String, Graphic]]

  @StackrateApi
  def analyzeBoard(assembly: UUID, version: UUID): ServiceCall[NotUsed, Done]

  def _analyzeBoard(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Done]

  @StackrateApi
  def getRenderStatus(assembly: UUID, version: UUID, file: String): ServiceCall[NotUsed, RenderResult]

  @StackrateApi
  def renderUpdates(
      assembly: UUID,
      version: UUID,
      k: String
  ): ServiceCall[NotUsed, Source[RenderStreamMessage, NotUsed]]

  @StackrateApi
  def getAllAnnularRings(assembly: UUID, version: UUID): ServiceCall[NotUsed, AnnularRingsDescription]

  def _getAllAnnularRings(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, AnnularRingsDescription]

  @StackrateApi
  def getDistance(
      assembly: UUID,
      version: UUID,
      file: String,
      from: Int,
      to: Int
  ): ServiceCall[NotUsed, DistanceDescription]

  @StackrateApi
  def getDistances(
      assembly: UUID,
      version: UUID,
      file: String,
      from: Int
  ): ServiceCall[NotUsed, Seq[DistanceDescription]]

  @StackrateApi
  def getDistanceByCoordinates(
      assembly: UUID,
      version: UUID,
      file: String,
      fromX: Double,
      fromY: Double,
      toX: Double,
      toY: Double
  ): ServiceCall[NotUsed, DistanceDescription]

  @StackrateApi
  def getDistancesByCoordinates(
      assembly: UUID,
      version: UUID,
      file: String,
      fromX: Double,
      fromY: Double
  ): ServiceCall[NotUsed, Seq[DistanceDescription]]

  @StackrateApi
  def getAllDistances(
      assembly: UUID,
      version: UUID,
      file: String,
      max: Option[Double]
  ): ServiceCall[NotUsed, Seq[DistanceDescription]]

  def _getAllDistances(
      team: String,
      assembly: UUID,
      version: UUID,
      file: String,
      max: Option[Double]
  ): ServiceCall[NotUsed, Seq[DistanceDescription]]

  @StackrateApi
  def getAllTracewidths(assembly: UUID, version: UUID, file: String): ServiceCall[NotUsed, Seq[TraceWidthDescription]]

  def _getAllTracewidths(
      team: String,
      assembly: UUID,
      version: UUID,
      file: String
  ): ServiceCall[NotUsed, Seq[TraceWidthDescription]]

  @StackrateApi
  def getAllBoardDistances(
      assembly: UUID,
      version: UUID,
      max: Option[Double],
      files: Seq[String]
  ): ServiceCall[NotUsed, Map[String, Seq[DistanceDescription]]]

  @StackrateApi
  def renderPreview(assembly: UUID, version: UUID, specification: Option[UUID]): ServiceCall[NotUsed, String]

  @StackrateApi
  def renderSpecificationPreview(
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[NotUsed, SpecificationPreviews]

  /** synchronously renders the specification and returns the rendered files
    */
  def _renderSpecificationPreview(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[SpecificationRenderRequest, RenderedPreviewFile]

  @StackrateApi
  def createTraceOutline(assembly: UUID, version: UUID): ServiceCall[OutlineTraceCreation, Done]

  @StackrateApi
  def createBoxOutline(assembly: UUID, version: UUID): ServiceCall[OutlineCreation, Done]

  def shutdownRenderer(): ServiceCall[NotUsed, Boolean]

  @StackrateApi
  def kiMatchFile(): ServiceCall[FilePath, Seq[FileType]]

  def _renderLayerstack(): ServiceCall[LayerstackRendererRequest, FilePath]

  def renderAdded(): Topic[RenderAdded]

  def treeAdded(): Topic[QuadTreeAdded]

  def previewAdded(): Topic[PreviewAdded]

  override def descriptor: Descriptor = {
    import Service._
    withDocumentation(subPath)(
      named("renderer")
        .withCalls(
          //        restCall(Method.POST, s"$basePath/convert?format&width&height", convertSVG _),
          restCall(Method.POST, s"/shutdown", shutdownRenderer _),
          restCall(Method.POST, s"$internalBasePath/matcher/ki", kiMatchFile _),
          restCall(Method.POST, s"$internalBasePath/layerstack/render", _renderLayerstack _),
          restCall(Method.POST, s"$internalSpecificBasePath/files/:file", _getRender _),
          restCall(Method.GET, s"$specificBasePath/distances?max&files", getAllBoardDistances _),
          restCall(Method.PUT, s"$specificBasePath/files/:file/meta", analyzeLayers _),
          restCall(Method.GET, s"$specificBasePath/files/graphics?file", getGraphics _),
          restCall(Method.GET, s"$specificSharedBasePath/files/graphics?file", getSharedGraphics _),
          restCall(Method.PUT, s"$specificBasePath/meta", analyzeBoard _),
          restCall(
            Method.PUT,
            s"$internalBasePath/teams/:team/assemblies/:assembly/versions/:version/meta",
            _analyzeBoard _
          ),
          restCall(Method.GET, s"$specificBasePath/files/:file/status", getRenderStatus _),
          restCall(Method.GET, s"$specificBasePath/updates?k", renderUpdates _),
          restCall(Method.POST, s"$specificBasePath/outline/create/trace", createTraceOutline _),
          restCall(Method.POST, s"$specificBasePath/outline/create/box", createBoxOutline _),
          restCall(Method.PUT, s"$specificBasePath/preview?specification", renderPreview _),
          restCall(
            Method.PUT,
            s"$specificBasePath/specifications/:specification/preview",
            renderSpecificationPreview _
          ),
          restCall(
            Method.PUT,
            s"$internalBasePath/team/:team/assemblies/:assembly/versions/:version/specifications/:specification/preview",
            _renderSpecificationPreview _
          ).withCircuitBreaker(CircuitBreaker.identifiedBy("render")),
          restCall(Method.GET, s"$specificBasePath/annularRings", getAllAnnularRings _),
          restCall(
            Method.GET,
            s"$internalBasePath/teams/:team/assemblies/:assembly/versions/:version/annularRings",
            _getAllAnnularRings _
          ),
          restCall(Method.GET, s"$specificBasePath/files/:file/tracewidths", getAllTracewidths _),
          restCall(
            Method.GET,
            s"$internalBasePath/teams/:team/assemblies/:assembly/versions/:version/files/:file/tracewidths",
            _getAllTracewidths _
          ),
          restCall(Method.GET, s"$specificBasePath/files/:file/distances/:from/:to", getDistance _),
          restCall(Method.GET, s"$specificBasePath/files/:file/distances/:from", getDistances _),
          restCall(Method.GET, s"$specificBasePath/files/:file/distances?max", getAllDistances _),
          restCall(
            Method.GET,
            s"$internalBasePath/teams/:team/assemblies/:assembly/versions/:version/files/:file/distances?max",
            _getAllDistances _
          ),
          restCall(
            Method.GET,
            s"$specificBasePath/files/:file/distancesByCoordinates/:fromX/:fromY/:toX/:toY",
            getDistanceByCoordinates _
          ),
          restCall(
            Method.GET,
            s"$specificBasePath/files/:file/distancesByCoordinates/:fromX/:fromY",
            getDistancesByCoordinates _
          )
        )
        .withTopics(
          topic(RendererService.RENDER_ADDED, renderAdded()),
          topic(RendererService.TREE_ADDED, treeAdded()),
          topic(RendererService.PREVIEW_ADDED, previewAdded())
        )
        .withAcls(
          ServiceAcl(pathRegex = Some(s"$basePath/.*")),
          ServiceAcl(pathRegex = Some(s"/files/$subPath/.*"))
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )
  }

}

object RendererService {
  val VERSION = "v1.3"

  val FILE_TYPE     = s"domain.ems.pcb.type-$VERSION"
  val RENDER_ADDED  = s"domain.ems.pcb.renderer.added-$VERSION"
  val TREE_ADDED    = s"domain.ems.pcb.renderer.treeadded-$VERSION"
  val PREVIEW_ADDED = s"domain.ems.pcb.renderer.preview-$VERSION"
  val DRILLS_ADDED  = s"domain.ems.pcb.renderer.drills-$VERSION"
}
