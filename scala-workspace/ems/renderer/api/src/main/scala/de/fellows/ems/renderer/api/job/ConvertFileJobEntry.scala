package de.fellows.ems.renderer.api.job

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.GerberFile
import de.fellows.utils.redislog.jobs.{JobEntry, JobType}
import play.api.libs.json.{Format, Json}

case class ConvertFileJobEntry(ass: AssemblyReference, file: GerberFile) extends JobEntry {
  override val jobType: JobType = JobType.JobTypeConvertNative
}

object ConvertFileJobEntry {
  implicit val format: Format[ConvertFileJobEntry] = Json.format[ConvertFileJobEntry]
}
