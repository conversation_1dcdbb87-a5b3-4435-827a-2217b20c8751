package de.fellows.ems.renderer.impl.gerber

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.layerstack.api.{LayerStack, MaterialProperties}
import de.fellows.ems.layerstack.impl.LayerstackServiceImpl
import de.fellows.ems.layerstack.impl.layerstack.LayerStackMatcher
import de.fellows.ems.layerstack.impl.standard.StandardLayerstackComponentsRepository
import de.fellows.ems.pcb.model.graphics.tree.PCBLayerInternalData
import de.fellows.ems.pcb.model.{GerberFile, Graphic, GraphicElement, LayerConstants, RenderConstants}
import de.fellows.ems.renderer.impl.PCBListener.{renderExcellonDrills, renderGerberDrills}
import de.fellows.ems.renderer.impl.analysis.tasks.{
  ClearanceAnalysisTask,
  CopperDensityAnalyzerTask,
  CopperDensityContext,
  TracewidthAnalysisTask,
  WeightTask,
  WeightTaskContext
}
import de.fellows.ems.renderer.impl.analysis.{
  <PERSON>ular<PERSON>ing<PERSON>ontext,
  AnnularRingStackAnalyzer,
  GerberBoardAnalyzer,
  HoleListWithFile,
  Reconciliation
}
import de.fellows.ems.renderer.impl.outline.OutlineCandidateBuilder
import de.fellows.ems.renderer.impl.pool.{RenderTask, RendererCoordinator}
import de.fellows.ems.renderer.impl.progress.RenderProgressSink
import de.fellows.utils.internal.{CSVUtils, FileType}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta.{DecimalProperty, MetaInfo, Property}
import de.fellows.utils.spi.GerberFileTypeDetector
import de.fellows.utils.{FilePath, PathUtils}
import play.api.libs.json.Json

import java.nio.file.attribute.BasicFileAttributes
import java.nio.file.{FileSystems, FileVisitResult, Files, Path, Paths, SimpleFileVisitor, StandardOpenOption}
import java.time.Instant
import java.util.UUID
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.math.BigDecimal.RoundingMode
import scala.util.{Failure, Try}

object AnalysisHelper extends StackrateLogging {

  val materialreporter = new UnitTestMaterialReporter

  def glob(path: Path, pattern: String): Seq[Path] = {
    val matchesList = Seq.newBuilder[Path]
    val fs          = FileSystems.getDefault
    val matcher     = fs.getPathMatcher(pattern)

    val visitor = new SimpleFileVisitor[Path] {
      override def visitFile(file: Path, attrs: BasicFileAttributes): FileVisitResult = {
        val _path = path.relativize(file)
        if (matcher.matches(_path)) {
          matchesList += file
        }
        FileVisitResult.CONTINUE
      }
    }
    Files.walkFileTree(path, visitor)

    matchesList.result()
  }

  def analyzeProjects(
      testName: String,
      assemblyFolder: Path,
      project: Map[String, String],
      propertiesToCheck: Seq[(String, String)],
      testrun: Instant
  )(implicit
      ectx: ExecutionContext
  ): Seq[(String, Option[Any], Option[Any])] = {

    val layerstackDefinitions =
      Await.result(
        StandardLayerstackComponentsRepository.getTeamLayerstacks("", materialreporter),
        1 minute
      )

    val start = System.currentTimeMillis()
    val expected =
      CSVUtils.readCSV(assemblyFolder.resolve("files.csv"), separator = ";")

    val assRef = AssemblyReference(
      "",
      UUID.randomUUID(),
      Some(assemblyFolder.toFile.getName),
      UUID.randomUUID()
    )

    val expectedAnalysis = project

    val files = expected.map { x =>
      val filename = x("filename")
      val filetype = x("filetype")
      val index    = x.get("index").map(_.toInt)
      val path     = FilePath(glob(assemblyFolder, s"glob:files/*/$filename").head.toString)
      // FilePath(assemblyFolder.resolve(s"files/$filename").toString)
      GerberFile(
        UUID.randomUUID(),
        filename,
        path,
        toFiletype(filetype, Some(Files.probeContentType(path.toJavaPath)), index).getOrElse(FileType.UNKNOWN),
        hash = None
      )
    }.toSeq

    val copperfiles = files.filter(gf => LayerConstants.COPPER.contains(gf.fType.fileType))
    val drillfiles  = files.filter(gf => LayerConstants.DRILLS.contains(gf.fType.fileType))

    val eventualAnalyzedCopperFiles = files.flatMap { gf =>
      if (copperfiles.contains(gf)) {
        analyzeCopperFile(assRef, gf, testrun)
      } else {
        None
      }
    }

    println("wait for coordinator to settle...")

    RendererCoordinator.waitForZero()
    println("done")

    val analyzedCopperFiles = eventualAnalyzedCopperFiles.map { x =>
      x._2.waitFor()
      println(s"get results for copper file ")

      val meta =
        Seq(x._3._distances, x._3._tracewidth).foldLeft(x._1.metaInfo.getOrElse(MetaInfo())) { (b, a) =>
          a match {
            case Some(value) => b + value
            case None        => b
          }
        }

      (x._1.copy(metaInfo = Some(meta)) -> x._3)
    }.toMap

    val possibleFilesWithOutline =
      (
        files.filter(gf => LayerConstants.OUTLINE.contains(gf.fType.fileType)) ++
          files.filter(gf => LayerConstants.MECHANICAL.contains(gf.fType.fileType)) ++
          files.filter(gf => LayerConstants.KEEP_OUT.contains(gf.fType.fileType)) ++
          files.filter(gf => gf.fType.category == LayerConstants.Categories.odb)
      )
        .filter(_.name != "generated-outline.gbr")

    val coppergraphics = analyzedCopperFiles.flatMap(x => x._2._data).toSeq
    val copperFiles    = analyzedCopperFiles.keys.toSeq

    val candidates = Await.result(
      new OutlineCandidateBuilder()
        .build(possibleFilesWithOutline, copperFiles, coppergraphics, assRef),
      10 minutes
    )

    logger.info(s"found ${candidates.length} outline candidates, select best")

    import de.fellows.ems.renderer.impl.outline.OutlineCandidateHeuristic._
    val outlineCandidate = candidates.max

    logger.info(
      s"best is ${outlineCandidate.density} with size ${outlineCandidate.candidate.bounds} and aperture ${outlineCandidate.candidate.aperture}"
    )

    val outlineMeta =
      createMeta(outlineCandidate.properties(
        scale = 2
      ))

    val stackdef = layerstackDefinitions.filter(d =>
      d.metaInfo.get[DecimalProperty](MaterialProperties.CuCount).map(_.value.intValue).contains(copperfiles.length)
    ).head

    val stack    = LayerStackMatcher.createLayerStack(stackdef, assRef, analyzedCopperFiles.keys.toSeq).get
    val substack = stack.stacks.head

    val copperGraphicsWithInvertedFlag = substack.layers.flatMap(_.files).flatten
      .flatMap { gf =>
        analyzedCopperFiles.get(gf).flatMap(_.data).map(ctx => ctx -> gf)
      }
    val holes = analyzeDrillFiles(
      outlineCandidate.candidate.graphic,
      files.filter(drillfiles.contains),
      copperGraphicsWithInvertedFlag,
      stack
    )

    val annularRings = new AnnularRingStackAnalyzer(
      assRef = assRef,
      ls = substack,
      ld = (gf) => getFile(analyzedCopperFiles, gf)._data.get,
      coord = RendererCoordinator
    ).analyzeHoles(() => holes.map(_.holeList))

    val copperDensityCtx = new CopperDensityContext()

    def getGraphicByType = {
      (
          gf: GerberFile,
          tp: Seq[String]
      ) =>
        if (tp.contains(RenderConstants.COPPER_JSON)) {
          Future.successful(getFile(analyzedCopperFiles, gf)._graphic)
        } else {
          Future.successful(None)
        }
    }

    val copperDensity = new CopperDensityAnalyzerTask(
      assRef = assRef,
      ctx = copperDensityCtx,
      stack = substack,
      outline = () => outlineCandidate.candidate.graphic,
      outlineMeta = () => outlineMeta,
      gr = getGraphicByType
    )
    RendererCoordinator.submitTask(copperDensity)

    println("wait for copper density")
    copperDensity.waitFor()
    val densityResult = copperDensityCtx.result

    val weightTaskCtx = new WeightTaskContext()
    val weightTask = new WeightTask(
      assRef = assRef,
      ctx = weightTaskCtx,
      stack = substack,
      outlineMeta = () => outlineMeta,
      pcbMeta = () => MetaInfo(),
      densityResult.map(_.layerMetas).getOrElse(Map())
    )
    RendererCoordinator.submitTask(weightTask)

    println("wait for weight task")
    weightTask.waitFor()
    val weightResult = weightTaskCtx.result

    println(s"copper density: ${copperDensityCtx.result}")

    val annularRingResult = GerberBoardAnalyzer.getAnnularRingsResult(annularRings.flatMap { artask =>
      artask.waitFor()
      artask.ctx.asInstanceOf[AnnularRingContext].result.map(GerberBoardAnalyzer.getAnnularRingsMetaData)
        .getOrElse(Map())
    })

    val stackdefmeta = Await.result(LayerstackServiceImpl.createMeta("", stackdef, materialreporter), 1 minute)

    val base = Seq(densityResult, weightResult, annularRingResult).flatten.reduce(_ ++ _)

    val consolidatedResult =
      GerberBoardAnalyzer.consolidateMetaDataByStack(base, substack, analyzedCopperFiles.keys.toSeq)
    val drillResults = GerberBoardAnalyzer.getHoleProperties(holes.map(_.holeList))

    val allResults = (annularRingResult.toSeq ++ densityResult.toSeq ++ weightResult.toSeq :+ consolidatedResult)

    val reducedResults = allResults.reduce(_ ++ _)

    val pcbMeta =
      createMeta(drillResults) ++ reducedResults.pcbMeta ++ reducedResults.stackMeta ++ outlineMeta ++ stackdefmeta

    expectedAnalysis
      .filter(p => p._1.startsWith("meta.") || p._1.startsWith("__meta."))
      .map(p => p._1.replace("__meta.", "").replace("meta.", ""))
    //

    println(Json.prettyPrint(Json.toJson(pcbMeta)))

    val duration = System.currentTimeMillis() - start

    append(Paths.get(s"/tmp/analysis-${testrun.toEpochMilli}.log"), s"${testName};$duration")
    println(s"Duration: ${testName} in $duration ")
    val result = propertiesToCheck.map { p =>
      val value = pcbMeta.get[Property](p._2).map(_.getValue)

      val exp = expectedAnalysis.get(p._1).flatMap {
        case x if x.trim.isEmpty => None
        case x                   => Some(x)
      }

      val coercedValues: (Option[Any], Option[Any]) = value match {
        case None => (None, None)
        case Some(value: BigDecimal) =>
          (
            Some(value.setScale(3, RoundingMode.HALF_UP)),
            Some(exp.flatMap(e =>
              Try {
                val expvalue = BigDecimal(e)

                if (expvalue > 100) {
                  // sometimes the expected values are in um while we need mm
                  val scaledExpectedValue = expvalue / 1000

                  if (value <= scaledExpectedValue + 50) {
                    scaledExpectedValue
                  } else {
                    expvalue
                  }

                } else {
                  expvalue
                }
              }.recoverWith {
                case exc =>
                  logger.error(s"failed to parse expected value ${e}", exc)
                  Failure(exc)
              }
                .toOption
            ).getOrElse(BigDecimal(0)).setScale(3, RoundingMode.HALF_UP))
          )
        case x => (x, exp)
      }

      (p._2, coercedValues._1, coercedValues._2)
    }

    val values = result.map(x => s"${x._2.getOrElse("")};${x._3.getOrElse("")};${x._2 == x._3}").mkString(";")
    append(Paths.get(s"/tmp/analysis-${testrun.toEpochMilli}-results.csv"), s"${testName};$values")

    result
  }

  private def createMeta(properties: Seq[Property]) =
    MetaInfo(properties.map(p => p.name -> p).toMap)

  private def getFile[X](files: Map[GerberFile, X], gf: GerberFile): X =
    files.find(_._1.name == gf.name).map(_._2).get

  private def analyzeCopperFile(
      assRef: AssemblyReference,
      gf: GerberFile,
      instant: Instant
  ) = {
    val sink = new RenderProgressSink(assRef, gf.name)

    val rctx = new UnitTestPersistenceContext(instant, sink)

    val rt = new RenderTask(assRef, gf, rctx)

    rt.onSuccess { (s, l) =>
      val twAnalysis = new TracewidthAnalysisTask(
        assRef,
        gf,
        rctx,
        () =>
          rctx._data.getOrElse(throw new IllegalStateException(
            "QuadTree not found"
          )),
        rctx._graphic.get.format
      )

      val distAnalysis = new ClearanceAnalysisTask(
        assRef,
        gf,
        rctx,
        () =>
          rctx._data.getOrElse(throw new IllegalStateException(
            "QuadTree not found"
          )),
        rctx._graphic.get.format
      )

      RendererCoordinator.submitTask(twAnalysis)
      RendererCoordinator.submitTask(distAnalysis)

    }

    RendererCoordinator.submitTask(rt)

    Some((gf, rt, rctx))
  }

  def toFiletype(f: String, mime: Option[String], index: Option[Int]): Option[FileType] = {

    val gerber = Seq(
      LayerConstants.OUTLINE,
      LayerConstants.MECHANICAL,
      LayerConstants.KEEP_OUT,
      LayerConstants.PASTE_TOP,
      LayerConstants.PASTE_BOTTOM,
      LayerConstants.SILKSCREEN_TOP,
      LayerConstants.SILKSCREEN_BOTTOM,
      LayerConstants.SOLDERMASK_TOP,
      LayerConstants.SOLDERMASK_BOTTOM,
      LayerConstants.COPPER_TOP,
      LayerConstants.COPPER_MID,
      LayerConstants.PLANE_MID,
      LayerConstants.COPPER_BOTTOM,
      LayerConstants.ADHESIVE_TOP,
      LayerConstants.ADHESIVE_BOTTOM,
      LayerConstants.PEELABLE_TOP,
      LayerConstants.PEELABLE_BOTTOM,
      LayerConstants.DRILL,
      LayerConstants.NPH_DRILL,
      LayerConstants.PH_DRILL
      //      LayerConstants.DRILLSETS,
      //      LayerConstants.DRILL_PARAMETERS
    )

    gerber.find(_.toLowerCase == f.toLowerCase).map(ft =>
      FileType("", LayerConstants.Categories.gerber, ft, true, mime, index = index)
    )
  }

  def append(file: Path, line: String) =
    Files.writeString(
      file,
      s"$line\n",
      StandardOpenOption.APPEND,
      StandardOpenOption.CREATE
    )

  private def analyzeDrillFiles(
      outlineGraphic: Graphic,
      files: Seq[GerberFile],
      coppergraphics: Seq[(PCBLayerInternalData, GerberFile)],
      stack: LayerStack
  ): Seq[HoleListWithFile] = {
    val unreconciled = files.map { f =>
      val fileID = GraphicElement.getChar(f.id)
      PathUtils.probeContentType(f.path.toJavaPath).toOption.flatten match {
        case Some(GerberFileTypeDetector.MIME) =>
          renderGerberDrills(f, fileID) -> f
        case _ =>
          renderExcellonDrills(f, files) -> f
      }
    }.toMap

    println(s"lists: ${unreconciled.size}, drills: ${unreconciled.values.map(_.name)}")

    if (unreconciled.nonEmpty) {
      println(s"got drill lists with hints ${unreconciled.map(_._1.formatHints)}")
      val formats = Reconciliation.createEligibleFormats(unreconciled.keys)

      val scoredFormats = formats.map { x =>
        x -> Reconciliation.scoreFormat(outlineGraphic, x._2)
      }

      val win = scoredFormats.filter(x =>
        !x._2.overlap.isNaN && !x._2.drillCoverage.isNaN && !x._2.drillsInsideOutline.isNaN
      ).maxBy(x => x._2)

      println(s"working with format ${win._1._1}")

      val reconciled = unreconciled.map { x =>
        win._1._2(x._1) -> x._2
      }

//      val (reorder, _) =
//        Reconciliation.reorder(reconciled.keys.toSeq, coppergraphics, stack, Seq())
//      Reconciliation.distinct(reorder)
      Seq()
    } else {
      Seq()
    }
  }

}
