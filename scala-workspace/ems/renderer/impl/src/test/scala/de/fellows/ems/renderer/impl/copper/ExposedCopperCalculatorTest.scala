package de.fellows.ems.renderer.impl.copper

import copper.ExposedCopperCalculator
import de.fellows.ems.pcb.model.{BigPoint, Dimension, Format, Graphic, GraphicElement}
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import de.fellows.ems.pcb.api.specification.units.AreaWithUnit

class ExposedCopperCalculatorTest extends AnyWordSpec with BeforeAndAfterAll with Matchers {
  "ExposedCopperCalculator" should {
    "calculates copper area" in {
      val dimension = Dimension(
        min = BigPoint(0, 0),
        max = BigPoint(3200, 3200)
      )
      val soldermask = Graphic(
        viewbox = dimension,
        format = Format(
          unit = "mm",
          dimension = Some(dimension),
          scaling = Some(100),
          resolution = BigDecimal(1) / 100
        ),
        count = 1,
        paths = Seq(
          Seq(
            GraphicElement(
              path = Some("M 0 0 H 1000 V 1000 H 0 L 0 0"),
              tag = None,
              use = None,
              attributes = Some(Map("fill" -> Seq("lime")))
            ),
            GraphicElement(
              path = Some("M 2200 2200 H 3200 V 3200 H 2200 L 2200 2200"),
              tag = None,
              use = None,
              attributes = Some(Map("fill" -> Seq("lime")))
            )
          )
        ),
        defs = Seq.empty
      )

      val copper = Seq(
        Graphic(
          viewbox = dimension,
          format = Format(
            unit = "mm",
            dimension = Some(dimension),
            scaling = Some(100),
            resolution = BigDecimal(1) / 100
          ),
          count = 1,
          paths = Seq(
            Seq(
              GraphicElement(
                path = Some("M 0 0 H 3200 V 3200 H 0 L 0 0"),
                tag = None,
                use = None,
                attributes = Some(Map("fill" -> Seq("brown")))
              )
            )
          ),
          defs = Seq.empty
        )
      )

      val exposedCopper = ExposedCopperCalculator.getExposedCopper(
        name = "test",
        soldermask = soldermask,
        copper = copper,
        boardArea = AreaWithUnit.sqmm(32 * 32)
      )

      exposedCopper.exposedCopperArea.floor should be(201)
      exposedCopper.exposedCopperPercentage.floor should be(19)
    }
  }
}
