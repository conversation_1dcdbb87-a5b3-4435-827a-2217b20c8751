package de.fellows.ems.renderer.impl.gerber.utils

import de.fellows.ems.pcb.model.graphics._
import de.fellows.ems.pcb.model.graphics.tree.{ElementId, Node, PCBLayerInternalData, QuadTree, Trace}
import de.fellows.ems.pcb.model.{Dimension, Format, GraphicDefinition, GraphicElement}
import de.fellows.ems.renderer.impl.RendererHelper
import de.fellows.ems.renderer.impl.analysis.GerberLayerAnalyzer
import de.fellows.ems.renderer.impl.gerber.builders.BoundsBuilder
import de.fellows.ems.renderer.impl.pool.{RenderTask, SyncRenderer}
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.{DebugUtils, UUIDUtils}

import java.awt.geom.{AffineTransform, Ellipse2D, Line2D}
import java.awt.image.BufferedImage
import java.awt.{BasicStroke, Color}
import java.io.File
import javax.imageio.ImageIO
import scala.collection.immutable.HashSet
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext}
import scala.math.BigDecimal.RoundingMode
import scala.util.Random

/** Helper program to render a gerber layer as a PNG
  *
  * takes two arguments:
  *   1. file to render
  *   1. path to render to
  *
  *   you maybe need `--add-opens=java.desktop/java.awt.geom=ALL-UNNAMED --add-exports=java.desktop/sun.awt.geom=ALL-UNNAMED` as JVM arguments
  */
object LayerRenderer extends App with StackrateLogging {
  val MARGIN = 1000

  DebugUtils.timed("rendering... ") {
    val mainScale = 0.5

    val file     = args(0)
    val analysis = args.lift(2).map(_.toBoolean).getOrElse(true)

    val renderer = new SyncRenderer(new java.io.File(file), None)
    val paths    = Seq.newBuilder[Seq[GraphicElement]]
    val defs     = Seq.newBuilder[GraphicDefinition]
    renderer.prepare()

    //  val graphics2D = Renderer.createGraphics
    //  RenderTask.addApertureDefinitions(defs, renderer, graphics2D)

    import java.awt.image.BufferedImage

    val tree       = renderer.builder.tree
    val dimensions = renderer.dimensions
    val awtImage = new BufferedImage(
      Math.abs((dimensions.size.x.intValue + (MARGIN * 2)) * mainScale).intValue,
      Math.abs((dimensions.size.y.intValue + (MARGIN * 2)) * mainScale).intValue,
      BufferedImage.TYPE_INT_RGB
    )


    val colors = Seq(
      Color.GRAY,
      Color.RED,
      Color.PINK,
      Color.ORANGE,
      Color.YELLOW,
      Color.GREEN,
      Color.MAGENTA,
      Color.CYAN,
      Color.BLUE
    )

    var idx = Random.nextInt(20)
    val format = Format(
      dimension = Some(dimensions),
      unit = RendererHelper.unit(renderer.glst.gContext.format.get.getStardizedUnit),
      resolution = renderer.glst.gContext.format.get.resolution(),
      complexity = Some(tree.getComplexity),
      scaling = Some(renderer.glst.gContext.format.get.getImageScaling),
      gerberscale = Some(renderer.glst.gContext.format.get.getGerberScaling)
    )

    val (thicknesses, clearance) =
      if (analysis) {
        val _traces = Seq.newBuilder[Trace]
        Renderer.drawWithCollisionGroupsIter(
          tree,
          group => {
            idx += 1
            val thisTraceID = Some(s"trace-${idx}-${UUIDUtils.createTiny()}")
            val ids         = HashSet.newBuilder[ElementId[Int]].addAll(group.map(x => x.index)).result()
            _traces += Trace(thisTraceID.get, ids, Some(RenderTask.hasPads(group.toVector))) // [ElementId[Int]])
          }
        )

        val pit = PCBLayerInternalData(
          tree = tree,
          traces = Some(_traces.result()),
          format = Some(format),
          macros = Some(renderer.getMacroDefinitions().values.toSeq)
        )

        val thicknesses = DebugUtils.timed("thicknesses") {
          new GerberLayerAnalyzer(
            pit,
            format,
            ExecutionContext.global,
            None
          ).analyzeThicknesses()
            .groupBy(x => BigDecimal(x.width).setScale(0, RoundingMode.DOWN))
            .toSeq.minByOption(_._1)
            .map(_._2)
        }(logger.logger)

        val clearance =
          DebugUtils.timed("clearance") {
            Await.result(
              new GerberLayerAnalyzer(
                pit,
                format,
                ExecutionContext.global,
                None,
                ignoreTraces = true
              ).analyzeDistancesParallel(),
              10 minute
            )
          }(logger.logger)

        (thicknesses, Some(clearance))
      } else {
        (None, None)
      }

    val g2d = awtImage.createGraphics()

    g2d.setStroke(new BasicStroke(0.01f))
    g2d.setColor(Color.WHITE)

    println(s"work with graphics ${g2d.getClass} ${dimensions}")
    g2d.setColor(Color.RED)
    g2d.setStroke(new BasicStroke(0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_ROUND))

    //  val smallest = thicknesses.minBy(_.width)
    //  println(s"smallest thickness: ${thicknesses.map(_.map(_.width))}")
    //  println(s"smallest clearance: ${clearance}")

    g2d.transform(AffineTransform.getScaleInstance(mainScale, mainScale))

    val bb = new BoundsBuilder()

    g2d.transform(AffineTransform.getTranslateInstance(
      -dimensions.min.x.doubleValue + MARGIN,
      -dimensions.min.y.doubleValue + MARGIN
    ))
    DebugUtils.timed("collision... ") {
      Renderer.drawWithCollisionGroupsIter(
        tree,
        group => {
          idx += 1

          g2d.setColor(
            new Color(
              (Random.nextInt(25) * 10),
              (Random.nextInt(25) * 10),
              (Random.nextInt(25) * 10),
              100
            )
          )
          group.foreach { g =>
            bb.extend(g.bounds)
            val color =
              if (thicknesses.getOrElse(Seq()).map(_.element).contains(g.index.x)) {
                //          if (smallest.element == g.index.x) {
                val c = g2d.getColor
//                g2d.setColor(Color.RED)
//                val bounds = g.bounds
//                g2d.draw(bounds)

                c

              } else {
                g2d.getColor
                //          g2d.setColor(Color.WHITE)
              }
            val s =
              g.shape

            //          s.getPathIterator(null).foreach { c =>
            //            Paths.toLine(c).foreach(g2d.draw)
            //          }

            g2d.fill(s)
            g2d.setColor(color)
          }
        }
      )
    }(logger.logger)

    g2d.setColor(Color.RED)
    clearance.getOrElse(List())
      .groupBy(_._3.distance.distance)
      .minByOption(_._1)
      .map(_._2)
      .getOrElse(List())
      .foreach { d =>
        println(s"clearance: ${d._3.distance.distance}  ${d._3} ")
        val start = d._3.distance.start
        val end   = d._3.distance.end

        val c = new Ellipse2D.Double()
        c.setFrameFromCenter(start.x, start.y, start.x + 0, start.y + 0)

        Range(1, 5).foreach { x =>
          val helperC = new Ellipse2D.Double()
          helperC.setFrameFromCenter(start.x, start.y, start.x + x * 10, start.y + x * 10)
          g2d.draw(helperC)
        }

        g2d.fill(c)

        g2d.draw(new Line2D.Double(start.x, start.y, end.x, end.y))
      }

    g2d.setColor(Color.LIGHT_GRAY)

    g2d.setStroke(new BasicStroke(0.01f))

    def drawNode(n: Node[Graphic]): Unit =
      //    g2d.draw(n.bounds)
      n.children.foreach(_.foreach(drawNode))

    drawNode(tree.getRoot())

    //  g2d.transform()
    //  g2d.translate(0, 5140)

    println(s"bounds ${bb.result().getWidth}x${bb.result().getHeight}")

    val filefolder = (args(1))
    val fullfile   = new File(filefolder + "/full.png")
    ImageIO.write(awtImage, "png", fullfile)

    drawDebug(tree, new File(filefolder + "/debug.png"), dimensions, mainScale)

  }(logger.logger)

  private def drawDebug(tree: QuadTree[Graphic], file: File, dimensions: Dimension, mainScale: Double) = {
    val awtImage = new BufferedImage(
      Math.abs((dimensions.size.x.intValue + (MARGIN * 2)) * mainScale).intValue,
      Math.abs((dimensions.size.y.intValue + (MARGIN * 2)) * mainScale).intValue,
      BufferedImage.TYPE_INT_RGB
    )

    val g2d = awtImage.createGraphics()
    g2d.transform(AffineTransform.getScaleInstance(mainScale, mainScale))

    g2d.transform(AffineTransform.getTranslateInstance(
      -dimensions.min.x.doubleValue + MARGIN,
      -dimensions.min.y.doubleValue + MARGIN
    ))
    tree.walkTree(s =>{

      s.elements.foreach(g => {
        g2d.setStroke(new BasicStroke(0.01f))
        g2d.fill(g.shape)
      })
    })


    g2d.setColor(Color.ORANGE)
    tree.walkTree(s => {
      g2d.draw(s.bounds)
    })

    ImageIO.write(awtImage, "png", file)
  }

}
