package de.fellows.ems.renderer.impl.gerber.utils

import de.fellows.ems.pcb.model.{GerberFile, LayerConstants}
import de.fellows.ems.renderer.impl.gerber.Graphics
import de.fellows.utils.FilePath
import de.fellows.utils.internal.FileType
import de.fellows.utils.logging.StackrateLogging
import org.apache.commons.math3.exception.util.ExceptionContext

import scala.concurrent.duration._
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}
import de.fellows.ems.renderer.impl.outline.OutlineCandidateBuilder
import de.fellows.ems.renderer.impl.outline.TransientOutlineCandidate
import de.fellows.ems.renderer.impl.worker.SafeAwait
import java.awt.image.BufferedImage
import java.awt.Color
import java.awt.BasicStroke
import java.awt.geom.AffineTransform
import javax.imageio.ImageIO
import java.io.File

object OutlineCandidateBuilderApp extends App with StackrateLogging {

  implicit val exc = ExecutionContext.global
  val outlineFile  = args(0)

  val gf = GerberFile(
    id = UUID.randomUUID(),
    name = "test",
    path = FilePath(outlineFile),
    fType = FileType(
      service = "test",
      category = LayerConstants.Categories.mechanical,
      fileType = LayerConstants.OUTLINE,
      productionFile = true,
      mimeType = LayerConstants.Mime.gerber,
      index = None,
      from = None,
      to = None
    ),
    format = None,
    renderinfo = None,
    metaInfo = None,
    inverted = None,
    hash = None
  )

  val res = new OutlineCandidateBuilder().createOutlineGraphics(gf)
    .map {
      case value =>
        logger.info(s"rendered outline: ${value.length}")

        value.zipWithIndex.map { x =>
          logger.info(s"render outline: ${x._2}")

          try
            drawOutline(x._1, x._2)
          catch {
            case e: Exception =>
              logger.error(s"Failed to draw outline ${x._2}", e)
          }

        }

    }

  private def drawOutline(candidate: TransientOutlineCandidate, index: Int) = {
    val outlineFile = new File(s"/home/<USER>/tmp/outlines/outline-${"%07d".format(index)}.png")
    val g           = candidate.graphic
    logger.info(s"g: ${g}")
    val MARGIN    = 1000
    val mainScale = 0.5

    val dimensions = g.viewbox
    val awtImage = new BufferedImage(
      Math.abs((dimensions.size.x.intValue + (MARGIN * 2)) * mainScale).intValue,
      Math.abs((dimensions.size.y.intValue + (MARGIN * 2)) * mainScale).intValue,
      BufferedImage.TYPE_INT_RGB
    )

    val g2d = awtImage.createGraphics()
    g2d.setColor(Color.WHITE)
    g2d.fillRect(0, 0, awtImage.getWidth, awtImage.getHeight)

    g2d.setColor(Color.BLACK)
    g2d.setStroke(new BasicStroke(0.01f))

    g2d.transform(AffineTransform.getScaleInstance(mainScale, mainScale))
    g2d.transform(AffineTransform.getTranslateInstance(
      -dimensions.min.x.doubleValue + MARGIN,
      -dimensions.min.y.doubleValue + MARGIN
    ))

    g2d.fill(candidate.path)
    g2d.dispose()

    outlineFile.getParentFile.mkdirs()
    ImageIO.write(awtImage, "png", outlineFile)
    logger.info(s"Saved outline ${index} to ${outlineFile.getAbsolutePath}")
  }

  val fres = SafeAwait.result(res)
  logger.info(s"done all: ${fres}")

}
