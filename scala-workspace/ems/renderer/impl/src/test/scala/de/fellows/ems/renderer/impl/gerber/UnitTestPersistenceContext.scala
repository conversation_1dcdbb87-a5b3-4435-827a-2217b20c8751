package de.fellows.ems.renderer.impl.gerber

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.graphics.tree.{PCBLayerInternalData, TraceWidthDescription}
import de.fellows.ems.pcb.model.{Dimension, GerberFile, Graphic, LayerConstants}
import de.fellows.ems.renderer.api.DistanceDescription
import de.fellows.ems.renderer.impl.SVGStackup
import de.fellows.ems.renderer.impl.pool.{PersistentRenderContext, RenderContext, SyncRenderer}
import de.fellows.ems.renderer.impl.progress.{RenderProgressSink, RendererProgress}
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.utils.logging.StackrateLogger
import de.fellows.utils.meta.{DecimalProperty, MetaInfo}
import org.apache.batik.anim.dom.SVGDOMImplementation
import org.w3c.dom.Element

import java.nio.file.Paths
import java.time.Instant
import scala.util.Try

class UnitTestPersistenceContext(run: Instant, override val sink: RenderProgressSink)(implicit logger: StackrateLogger)
    extends RenderContext(sink) {

  var _result: Option[Any]                 = None
  var _data: Option[PCBLayerInternalData]  = None
  var _graphic: Option[Graphic]            = None
  var _tracewidth: Option[DecimalProperty] = None
  var _distances: Option[DecimalProperty]  = None

  def scale = _graphic.flatMap(_.format.scaling).getOrElse(100.0)

  override def persistTraceWidth(
      assRef: AssemblyReference,
      gf: GerberFile,
      value: List[TraceWidthDescription]
  ): Unit = {
    this._tracewidth =
      if (gf.inverted.contains(true) || gf.fType.fileType == LayerConstants.PLANE_MID) {
        PersistentRenderContext.getSameNetClearanceProperty(value.map(_.width), scale)
      } else {
        PersistentRenderContext.getTraceWidthProperty(value.map(_.width), scale)
      }
    println(s"[CTX ${gf.name}] persistTraceWidth ${this._tracewidth}")
  }

  override def persistDistances(assRef: AssemblyReference, gf: GerberFile, value: List[DistanceDescription]): Unit = {
    this._distances =
      if (gf.inverted.contains(true) || gf.fType.fileType == LayerConstants.PLANE_MID) {
        PersistentRenderContext.getTraceWidthProperty(value.map(_.distance.distance), scale)
      } else {
        PersistentRenderContext.getSameNetClearanceProperty(value.map(_.distance.distance), scale)
      }

    println(s"[CTX ${gf.name}] persistDistances")
  }

  override def persist(
      assRef: AssemblyReference,
      gf: GerberFile,
      dimensions: Dimension,
      data: PCBLayerInternalData,
      _1: String,
      gr: Graphic,
      renderer: Option[SyncRenderer]
  ): Unit = {
    println(s"[CTX ${gf.name}] persist")
    this._data = Some(data)
    this._graphic = Some(gr)
    val parent = Paths.get(s"/tmp/unittest-${run}/${assRef.gid.get}/")
    parent.toFile.mkdirs()

    val svg     = Renderer.createSvg(gr)
    val svgRoot = svg.getDocumentElement
    val group =
      svgRoot.getElementsByTagNameNS(SVGDOMImplementation.SVG_NAMESPACE_URI, "g").item(0).asInstanceOf[Element]
    SVGStackup.setTransform(false, group, svgRoot, gr.format.scaling.getOrElse(100.0).intValue, gr.viewbox, margin = 0)
    Renderer.writePng(svg, parent.resolve(s"${gf.name}.png").toString, gray = true)
  }

  override def persistMeta(assRef: AssemblyReference, gf: GerberFile, meta: MetaInfo): Unit =
    println(s"[CTX ${gf.name}] persistMeta")

  override def setTimeout(endTime: Long): Unit =
    println(s"[CTX] setTimeout")

  override def result: Option[Any] = {
    println(s"[CTX] result")
    _result
  }

  override def setResult(r: Any): Unit = {
    println(s"[CTX] setResult")
    _result = Some(r)
  }

  override def stop(assRef: AssemblyReference, gf: Option[GerberFile]): Unit =
    println(s"[CTX ${gf.map(_.name)}] stop")

  override def start(assRef: AssemblyReference, gf: Option[GerberFile]): Unit =
    println(s"[CTX ${gf.map(_.name)}] start")

  override def error(assRef: AssemblyReference, gf: Option[GerberFile], t: Throwable): Unit =
    println(s"[CTX ${gf.map(_.name)}] error")

  override def getMessages(): Seq[RendererProgress.RenderMessage] = {
    println(s"[CTX] getMessages")
    Seq()
  }
}
