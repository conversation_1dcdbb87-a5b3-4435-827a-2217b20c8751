package de.fellows.ems.renderer.impl.analysis

import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class ReconciliationSpec extends AnyWordSpec with BeforeAndAfterAll with Matchers {

  "Reconciliation" should {
    "match range by name" in {
      val coppers = Seq(
        "TOP",
        "INT1",
        "INT2",
        "INT3",
        "INT4",
        "INT5",
        "INT6",
        "INT7",
        "INT8",
        "INT9",
        "INT10",
        "BOTTOM"
      )

      RangeAnalyzer.getDrillRangeByLayerNames("D_INT6_INT7", coppers) should be(Some(<PERSON><PERSON><PERSON><PERSON><PERSON>(6, 7)))
      RangeAnalyzer.getDrillRangeByLayerNames("D_INT9_INT10", coppers) should be(Some(<PERSON><PERSON><PERSON><PERSON><PERSON>(9, 10)))
    }
  }

}
