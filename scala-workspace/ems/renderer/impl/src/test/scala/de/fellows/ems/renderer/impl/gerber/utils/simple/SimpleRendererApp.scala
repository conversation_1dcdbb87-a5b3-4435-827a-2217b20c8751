package de.fellows.ems.renderer.impl.gerber.utils.simple

import de.fellows.ems.renderer.impl.simple.SimpleGerberRenderer
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.{DebugUtils, FilePathUtils}
import de.fellows.utils.spi.GerberFileTypeDetector

import java.io.{
  BufferedInputStream,
  ByteArrayInputStream,
  File,
  FileInputStream,
  InputStream,
  StringBufferInputStream,
  StringReader
}
import java.nio.charset.StandardCharsets
import java.nio.file.{Files, Path, Paths}
import java.util.zip.ZipInputStream
import scala.util.Using

object SimpleRendererApp extends App with StackrateLogging {

  val gerberFileTypeDetector = new GerberFileTypeDetector

  val p = Paths.get(args(0))

  val files =
    if (Files.isDirectory(p)) {
      p.toFile.listFiles().toSeq
    } else {
      Seq(p.toFile)
    }

  val target = Paths.get(args(1))

  target.toFile.mkdirs()

  files.foreach { f =>
    renderFile(target, f)
  }

  def renderZip(target: Path, f: File, is: FileInputStream) = {
    val thistarget = target.resolve(f.getName + "-rendered/")
    thistarget.toFile.mkdirs()

    Using.resource(new ZipInputStream(is, StandardCharsets.ISO_8859_1)) { zis =>
      var entry = zis.getNextEntry

      while (entry != null) {
        val name = entry.getName

        if (!entry.isDirectory) {
          val content = new String(zis.readAllBytes())
          if (gerberFileTypeDetector.applies(content)) {
            renderStream(thistarget, new File(name), new ByteArrayInputStream(content.getBytes))
          }

        }

        entry = zis.getNextEntry
      }
    }
  }

  private def renderFile(target: Path, f: File): Unit =
    if (f.isFile) {

      DebugUtils.timed("rendering " + f.getName) {
        Using.resource(new FileInputStream(f)) { is =>
          val mime = Files.probeContentType(f.toPath)

          if (mime == GerberFileTypeDetector.MIME) {

            renderStream(target, f, is)
          } else if (mime == "application/zip") {

            renderZip(target, f, is)

          } else {
            println(s"mime: $mime")
          }

        }
      }(logger.logger)
    } else if (f.isDirectory) {
      val subTarget = target.resolve(f.getName)
      f.listFiles().foreach { f =>
        subTarget.toFile.mkdirs()
        renderFile(subTarget, f)
      }
    }

  private def renderStream(target: Path, f: File, is: InputStream) =
    try {
      val renderer = new SimpleGerberRenderer(is)
      val result   = renderer.render()
      Files.writeString(target.resolve(f.getName + ".svg"), result)
    } catch {
      case x: Throwable =>
        x.printStackTrace()
        println(s"Error rendering $f: ${x.getMessage}")
    }
}
