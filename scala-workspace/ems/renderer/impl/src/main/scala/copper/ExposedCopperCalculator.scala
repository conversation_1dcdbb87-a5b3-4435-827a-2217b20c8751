package copper

import com.typesafe.config.ConfigFactory
import de.fellows.app.assembly.commons.AssemblyFiles
import de.fellows.ems.pcb.model.{BigPoint, Dimension, Graphic}
import de.fellows.ems.renderer.impl.outline.SvgTransformer
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.ems.renderer.impl.{Rasterizer, SvgOps}
import org.apache.batik.transcoder.TranscoderInput
import play.api.Logging

import java.awt.Color
import java.awt.image.BufferedImage
import java.util.UUID
import javax.imageio.ImageIO
import de.fellows.ems.pcb.api.specification.units.AreaWithUnit
import de.fellows.ems.pcb.api.specification.units.AreaUnit

case class DebugImageDescriptor(
    team: String,
    assembly: UUID,
    debugName: String,
    version: UUID
)

object ExposedCopperCalculator extends Logging {

  final case class ExposedCopper(
      exposedCopperArea: Double,
      exposedCopperPercentage: Double,
      copperArea: Double
  )

  def getExposedCopper(
      name: String,
      soldermask: Graphic,
      copper: Seq[Graphic],
      boardArea: AreaWithUnit,
      debug: Option[DebugImageDescriptor] = None
  ): ExposedCopper = {
    val dimension = (copper.flatMap(_.format.dimension) :+ soldermask.format.dimension.getOrElse(Dimension(
      BigPoint(0, 0),
      BigPoint(0, 0)
    ))).reduce(_ += _)

    val renderSize = 300f

    val negOffset = dimension.min

    val coppers = Renderer.createSvg(
      graphic = copper,
      graphictransformer = SvgTransformer.translateTransform(negOffset)
    )
    SvgOps.setSvgDocumentSize(coppers, dimension)

    val copperImage = Rasterizer.toBMP(new TranscoderInput(coppers), renderSize)

    val soldermaskSvg = Renderer.createSvg(
      graphic = Seq(soldermask),
      graphictransformer = SvgTransformer.translateTransform(negOffset)
    )
    SvgOps.setSvgDocumentSize(soldermaskSvg, dimension)

    val soldermaskImage = Rasterizer.toBMP(new TranscoderInput(soldermaskSvg), renderSize)

    val width  = Math.min(soldermaskImage.getWidth, copperImage.getWidth)
    val height = Math.min(soldermaskImage.getHeight, copperImage.getHeight)

    val unit        = soldermask.format.unit
    val size        = dimension.size
    val unitScaling = BigDecimal(soldermask.format.scaling.getOrElse(1.0))
    val imageScale  = ((size.x / unitScaling) * (size.y / unitScaling)) / (width * height) // mm^2 per pixel^2

    logger.info(
      s"[EXPOSED COPPER ${name.toUpperCase}] found image size ${size} and image scale ${imageScale} (${size} / ${renderSize} / ${unitScaling})"
    )

    var surfacePixels            = 0
    var completeSoldermaskPixels = 0
    var exposedCopperPixels      = 0
    var copperAreaPixels         = 0
    var outsidePixels            = 0

    val debugImage = debug.map { _ =>
      val i = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB)

      val gr = i.createGraphics()
      gr.setColor(Color.WHITE)
      gr.fillRect(0, 0, width, height)
      i
    }

    for {
      x <- 0 until width
      y <- 0 until height
    } yield {
      val valueInSoldermaskInverted = soldermaskImage.getRaster.getSample(x, y, 0) == 0
      val valueInCopper             = copperImage.getRaster.getSample(x, y, 0) == 0

      val valueInActualSoldermask = !valueInSoldermaskInverted

      // count boundary and feature pixels
      if (valueInActualSoldermask) {
        completeSoldermaskPixels += 1
        debugImage.foreach(i => i.setRGB(x, y, 0xadadad))
      }

      if (valueInCopper) {
        copperAreaPixels += 1
      }

      if (valueInActualSoldermask || valueInCopper) {
        surfacePixels += 1
      }

      // not in soldermask but is copper -- exposed copper
      if (!valueInActualSoldermask && valueInCopper) {
        exposedCopperPixels += 1
        debugImage.foreach(i => i.setRGB(x, y, 0xb87333))
      }

      // both soldermask and copper -- intersection, doesn't matter since soldermask
      // will cover it
      if (valueInActualSoldermask && valueInCopper) {
        debugImage.foreach(i => i.setRGB(x, y, 0x006400))
      }

      // neither soldermask nor copper -- doesn't matter
      if (!valueInActualSoldermask && !valueInCopper) {
        outsidePixels += 1
        debugImage.foreach(i => i.setRGB(x, y, 0xff3333))
      }

      // soldermask but not copper -- doesn't matter
      if (valueInActualSoldermask && !valueInCopper) {
        debugImage.foreach(i => i.setRGB(x, y, 0x006400))
      }
    }

    debugImage.foreach { bi =>
      val d = debug.get

      Seq(
        s"debug/${d.debugName}-comparison.png" -> bi,
        s"debug/boundary-${d.debugName}.png"   -> soldermaskImage,
        s"debug/features-${d.debugName}.png"   -> copperImage
      ).map {
        case (filename, image) =>
          val f = AssemblyFiles.createAssemblyResourcePath(
            team = d.team,
            assembly = d.assembly,
            version = d.version,
            resourceType = "debug",
            filename = filename
          )(ConfigFactory.load()).toJavaPath
          f.toFile.getParentFile.mkdirs()
          ImageIO.write(image, "png", f.toFile)
      }
    }

    if (completeSoldermaskPixels > 0) {
      val surfaceArea = BigDecimal(surfacePixels) * imageScale
      val areaExposed = BigDecimal(exposedCopperPixels) * imageScale

      val exposedCopperPercentage = (areaExposed / boardArea.to(AreaUnit.SquareMillimeter)).toDouble

      logger.info(
        s"[EXPOSED COPPER ${name.toUpperCase}] area in ${unit}² surfaceArea=${surfaceArea} areaExposed=${areaExposed} exposedCopperPercentage=${exposedCopperPercentage} boardArea=${boardArea}"
      )

      ExposedCopper(
        exposedCopperArea = areaExposed.toDouble,
        exposedCopperPercentage = exposedCopperPercentage * 100,
        copperArea = (BigDecimal(copperAreaPixels) * imageScale).doubleValue
      )
    } else {
      ExposedCopper(0.0, 0.0, 0.0)
    }
  }
}
