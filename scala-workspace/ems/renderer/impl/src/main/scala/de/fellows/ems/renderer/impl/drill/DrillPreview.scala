package de.fellows.ems.renderer.impl.drill

import de.fellows.ems.pcb.model.{BigPoint, HoleList, Scaling}
import org.apache.batik.anim.dom.SVGDOMImplementation
import org.apache.batik.svggen.{SVGGeneratorContext, SVGGraphics2D}

import java.awt.{BasicStroke, Color}

object DrillPreview {

  def renderPreview(holes: HoleList, scale: Scaling = Scaling(1.0, 1.0)) = {
    val domImpl  = SVGDOMImplementation.getDOMImplementation
    val svgNS    = "http://www.w3.org/2000/svg"
    val document = domImpl.createDocument(svgNS, "svg", null)
    val svgctx   = SVGGeneratorContext.createDefault(document)
    val g        = new SVGGraphics2D(svgctx, false)

    var max: Option[BigPoint] = None
    var min: Option[BigPoint] = None

    holes.tools.flatMap { tool =>
      g.setColor(Color.BLACK)

      tool.drills.map { bp =>
        val c = tool.graphic(bp, scale)

        val rec  = c.getBounds2D
        val maxp = BigPoint(rec.getMaxX, rec.getMaxY)
        val minp = BigPoint(rec.getMinX, rec.getMinY)
        max = max.orElse(Some(maxp)).map(m => m.max(maxp))
        min = min.orElse(Some(minp)).map(m => m.min(minp))

        c
      }
    }

    val margin = 5

    val minPoint = min.getOrElse(BigPoint(0, 0))
    max.foreach { maxv =>
      val d = new java.awt.Dimension()

      d.setSize(
        (maxv.x.doubleValue - min.get.x.doubleValue).abs + 2 * margin,
        (maxv.y.doubleValue - min.get.y.doubleValue).abs + 2 * margin
      )

      g.setSVGCanvasSize(d)
    }
//    g.scale(1, -1)

    val ellipses = holes.tools.foreach { tool =>
      g.setColor(Color.BLACK)
      val dia = tool.diameter.doubleValue

      tool.drills.foreach { bp =>
        val c = tool.graphic(bp, scale)
        g.fill(c)
      }

      tool.slots.toSeq.foreach { sls =>
        sls.slots.foreach { x =>
          g.setStroke(new BasicStroke(tool.diameter.intValue, BasicStroke.CAP_ROUND, BasicStroke.JOIN_BEVEL))

          g.drawLine(
            x._1.x.intValue,
            x._1.y.intValue,
            x._2.x.intValue,
            x._2.y.intValue
          )
        }
      }
    }

    (document, g)
  }

}
