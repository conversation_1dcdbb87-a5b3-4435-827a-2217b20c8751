package de.fellows.ems.renderer.impl.progress

import de.fellows.ems.renderer.impl.progress.RendererProgress.RenderMessage
import org.reactivestreams.{ Subscriber, Subscription }

import java.util.concurrent.LinkedBlockingQueue

class RenderSubscription(q: LinkedBlockingQueue[RenderMessage], s: Subscriber[_ >: RenderMessage])
    extends Subscription {

  var cancelled = false

  override def request(n: Long): Unit =
    for (i <- 0 to n.intValue()) {
      val e = q.take()

      if (!cancelled) {
        s.onNext(e)
      } else {
        return
      }
    }

  override def cancel(): Unit =
    cancelled = true
}
