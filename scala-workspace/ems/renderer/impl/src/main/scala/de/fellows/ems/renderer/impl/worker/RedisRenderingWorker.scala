package de.fellows.ems.renderer.impl.worker

import akka.Done
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.renderer.api.job._
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.jobs.JobType._
import de.fellows.utils.redislog.jobs.{JobBuilder, JobEntry, QueuedJob, WorkerId}

import java.net.ConnectException
import java.util.concurrent.{Executors, ScheduledExecutorService, TimeUnit}
import scala.concurrent.{ExecutionContext, Future}
import scala.concurrent.duration.{Duration, DurationInt, FiniteDuration}
import scala.util.{Failure, Success, Try}

class RedisRenderingWorker(
    workerId: WorkerId,
    jobBuilder: JobBuilder,
    scheduler: ScheduledExecutorService,
    fileRenderService: FileRenderService,
    postProcessingService: PostProcessingService,
    reconciliationService: ReconciliationService,
    boardAnalysisService: BoardAnalysisService,
    productionAnalysisService: ProductionAnalysisService,
    outlineService: OutlineService,
    specificationRenderService: SpecificationRenderService
) extends Runnable
    with StackrateLogging {

  private val TRY_THRESHOLD = 5

  implicit val ctx: ExecutionContext = ExecutionContext.fromExecutor(Executors.newWorkStealingPool(10))

  override def run(): Unit = {
    val rescheduleTime: FiniteDuration =
      try
        jobBuilder.waitForJob("rendering", workerId) match {
          case None =>
            // Queue is empty, wait again
            logger.debug(s" Queue is empty")
            0 second

          case Some(job) =>
            try {
              if (Thread.currentThread().isInterrupted) {
                logger.warn(s"got new job ${job.job} but worker is shutting down")
                throw new InterruptedException
              }
              job.jobtype match {
                case JobTypeRender =>
                  val jobInfo = jobBuilder.getJobInfo[RenderFileJobEntry](job)

                  handleRetries(job, jobInfo) { () =>
                    val triedDone = fileRenderService.handleFileRendering(workerId, jobInfo, job)
                    val debugStr  = s"${jobInfo.ass.team}/${jobInfo.ass.id}/${jobInfo.ass.version}/${jobInfo.file.name}"
                    handleDone(job, jobInfo, debugStr, triedDone, delay = None)
                  }

                case JobTypeConvertNative =>
                  val jobInfo = jobBuilder.getJobInfo[ConvertFileJobEntry](job)

                  handleRetries(job, jobInfo) { () =>
                    val triedDone = fileRenderService.handleFileConversion(workerId, jobInfo, job)
                    val debugStr  = s"${jobInfo.ass.team}/${jobInfo.ass.id}/${jobInfo.ass.version}/${jobInfo.file.name}"
                    handleDone(job, jobInfo, debugStr, triedDone, delay = None)
                  }

                case JobTypeRenderPostProcessing =>
                  val jobInfo = jobBuilder.getJobInfo[RenderBoardJobEntry](job)

                  handleDependencies(job, jobInfo.dependencies) { () =>
                    handleRetries(job, jobInfo) { () =>
                      val triedDone = postProcessingService.handlePostProcessing(jobInfo, job)
                      handleDoneWithDelay(job, jobInfo, jobInfo.assembly, triedDone, 10.seconds)
                    }
                  }

                case JobTypeOutline =>
                  val jobInfo = jobBuilder.getJobInfo[OutlineJobEntry](job)

                  handleRetries(job, jobInfo) { () =>
                    val triedDone = outlineService.handleOutline(jobInfo, job)
                    handleDone(job, jobInfo, jobInfo.assembly, triedDone)
                  }

                case JobTypeReconciliation =>
                  val jobInfo = jobBuilder.getJobInfo[ReconciliationJobEntry](job)

                  handleRetries(job, jobInfo) { () =>
                    val triedDone = reconciliationService.handleReconciliation(jobInfo, job)
                    handleDone(job, jobInfo, jobInfo.assembly, triedDone)
                  }

                case JobTypeBoardAnalysis =>
                  val jobInfo = jobBuilder.getJobInfo[BoardAnalysisJobEntry](job)

                  handleRetries(job, jobInfo) { () =>
                    val triedDone = boardAnalysisService.handleBoardAnalysis(jobInfo, job)
                    handleDone(job, jobInfo, jobInfo.assembly, triedDone)
                  }

                case JobTypeProductionAnalysis =>
                  val jobInfo = jobBuilder.getJobInfo[ProductionAnalysisJobEntry](job)

                  handleRetries(job, jobInfo) { () =>
                    val triedDone = productionAnalysisService.handleProductionAnalysis(jobInfo, job)
                    handleDone(job, jobInfo, jobInfo.assembly, triedDone)
                  }

                case JobTypeRenderSpecification =>
                  val jobInfo = jobBuilder.getJobInfo[RenderSpecificationJobEntry](job)
                  handleRetries(job, jobInfo) { () =>
                    val triedDone = specificationRenderService.handleSpecificationRender(jobInfo, job)
                    handleDone(job, jobInfo, s"${jobInfo.team}/${jobInfo.assembly}", triedDone, delay = None)
                  }
              }
            } catch {
              case _: InterruptedException =>
                logger.warn(s"job ${job.job} was interrupted, reinserting back into redis")
                jobBuilder.reInsertExistingJob(job)
                0 second

              case e: Exception =>
                logger.error(s"fatal error in job ${job.job}: ${e.getMessage}", e)
                jobBuilder.failed(job, s"Fatal error: ${e.getMessage}")
                0 second
            }

        }
      catch {
        case e: Exception =>
          logger.error(s"fatal error in RedisRenderingWorker: ${e.getMessage}", e)
          0 second
      }

    scheduler.schedule(this, rescheduleTime.toMillis, TimeUnit.MILLISECONDS)
  }

  private def handleRetries[R](job: QueuedJob, jobInfo: R)(f: () => FiniteDuration): FiniteDuration = {
    val jobTry = jobBuilder.startJob(job)

    logger.info(
      s"[REDIS ${workerId}] processing job: '${job}' jobInfo: '${jobInfo}' on Try '${jobTry.number}'"
    )

    if (jobTry.number >= TRY_THRESHOLD) {
      logger.error(s"[REDIS ${workerId}] job ${job} is on try ${jobTry.number}, bailing out")
      jobBuilder.failed(job, "Exceeded Tries")
      0 second
    } else {
      f()
    }
  }

  private def handleDependencies(
      job: QueuedJob,
      dependencies: Seq[QueuedJob]
  )(f: () => FiniteDuration): FiniteDuration = {
    val dependencyStates = jobBuilder.getStates(dependencies)
    // no dependencies are in the progress state

    if (!dependencyStates.exists(_ != null)) {
      f()
    } else {
      // some dependencies are still in progress, we reinsert the job to be tried again later
      logger.debug(
        s"[REDIS ${workerId}] dependencies not ready for job ${job}, reinsert. dependencyStates: ${dependencyStates}"
      )
      jobBuilder.reInsertExistingJob(job)

      // reschedule in 1 second. If we reschedule immediately, we basically have a busy loop until the dependencies are ready (if the queue is otherwise empty)
      // TODO: is there a better way to handle this?
      1 second
    }
  }

  private def handleDoneWithDelay(
      job: QueuedJob,
      jobInfo: JobEntry,
      assemblyReference: AssemblyReference,
      triedDone: Try[Done],
      delay: FiniteDuration
  ): FiniteDuration = {
    val debugString = s"${assemblyReference.team}/${assemblyReference.id}/${assemblyReference.version}"
    handleDone(job, jobInfo, debugString, triedDone, Some(delay))
  }

  private def handleDone(
      job: QueuedJob,
      jobInfo: JobEntry,
      assemblyReference: AssemblyReference,
      triedDone: Try[Done]
  ): FiniteDuration = {
    val debugString = s"${assemblyReference.team}/${assemblyReference.id}/${assemblyReference.version}"
    handleDone(job, jobInfo, debugString, triedDone, delay = None)
  }

  private def handleDone(
      job: QueuedJob,
      jobInfo: JobEntry,
      debugInfo: String,
      triedDone: Try[Done],
      delay: Option[FiniteDuration]
  ): FiniteDuration =
    triedDone match {
      case Failure(exception) =>
        logger.error(
          s"[REDIS ${workerId}] failed try ${jobInfo.jobType} ${job.job} for ${debugInfo}: ${exception.getMessage}",
          exception
        )

        val delayToReinsert = delay.getOrElse(2.seconds)
        scheduler.schedule(
          new Runnable {
            override def run(): Unit = jobBuilder.reInsertExistingJob(job)
          },
          delayToReinsert.toMillis,
          TimeUnit.MILLISECONDS
        )

        exception match {
          case x: ConnectException =>
            // if a connect exception occurs we pause scheduling for a bit.
            // Either the pod was shut down, so we try to give the shutdown hooks time to react
            // Or networking is wonky, so we dont want to reschedule too often.
            // This is a bit of a hack, but there is no obvious way to detect if the pod is shutting down
            // (since the linkerd proxy is shutdown first and further connections will fail)
            logger.error(s"[REDIS ${workerId}] A Connection Exception occured, wait for a bit...")
            20 seconds

          case _ => 0 second
        }

      case Success(value) =>
        logger.info(s"[REDIS ${workerId}] finished job ${jobInfo.jobType} jobId: ${job.job} for ${debugInfo}")
        jobBuilder.finishJob(job)
        0 second
    }

}
