package de.fellows.ems.renderer.impl.pool.odb

import de.fellows.ems.pcb.model.graphics.{GPoint, Geometry, Graphic, ReversePathIterator}
import de.fellows.ems.renderer.impl.simple.SimpleGerberProcessor
import de.luminovo.odb.odbpp.model.ODBFeatures
import de.luminovo.odb.odbpp.model.constants.{Clockwise, CounterClockwise, PolygonHole, PolygonIsland}
import org.apache.batik.ext.awt.geom.ExtendedGeneralPath

import java.awt.Shape
import java.awt.geom.{Area, GeneralPath, Path2D}

object SurfaceRenderer {

  implicit def toFloat(d: Double): Float = d.floatValue

  def render(polygons: Seq[ODBFeatures.Polygon], internalScale: Int): ExtendedGeneralPath = {
    val fullPath = new ExtendedGeneralPath(Path2D.WIND_EVEN_ODD)

    polygons.foreach { p =>
      val path = new ExtendedGeneralPath(Path2D.WIND_EVEN_ODD)
      path.moveTo(p.xbs * internalScale, p.ybs * internalScale)

      p.curves.foreach {
        case ODBFeatures.CurveLine(x, y, cw) =>
          path.lineTo(x * internalScale, y * internalScale)
        case ODBFeatures.CurveArc(xe, ye, xc, yc, cw) =>
          val end    = GPoint(xe, ye) * internalScale
          val center = GPoint(xc, yc) * internalScale
          val _start = path.getCurrentPoint
          val start  = GPoint(_start.getX, _start.getY)
          val direction = cw match {
            case Clockwise => de.fellows.ems.renderer.impl.gerber.Clockwise()
            case CounterClockwise => de.fellows.ems.renderer.impl.gerber.Counterclockwise()
          }

          val arcPositions = SimpleGerberProcessor.getArcPositions(
            from = start,
            to = end,
            center = center,
            mode = direction
          )
          val (absSweep: Double, sweepFlag: Int, largeFlag: Int) = SimpleGerberProcessor.findArcParameters(arcPositions)

          val radius = end.distance(center)
          val large = !Geometry.onConvexSide(start, end, center, cw == Clockwise)
          if(absSweep == 2 * Math.PI) {
            // full circle
            val (mx, my) = (2 * center.x - end.x, (2 * center.y - end.y))
            path.arcTo(radius, radius, 0, large, cw == CounterClockwise, mx, my)
            path.arcTo(radius, radius, 0, large, cw == CounterClockwise, end.x, end.y)
          }else{
            path.arcTo(radius, radius, 0, large, cw == CounterClockwise, end.x, end.y)
          }


        //path.moveTo(end.x, end.y)
      }

      //path.closePath()

      p.polyType match {
        case PolygonHole   => fullPath.append(path, false) // fullPath.append(path, false) //  todo inverse
        case PolygonIsland => fullPath.append(path, false)
      }
    }

    fullPath
  }

}
