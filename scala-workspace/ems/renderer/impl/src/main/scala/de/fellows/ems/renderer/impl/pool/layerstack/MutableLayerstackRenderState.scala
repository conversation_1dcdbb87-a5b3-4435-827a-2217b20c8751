package de.fellows.ems.renderer.impl.pool.layerstack

import de.fellows.ems.layerstack.api
import de.fellows.ems.renderer.impl.render.Renderer
import org.apache.batik.svggen.SVGGraphics2D

import java.awt.Dimension
import java.awt.geom.Rectangle2D

class MutableLayerstackRenderState {
  def draw(params: RenderParameters): SVGGraphics2D = {
    val gr = Renderer.createGraphics

    gr.translate(params.padding, params.padding)

    var currentSubstackOffset = 0.0

    val allDims = substacks.result().map { substack =>
      val subgraphics = gr.create().asInstanceOf[SVGGraphics2D]
      val dims        = substack.draw(subgraphics, params)

      subgraphics.dispose()

      val thisWidth = dims.getWidth + params.verticalGap
      currentSubstackOffset += thisWidth
      gr.translate(thisWidth, 0)

      new Rectangle2D.Double(0, 0, currentSubstackOffset, dims.getHeight)
    }

    val rec: Rectangle2D = allDims.reduce[Rectangle2D]((a, b) => a.createUnion(b))
    gr.setSVGCanvasSize(new Dimension(
      rec.getWidth.intValue,
      (rec.getHeight + params.padding + params.padding).intValue
    ))

    gr
  }

  val substacks                                        = Seq.newBuilder[MutableSubStackRenderState]
  var currentStack: Option[MutableSubStackRenderState] = None

  def newSubStack(sstack: api.SubStackDefinition): MutableSubStackRenderState = {
    val substack = new MutableSubStackRenderState(sstack)
    currentStack = Some(substack)
    substacks += substack
    substack
  }
}
