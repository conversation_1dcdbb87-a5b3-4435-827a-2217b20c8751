package de.fellows.ems.renderer.impl.simple

import de.fellows.ems.pcb.model.graphics.GerberApertureDefinition

import scala.collection.immutable.Seq

sealed trait SVGCommand {
  val transform: Seq[SVGTransformCommand]

  def hardTranslate(x: Double, y: Double): SVGCommand
}

object SVGCommand {
  def nonEmpty(content: Seq[SVGCommand]): Boolean =
    content.exists {
      case SVGGroupCommand(_, commands, _) => nonEmpty(commands)
      case _                               => true
    }

  def isEmpty(content: Seq[SVGCommand]): Boolean =
    !nonEmpty(content)
}

case class SVGPathInstruction(
    instruction: SVGPathInstructionIdentifier,
    params: Seq[Number],
    target: Option[(Number, Number)],
    override val transform: Seq[SVGTransformCommand] = Seq()
) extends SVGCommand {

  def getTarget(): Option[(Number, Number)] =
    instruction match {
      case M | L | S | C | Q | A     => target
      case RelM | RelL | RelH | RelV => target
      case _                         => None
    }

  override def hardTranslate(x: Double, y: Double): SVGPathInstruction = {
    val _params = params.map(_.doubleValue())
    val translatedParams: Seq[Number] = instruction match {
      case M => Seq(_params(0) + x, _params(1) + y)
      case L => Seq(_params(0) + x, _params(1) + y)
      case H => Seq(_params(0) + x)
      case V => Seq(_params(0) + y)
      case S => Seq(
          _params(0) + x,
          _params(1) + y,
          _params(2) + x,
          _params(3) + y
        )
      case A => Seq(
          _params(0), // radius x
          _params(1), // radius y
          _params(2), // rotation
          _params(3), // large arc flag
          _params(4), // sweep,
          _params(5) + x,
          _params(6) + y
        )
      case Q => Seq(
          _params(0) + x,
          _params(1) + y,
          _params(2) + x,
          _params(3) + y
        )
      case T => Seq(
          _params(0) + x,
          _params(1) + y
        )
      case C => Seq(
          _params(0) + x,
          _params(1) + y,
          _params(2) + x,
          _params(3) + y,
          _params(4) + x,
          _params(5) + y
        )

      case _ => params
    }

    copy(params = translatedParams)
  }
}

object SVGPathInstruction {

  def apply(
      instruction: SVGPathInstructionIdentifier,
      params: Seq[Number]
  ): SVGPathInstruction =
    new SVGPathInstruction(instruction, params, getTarget(instruction, params))

  def apply(
      instruction: SVGPathInstructionIdentifier,
      params: Seq[Number],
      target: (Number, Number)
  ): SVGPathInstruction =
    new SVGPathInstruction(instruction, params, Some(target))

  def apply(
      instruction: String,
      params: Seq[Number]
  ): SVGPathInstruction =
    SVGPathInstruction(SVGPathInstructionIdentifier(instruction), params)

  def getTarget(instruction: SVGPathInstructionIdentifier, params: Seq[Number]): Option[(Number, Number)] =
    instruction match {
      case M => Some((params(0), params(1)))
      case L => Some((params(0), params(1)))
      case S => Some((params(2), params(3)))
      case C => Some((params(4), params(5)))
      case Q => Some((params(2), params(3)))
      case A => Some((params(5), params(6)))
      case _ => None
    }

}

case class SVGPathCommand(
    path: Seq[SVGPathInstruction],
    aperture: GerberApertureDefinition,
    override val transform: Seq[SVGTransformCommand] = Seq()
) extends SVGCommand {
  override def hardTranslate(x: Double, y: Double): SVGPathCommand =
    copy(path = path.map(_.hardTranslate(x, y)))
}
case class SVGPolygonCommand(
    path: Seq[SVGPathInstruction],
    override val transform: Seq[SVGTransformCommand] = Seq()
) extends SVGCommand {
  override def hardTranslate(x: Double, y: Double): SVGPolygonCommand =
    copy(path = path.map(_.hardTranslate(x, y)))
}
case class SVGUseCommand(
    ref: String,
    x: Double,
    y: Double,
    aperture: GerberApertureDefinition,
    override val transform: Seq[SVGTransformCommand] = Seq()
) extends SVGCommand {

  override def hardTranslate(_x: Double, _y: Double): SVGUseCommand =
    copy(x = x + _x, y = y + _y)
}

case class SVGGroupCommand(
    id: String,
    commands: Seq[SVGCommand],
    override val transform: Seq[SVGTransformCommand] = Seq()
) extends SVGCommand {
  override def hardTranslate(x: Double, y: Double): SVGGroupCommand =
    copy(commands = commands.map(_.hardTranslate(x, y)))
}
