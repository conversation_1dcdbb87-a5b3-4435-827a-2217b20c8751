package de.fellows.ems.renderer.impl.simple

import de.fellows.ems.gerber.parser.{Ger<PERSON><PERSON><PERSON><PERSON>, GerberParser}
import de.fellows.ems.pcb.model.graphics.{GPoint, GerberApertureDefinition, Macro}
import de.fellows.ems.renderer.impl.gerber.Graphics
import de.fellows.ems.renderer.impl.gerber.builders.BoundsBuilder
import de.fellows.ems.renderer.impl.simple.SimpleGerberRenderer.{optimizePath, setPrecision}
import de.fellows.utils.graphics.PathPrinter
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.{DebugUtils, UUIDUtils}
import org.antlr.v4.runtime.tree.ParseTreeWalker
import org.antlr.v4.runtime.{CharStreams, CommonTokenStream}
import org.slf4j.Logger

import java.io.InputStream
import java.text.DecimalFormat
import java.util.UUID

case class ConvertedGerberLayer(id: String, svg: String, mask: Option[String], transform: Option[String])

/** Renders a gerber file to SVG.
  *
  * This rendering only yields the svg, no collision detection, trace detection, etc.
  * the svg heavily relies on masks, which should be supported by all modern browsers.
  *
  * @param file
  */
class SimpleGerberRenderer(file: InputStream) extends StackrateLogging {
  implicit val lg: Logger = logger.logger
  val ids                 = new IDGenerator()

  private def createGerberParser: GerberParser = {
    val stream                = CharStreams.fromStream(file)
    val l: GerberLexer        = new GerberLexer(stream)
    val ts: CommonTokenStream = new CommonTokenStream(l)
    val parser                = new GerberParser(ts)

    parser
  }

  private def convertLayers(
      value: Seq[GerberMaskLayer],
      scaling: Int,
      proc: SimpleGerberProcessor,
      bounds: BoundsBuilder
  ): Seq[ConvertedGerberLayer] = {

    def convertLayer(
        id: String,
        svg: Seq[SVGCommand],
        mask: Seq[SVGCommand],
        transform: Option[SVGTransform]
    ): ConvertedGerberLayer = {
      val maskCommands =
        if (SVGCommand.isEmpty(mask)) {
          None
        } else {
          Some(createSVG(mask, scaling, proc.getMacroDefinitions(), false).mkString(""))
        }

      val svgCommands = createSVG(svg, scaling, proc.getMacroDefinitions()).mkString("")

      val transformString =
        if (transform.isEmpty || transform.get.commands.isEmpty) {
          None
        } else {
          val origin = transform.flatMap(_.origin).map(o => s""" transform-origin="${o.x} ${o.y}" """)
          Some(s"""transform="${transform.toSeq.flatMap(_.commands.map(_.toSVGString)).mkString(
              " "
            )}" ${origin.getOrElse("")}""")
        }

      ConvertedGerberLayer(id, svgCommands, maskCommands, transformString)
    }

    condense(value)
      .map { x =>
        bounds.extend(x.bounds.result())
        convertLayer(x.id, x.svg.result(), x.mask.result(), x.transform)
      }
  }

  private def nestLayers(orderedLayers: Seq[ConvertedGerberLayer], uniqueLayerId: String): String = {
    def _nestLayers(layer: ConvertedGerberLayer, tail: Seq[ConvertedGerberLayer]): String = {

      val inner =
        if (tail.isEmpty) {
          ""
        } else {
          _nestLayers(tail.head, tail.tail)
        }

      val maskString = layer.mask.map(m => s"""mask="url(#${getMaskId(uniqueLayerId, layer)})"""").getOrElse("")
      s"""<g id="${layer.id}" $maskString ${layer.transform.getOrElse("")}>${inner} ${layer.svg}</g>"""
    }

    // reverse the order to make nesting easier. last drawn layers are the most outer groups
    val reversedOrder = orderedLayers.reverse
    if (reversedOrder.isEmpty) {
      ""
    } else {
      _nestLayers(reversedOrder.head, reversedOrder.tail)
    }
  }

  /** combines layers that have the same transform and no mask. also removes empty layers
    *
    * the list must be in draw order. Earlier drawn features must be before later drawn features
    *
    * @param layers
    * @return
    */
  private def condense(layers: Seq[GerberMaskLayer]): Seq[GerberMaskLayer] = {
    val condensed = Seq.newBuilder[GerberMaskLayer]

    var workingCopyContainer: Option[GerberMaskLayer] = None

    layers
      .dropWhile { x =>
        // we can drop all layers until we have one where we actually draw something.
        // reason: sometimes gerbers define useless clear elements at the beginning even though nothing is drawn. we can drop those masks
        x.svg.result().isEmpty
      }
      .foreach { next =>
        workingCopyContainer match {
          case Some(workingCopy) =>
            val mask        = next.mask.result()
            val svg         = next.svg.result()
            val currentMask = workingCopy.mask.result()
            val transform   = next.transform

            if (svg.isEmpty && mask.isEmpty) {
              // drop this layer
            } else if (mask.isEmpty && currentMask.isEmpty && transform == workingCopy.transform) {
              // combine both layers
              val b = Seq.newBuilder[SVGCommand]
              b ++= workingCopy.svg.result()
              b ++= next.svg.result()
              workingCopyContainer = Some(
                workingCopy.copy(
                  svg = b
                  // mask is empty anyway
                )
              )
            } else {
              // layer changed significantly, add the current working copy and use this layer as the next working copy
              condensed += workingCopy
              workingCopyContainer = Some(next)
            }

          case None => workingCopyContainer = Some(next)
        }
      }

    // dont forget to add the current working copy too
    workingCopyContainer.foreach(condensed += _)

    condensed.result()
  }

  def render(): String = {

    val proc: SimpleGerberProcessor = doProcessing

    val scaling = proc.gContext.format.get.getGerberScaling.intValue

    val allGerberLayers: Seq[GerberMaskLayer] = proc.layers.result()

    val defs = createApertureDefs(proc, allGerberLayers, scaling)

    val boundsBuilder = new BoundsBuilder()
    val layers        = convertLayers(allGerberLayers, scaling, proc, boundsBuilder)

    val bounds = boundsBuilder.result()

    // create a unique id for this svg. When multiple svgs are shown,
    // the client can get confused if the ids are not unique.
    val uniqueLayerId = UUIDUtils.createTiny()

    // create all the masks
    val masks = layers.flatMap { layer =>
      layer.mask.map { mask =>
        val maskId = getMaskId(uniqueLayerId, layer)
        s"""<mask id="${maskId}" fill="#000" stroke="#000"><g><rect x="${bounds.getMinX}" y="${bounds.getMinY}" height="${bounds.getHeight}" width="${bounds.getWidth}"  fill="#fff"/>${mask}</g></mask>"""
      }
    }

    val nestedSvg = nestLayers(layers, uniqueLayerId)
    val flip      = s"""transform="scale(1,-1) translate(0, ${-bounds.getMaxY - bounds.getMinY})" """

    s"""<svg class="gerber-svg" fill="black" stroke="black" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="${bounds.getMinX} ${bounds.getMinY} ${bounds.getWidth} ${bounds.getHeight}"><defs>${masks.mkString(
        ""
      )} ${defs}</defs><g>${nestedSvg}</g></svg>"""
  }

  private def getMaskId(uniqueLayerId: String, layer: ConvertedGerberLayer) =
    uniqueLayerId + "-" + layer.id + "-mask"

  def doProcessing = {
    val proc = new SimpleGerberProcessor(ids)
    DebugUtils.timed("parse") {
      val p      = createGerberParser
      val walker = new ParseTreeWalker

      walker.walk(proc, p.gerber())
    }
    proc
  }

  /** collect all the aperture D-Codes that are actually used
    *
    * @param layers
    * @return
    */
  private def collectUsedApertures(layers: Seq[GerberMaskLayer]): Set[String] = {

    def collectInCommands(commands: Seq[SVGCommand]): Set[String] =
      commands.flatMap {
        case SVGPathCommand(path, aperture, transform)     => Some(aperture.id)
        case SVGUseCommand(ref, x, y, aperture, transform) => Some(aperture.id)
        case SVGGroupCommand(id, commands, transform)      => collectInCommands(commands)
        case _                                             => None
      }.toSet

    layers.flatMap { l =>
      collectInCommands(l.svg.result() ++ l.mask.result())
    }.toSet
  }

  /** convert all apertures to svg defs
    */
  def createApertureDefs(proc: SimpleGerberProcessor, layers: Seq[GerberMaskLayer], scaling: Int) = {
    val aperturesInUse = collectUsedApertures(layers)
    // we only add defs for apertures that are in use
    proc.apertures.getApertureDefinitions().filter(a => aperturesInUse.contains(a._2.id)).flatMap { x =>
      val (id, aperture) = x

      createApertureDefSvg(id, scaling, id, aperture, proc.getMacroDefinitions())
    }.mkString("")
  }

  /** create the svg string for a single aperture.
    *
    * does not use a target point, so this should be used in a defs tag
    * @return
    */
  private def createApertureDefSvg(
      svgid: String,
      scaling: Int,
      id: String,
      aperture: GerberApertureDefinition,
      macros: Map[String, Macro],
      target: Option[GPoint] = None
  ): Option[String] = {
    val t = target.getOrElse(GPoint(0, 0))
    aperture.template match {
      case "C" =>
        val dia    = aperture.args(0).toDouble * scaling
        val radius = setPrecision(dia / 2)
        val aid    = svgid

        Some(s"""<circle id="$aid" cx="${t.x}" cy="${t.y}" r="$radius"/>""")

      case "R" =>
        val height = setPrecision(aperture.args(1).toDouble * scaling)
        val width  = setPrecision(aperture.args(0).toDouble * scaling)

        val aid = svgid
        Some(
          s"""<rect id="$aid" x="${-(width / 2) + t.x}" y="${-(height / 2) + t.y}" width="$width" height="$height"/>"""
        )

      case "P" =>
        val aid = svgid

        val radius       = setPrecision(aperture.args(0).toDouble * scaling / 2)
        val vertices     = aperture.args(1).toInt
        val rotation     = aperture.args.lift(2).map(_.toDouble)
        val holeDiameter = aperture.args.lift(3).map(_.toDouble)

        val polygon = Graphics.createNaturalPolygon(vertices, GPoint(0, 0), radius, rotation)

        val sb = new StringBuffer

        PathPrinter.print(polygon.getPathIterator(null), sb, invertY = true, shift = Some((t.x, t.y)))
        Some(
          s"""<path id="$aid" d="${sb.toString}"/>"""
        )
      case "O" =>
        val height = setPrecision(aperture.args(1).toDouble * scaling)
        val width  = setPrecision(aperture.args(0).toDouble * scaling)

        val aid = svgid

        val round =
          if (height < width) {
            s"""ry="${height / 2}" """
          } else {
            s"""rx="${width / 2}" """
          }

        Some(
          s"""<rect id="$aid" ${round} x="${-(width / 2)}" y="${-(height / 2)}" width="$width" height="$height"/>"""
        )

      case x =>
        macros.get(x) match {
          case None =>
            logger.info(s"unable to render ${x}")
            None
          case Some(mac) =>
            val macDef = new SimpleMacroConverter(aperture, mac, scaling)
              .flash(GPoint(0, 0))

            val aid = svgid
            Some(
              s"""<g id="${aid}">${macDef}</g>"""
            )

        }

    }
  }

  /** creates the svg transform parameter for the given command
    * @param c
    * @return
    */
  def transformString(c: SVGCommand) =
    if (c.transform.isEmpty) {
      ""
    } else {
      s"""transform="${c.transform.map(_.toSVGString).mkString(" ")}""""
    }

  private def createSVG(
      svgcommands: Seq[SVGCommand],
      scaling: Int,
      macros: Map[String, Macro],
      withUses: Boolean = true
  ): Seq[String] = {
    val commands = Seq.newBuilder[String]

    // draw lines with the same aperture in one big path
    val byAperture = svgcommands.collect {
      case x: SVGPathCommand => x
    }.groupBy(_.aperture)

    byAperture.map { x =>
      val (aperture, cs) = x

      val combinedPath = (cs.flatMap {
        case SVGPathCommand(path, aperture, transform) =>
          // TODO: apply transform
          path

        case _ => Seq()
      })

      if (combinedPath.nonEmpty) {
        val combinedPathString = optimizePath(combinedPath)
        commands += s"""<path d="$combinedPathString" fill="none" stroke-width="${setPrecision(
            aperture.width(scaling.toDouble).getOrElse(1.0)
          )}" stroke-linecap="round"/>"""
      }
    }

    // draw the rest
    svgcommands.foreach {
      case c: SVGPolygonCommand =>
        commands += s"""<path stroke="none" ${transformString(c)} d="${optimizePath(c.path)}" />"""
      case c: SVGUseCommand =>
        if (withUses) {
          commands += s"<use ${transformString(c)} xlink:href=\"#${c.ref}\" x=\"${c.x}\" y=\"${c.y}\" />"
        } else {
          createApertureDefSvg("", scaling, c.ref, c.aperture, macros, target = Some(GPoint(c.x, c.y)))
            .foreach(commands += _)

        }
      case c: SVGGroupCommand =>
        commands += s"""<g ${transformString(c)} id="${c.id}"> ${createSVG(
            c.commands,
            scaling,
            macros,
            withUses
          ).mkString(" ")} </g>"""
      case _ =>
    }
    commands.result()
  }
}

object SimpleGerberRenderer {

  /** applies some optimizations for paths. this decreases file size and complexity, hopefully improving performance
    *
    *  1. relativize moves.
    *  1. remove redundant moves
    *  1. convert straight lines to h/v
    *
    * @param combinedPath
    * @return
    */
  def optimizePath(combinedPath: Seq[SVGPathInstruction]) = {
    var previousOption: Option[SVGPathInstruction] = None

    val optimizedPaths: Seq[SVGPathInstruction] = combinedPath.flatMap { x =>
      val keep: Option[SVGPathInstruction] =
        previousOption match {
          case Some(previous) =>
            x match {
              case path @ SVGPathInstruction(M, _, _, transform) =>
                (previous.getTarget(), path.getTarget()) match {
                  case (Some((previousX, previousY)), Some(target @ (nextX, nextY))) =>
                    if (previousX != nextX || previousY != nextY) {
                      // relative move. this decreases the file size a lot
                      Some(
                        SVGPathInstruction(
                          RelM,
                          Seq(
                            setPrecision(nextX.doubleValue() - previousX.doubleValue()),
                            setPrecision(nextY.doubleValue() - previousY.doubleValue())
                          ),
                          Some(target),
                          transform
                        )
                      )

                    } else {
                      // we can drop this move, since it just moves to the point we are already at
                      None
                    }

                  case _ => Some(path)
                }

              case path @ SVGPathInstruction(L, _, _, _) =>
                (previous.getTarget(), path.getTarget()) match {
                  case (Some((previousX, previousY)), Some(target @ (nextX, nextY))) =>
                    if (nextX == previousX && nextY == previousY) {
                      // if we end up in the same place, we don't need this line
                      None
                    } else if (nextX == previousX) {
                      // if we're moving in a straight line, we don't need both x and y
                      Some(
                        RelV(
                          setPrecision(nextY.doubleValue() - previousY.doubleValue()),
                          target
                        )
                      )
                    } else if (nextY == previousY) {
                      // if we're moving in a straight line, we don't need both x and y
                      Some(
                        RelH(
                          setPrecision(nextX.doubleValue() - previousX.doubleValue()),
                          target
                        )
                      )
                    } else {
                      Some(
                        RelL(
                          setPrecision(nextX.doubleValue() - previousX.doubleValue()),
                          setPrecision(nextY.doubleValue() - previousY.doubleValue()),
                          target
                        )
                      )
                    }

                  case _ => Some(path)
                }

              case other => Some(other)

            }
          case None => Some(x)
        }

      keep match {
        case Some(value) =>
          previousOption = keep
        case _ =>
      }

      keep
    }

    val sb = new StringBuilder
    previousOption = None

    optimizedPaths.foreach { i =>
      var added = false
      previousOption match {
        case Some(previous) if previous.instruction == i.instruction =>
        // do nothing
        case _ =>
          sb.append(s"${i.instruction}")
          added = true
      }

      val psb = new StringBuilder()
      i.params.foreach { p =>
//        psb.append(p.doubleValue())
        psb.append(df.format(p))
      }

      // we don't need the extra space if we added the instruction command before
      if (added && psb.startsWith(" ")) {
        sb.append(psb.substring(1))
      } else {
        sb.append(psb)
      }

      previousOption = Some(i)
    }

    sb.toString()
  }

  /** rounds a double to a precision of 3 digits.
    * may reduce accuracy a tiny bit, but makes more optimization possible, as well as making the svg smaller
    *
    * @param d
    * @return
    */
  def setPrecision(d: Double): Double =
    (d * 1000.0).intValue / 1000.0

  def setPrecision(d: GPoint): GPoint =
    GPoint(
      setPrecision(d.x),
      setPrecision(d.y)
    )

  /** number format to print doubles with a precision of 4 digits. if there are no decimal digits (.0) we skip them
    * altogether. additionally, negative numbers do not need to be separated by a space, so we skip that too.
    */
  private val df = new DecimalFormat(" 0.####;-0.####")
}
