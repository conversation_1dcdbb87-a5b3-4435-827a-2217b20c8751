package de.fellows.ems.renderer.impl.gerber.graphic

import de.fellows.ems.pcb.model.graphics.{ApertureDefinition, GPoint, GerberApertureDefinition, Macro, ODBSymbolDefinition}
import de.fellows.ems.renderer.impl.gerber.GerberExceptions._
import de.fellows.ems.renderer.impl.gerber.Graphics
import de.fellows.ems.renderer.impl.gerber.builders.Java2DUtils
import de.fellows.ems.renderer.impl.gerber.macros.MacroFlasher
import de.fellows.ems.renderer.impl.pool.odb.{ODBGraphicsFactory, PadSupport}
import de.luminovo.odb.odbpp.model.features.symbols.{BasicStandardSymbol, UserSymbol}

import java.awt.Shape
import java.awt.geom._
import scala.util.Try

object ApertureUtils {

  def getNonZeroDecimal(s: Double): Double =
    getNonZeroDecimal(String.format("%f", s), 1).get.doubleValue

  /** Sometimes diameters are zero for some utility apertures. For rendering, we usually want to render them visibly, so
    * we take the smallest amount of size possible.
    * @param s
    * @return
    *   the paramter s as a Decimal that is not 0
    */
  def getNonZeroDecimal(s: String, scaling: Int): Try[BigDecimal] =
    Try {
      BigDecimal(s) * scaling match {
        case x if x == 0 =>
          // Sometimes the circle diameter is 0 for some utility lines. render them as the smallest diameter
          // possible, so that they are at least visible
          BigDecimal(1)
        case x => x
      }
    }

  def getDecimal(s: String): Try[BigDecimal] =
    Try(BigDecimal(s))

  def getShape(
      target: GPoint,
      ad: GerberApertureDefinition,
      scaling: Int,
      macroDefinitions: Map[String, Macro]
  ): Shape =
    ad.template match {
      case "C" =>
        val diameter = getNonZeroDecimal(ad.diameter(scaling).get)
        val radius   = diameter / 2
        new Ellipse2D.Double(
          (target.getBX - radius).toDouble,
          (target.getBY - radius).toDouble,
          diameter.toDouble,
          diameter.toDouble
        )

      case "R" =>
        Java2DUtils.rectangly(
          ad,
          target,
          scaling,
          (x, y, w, h) => new Rectangle2D.Double(x, y, w, h)
        )

      case "O" =>
        Java2DUtils.rectangly(
          ad,
          target,
          scaling,
          (x, y, w, h) => {
            val arc =
              w min h // TODO: correct arc, just use the width or height for now, as this seems to be the most common case
            new RoundRectangle2D.Double(
              x,
              y,
              w,
              h,
              arc,
              arc
            )
          }
        )

      case "P" =>
        val diameter = ad.getDouble(0) * scaling
        val vertices = ad.getDouble(1)

        val rotation = ad.getDoubleOption(2)

        val radius  = diameter / 2
        val centerX = target.getBX
        val centerY = target.getBY

        val poly: Area = Graphics.createNaturalPolygonArea(vertices, GPoint(centerX, centerY), radius, rotation)

        poly

      case x if macroDefinitions.contains(x) =>
        val macrodef = macroDefinitions.get(x)

        MacroFlasher(ad, macrodef.get, scaling).flash(target)

      case x => s"Aperture $x unavailable" !!
    }

  def getArea(target: GPoint, ad: ApertureDefinition, scaling: Int, macroDefinitions: Map[String, Macro]): Area =
    ad match {
      case gad: GerberApertureDefinition =>
        new Area(getShape(target, gad, scaling, macroDefinitions))

      case osd: ODBSymbolDefinition =>
        osd.symbol match {
          case x: UserSymbol => ???
          case x: BasicStandardSymbol =>
            new Area(ODBGraphicsFactory.renderer(x).asInstanceOf[PadSupport[BasicStandardSymbol]].flash(
              x,
              target,
              scaling
            ))
        }
    }
}
