package de.fellows.ems.renderer.impl.gerber

import de.fellows.ems.gerber.parser.GerberParser
import de.fellows.ems.renderer.impl.gerber.GerberExceptions._
import de.fellows.utils.Units
import org.antlr.v4.runtime.ParserRuleContext

sealed trait MeasurementUnit {
  val unitScale: Double
  val name: String

  def unit: String =
    this match {
      case Inch()       => Units.INCH
      case Millimetre() => Units.MILLIMETER
    }
}

case class Inch() extends MeasurementUnit {
  override val unitScale    = 25.4
  override val name: String = Units.INCH
}

case class Millimetre() extends MeasurementUnit {
  override val unitScale    = 1
  override val name: String = Units.MILLIMETER
}

sealed trait GerberCoordinate
object GerberCoordinate {
  case object Absolute    extends GerberCoordinate
  case object Incremental extends GerberCoordinate
}

case class GerberFormat(
    xInt: Int,
    xDec: Int,
    yInt: Int,
    yDec: Int,
    leadingZeroes: Boolean = false,
    gerberUnit: Option[MeasurementUnit],
    gerberScaling: Option[Double] = None,
    coordinate: GerberCoordinate
) {

  def getGerberScaling: Double =
    gerberScaling.getOrElse(GerberFormat.DEFAULT_IMAGE_SCALING)

  def getImageScaling: Double =
    GerberFormat.DEFAULT_IMAGE_SCALING

  def getStardizedUnit: MeasurementUnit = Millimetre()

  def toX(str: String)(implicit ctx: ParserRuleContext): Double =
    to(str, x = true)

  def toY(str: String)(implicit ctx: ParserRuleContext): Double =
    to(str, x = false)

  def to(str: String, x: Boolean)(implicit ctx: ParserRuleContext) = {

    val numberstring =
      if (str.contains(".")) {
        str
      } else {
        var s = str
        var i =
          if (x) {
            xInt
          } else {
            yInt
          }

        val d =
          if (x) {
            xDec
          } else {
            yDec
          }

        var neg = false

        if (s.startsWith("+")) {
          s = s.substring(1);
          neg = false
        }
        if (s.startsWith("-")) {
          s = s.substring(1);
          neg = true
        }

        val sum = i + d
        //    var debug = false
        //    if (s.length > sum) {
        //      val diff = s.length - sum
        //      s"Illegal Decimal $str has ${diff} extra characters" ~;
        //      debug = true
        //      sum = s.length
        //      i = i + diff
        //      //      s = s.substring((s.length - sum), sum)
        //    }

        if (s.length < sum) {
          val zeroes = "0" * (sum - s.length)
          if (leadingZeroes) {
            s = s + zeroes
          } else {
            s = zeroes + s
          }
        }

        val preComma = s.length - d

        var toParse = s"${s.substring(0, preComma)}.${s.substring(preComma)}"
        if (neg) {
          toParse = s"-$toParse"
        }

        toParse
      }

    //    if (debug) {
    //      println(s"nmbr is ${toParse}")
    //    }

    try {
      val parsed  = BigDecimal(numberstring)
      val scaling = gerberScaling.getOrElse(GerberFormat.DEFAULT_IMAGE_SCALING)
      val res     = (parsed * scaling).doubleValue
      res
    } catch {
      case e: NumberFormatException => s"Illegal Number ${numberstring}" !
    }
  }

  def resolution(): BigDecimal =
    BigDecimal(1.0 / Math.pow(10, Math.max(this.xDec, this.yDec)))
}

object GerberFormat {
  val DEFAULT_IMAGE_SCALING: Double = 100.0

  val DEFAULT_UNIT         = Inch()
  val DEFAULT_UNIFIED_UNIT = Millimetre()

  val DEFAULT_FORMAT = GerberFormat(
    xInt = 2,
    xDec = 5,
    yInt = 2,
    yDec = 5,
    gerberUnit = Some(DEFAULT_UNIT),
    gerberScaling = Some(DEFAULT_IMAGE_SCALING * DEFAULT_UNIT.unitScale),
    coordinate = GerberCoordinate.Absolute
  )

  def of(implicit ctx: GerberParser.FsContext): GerberFormat =
    if (ctx.x == null || ctx.y == null) {
      DEFAULT_FORMAT
    } else {
      val xText = ctx.x.getText
      val yText = ctx.y.getText

      if (xText.length != 2 || yText.length != 2) "Wrong format declaration" !

      val leading =
        ctx.T() != null

      val coordinate = if(ctx.I() != null){
        GerberCoordinate.Incremental
      }else{
        GerberCoordinate.Absolute
      }

      GerberFormat(
        Character.getNumericValue(xText.charAt(0)),
        Character.getNumericValue(xText.charAt(1)),
        Character.getNumericValue(yText.charAt(0)),
        Character.getNumericValue(yText.charAt(1)),
        leadingZeroes = leading,
        gerberUnit = Some(DEFAULT_UNIT),
        gerberScaling = Some(DEFAULT_IMAGE_SCALING * DEFAULT_UNIT.unitScale),
        coordinate = coordinate
      )
    }
}
