package de.fellows.ems.renderer.impl.pool

import java.util.concurrent.{PriorityBlockingQueue, ThreadPoolExecutor, TimeUnit}

class RendererExecutor(size: Int, q: PriorityBlockingQueue[Runnable] = new PriorityBlockingQueue[Runnable]())
    extends ThreadPoolExecutor(size, size, 10, TimeUnit.MINUTES, q) {}

object RendererExecutor {
  //  def compare[T >: Runnable]: Comparator[T] = (o1: T, o2: T) => {
  //    if (o1.isInstanceOf[RenderTaskWrapper] && o2.isInstanceOf[RenderTaskWrapper]) {
  //      val rtw1 = o1.asInstanceOf[RenderTaskWrapper]
  //      val rtw2 = o2.asInstanceOf[RenderTaskWrapper]
  //
  //      if (rtw1.rt.isInstanceOf[RenderTask] && !rtw2.rt.isInstanceOf[RenderTask]) {
  //        Integer.MIN_VALUE
  //      } else if (!rtw1.rt.isInstanceOf[RenderTask] && rtw2.rt.isInstanceOf[RenderTask]) {
  //        Integer.MAX_VALUE
  //      } else {
  //        val s1: Long = rtw1.scheduledAt.getOrElse(0L)
  //        val s2: Long = rtw2.scheduledAt.getOrElse(0L)
  //
  //        s1.compareTo(s2)
  //      }
  //    }
  //    else if (o1.isInstanceOf[Comparable[T]] && o2.isInstanceOf[T]) {
  //      o1.asInstanceOf[Comparable[T]].compareTo(o2.asInstanceOf[T])
  //    } else {
  //      0
  //    }
  //  }

}
