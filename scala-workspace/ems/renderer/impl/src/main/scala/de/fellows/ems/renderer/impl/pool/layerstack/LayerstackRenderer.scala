package de.fellows.ems.renderer.impl.pool.layerstack

import com.typesafe.config.Config
import de.fellows.app.assembly.commons.{AssemblyFiles, AssemblyReference}
import de.fellows.ems.layerstack.api
import de.fellows.ems.layerstack.api.{LayerStack, LayerstackDefinition, MaterialProperties, MaterialTypes}
import de.fellows.ems.pcb.model.HoleList
import de.fellows.utils.FilePath
import de.fellows.utils.meta.DecimalProperty
import org.apache.batik.svggen.SVGGraphics2D

import java.util.UUID

case class DrawnLayer(height: Double, material: Option[String], padding: Boolean, copper: Boolean)

case class DrawnDrills(from: Option[Int], to: Option[Int], micro: Boolean, plated: Boolean)

object LayerstackRenderer {
  def getLayerstackImagePath(team: String, id: UUID, version: UUID)(implicit conf: Config): FilePath =
    AssemblyFiles.createAssemblyResourceFolder(team, id, version)
      .copy(base = "layerstacks", filename = "preview.svg")

  def getLayerstackImagePath(assemblyReference: AssemblyReference)(implicit conf: Config): FilePath =
    getLayerstackImagePath(assemblyReference.team, assemblyReference.id, assemblyReference.version)

  def drawSingleLayer(state: MutableSubStackRenderState, height: Double, layer: api.LayerDefinition, copper: Boolean) =
    state.addMaterial(height, layer.material.flatMap(_.materialType), padding = true, copper = copper)

  def drawCoreLayer(state: MutableSubStackRenderState, height: Double, layer: api.LayerDefinition) = {
    // get CuThickness. this can be used to specify the height of both foils on this core layer
    val both = layer.material.flatMap(_.meta).flatMap(_.lowerCaseProperties.get(MaterialProperties.CuThickness)).filter(
      _.isInstanceOf[DecimalProperty]
    ).map(_.asInstanceOf[DecimalProperty].value).getOrElse(BigDecimal(0.0))

    // get specific heights if available
    val upperC: BigDecimal = layer.material.flatMap(_.meta).flatMap(
      _.lowerCaseProperties.get(MaterialProperties.UpperCuThickness)
    ).filter(_.isInstanceOf[DecimalProperty]).map(_.asInstanceOf[DecimalProperty].value).getOrElse(both)
    val lowerC: BigDecimal = layer.material.flatMap(_.meta).flatMap(
      _.lowerCaseProperties.get(MaterialProperties.LowerCuThickness)
    ).filter(_.isInstanceOf[DecimalProperty]).map(_.asInstanceOf[DecimalProperty].value).getOrElse(both)

    if (upperC > 0) {
      state.addMaterial(upperC.doubleValue, Some(MaterialTypes.FOIL), padding = false, copper = true)
    }

    val dielectricHeight = height - upperC - lowerC
    state.addMaterial(
      dielectricHeight.doubleValue,
      layer.material.flatMap(_.materialType),
      padding = false,
      copper = false
    )

    if (lowerC > 0) {
      state.addMaterial(lowerC.doubleValue, Some(MaterialTypes.FOIL), padding = false, copper = true)
    }
  }

  def drawLayerDefinition(layer: api.LayerDefinition, state: MutableSubStackRenderState) = {
    val height = layer.material.get.height().intValue()
    // val color  = layer.layerType.flatMap(x => LAYER_COLORS.get(x)).getOrElse(Color.LIGHT_GRAY)
    import de.fellows.ems.layerstack.api.MaterialTypes._
    val copperMaterials  = Seq(FOIL, RCC)
    val isCopperMaterial = layer.layerType.forall(x => copperMaterials.contains(x))

    layer.layerType.foreach {
      case CORE | FLEXCORE => drawCoreLayer(state, height, layer)
      case _               => drawSingleLayer(state, height, layer, isCopperMaterial)
    }

  }

  def drawSubStackDefinition(
      sstack: api.SubStackDefinition,
      state: MutableSubStackRenderState,
      holes: Seq[HoleList]
  ) = {
    sstack.layers.getOrElse(Seq()).foreach { layer =>
      drawLayerDefinition(layer, state)
    }

    holes.foreach { hl =>
      state.addDrills(hl)
    }
  }

  def renderLayerstack(
      layerStack: LayerStack,
      holes: Seq[HoleList],
      params: RenderParameters
  ): SVGGraphics2D = {
    val state: MutableLayerstackRenderState = new MutableLayerstackRenderState()

    layerStack.stacks.zipWithIndex.foreach { x =>
      val (sstack, index) = x

      val holesForThisSubStack =
        if (index == 0) {
          holes
        } else {
          Seq()
        }
      val sstackdef = sstack.definition.copy(layers = Some(sstack.layers.map(_.definition)))
      drawSubStackDefinition(sstackdef, state.newSubStack(sstackdef), holesForThisSubStack)
    }

    state.draw(params)
  }
  def renderLayerstackDefinition(
      layerStack: LayerstackDefinition,
      holes: Seq[HoleList],
      params: RenderParameters
  ): SVGGraphics2D = {
    val state: MutableLayerstackRenderState = new MutableLayerstackRenderState()

    layerStack.stacks.getOrElse(Seq()).zipWithIndex.foreach { x =>
      val (sstack, index) = x

      val holesForThisSubStack =
        if (index == 0) {
          holes
        } else {
          Seq()
        }
      drawSubStackDefinition(sstack, state.newSubStack(sstack), holesForThisSubStack)
    }

    state.draw(params)
  }

}
