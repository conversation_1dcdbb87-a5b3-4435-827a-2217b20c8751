package de.fellows.ems.renderer.impl.gerber.builders

import de.fellows.ems.gerber.parser.GerberParser.OpContext
import de.fellows.ems.gerber.parser.{GerberParser, GerberParserBaseListener}
import de.fellows.ems.pcb.model.graphics._
import de.fellows.ems.pcb.model.graphics.ops.Movement
import de.fellows.ems.renderer.impl.gerber.GerberExceptions._
import de.fellows.ems.renderer.impl.gerber._
import de.fellows.ems.renderer.impl.gerber.interpolation.awt.Interpolator
import de.fellows.ems.renderer.impl.gerber.macros.MacroWalker
import org.antlr.v4.runtime.ParserRuleContext
import org.antlr.v4.runtime.tree.{ParseTree, ParseTreeWalker}
import org.apache.batik.ext.awt.geom.ExtendedGeneralPath
import play.api.Logging

import java.awt.geom.Rectangle2D
import scala.jdk.CollectionConverters._
import scala.collection.mutable

abstract class BasicFileListener() extends GerberParserBaseListener with Logging {
  val gContext: GerberContext             = GerberContext()
  var beforeRegion: Option[GerberContext] = None

  protected val macroDefinitions = mutable.HashMap[String, Macro]()

  private var deprecatedOperationMode = false

  def apReg: ApertureRegistry

  var apertureAttributes: Option[Map[String, Seq[String]]] = None
  var objectAttributes: Option[Map[String, Seq[String]]]   = None

  override def enterEveryRule(ctx: ParserRuleContext): Unit = {}

  override def enterOf(ctx: GerberParser.OfContext): Unit = {}

  override def enterDeprecated(ctx: GerberParser.DeprecatedContext): Unit = {
    if (ctx.G71() != null) {
      gContext.setMeasurementUnit(Millimetre())
    }
    if (ctx.G70() != null) {
      gContext.setMeasurementUnit(Inch())
    }
    if (ctx.G91() != null) {
      gContext.setGerberCoordinate(GerberCoordinate.Incremental)
    }
  }

  override def enterG54(ctx: GerberParser.G54Context): Unit =
    selectAperture(s"D${ctx.integ().getText}")(ctx)

  override def enterGerber(ctx: GerberParser.GerberContext): Unit = {}

  override def exitGerber(ctx: GerberParser.GerberContext): Unit = {
    implicit val c = ctx
    if (gContext.steprepeat.isDefined) {
      closeStepRepeat
    }
  }

  override def enterMo(ctx: GerberParser.MoContext): Unit = {
    if (ctx.IN() != null) {
      gContext.setMeasurementUnit(Inch())
    }
    if (ctx.MM() != null) {
      gContext.setMeasurementUnit(Millimetre())
    }
  }

  override def enterTd(ctx: GerberParser.TdContext): Unit = {
    val params = Option(ctx.string()).map(_.getText.split(",").toSeq)

    if (params.isEmpty) {
      this.objectAttributes = None
    } else {
      this.objectAttributes = this.objectAttributes.map(x => x -- params.getOrElse(Seq()))
    }

  }

  override def enterTa(ctx: GerberParser.TaContext): Unit = {
    val attribute = ctx.string().getText
    val values    = Option(ctx.attval()).map(_.attstring().asScala.map(asc => asc.getText).toSeq).getOrElse(Seq())

    this.apertureAttributes = (this.apertureAttributes match {
      case None       => Some(Map(attribute -> values))
      case Some(atts) => Some(atts + (attribute -> values))
    })
  }

  val STANDARD_OBJECT_ATTRIBUTES = Seq(".N", ".P", ".C")

  override def enterTo(ctx: GerberParser.ToContext): Unit =
    if (ctx.attribute != null) {
      val attribute = ctx.attribute.getText
      val values    = ctx.toattr().asScala.map(_.getText).filter(_.nonEmpty).toSeq
      this.objectAttributes = this.objectAttributes match {
        case None    => Some(Map(attribute -> values))
        case Some(a) => Some(a + (attribute -> values))
      }
    }

  override def enterAd(ctx: GerberParser.AdContext): Unit =
    if (ctx.D() != null) {
      val text     = BigDecimal(ctx.name.getText).toString()
      val dCode    = s"D${text}"
      val template = ctx.template.getText
      val _args    = ctx.number().asScala.toSeq.map(_.getText)

      val ad = GerberApertureDefinition(dCode, template, _args, apertureAttributes)
      apReg.addApertureDefinition(dCode, ad)
      // TODO: this would break a lot of files (/home/<USER>/ki/grouped-files/SilkscreenTop/46389f1b-d2ec-49d1-affe-1a4372a204da/11ua-SilkscreenTop.gto)
      // but it was introduced to fix some broken files (unknown which ones)
      //
      //      this.gContext.explicitlySelected match {
      //        case None | Some(false) => this.gContext.aperture = Some(ad)
      //      }
    }

  def closeStepRepeat(implicit ctx: ParserRuleContext): Unit
  def openStepRepeat(implicit ctx: ParserRuleContext): Unit = {}

  override def enterSr(ctx: GerberParser.SrContext): Unit = {
    implicit val c = ctx

    def doClose(): Unit = {
      logger.warn(s"close step repeat! empty: ${gContext.steprepeat.isEmpty}")
      if (gContext.steprepeat.isEmpty) {
        "Cannot close missing Step-Repeat" !
      } else {
        closeStepRepeat
        gContext.steprepeat = None
      }
    }

    if (ctx.idist == null && ctx.jdist == null && ctx.xrep == null && ctx.yrep == null) {
      doClose()
    } else if (ctx.idist != null && ctx.jdist != null && ctx.xrep != null && ctx.yrep != null) {
      val xrep  = BigDecimal(ctx.xrep.getText).intValue
      val yrep  = BigDecimal(ctx.yrep.getText).intValue
      val idist = BigDecimal(ctx.idist.getText)
      val jdist = BigDecimal(ctx.jdist.getText)

      if (xrep == 1 && yrep == 1 && idist == 0.0 && jdist == 0.0) {
        // deprecated gerber usage, handle as a SR-close
        if (gContext.steprepeat.isDefined) {
          doClose
        }
      } else {
        if (gContext.steprepeat.isDefined) {
          // deprecated gerber usage, handle as a SR-close and start
          doClose
        }
        logger.warn("start step repeat!")
        gContext.point = Some(GPoint(0, 0))
        gContext.steprepeat = Some(
          StepRepeat(
            new GerberBlock(Seq(), xrep, yrep, idist, jdist),
            xrep,
            yrep,
            idist,
            jdist
          )
        )
        openStepRepeat
      }

    } else {
      "Illegal Step Repeat" !
    }
  }

  def polarityChanged(from: Polarity, to: Polarity): Unit

  override def enterLp(ctx: GerberParser.LpContext): Unit = {
    val oldPolarity = this.gContext.polarity
    this.gContext.polarity = ctx.polarity.getText match {
      case "C" => Clear()
      case "D" => Dark()
      case _   => Dark()
    }

    if (oldPolarity != this.gContext.polarity) {
      polarityChanged(oldPolarity, this.gContext.polarity)
    }
  }

  def endRegion(): Unit

  override def enterData(ctx: GerberParser.DataContext): Unit = {
    implicit val c: ParserRuleContext = ctx

    if (ctx.g != null) {
      ctx.g.getText match {
        case "G36" =>
          if (this.gContext.region.isDefined) {
            "Region can not be entered while another Region is still open" !
          }

          this.beforeRegion = Some(this.gContext.copy())

          this.gContext.region = Some(Seq())

        case "G37" =>
          if (this.gContext.contour.isDefined) {
            closeContour(c)
          }

          if (this.gContext.region.isEmpty) {
            "No open region" !
          }

          endRegion()

          this.gContext.interpolation = this.beforeRegion.get.interpolation
          this.beforeRegion = None
          this.gContext.region = None
      }
    }
  }

  protected def onApertureSelected(ap: ApertureDefinition)(implicit ctx: ParserRuleContext) = {}

  private def selectAperture(dCode: String)(implicit ctx: ParserRuleContext): Unit = {
    def noZeroes(dCode: String): String = {
      def cleanZeroes(dCode: String): String = {
        var x = dCode
        while (x.startsWith("0"))
          x = x.substring(1)
        x
      }

      if (dCode.startsWith("D")) {
        "D" + cleanZeroes(dCode.substring(1))
      } else {
        dCode
      }

    }

    def doSelect(ad: Option[GerberApertureDefinition]): Unit = {
      this.gContext.explicitlySelected = Some(true)
      this.gContext.aperture = ad
      this.onApertureSelected(ad.get)
    }

    val ad = apReg.getApertureDefinition(dCode)
    if (ad.isDefined) {
      doSelect(ad)
    } else {
      // some legacy gerbers include leading zeroes...
      val cleanAp = noZeroes(dCode)
      val ad      = apReg.getApertureDefinition(cleanAp)
      if (ad.isDefined) {
        doSelect(ad)
      } else {
        throw GerberProblem(s"Aperture $cleanAp not found", Some(ctx))
      }
    }

  }

  private def closeContour(ctx: ParserRuleContext) = {
    implicit val c: ParserRuleContext = ctx

    if (this.gContext.contour.isDefined) {
      if (this.gContext.region.isDefined) {
        this.gContext.contour.get.path.closePath()
        this.gContext.region = this.gContext.region.map(s => s :+ this.gContext.contour.get)
        this.gContext.contour = None
      } else {

        "No Region Open" !
      }

    }
  }

  override def enterOp(ctx: GerberParser.OpContext): Unit = {
    if (ctx.inter != null) {
      doInterpolation(ctx)
    }

    if (ctx.oper != null || ctx.x != null || ctx.y != null) {
      doOperation(ctx)
    }
  }

  val dcode = "D([0-9]+)".r

  private def doOperation(implicit ctx: GerberParser.OpContext): Unit = {
    def isEmpty(): Boolean =
      ctx.i == null && ctx.j == null && ctx.x == null && ctx.y == null && ctx.inter == null && ctx.oper == null

    if (!isEmpty()) {
      var oper = ""
      if (ctx.oper == null) {
        if (this.gContext.deprecatedOperation.isEmpty) {
          s"deprecated operation is ${this.gContext.deprecatedOperation}" ~

          oper = "D02"
        } else {
          // if the operation is empty, use the last one.
          // according to the specs, this is illegal if the last code is anything other than D01 (and deprecated in any case)
          // but we encountered some files that rely on this
          oper = this.gContext.deprecatedOperation.get // deprecated gerber
        }
      } else {
        oper = s"D${ctx.oper.getText}"
      }

      dcode.findFirstMatchIn(oper) match {
        case Some(x) =>
          val code = BigDecimal(x.group(1))
          if (code <= 3) {
            this.gContext.deprecatedOperation = Some(oper)
            if (this.gContext.region.isDefined) {
              doRegionOp(oper)
            } else {
              doNoRegionOp(oper)
            }
          } else {
            selectAperture(s"D$code")(ctx)
          }

        case None => s"Illegal DCode ${oper}" !
      }

    }

    // ??(ctx)
  }

  private def movePoint(point: GPoint): Unit =
    this.gContext.point = Some(point)

  def doInterpolation(implicit c: GerberParser.OpContext) =
    c.inter.getText match {
      case "G01" | "G1" => this.gContext.interpolation = Some(Linear())
      case "G02" | "G2" => this.gContext.interpolation = Some(Clockwise())
      case "G03" | "G3" => this.gContext.interpolation = Some(Counterclockwise())
      case "G55"        =>                                            // legacy gerber
      case "G74"        => gContext.quadrant = Some(SingleQuadrant()) // "Quadrant Mode not available" !
      case "G75"        => gContext.quadrant = Some(MultiQuadrant())  // "Quadrant Mode not available" !

      case x => s"Invalid Interpolation mode $x" !
    }

  private def createBothPoints(implicit ctx: OpContext): (GPoint, Option[GPoint]) =
    (createPoint, createRelPoint)

  private def createRelPoint(implicit ctx: OpContext): Option[GPoint] = {
    val format = gContext.format.get

    val i: Option[Double] =
      if (ctx.i != null) {
        Some(format.toX(ctx.i.getText))
      } else {
        None
      }

    val j: Option[Double] =
      if (ctx.j != null) {
        Some(format.toY(ctx.j.getText))
      } else {
        None
      }

    Some(GPoint(i.getOrElse(0), j.getOrElse(0)))
  }

  private def createPoint(implicit ctx: OpContext): GPoint = {
    val format = gContext.format.get

    val x: Double =
      if (ctx.x == null) {
        gContext.point.get.getX
      } else {
        val x = format.toX(ctx.x.getText)
        if (format.coordinate == GerberCoordinate.Absolute) {
          x
        } else {
          this.gContext.point.get.x + x
        }
      }

    val y: Double =
      if (ctx.y == null) {
        gContext.point.get.getY
      } else {
        val y = format.toY(ctx.y.getText)
        if (format.coordinate == GerberCoordinate.Absolute) {
          y
        } else {
          this.gContext.point.get.y + y
        }
      }

    GPoint(x, y)
  }

  private def doRegionOp(oper: String)(implicit ctx: GerberParser.OpContext): Unit = {
    def doMove(): Unit = {
      closeContour(ctx)
      val to = createPoint(ctx)
      movePoint(to)
    }

    oper match {
      case "D01" | "D1" =>
        if (this.gContext.contour.isEmpty) {
          this.gContext.contour = Some(GerberContour(new ExtendedGeneralPath()))
        }

        val (to, rel) = createBothPoints
        def draw() = {
          val from = gContext.point.get
          val ip = Interpolator(
            quad = this.gContext.quadrant,
            interpolate = this.gContext.interpolation,
            from = from,
            to = to,
            relativePoint = rel,
            ad = null,
            movement = Movement(gContext.point.get, to),
            scaling = gContext.format.get.getGerberScaling.intValue
          )
          val line = ip.getLine(false)
          val cont = this.gContext.contour.get

          // add the line to the current contour
          this.gContext.contour = Some(cont.copy(
            path = {
              val p = cont.path
              p.append(line.getPathIterator(null), true)
              p
            },
            flash = cont.flash && from == to
          ))
        }

        draw()

        movePoint(to)

      case "D02" | "D2" =>
        doMove()

      case "D03" | "D3" =>
        doMove()
    }
  }

  def createFrom(implicit ctx: OpContext): Option[GPoint] = {
    val format = this.gContext.format.get
    val x =
      if (ctx.x != null) {
        val x = format.toX(ctx.x.getText)
        val xCoord =
          if (format.coordinate == GerberCoordinate.Absolute) {
            x
          } else {
            this.gContext.point.get.x + x
          }
        Some(xCoord)
      } else {
        this.gContext.point.map(_.getBX)
      }

    val y =
      if (ctx.y != null) {
        val y = format.toY(ctx.y.getText)
        val yCoord =
          if (format.coordinate == GerberCoordinate.Absolute) {
            y
          } else {
            this.gContext.point.get.y + y
          }
        Some(yCoord)
      } else {
        this.gContext.point.map(_.getBY)
      }

    (x, y) match {
      case (Some(xv), Some(yv)) => Some(GPoint(xv, yv))
      case _                    => None
    }

  }

  private def getFormatOrSetDefault(): GerberFormat =
    gContext.format match {
      case Some(value) => value
      case None =>
        gContext.setMeasurementUnit(Millimetre())
        this.gContext.format.get
    }

  private def doNoRegionOp(oper: String)(implicit ctx: GerberParser.OpContext): Unit = {

    val format = getFormatOrSetDefault()

    oper match {
      case "D01" | "D1" =>
        val p = createFrom(ctx)

        val i =
          if (ctx.i == null) {
            None
          } else {
            Some(format.toX(ctx.i.getText))
          }

        val j =
          if (ctx.j == null) {
            None
          } else {
            Some(format.toX(ctx.j.getText))
          }

        p.foreach { pv =>
          doInterpolate(pv, i, j)
          movePoint(pv)
        }

      case "D02" | "D2" =>
        val p = createFrom(ctx)
        p.foreach { pv =>
          movePoint(pv)
        }

      case "D03" | "D3" =>
        val p = createFrom(ctx)
        p.foreach { pv =>
          flash(pv)
          movePoint(pv)
        }
    }

  }

  def flash(target: GPoint)(implicit v: ParserRuleContext): Unit

  def doInterpolate(point: GPoint, i: Option[Double], j: Option[Double])(implicit v: OpContext): Unit

  //  val scaling = gContext.format.map(_.scaling).getOrElse(100)
  def scaling: Int =
    gContext.format.map(_.getGerberScaling.intValue).getOrElse(GerberFormat.DEFAULT_IMAGE_SCALING.intValue)

  def imagescaling: Double =
    gContext.format.map(_.getImageScaling).getOrElse(GerberFormat.DEFAULT_IMAGE_SCALING)

  override def enterFs(ctx: GerberParser.FsContext): Unit = {
    implicit val c: ParserRuleContext = ctx

    this.gContext.setFormat(GerberFormat.of(ctx))

  }

  override def enterAm(ctx: GerberParser.AmContext): Unit = {
    implicit val c = ctx

    val prims = ctx.macrocontent().macrodirective().asScala.map(_.prim()).toSeq
    val vars  = ctx.macrocontent().macrodirective().asScala.map(_.vardef()).toSeq

    val macroWalker = new MacroWalker()
    val walker      = new ParseTreeWalker

    walker.walk(macroWalker, ctx)

    val mac = macroWalker.getMacro()

    this.macroDefinitions.put(mac.name, mac)

  }

  override def enterComment(ctx: GerberParser.CommentContext): Unit = {}

  override def enterAb(ctx: GerberParser.AbContext): Unit = {
    implicit val c = ctx
    "Aperture Blocks unsupported" !
  }

  def getMacroDefinitions(): Map[String, Macro] =
    this.macroDefinitions.toMap

}
