package de.fellows.ems.renderer.impl.drill

import de.fellows.ems.pcb.model.DrillSet

import java.nio.file.{ Files, Path }
import scala.jdk.CollectionConverters._

class DrillSetParser(file: Path) {

  def build(): Seq[DrillSet] =
    Files.readAllLines(file).asScala.flatMap { line =>
      val props = line.split("\\|").map { part =>
        val s = part.split("=")
        if (s.size == 2) {
          Some(s(0) -> s(1))
        } else {
          None
        }
      }.flatten.toMap

      val lsn = props.get("LayersSetName")
      val df  = props.get("DrillFile")
      val dl  = props.get("DrillLayers").orElse(props.get("LayerPairs"))

      if (Seq(lsn, df, dl).forall(_.isDefined)) {
        Some(DrillSet(
          lsn,
          df,
          dl.get.split(",")
        ))
      } else {
        None
      }

    }.toSeq
}
