package de.fellows.ems.renderer.impl

import akka.actor.ActorSystem
import de.fellows.ems.gerber.parser.{<PERSON><PERSON><PERSON><PERSON>ex<PERSON>, GerberParser}
import org.antlr.v4.runtime.CharStreams

import scala.concurrent.Future
import scala.util.Try

final class RendererHealth(system: ActorSystem) extends (() => Future[Boolean]) {

  override def apply(): Future[Boolean] =
    Future.successful {
      Try {
        val l = new GerberLexer(CharStreams.fromString(""))
        val c = classOf[GerberParser]
      }.isSuccess
    }
}
