package de.fellows.ems.renderer.impl
import de.fellows.ems.pcb.model
import de.fellows.ems.pcb.model.graphics.Graphic
import de.fellows.ems.renderer.impl.gerber.builders.BoundsBuilder
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.utils.graphics.PathReader
import de.fellows.utils.internal.FileReader._
import org.apache.batik.ext.awt.geom.ExtendedGeneralPath
import org.apache.batik.parser.{AWTPathProducer, PathParser}
import org.apache.batik.svggen.SVGGraphics2D
import org.apache.batik.transcoder.image.{ImageTranscoder, PNGTranscoder}
import org.apache.batik.transcoder.{SVGAbstractTranscoder, TranscoderInput, TranscoderOutput}

import java.awt.{BasicStroke, Color, Dimension}
import java.io.{File, FileOutputStream, StringReader, StringWriter}
import java.nio.file.Path
import java.text.DecimalFormat

object SVGUtils {

  private val df = new DecimalFormat("0.####;-0.####")

  def format(n: Double): String =
    df.format(n)

  def format(n: BigDecimal): String =
    df.format(n)

  def writePNG(svg: SVGGraphics2D, path: File) = {
    val t = new PNGTranscoder()
    t.addTranscodingHint(SVGAbstractTranscoder.KEY_HEIGHT, 300f)
    t.addTranscodingHint(ImageTranscoder.KEY_BACKGROUND_COLOR, Color.WHITE)
    t.addTranscodingHint(ImageTranscoder.KEY_FORCE_TRANSPARENT_WHITE, true)

    withResource(new StringWriter()) { s =>
      svg.stream(s)
      withResource(new StringReader(s.toString)) { r =>
        val input: TranscoderInput = new TranscoderInput(r)
        withResource(new FileOutputStream(path)) { ostream =>
          val output = new TranscoderOutput(ostream)
          t.transcode(input, output)
        }
      }
    }
  }

  def drawGraphics(gs: Seq[Graphic], path: File) = {
    val svg: SVGGraphics2D = createSVG(Seq(gs))
    svg.stream(path.getAbsolutePath)

  }

  def drawGraphics(gs: Seq[model.Graphic], target: Path) = {
    val svg = Renderer.createGraphics

    val color = Seq(Color.BLACK, Color.RED, Color.GREEN, Color.BLUE)
    var i     = 0
    gs.foreach { gr =>
      i += 1;
      svg.setStroke(new BasicStroke(1f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_BEVEL))
      svg.setColor(color(i % color.size))
      gr.paths.foreach { group =>
        group.foreach { element =>
          element.path.foreach { path =>
            svg.fill(PathReader.read(path))
          }
        }
      }
    }

    svg.stream(target.toAbsolutePath.toString)
  }

  private def createSVG(gs: Seq[Seq[Graphic]]) = {

    val color = Seq(Color.BLACK, Color.RED, Color.GREEN, Color.BLUE)

    val b = new BoundsBuilder()
    gs.foreach { g =>
      g.foreach(g2 => b.extend(g2.bounds))
    }

    val svg = Renderer.createGraphics

    var i = 0
    gs.foreach { grp =>
      i += 1;
      svg.setStroke(new BasicStroke(1f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_BEVEL))
      svg.setColor(color(i % color.size))
      grp.foreach { g =>
        svg.fill(g.shape)
      }
    }

    val bounds = b.result()
    val dim = new Dimension(
      Math.max(1, (Math.abs(bounds.getMinX) + bounds.getMaxX).ceil.intValue()),
      Math.max(1, (Math.abs(bounds.getMinY) + bounds.getMaxY)).ceil.intValue()
    )
    svg.setSVGCanvasSize(dim)
    svg
  }
}

object PathGenerator {
  def parse(s: String): ExtendedGeneralPath = {
    val parser = new PathParser()
    val gen    = new AWTPathProducer()
    parser.setPathHandler(gen)
    parser.parse(s)
    gen.getShape.asInstanceOf[ExtendedGeneralPath]
  }
}
