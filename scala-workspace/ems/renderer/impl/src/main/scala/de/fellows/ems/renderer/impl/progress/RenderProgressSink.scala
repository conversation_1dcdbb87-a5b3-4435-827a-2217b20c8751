package de.fellows.ems.renderer.impl.progress

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.renderer.impl.progress.RendererProgress._
import de.fellows.utils.logging.StackrateLogger
import org.antlr.v4.runtime.atn.ATNConfigSet
import org.antlr.v4.runtime.dfa.DFA
import org.antlr.v4.runtime.tree.{ ErrorNode, TerminalNode }
import org.antlr.v4.runtime.{ Parser, ParserRuleContext, RecognitionException, Recognizer }
import org.reactivestreams.{ Publisher, Subscriber }

import java.util
import java.util.concurrent.LinkedBlockingQueue
import scala.collection.mutable

class RenderProgressSink(val assRef: AssemblyReference, val file: String)(implicit logger: StackrateLogger)
    extends Publisher[RenderMessage] with RenderEventListener {

  val q = new LinkedBlockingQueue[RenderMessage]()

  val works    = mutable.HashMap[String, Int]()
  val progress = mutable.HashMap[String, Int]()

  val subs = mutable.HashSet[(Subscriber[_ >: RenderMessage], RenderSubscription)]()

  def startParsing(work: Int): Unit =
    //    logger.warn(s"[${file}] start parsing...")
    q.put(RenderMessage(assRef, RenderMessage.LVL_INFO, "parsing", s"Start Parsing"))

  def finishParsing(): Unit =
    //    logger.warn(s"[${file}] finish parsing...")
    q.put(RenderMessage(assRef, RenderMessage.LVL_INFO, "parsing", s"Parsing Finished"))

  def startRender(renderType: String, work: Int): Unit = {
    //    logger.warn(s"[${file}] start rendering ${work}")
    works.update(renderType, work)
    q.put(RenderMessage(assRef, RenderMessage.LVL_INFO, "render", s"Render ${renderType} Started"))
  }

  def work(renderType: String, w: Int) = {
    val pr = progress.getOrElse(renderType, 0) + w
    progress.update(renderType, pr)
    val work = works.getOrElse(renderType, -1)
    //    logger.warn(s"[${file}] render ${w}: ${pr}/$work")
    q.put(RenderMessage(assRef, RenderMessage.LVL_INFO, "render", s"Render ${renderType}: ${pr}/${work}"))

  }

  def finished(renderType: String) =
    q.put(RenderMessage(assRef, RenderMessage.LVL_INFO, "render", s"Render ${renderType} Finished"))

  def done() = {
    subs.foreach { x =>
      x._1.onComplete()
    }
    RendererProgress.done(this)
  }

  def log(msg: RenderMessage) =
    q.put(msg)

  def error(renderType: Option[String], msg: String, e: Throwable) =
    q.put(RenderMessage(assRef, RenderMessage.LVL_ERROR, "render", s"Render Error: $msg", Some(e)))

  def error(renderType: Option[String], msg: String) =
    q.put(RenderMessage(assRef, RenderMessage.LVL_ERROR, "render", s"Render Error: $msg"))

  def info(renderType: Option[String], msg: String) =
    q.put(RenderMessage(assRef, RenderMessage.LVL_INFO, "render", msg))

  def getMessages(): Seq[RenderMessage] = {
    import scala.jdk.CollectionConverters._
    this.q.asScala.toSeq
  }

  def getErrorMessages(): Seq[RenderMessage] = {
    import scala.jdk.CollectionConverters._
    this.q.asScala.toSeq.flatMap {
      case x if x.lvl == RenderMessage.LVL_ERROR => Some(x)
      case _                                     => None
    }
  }

  override def subscribe(s: Subscriber[_ >: RenderMessage]): Unit = {
    val subscription = new RenderSubscription(q, s)
    subs.update((s, subscription), true)
    s.onSubscribe(subscription)
  }

  override def syntaxError(
      recognizer: Recognizer[_, _],
      offendingSymbol: Any,
      line: Int,
      charPositionInLine: Int,
      msg: String,
      e: RecognitionException
  ): Unit =
    error(None, s"Syntax Error @$line:$charPositionInLine: $msg")

  override def reportAmbiguity(
      recognizer: Parser,
      dfa: DFA,
      startIndex: Int,
      stopIndex: Int,
      exact: Boolean,
      ambigAlts: util.BitSet,
      configs: ATNConfigSet
  ): Unit = {}

  override def reportAttemptingFullContext(
      recognizer: Parser,
      dfa: DFA,
      startIndex: Int,
      stopIndex: Int,
      conflictingAlts: util.BitSet,
      configs: ATNConfigSet
  ): Unit = {}

  override def reportContextSensitivity(
      recognizer: Parser,
      dfa: DFA,
      startIndex: Int,
      stopIndex: Int,
      prediction: Int,
      configs: ATNConfigSet
  ): Unit = {}

  override def visitTerminal(node: TerminalNode): Unit = {}

  override def visitErrorNode(node: ErrorNode): Unit = {}

  override def enterEveryRule(ctx: ParserRuleContext): Unit = {}

  override def exitEveryRule(ctx: ParserRuleContext): Unit = {}
}
