package de.fellows.ems.renderer.impl.simple

import de.fellows.ems.pcb.model.graphics.{ApertureDefinition, GerberApertureDefinition}
import de.fellows.ems.renderer.impl.gerber.ApertureRegistry

import scala.collection.mutable

class SimpleApertureStore extends ApertureRegistry {

  val apertures = new mutable.HashMap[String, GerberApertureDefinition]()

  override def addApertureDefinition(name: String, ad: GerberApertureDefinition): Unit = apertures.put(name, ad)

  override def getApertureDefinition(name: String): Option[GerberApertureDefinition] = apertures.get(name)

  override def getApertureDefinitions(): Map[String, GerberApertureDefinition] = apertures.toMap

}
