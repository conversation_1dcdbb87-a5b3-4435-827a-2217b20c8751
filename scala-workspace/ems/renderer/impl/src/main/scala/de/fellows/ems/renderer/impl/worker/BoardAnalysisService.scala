package de.fellows.ems.renderer.impl.worker

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, AssemblyService, AssemblyUtils}
import de.fellows.ems.layerstack.api.{LayerStacks, LayerstackService}
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.model.PCBVersion
import de.fellows.ems.renderer.api.job.BoardAnalysisJobEntry
import de.fellows.ems.renderer.impl.analysis.GerberBoardAnalyzer
import de.fellows.ems.renderer.impl.pool.RendererCoordinator
import de.fellows.utils.internal.LifecycleStageStatus
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.jobs.QueuedJob
import kamon.Kamon

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class BoardAnalysisService(
    assemblyService: AssemblyService,
    pcbService: PCBService,
    layerstackService: LayerstackService,
    panelService: PanelService,
    registry: PersistentEntityRegistry
) extends StackrateLogging {

  def handleBoardAnalysis(
      jobInfo: BoardAnalysisJobEntry,
      job: QueuedJob
  )(implicit
      ec: ExecutionContext
  ): Try[Done] =
    SafeAwait.result(
      Kamon.span("handleBoardAnalysis", getClass.getSimpleName) {
        for {
          _ <- Future.unit
          assRef = jobInfo.assembly
          _ <- AssemblyUtils.setLifecycle(
            assemblyService,
            assRef,
            AssemblyLifecycleStageName.Analysis,
            LifecycleStageStatus.emptyProgress
          )
          pcbv   <- pcbService._getPCBVersion(assRef.team, assRef.id, assRef.version).invoke()
          stacks <- layerstackService._getPCBLayerstack(assRef.team, assRef.version, Some(true)).invoke()
          _      <- doAnalyzeBoard(assRef, pcbv, stacks)
        } yield Done
      }
    )

  private def doAnalyzeBoard(
      assembly: AssemblyReference,
      pcbv: PCBVersion,
      stacks: LayerStacks
  )(implicit ec: ExecutionContext): Future[Done] =
    (for {
      _ <-
        if (stacks.selected.isDefined) {
          Future.successful({
            val fut = RendererCoordinator.analysisQueue.submit(
              new GerberBoardAnalyzer(
                assRef = assembly,
                pcbservice = pcbService,
                assService = assemblyService,
                stackService = layerstackService,
                layerstacks = stacks,
                reg = registry,
                pcb = pcbv
              )
            )

            Future(fut.get()).map(_ => Done)
          })
        } else {
          AssemblyUtils.setLifecycle(
            assemblyService,
            assembly,
            AssemblyLifecycleStageName.Analysis,
            LifecycleStageStatus.emptySuccess
          )
            .map(_ => Done)
        }
    } yield Done).recover {
      case e: Throwable =>
        e.printStackTrace()
        throw e
    }
}
