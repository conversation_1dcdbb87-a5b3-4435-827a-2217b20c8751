package de.fellows.ems.renderer.impl.pool

import com.pump.geom.AreaX
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.graphics.parts.{Flash, FlashOrientation, Polygon}
import de.fellows.ems.pcb.model.graphics.tree.{ElementId, PCBLayerInternalData, QuadTree, Trace}
import de.fellows.ems.pcb.model.graphics.{Graphic, Macro}
import de.fellows.ems.pcb.model.{
  graphics,
  BigPoint,
  Dimension,
  Format,
  GerberFile,
  Graphic => ApiGraphic,
  GraphicDefinition,
  GraphicElement,
  GraphicUsage,
  LayerConstants,
  RenderConstants
}
import de.fellows.ems.renderer.impl.RendererHelper
import de.fellows.ems.renderer.impl.RendererHelper.{mergeAttributes, stretchDim}
import de.fellows.ems.renderer.impl.entity.render.RenderProgressException
import de.fellows.ems.renderer.impl.gerber.graphic.ApertureUtils
import de.fellows.ems.renderer.impl.gerber.{GerberFormat, GerberProblem}
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.utils.logging.StackrateLogger
import de.fellows.utils.telemetry.PropagatingExecutorService
import de.fellows.utils.{DebugUtils, FutureUtils, ThreadUtils, TimeoutException}
import org.apache.batik.svggen.SVGGraphics2D

import java.awt.geom.Area
import java.util
import java.util.concurrent.Executors
import scala.collection.immutable.HashSet
import scala.collection.mutable
import scala.concurrent.ExecutionContext
import scala.concurrent.duration._

class RenderTask[C <: RenderContext](
    val assRef: AssemblyReference,
    val gf: GerberFile,
    override val ctx: C
)(implicit logger: StackrateLogger)
    extends AbstractTask[Any, C](ctx, System.currentTimeMillis(), HighTaskPriority) {

  lazy val basicExecutor = new PropagatingExecutorService(Executors.newCachedThreadPool())

  override def getTeam(): Option[String] = Some(assRef.team)

  override def getAssembly(): Option[AssemblyReference] = Some(assRef)

  def getExtraInfo(): Seq[(String, Any)] =
    RendererCoordinator.fileMetrics(gf)

  override def description: String =
    s"${assRef.gid.getOrElse("")} | ${gf.name} | ${getClass.getSimpleName}"

  def doRun(): Unit =
    try {
      val (data, gr) = _run()
      ctx.data = Some(data)
      ctx.graphic = Some(gr)
    } catch {
      case e: RenderProgressException =>
        logger.info("Render Progress", e)
        ctx.sink.info(None, e.getMessage)
        throw new Stop(e)
      case e: TimeoutException =>
        ctx.sink.error(None, "Rendering Timeout")
        ctx.error(assRef, Some(gf), e)
        throw e
      case e: GerberProblem =>
        logger.info("Gerber Problem", e)
        ctx.sink.error(None, e.msg, e)
        ctx.error(assRef, Some(gf), e)
        throw e
      case e: Throwable =>
        logger.error(e.getMessage, e)
        ctx.sink.error(None, s"Unexpected Error: ${e.getMessage}", e)
        ctx.error(assRef, Some(gf), e)
        throw e
    }

  private def _run(): (PCBLayerInternalData, ApiGraphic) = {

    val renderer = new SyncRenderer(new java.io.File(gf.path.toPath), None)

    this.state = "starting"
    ctx.start(assRef, Some(gf))

    this.state = "preparing"
    val defs = Vector.newBuilder[GraphicDefinition]

    // create render
    renderer.prepare()
    val graphics2D = Renderer.createGraphics
    RenderTask.addApertureDefinitions(defs, renderer, graphics2D)
    val tree: QuadTree[Graphic] = renderer.builder.tree

    this.state = "rendering"

    val gformat = renderer.glst.gContext.format.get

    val (newRender, internalData) =
      RenderTask.renderCopperLayer(
        tree = tree,
        gf = gf,
        graphics2D = graphics2D,
        executionContext = ExecutionContext.fromExecutor(basicExecutor),
        defs = defs.result(),
        format = dim =>
          Format(
            dimension = dim,
            unit = RendererHelper.unit(gformat.getStardizedUnit),
            resolution = gformat.resolution(),
            complexity = Some(tree.getComplexity),
            scaling = Some(gformat.getImageScaling),
            gerberscale = Some(gformat.getGerberScaling)
          ),
        macros = Some(renderer.getMacroDefinitions().values.toSeq),
        timeout = ctx.timeout
      )

    this.state = "persisting"
    ctx.persist(assRef, gf, renderer.dimensions, internalData, newRender._1, newRender._2, Some(renderer))

    ctx.stop(assRef, Some(gf))
    this.state = "done"

    (internalData, newRender._2)
  }
}

object RenderTask {

  def renderCopperLayer(
      tree: QuadTree[Graphic],
      gf: GerberFile,
      graphics2D: SVGGraphics2D,
      executionContext: ExecutionContext,
      defs: Seq[GraphicDefinition],
      format: Option[Dimension] => Format,
      macros: Option[Seq[Macro]],
      timeout: Option[Long]
  ): ((String, ApiGraphic), PCBLayerInternalData) = {
    var dim: Option[Dimension] = None
    val paths                  = Vector.newBuilder[Vector[GraphicElement]]
    var traceid                = 1
    val _traces                = Vector.newBuilder[Trace]
    Renderer.drawWithCollisionGroupsIter(
      tree,
      { collisions =>
        var minX: Double = Double.PositiveInfinity
        var minY: Double = Double.PositiveInfinity
        var maxX: Double = Double.NegativeInfinity
        var maxY: Double = Double.NegativeInfinity

        val mergedAttributes = mutable.Map.empty[String, Seq[String]]
        var containsPads     = false

        val idsBuilder  = HashSet.newBuilder[ElementId[Int]]
        val thisTraceID = Some(GraphicElement.traceID(gf, traceid))

        val group = Vector.newBuilder[GraphicElement]

        val elementIds = Seq.newBuilder[String]

        // split the set of collisions into smaller groups
        // if the group is too large, the rendering can potentially not finish
        // since we have a timeout of 10 seconds
        collisions.iterator.grouped(5000).foreach { collisions =>
          val merged = new AreaX()

          collisions.foreach { graphic =>
            idsBuilder.addOne(graphic.index)

            val bounds = graphic.bounds
            containsPads = containsPads || hasPads(graphic)

            minX = Math.min(minX, bounds.getMinX)
            minY = Math.min(minY, bounds.getMinY)
            maxX = Math.max(maxX, bounds.getMaxX)
            maxY = Math.max(maxY, bounds.getMaxY)

            graphic match {
              case x: Flash =>
                val orientation = x.orientation.flatMap {
                  case FlashOrientation(0, false) => None // dont keep normal orientation to save space
                  case x                          => Some(x)
                }
                val p = BigPoint(x.target.x, x.target.y)
                group += GraphicElement(
                  path = None,
                  tag = None,
                  use = Some(GraphicUsage(x.aperture.id, p)),
                  attributes = mergeAttributes(x.attributes, x.aperture.attributes),
                  elementIds = Some(Seq(GraphicElement.elementID(gf, x.index.x))),
                  trace = thisTraceID,
                  orientation = orientation
                )
              case x =>
                elementIds += GraphicElement.elementID(gf, x.index.x)
                mergeAttributes(mergedAttributes, x)
                merged.add(x.shape)
            }
          }

          val path = FutureUtils.timeout(10.seconds) {
            // Workaround to actually stop processing if the future reaches the timeout
            AreaXHelper.processQueue(merged)
            ThreadUtils.interruptCheck()
            Option(graphics2D.getShapeConverter.toSVG(merged))
          }(executionContext).getOrElse(None)

          path.foreach { path =>
            val mapResult = Option.when(mergedAttributes.nonEmpty)(mergedAttributes.toMap)

            group += GraphicElement(
              path = Some(path.getAttribute("d")),
              tag = None,
              use = None,
              attributes = mapResult,
              elementIds = elementIds.result() match {
                case x if x.isEmpty => None
                case x              => Some(x)
              },
              trace = thisTraceID
            )
          }
        }

        val newDim = Dimension(BigPoint(minX, minY), BigPoint(maxX, maxY))
        dim = dim match {
          case None    => Some(newDim)
          case Some(d) => Some(stretchDim(d, newDim))
        }

        val ids = idsBuilder.result()
        _traces += Trace(thisTraceID.value, ids, Some(containsPads))

        paths += group.result()
        traceid += 1

      },
      () =>
        timeout.foreach(to =>
          if (to < System.currentTimeMillis()) {
            throw new TimeoutException()
          }
        )
    )

    val p = paths.result()

    (
      RenderConstants.COPPER_JSON ->
        ApiGraphic(
          dim.getOrElse(Dimension.empty),
          format(dim),
          p.size,
          p,
          defs
        ),
      PCBLayerInternalData(
        tree = tree,
        traces = Some(_traces.result()),
        format = Some(format(dim)),
        macros = macros
      )
    )
  }

  def isCopperLayer(gf: GerberFile): Boolean =
    gf.fType.fileType match {
      case LayerConstants.COPPER_BOTTOM
          | LayerConstants.COPPER_MID
          | LayerConstants.COPPER_TOP =>
        true
      case _ =>
        false

    }

  def addApertureDefinitions(
      defs: mutable.Builder[GraphicDefinition, Seq[GraphicDefinition]],
      renderer: SyncRenderer,
      graphics2d: SVGGraphics2D
  ): Unit =
    renderer.glst.apReg.getApertureDefinitions().foreach { d =>
      val s = ApertureUtils.getArea(
        graphics.GPoint(0, 0),
        d._2,
        renderer.glst.gContext.format.get.getGerberScaling.intValue,
        renderer.glst.getMacroDefinitions()
      )
      val path = Option(graphics2d.getShapeConverter.toSVG(new Area(s))).map(_.getAttribute("d"))
      path match {
        case Some(p) => defs += GraphicDefinition(d._1, p)
        case None    =>
      }
    }

  def createFormat(format: Option[GerberFormat], dim: Option[Dimension], tree: QuadTree[Graphic]): Format =
    format match {
      case Some(f) =>
        Format(
          dimension = dim,
          unit = RendererHelper.unit(f.getStardizedUnit),
          resolution = f.resolution(),
          complexity = Some(tree.getComplexity),
          scaling = Some(f.getImageScaling)
        )
      case None =>
        Format(
          dimension = dim,
          unit = "mm",
          resolution = 1,
          complexity = Some(tree.getComplexity),
          scaling = Some(1)
        )
    }

  def hasPads(a: Iterable[Graphic]): Boolean =
    a.exists(hasPads)

  def hasPads(graphic: Graphic): Boolean =
    graphic match {
      case _: Flash   => true
      case x: Polygon => x.flash
      case _          => false
    }

}

/** Helper to process the queue of an AreaX
  * This is a workaround for the fact that once the queue starts processing, even if the future
  * is interrupted, the queue will continue to process.
  *
  * This helper will process the queue until it is empty or the thread is interrupted.
  * Helps avoiding out of memory issues if the queues are large enough
  *
  * The buffer size is completely arbitrary - didn't really experiment with it. The reason
  * for it is just to avoid the thread interruption check on every iteration
  */
object AreaXHelper {

  import com.pump.geom.area.AreaXBody
  import com.pump.geom.{AreaX, AreaXOperation, AreaXRules}

  private val queueField = classOf[AreaX].getDeclaredField("queue")
  queueField.setAccessible(true)

  private val rulesField = classOf[AreaX].getDeclaredField("rules")
  rulesField.setAccessible(true)

  private val bodyField = classOf[AreaX].getDeclaredField("body")
  bodyField.setAccessible(true)

  def processQueue(areaX: AreaX): Unit =
    areaX.synchronized {
      val queue = queueField.get(areaX).asInstanceOf[util.LinkedList[AreaXOperation]]
      val rules = rulesField.get(areaX).asInstanceOf[AreaXRules]
      var body  = bodyField.get(areaX).asInstanceOf[AreaXBody]

      val bufferSize = 1000
      val tmp        = Array.ofDim[AreaXOperation](bufferSize)
      var current    = 0

      try
        while (queue.size() > 0) {
          while (current < bufferSize && queue.size() > 0) {
            val op = queue.pop()
            tmp(current) = op
            current += 1
          }

          if (current < bufferSize) {
            while (current < bufferSize) {
              tmp(current) = null
              current += 1
            }
          }

          body = rules.execute(body, tmp)

          current = 0
          ThreadUtils.interruptCheck()
        }
      finally
        // whether there was an interruption or not, we need to set the body back
        bodyField.set(areaX, body)
    }

}
