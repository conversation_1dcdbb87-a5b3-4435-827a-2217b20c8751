package de.fellows.ems.renderer.impl.pool

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.GerberFile
import de.fellows.ems.renderer.impl.progress.RendererProgress.RenderMessage

trait TaskContext[X] {

  def setTimeout(endTime: Long)

  def result: Option[X]

  def setResult(r: X)

  def stop(assRef: AssemblyReference, gf: Option[GerberFile]): Unit

  def start(assRef: AssemblyReference, gf: Option[GerberFile]): Unit

  def error(assRef: AssemblyReference, gf: Option[GerberFile], t: Throwable): Unit

  def getMessages(): Seq[RenderMessage]
}
