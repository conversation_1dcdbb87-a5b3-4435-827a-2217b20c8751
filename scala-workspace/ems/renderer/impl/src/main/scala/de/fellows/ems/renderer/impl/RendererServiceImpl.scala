package de.fellows.ems.renderer.impl

import akka.actor.ActorSystem
import akka.dispatch.MessageDispatcher
import akka.persistence.query.Offset
import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.datastax.driver.core.utils.UUIDs
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{
  MessageProtocol,
  ResponseHeader,
  TransportErrorCode,
  TransportException
}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRef, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.typesafe.config.Config
import de.fellows.app.assembly.commons.{AssemblyFiles, AssemblyReference}
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, AssemblyService, File}
import de.fellows.app.security.AccessControlServiceComposition.{authorizedString, authorizedStringWithToken}
import de.fellows.app.security.CombinedTokenAccessServiceComposition.auth
import de.fellows.ems.layerstack.api.{LayerStacks, LayerstackService}
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBApi.SetPreviews
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.model._
import de.fellows.ems.pcb.model.graphics.GPoint
import de.fellows.ems.pcb.model.graphics.ops.DistanceOp
import de.fellows.ems.pcb.model.graphics.tree.{Distance, ElementId, PCBLayerInternalData, TraceWidthDescription}
import de.fellows.ems.renderer.api
import de.fellows.ems.renderer.api.Streams.{MessageChangedMessage, Ping, RenderStreamMessage, StatusChangedMessage}
import de.fellows.ems.renderer.api.{DrillsAdded => _, RenderAdded => _, _}
import de.fellows.ems.renderer.impl.analysis.GerberBoardAnalyzer
import de.fellows.ems.renderer.impl.entity.annularring.{AnnularRings, AnnularRingsEntity, GetAnnularRings}
import de.fellows.ems.renderer.impl.entity.distances.{AddDistances, Distances, DistancesEntity}
import de.fellows.ems.renderer.impl.entity.render._
import de.fellows.ems.renderer.impl.entity.tracewidth.{GetTraceWidths, TraceWidths, TraceWidthsEntity}
import de.fellows.ems.renderer.impl.outline.{OutlineBoxCreatorTask, OutlineCreationContext, OutlineTraceCreatorTask}
import de.fellows.ems.renderer.impl.pool.layerstack.LayerStackDefinitionRenderTask
import de.fellows.ems.renderer.impl.pool.{
  RendererCoordinator,
  SimplePreviewRenderTask,
  SimpleRenderContext,
  SimpleTaskContext
}
import de.fellows.ems.renderer.impl.read.GerberInformationRepository
import de.fellows.ems.renderer.impl.worker.RedisWorker
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.internal.FileReader.withResource
import de.fellows.utils.internal.{FileType, LifecycleDeadline, LifecycleStageStatus}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.security.{Auth0TokenContent, AuthenticationServiceComposition}
import de.fellows.utils.{internal, FilePath}
import org.apache.batik.svggen.SVGGraphics2D
import play.api.libs.json.{JsError, JsSuccess, Json}

import java.io.{FileInputStream, FileWriter}
import java.net.URLDecoder
import java.nio.file.Files
import java.time.{Instant, ZoneId, ZonedDateTime}
import java.util.{Base64, UUID}
import scala.collection.immutable
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}

class RendererServiceImpl(
    system: ActorSystem,
    registry: PersistentEntityRegistry,
    info: GerberInformationRepository,
    pcb: PCBService,
    ass: AssemblyService,
    layerstack: LayerstackService,
    redisWorker: RedisWorker
)(implicit sd: ServiceDefinition)
    extends RendererService with StackrateLogging with StackrateAPIImpl {
  implicit private val actorSystem: ActorSystem = system
  implicit private val conf: Config             = config

  import RendererServiceImpl._

  implicit val rctx: MessageDispatcher = system.dispatchers.lookup("rendering-service-dispatcher")

  def _measureNewDistance(
      version: UUID,
      file: String,
      from: ElementId[Int],
      to: ElementId[Int]
  ): Future[DistanceDescription] =
    withData(version, file, registry) { (ref, version, datapath, gfile, format) =>
      Future {
        withResource(new FileInputStream(datapath.toJavaFile)) { is =>
          Json.fromJson[PCBLayerInternalData](Json.parse(is)) match {
            case JsSuccess(value, path) =>
              val dist        = _getMeasurement(from, to, format, value)
              val description = DistanceDescription(Some(from.x), Some(to.x), dist, dist.distance, None, None)
              val rRef        = registry.refFor[DistancesEntity](Distances.id(version, file))
              rRef.ask(AddDistances(version, file, Seq(description))).map(_ => description)

            case JsError(_) =>
              throw new TransportException(TransportErrorCode.InternalServerError, "failed to read PCB data")
          }
        }
      }(this.rctx).flatten.recover {
        case e =>
          e.printStackTrace()
          throw e
      }(this.rctx)

    }

  private def _getMeasurement(
      from: ElementId[Int],
      to: ElementId[Int],
      format: Format,
      value: PCBLayerInternalData
  ): Distance = {

    val fvo = value.tree.findFirst(_.index == from)
    val tvo = value.tree.findFirst(_.index == to)

    (fvo, tvo) match {
      case (Some(fv), Some(tv)) =>
        val dst = DistanceOp(fv.shape, fv.bounds, tv.shape, tv.bounds, format.scaling.get, margin = None).distance()
        if (dst.nonEmpty) {
          dst.minBy(_.distance)
        } else {
          throw new TransportException(TransportErrorCode.InternalServerError, "failed to measure distance")
        }
      case _ => throw new TransportException(TransportErrorCode.NotFound, "Nodes not found")
    }

  }

  def _getAllAnnularRings(ass: UUID, version: UUID): Future[AnnularRingsDescription] =
    registry.refFor[AnnularRingsEntity](AnnularRings.id(version)).ask(GetAnnularRings(version)).map { rings =>
      AnnularRingsDescription(rings.rings.map { ar =>
        AnnularRingDescription(
          t = ar.t.copy(drills = Seq()),
          layer = ar.layer,
          ringType = ar.ringType,
          hit = ar.hit,
          size = ar.size,
          line = ar.line,
          file = ar.file.map(x => FileReference.from(x, None))
        )
      })
    }

  override def _getRender(version: UUID, file: String): ServiceCall[NotUsed, Render] =
    ServerServiceCall { _ =>
      registry.refFor[RenderEntity](Render.fileID(version, file)).ask(GetRender)
    }

  def generateEtag(paths: Iterable[FilePath]) =
    paths.map(_.toJavaFile.lastModified()).maxOption.map { newest =>
      val time = ZonedDateTime.ofInstant(Instant.ofEpochMilli(newest), ZoneId.systemDefault())
      val etag = s"${time}.${paths.size}"
      Base64.getEncoder.encodeToString(etag.getBytes)
    }

  override def getSharedGraphics(share: UUID, file: String): ServiceCall[NotUsed, Map[String, Graphic]] =
    auth {
      case _: Auth0TokenContent => "view:pcb"
      case t                    => s"pcb:${t.getTeam}:${t.getTeam}:$share:*:read"
    } { (token, _) =>
      ServerServiceCall { (req, _) =>
        val nonematch: Option[String] = req.headers.map(x => (x._1.toLowerCase, x._2)).toMap.get("if-none-match")
        for {
          validatedShare <- this.ass._getAssemblyShare(token.getTeam, share).invoke()
          graphics       <- _doGetGraphics(validatedShare.share.ref.version, file, nonematch)
        } yield graphics
      }
    }

  override def getGraphics(assembly: UUID, version: UUID, file: String): ServiceCall[NotUsed, Map[String, Graphic]] =
    auth {
      // If the auth token comes from lumiquote, we need a different permission check.
      // pcb permission is not part of auth0 token permissions
      case _: Auth0TokenContent => "view:pcb"
      case t                    => s"pcb:${t.getTeam}:${t.getTeam}:$version:stack:read"
    } { (_, _) =>
      ServerServiceCall { (req, _) =>
        val nonematch: Option[String] = req.headers.map(x => (x._1.toLowerCase, x._2)).toMap.get("if-none-match")
        _doGetGraphics(version, file, nonematch)
      }
    }

  private def _doGetGraphics(version: UUID, file: String, nonematch: Option[String]) = {
    val id = Render.fileID(version, file)
    logger.info(s"get renders for $version / $file: $id")
    registry.refFor[RenderEntity](id).ask(GetRender)
      .map { render =>
        if (render.fileID.isEmpty) {
          logger.error(s"Render not found for pcb '$version' file '$file'")
          throw new TransportException(TransportErrorCode.NotFound, "File render not found")
        } else {
          val etag = generateEtag(render.graphics.values)

          if (etag.exists(nonematch.contains)) {
            (ResponseHeader(304, MessageProtocol.empty, Seq()), Map[String, Graphic]())
          } else {

            val hdr = etag match {
              case Some(tag) => ResponseHeader.Ok
                  .withHeader("ETAG", tag)
                  .withHeader("E-TAG", tag)
              case None => ResponseHeader.Ok
            }

            hdr -> render.graphics.flatMap { x =>
              GraphicUtils.readFile(x._2).map(x._1 -> _)
            }
          }
        }
      }
  }

  override def _getAllAnnularRings(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServerServiceCall[NotUsed, AnnularRingsDescription] =
    ServerServiceCall { _ =>
      logger.withAssembly(team, assembly, Some(version), None)
      _getAllAnnularRings(assembly, version)
    }

  override def getAllAnnularRings(assembly: UUID, version: UUID): ServiceCall[NotUsed, AnnularRingsDescription] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:read"
    ) { (token, _) =>
      _getAllAnnularRings(token.team, assembly, version)
    }

  override def getDistance(
      assembly: UUID,
      version: UUID,
      file: String,
      from: Int,
      to: Int
  ): ServiceCall[NotUsed, DistanceDescription] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        logger.withAssembly(token.team, assembly, Some(version), None)
        _getDistance(version, file, ElementId(from), ElementId(to))
      }
    }

  private def _getDistance(version: UUID, file: String, from: ElementId[Int], to: ElementId[Int]) =
    if (from == to) {
      throw new TransportException(TransportErrorCode.PolicyViolation, "Same element")
    } else {
      info.getDistance(version, file, from.x, to.x).flatMap {
        case Some(d) => Future.successful(d)
        case None =>
          _measureNewDistance(version, file, from, to)
        //            throw new TransportException(TransportErrorCode.NotFound, "Distance not available")
      }
    }

  override def getDistances(
      assembly: UUID,
      version: UUID,
      file: String,
      from: Int
  ): ServiceCall[NotUsed, Seq[DistanceDescription]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        logger.withAssembly(token.team, assembly, Some(version), None)
        info.getDistances(version, file, from)
      }
    }

  override def getDistancesByCoordinates(
      assembly: UUID,
      version: UUID,
      file: String,
      fromX: Double,
      fromY: Double
  ): ServiceCall[NotUsed, Seq[DistanceDescription]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        logger.withAssembly(token.team, assembly, Some(version), None)
        withLayerData(version, file, registry) { (ref, data, gerber, format) =>
          data.tree.getObjectAt(GPoint(fromX, fromY)) match {
            case Some(g) => info.getDistances(version, file, g.index.x)
            case None => throw new TransportException(TransportErrorCode.NotFound, "No element found at this position")
          }
        }
      }
    }

  override def getDistanceByCoordinates(
      assembly: UUID,
      version: UUID,
      file: String,
      fromX: Double,
      fromY: Double,
      toX: Double,
      toY: Double
  ): ServiceCall[NotUsed, DistanceDescription] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        logger.withAssembly(token.team, assembly, Some(version), None)
        withLayerData(version, file, registry) { (ref, data, gerber, format) =>
          (data.tree.getObjectAt(GPoint(fromX, fromY)), data.tree.getObjectAt(GPoint(toX, toY))) match {
            case (Some(g1), Some(g2)) => _getDistance(version, file, g1.index, g2.index)
            case _ => throw new TransportException(TransportErrorCode.NotFound, "No element found at this position")
          }
        }
      }
    }

  override def createTraceOutline(assembly: UUID, version: UUID): ServiceCall[OutlineTraceCreation, Done] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:outline:write"
    ) { (token, _) =>
      ServerServiceCall { oc =>
        logger.withAssembly(token.team, assembly, Some(version), None)
        logger.warn(s"[OUTLINE TRACE] start outline creator task")

        val ctx = new OutlineCreationContext(AssemblyReference(token.team, assembly, None, version), pcb)

        pcb._getPCBVersion(token.team, assembly, version).invoke().map { pcbv =>
          RendererCoordinator.submitTask(new OutlineTraceCreatorTask(
            ctx,
            oc.traces,
            pcbv,
            registry
          ))

          Done
        }
      }
    }

  override def createBoxOutline(assembly: UUID, version: UUID): ServiceCall[OutlineCreation, Done] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:outline:write"
    ) { (token, _) =>
      ServerServiceCall { oc =>
        val ctx = new OutlineCreationContext(AssemblyReference(token.team, assembly, None, version), pcb)
        pcb._getPCBVersion(token.team, assembly, version).invoke().map { pcbv =>
          RendererCoordinator.submitTask(new OutlineBoxCreatorTask(
            ctx,
            oc.rectangle,
            pcbv,
            registry
          ))

          Done
        }
      }
    }

  def toApi(file: internal.File): File =
    File(
      id = file.id,
      name = file.name,
      subPath = file.path.subPath,
      fType = file.fType,
      detectedTypes = file.detectedTypes,
      created = file.created,
      preview = file.preview.map(_.toApi),
      lifecycles = file.lifecycles.getOrElse(Seq()),
      hash = file.hash
    )

  override def renderPreview(assembly: UUID, version: UUID, specification: Option[UUID]): ServiceCall[NotUsed, String] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:preview:write"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        logger.withAssembly(token.team, assembly, Some(version), None)
        val team = token.team
        logger.warn(s"try to render preview for assembly $assembly  version $version")

        Future.successful("nope")
      }
    }

  def savePreviews(renderedPreview: RenderedPreview, target: FilePath, team: String, assembly: UUID, version: UUID) = {

    val rearFile =
      renderedPreview.rear.map(PreviewRenderer.save(
        true,
        _,
        target,
        renderedPreview.specification,
        PreviewRenderer.DEFAULT_SIZE
      ))

    val frontFile = renderedPreview.front.map(PreviewRenderer.save(
      false,
      _,
      target,
      renderedPreview.specification,
      PreviewRenderer.DEFAULT_SIZE
    ))

    RenderedPreviewFile(renderedPreview.specification, front = frontFile, rear = rearFile)
  }

  override def _renderSpecificationPreview(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[SpecificationRenderRequest, RenderedPreviewFile] =
    ServerServiceCall { req =>
      for {
        _     <- Future.successful(logger.info(s"Rendering of previews for specification $specification started"))
        start <- Future.successful(System.currentTimeMillis())
        render: Seq[Render] <-
          Future.sequence(req.pcbv.files.map(f =>
            registry.refFor[RenderEntity](fileID(version, f.name)).ask(GetRender)
          ))

        renderedPreview <-
          Future.successful(new StatelessPreviewRenderer(
            req.spec,
            req.pcbv,
            req.pcbv.outline.get,
            req.stack,
            render,
            StackConfig(true, true)
          ).renderPreview())

        targetPath    <- Future.successful(AssemblyFiles.createAssemblyResourceFolder(team, assembly, version))
        savedPreviews <- Future.successful(savePreviews(renderedPreview, targetPath, team, assembly, version))
        _   <- Future.successful(logger.info(s"Rendering of previews for specification $specification finished"))
        end <- Future.successful(System.currentTimeMillis())
        _   <- Future.successful(logger.info(s"Time to render: ${end - start} milliseconds"))
      } yield savedPreviews
    }

  override def renderSpecificationPreview(
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[NotUsed, SpecificationPreviews] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:preview:write"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        val team = token.team
        logger.withAssembly(token.team, assembly, Some(version), None)
        (for {
          pcbv <- this.pcb._getPCBVersion(token.team, assembly, version).invoke()
          prev <- new PreviewRenderer(
            pcb,
            layerstack,
            registry,
            pcbv,
            pcbv.outline.get
          ) // TODO outline: specify the outline
            ._doRenderPreview(
              assembly,
              version,
              Some(specification),
              team,
              StackConfig(true, true)
            )
          _ <- pcb._setSpecificationPreview(team, assembly, version, prev.specification).invoke(SetPreviews(
            front = prev.front,
            rear = prev.rear
          ))
        } yield SpecificationPreviews(
          prev.front.map(_.toApi),
          prev.rear.map(_.toApi)
        ))
      }
    }

  override def getAllBoardDistances(
      assembly: UUID,
      version: UUID,
      max: Option[Double],
      files: Seq[String]
  ): ServiceCall[NotUsed, Map[String, Seq[DistanceDescription]]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        logger.withAssembly(token.team, assembly, Some(version), None)
        Future.sequence(files.map(f =>
          max match {
            case Some(m) => info.getDistanceLessThan(version, f, m).map(x => (f -> x))
            case None    => info.getDistances(version, f).map(x => (f -> x))
          }
        )).map(_.toMap)
      }
    }

  override def _getAllTracewidths(
      team: String,
      assembly: UUID,
      version: UUID,
      file: String
  ): ServerServiceCall[NotUsed, Seq[TraceWidthDescription]] =
    ServerServiceCall { _ =>
      logger.withAssembly(team, assembly, Some(version), None)
      registry.refFor[TraceWidthsEntity](TraceWidths.id(version, file)).ask(GetTraceWidths(version, file)).map(_.widths)
    }

  override def getAllTracewidths(
      assembly: UUID,
      version: UUID,
      file: String
  ): ServerServiceCall[NotUsed, Seq[TraceWidthDescription]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:read"
    ) { (token, _) =>
      _getAllTracewidths(token.team, assembly, version, file)
    }

  override def _getAllDistances(
      team: String,
      assembly: UUID,
      version: UUID,
      file: String,
      max: Option[Double]
  ): ServerServiceCall[NotUsed, Seq[DistanceDescription]] =
    ServerServiceCall { _ =>
      logger.withAssembly(team, assembly, Some(version), None)
      max match {
        case Some(m) => info.getDistanceLessThan(version, file, m)
        case None    => info.getDistances(version, file)
      }
    }

  override def getAllDistances(
      assembly: UUID,
      version: UUID,
      file: String,
      max: Option[Double]
  ): ServerServiceCall[NotUsed, Seq[DistanceDescription]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:read"
    ) { (token, _) =>
      _getAllDistances(token.team, assembly, version, file, max)
    }

  override def getRenderStatus(assembly: UUID, version: UUID, file: String): ServiceCall[NotUsed, RenderResult] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:render:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        logger.withAssembly(token.team, assembly, Some(version), None)
        registry.refFor[RenderEntity](fileID(version, file)).ask(GetRender).map { r =>
          RenderResult(r.status.getOrElse(RenderStatus.STATUS_EMPTY), r.renderMessages.getOrElse(Seq()))
        }
      }
    }

  def _doAnalyzeBoard(
      team: String,
      assembly: UUID,
      version: UUID,
      pcbv: PCBVersion,
      stacks: LayerStacks
  ): Future[Done] = {
    val lcname = AssemblyLifecycleStageName.Analysis.value
    (for {
      _ <- ass._updateVersionLifecycle(team, assembly, Some(version), lcname, Some(System.currentTimeMillis())).invoke(
        LifecycleStageStatus.emptyProgress.withDeadline(LifecycleDeadline.in(10 minutes))
      )
      _ <-
        if (stacks.selected.isDefined) {
          Future.successful({
            val assRef = AssemblyReference(team, assembly, None, version)
            RendererCoordinator.analysisQueue.submit(new GerberBoardAnalyzer(
              assRef,
              pcb,
              ass,
              layerstack,
              stacks,
              registry,
              pcbv
            ))
            Done
          })
        } else {
          ass._updateVersionLifecycle(
            team = team,
            assembly = assembly,
            version = Some(version),
            lcname = lcname,
            time = Some(System.currentTimeMillis())
          ).invoke(
            LifecycleStageStatus.emptySuccess
          ).map(_ => Done)
        }
    } yield Done).recover {
      case e: Throwable =>
        e.printStackTrace()
        throw e
    }

    Future.successful(Done)
  }

  override def _analyzeBoard(team: String, assembly: UUID, version: UUID): ServerServiceCall[NotUsed, Done] = {
    logger.withAssembly(team, assembly, Some(version), None)
    ServerServiceCall { _ =>
      for {
        pcbv   <- pcb._getPCBVersion(team, assembly, version).invoke()
        stacks <- layerstack._getPCBLayerstack(team, version, Some(true)).invoke()
        _      <- Future.successful(logger.info(s"ASSEMBLY STARTED BY EXTERNAL CALL ${assembly}/$version"))
        _      <- _doAnalyzeBoard(team, assembly, version, pcbv, stacks)
      } yield Done
    }
  }

  override def analyzeBoard(assembly: UUID, version: UUID): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:write"
    ) { (token, _) =>
      _analyzeBoard(token.team, assembly, version)
    }

  override def renderUpdates(
      assembly: UUID,
      version: UUID,
      k: String
  ): ServiceCall[NotUsed, Source[Streams.RenderStreamMessage, NotUsed]] = {
    import scala.concurrent.duration._
    val b = AuthenticationServiceComposition.decodeTokenWithResponse(k)
    authorizedStringWithToken(b)(token => s"pcb:${token.team}:${token.team}:$version:render:read") {
      (token, _) =>
        ServerServiceCall { _ =>
          logger.withAssembly(token.team, assembly, Some(version), None)
          val ticks = Source.tick(30 seconds, 30 seconds, RenderStreamMessage(None, m = Ping(Instant.now())))

          val nowOffset = Offset.timeBasedUUID(UUIDs.timeBased())
          val sources = RenderEvent.Tag.allTags.map(tag =>
            registry.eventStream(tag, nowOffset)
              .collect {
                case EventStreamElement(_, event: StatusChanged, _) if event.assRef.version == version =>
                  RenderStreamMessage(
                    Some(event.assRef),
                    StatusChangedMessage(Instant.now(), event.file.name, event.status.name)
                  )
                case EventStreamElement(_, event: MessagesChanged, _) if event.assRef.version == version =>
                  RenderStreamMessage(
                    Some(event.assRef),
                    MessageChangedMessage(Instant.now(), event.file.name, event.messages)
                  )
              }
          )

          val src = sources.reduce((a, b) => a.merge(b))
          Future.successful(src.merge(ticks))

        }
    }

  }

  override def analyzeLayers(assembly: UUID, version: UUID, file: String): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:dfm:write"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        logger.withAssembly(token.team, assembly, Some(version), None)

        Future.successful(Done)
      }
    }

  override def _renderLayerstack(): ServerServiceCall[LayerstackRendererRequest, FilePath] =
    ServerServiceCall { request =>
      val ctx = new SimpleTaskContext[SVGGraphics2D]()
      val rt  = new LayerStackDefinitionRenderTask(request.stack, ctx)
      rt.onSuccess { (ctx, t) =>
        request.path.createParentDir()
        withResource(new FileWriter(request.path.toJavaFile)) { wr =>
          ctx._r.foreach(_.stream(wr))
        }
      }

      RendererCoordinator.submitTask(rt)
      Future {
        rt.waitFor()
        request.path
      }
    }

  override def kiMatchFile(): ServiceCall[FilePath, Seq[FileType]] =
    ServiceCall { file =>
      Future {
        val ctx = new SimpleRenderContext()
        val rt  = new SimplePreviewRenderTask(ctx, file.toJavaPath)
        Some(RendererCoordinator.submitTask(rt))
        rt.waitFor()

        val temp = Files.createTempFile("ki", "png")
        ctx.result.foreach(SimplePreviewRenderTask.write(_, temp))

        Seq()
      }
    }

  override def shutdownRenderer(): ServiceCall[NotUsed, Boolean] =
    ServerServiceCall { _ =>
      Future {
        redisWorker.shutdown()
        RendererCoordinator.shutdown(5 seconds).toOption.getOrElse(false)
        //
      }
    }

  override def renderAdded(): Topic[api.RenderAdded] =
    TopicProducer.taggedStreamWithOffset(RenderEvent.Tag) { (tag, offset) =>
      registry.eventStream(tag, offset).mapConcat(filterRenderAdded)
    }

  override def previewAdded(): Topic[api.PreviewAdded] =
    TopicProducer.taggedStreamWithOffset(RenderEvent.Tag) { (tag, offset) =>
      registry.eventStream(tag, offset).mapConcat(filterPreviewAdded)
    }

  override def treeAdded(): Topic[api.QuadTreeAdded] =
    TopicProducer.taggedStreamWithOffset(RenderEvent.Tag) { (tag, offset) =>
      registry.eventStream(tag, offset).mapConcat(filterTreeAdded)
    }

  private def filterDrillsAdded(ev: EventStreamElement[RenderEvent]) = ev match {
    case x @ EventStreamElement(_, ev: DrillsAdded, _) =>
      immutable.Seq((
        api.DrillsAdded(
          ev.assRef,
          ev.file,
          ev.newDrills,
          ev.info
        ),
        x.offset
      ))
    case _ => Nil

  }

  private def filterRenderAdded(ev: EventStreamElement[RenderEvent]) = ev match {
    case x @ EventStreamElement(_, ev: RenderAdded, _) =>
      immutable.Seq((
        api.RenderAdded(
          ev.assRef,
          ev.file,
          ev.newRender,
          ev.previousRenders,
          ev.format
        ),
        x.offset
      ))

    case _ => Nil
  }

  private def filterPreviewAdded(ev: EventStreamElement[RenderEvent]) = ev match {
    case x @ EventStreamElement(_, ev: PreviewSet, _) =>
      immutable.Seq((
        api.PreviewAdded(
          ev.assRef,
          ev.file,
          ev.preview
        ),
        x.offset
      ))

    case _ => Nil
  }

  private def filterTreeAdded(ev: EventStreamElement[RenderEvent]) = ev match {
    case x @ EventStreamElement(_, ev: TreeSet, _) =>
      immutable.Seq((
        api.QuadTreeAdded(
          ev.assRef,
          ev.file,
          ev.treeData,
          ev.format
        ),
        x.offset
      ))

    case _ => Nil
  }
}

object RendererServiceImpl {

  def id(version: UUID, file: String, entityType: String) = s"$version|$file|$entityType"

  def id(version: UUID, entityType: String) = s"$version||$entityType"

  def fileID(version: UUID, file: String) = s"$version|$file"

  def withData[T](
      version: UUID,
      file: String,
      registry: PersistentEntityRegistry
  )(handle: (PersistentEntityRef[RenderCommand], UUID, FilePath, GerberFile, Format) => Future[T])(implicit
      ctx: ExecutionContext
  ) = {
    val decFile = URLDecoder.decode(file)
    val entity  = registry.refFor[RenderEntity](fileID(version, decFile))
    entity.ask(GetRender).flatMap { render =>
      render.tree match {
        case Some(dataPath) if dataPath.filename.contains("layerdata") =>
          if (render.fileID.isDefined && render.format.isDefined) {
            handle(entity, version, dataPath, render.fileID.get, render.format.get)
          } else {
            throw new TransportException(TransportErrorCode.NotFound, "File is incomplete")
          }
        case Some(_) =>
          throw new TransportException(TransportErrorCode.NotFound, "no data available, assembly too old")
        case None =>
          println(s"found no data for file $decFile")
          throw new TransportException(TransportErrorCode.NotFound, "no data available")
      }
    }
  }

  def withLayerData[T](
      version: UUID,
      file: String,
      registry: PersistentEntityRegistry
  )(handle: (PersistentEntityRef[RenderCommand], PCBLayerInternalData, GerberFile, Format) => Future[T])(implicit
      ctx: ExecutionContext
  ) =
    withData(version, file, registry) { (ref, version, datapath, gfile, format) =>
      withResource(new FileInputStream(datapath.toJavaFile)) { is =>
        Json.fromJson[PCBLayerInternalData](Json.parse(is)) match {
          case JsSuccess(value, path) =>
            handle(ref, value, gfile, format)
          case JsError(_) =>
            throw new TransportException(TransportErrorCode.InternalServerError, "failed to read PCB data")
        }
      }
    }

}
