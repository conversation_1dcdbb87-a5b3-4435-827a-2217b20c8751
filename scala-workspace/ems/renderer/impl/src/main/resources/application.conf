include "main-application.conf"

play.application.loader = de.fellows.ems.renderer.impl.RendererServiceLoader


renderer.cassandra.keyspace = ${?fellows.persistence.rootKeyspace}renderer

akka.persistence.cassandra {
  events-by-tag {
    max-message-batch-size = 10
  }
  journal {
    max-message-batch-size = 10
  }
}

akka.management {
  health-checks {
    readiness-checks {
      stackrate-renderer-health = "de.fellows.ems.renderer.impl.RendererHealth"
    }
  }
}

cassandra-journal {
  keyspace = ${renderer.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${renderer.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${renderer.cassandra.keyspace}_read
}


akka.management.cluster.bootstrap.contact-point-discovery.service-name = "renderer"
# fellows.serviceconfig = ${fellows.services.renderer}

# akka {
#   cluster.seed-nodes = [
#     "akka://application@"${fellows.serviceconfig.seed}":"${fellows.serviceconfig.remoting}
#   ]
# }


akka {
  # Log level used by the configured loggers (see "loggers") as soon
  # as they have been started; before that, see "stdout-loglevel"
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  loglevel = "DEBUG"

  # Log level for the very basic logger activated during ActorSystem startup.
  # This logger prints the log messages to stdout (System.out).
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  stdout-loglevel = "DEBUG"
}

fellows.storage {
  service = ${fellows.storage.base}/renderer
}

fellows.renderer {
  colors {
    finish.hal = "#d1c9c9"

    finish.hal-pb = "#d1c9c9"
    finish.hal-leadfree = "#d1c9c9"
    finish.hal-pb-free = "#d1c9c9"

    finish.silver = "#d1c9c9"
    finish.chemical-tin = "#d1c9c9"
    finish.is = "#d1c9c9"
    finish.it = "#d1c9c9"
    finish.isig = "#CB9932"
    finish.enig = "#CB9932"
    finish.enepig = "#CB9932"
    finish.enipig = "#CB9932"
    finish.enag = "#CB9932"
    finish.epig = "#CB9932"
    finish.osp = "#FAD19B"

    finish.none = "#B87333"
    finish.default = "#cc9933"


    soldermask.default = "#004200"
    soldermask.red = "#b82915"
    soldermask.blue = "#06115b"
    soldermask.white = "#ffffff"
    soldermask.black = "#020202"
    soldermask.green-matt = "#003500"
    soldermask.green-glossy = "#004200"
    soldermask.purple = "#8B47AA"
    soldermask.yellow = "#EAC100"

    paste.default = "#888888"

    silkscreen.black = "#020202"
    silkscreen.yellow = "#EAC100"
    silkscreen.red = "#B82915"
    silkscreen.default = "#ffffff"
    silkscreen.green = "#004200"
    silkscreen.blue = "#06115b"

    drill.default = "#999999"
  }
}

rendering-service-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 5
  }
  throughput = 1
}
preview-service-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 10
  }
  throughput = 1
}

rendering-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 5
  }
  throughput = 1
}

analyze-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 5
  }
  throughput = 1
}
