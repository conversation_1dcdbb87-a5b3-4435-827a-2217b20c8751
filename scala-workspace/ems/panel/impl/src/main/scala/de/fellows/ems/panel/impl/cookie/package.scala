package de.fellows.ems.panel.impl

import java.awt.Shape
import java.awt.geom.{ AffineTransform, Rectangle2D }

package object cookie {

  sealed trait GElement

  case class GBlock(shape: Shape, paddedOutline: Rectangle2D, boards: Seq[GBoard]) extends GElement {
    def ~(transform: AffineTransform): GBlock =
      GBlock(
        transform.createTransformedShape(shape),
        transform.createTransformedShape(paddedOutline).getBounds2D,
        boards.map(gb => gb ~ transform)
      )
  }

  case class GBoard(shape: Shape, paddedOutline: Rectangle2D, multiplier: Int) extends GElement {
    def ~(transform: AffineTransform): GBoard = GBoard(
      transform.createTransformedShape(shape),
      transform.createTransformedShape(paddedOutline).getBounds2D,
      multiplier
    )
  }

  case class GCustomerPanel(shape: Shape, paddedOutline: Rectangle2D, elements: Seq[GElement]) {
    lazy val boards: Int =
      elements.map {
        case x: GBoard => 1
        case x: GBlock => x.boards.length
      }.sum

    def ~(transform: AffineTransform): GCustomerPanel =
      GCustomerPanel(
        transform.createTransformedShape(shape),
        transform.createTransformedShape(paddedOutline).getBounds2D,
        elements.map {
          case x: GBlock => x ~ transform
          case x: GBoard => x ~ transform
        }
      )
  }

}
