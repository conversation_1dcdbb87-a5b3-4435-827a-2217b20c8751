package de.fellows.ems.panel.impl.entity

import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag}
import de.fellows.ems.panel.api.WorkingPanel
import play.api.libs.json.{Format, Json}

import java.util.UUID

package object working {

  sealed trait WorkingPanelCommand

  sealed trait WorkingPanelEvent extends AggregateEvent[WorkingPanelEvent] {
    override def aggregateTag = WorkingPanelEvent.Tag
  }

  object WorkingPanelEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[WorkingPanelEvent](NumShards)
  }

  case class CreateWorkingPanel(team: String, panel: WorkingPanel) extends WorkingPanelCommand
      with ReplyType[WorkingPanel]

  case class GetWorkingPanel(team: String, panel: UUID) extends WorkingPanelCommand with ReplyType[WorkingPanel]

  case class UpdateWorkingPanel(team: String, panel: WorkingPanel) extends WorkingPanelCommand
      with ReplyType[WorkingPanel]

  case class DeleteWorkingPanel(team: String, panel: UUID) extends WorkingPanelCommand with ReplyType[WorkingPanel]

  case class WorkingPanelUpdated(team: String, panel: Option[WorkingPanel], previous: Option[WorkingPanel])
      extends WorkingPanelEvent

  object CreateWorkingPanel {
    implicit val ctx: Format[CreateWorkingPanel] = Json.format[CreateWorkingPanel]
  }

  object GetWorkingPanel {
    implicit val ctx: Format[GetWorkingPanel] = Json.format[GetWorkingPanel]
  }

  object UpdateWorkingPanel {
    implicit val ctx: Format[UpdateWorkingPanel] = Json.format[UpdateWorkingPanel]
  }

  object DeleteWorkingPanel {
    implicit val ctx: Format[DeleteWorkingPanel] = Json.format[DeleteWorkingPanel]
  }

  object WorkingPanelUpdated {
    implicit val ctx: Format[WorkingPanelUpdated] = Json.format[WorkingPanelUpdated]
  }

}
