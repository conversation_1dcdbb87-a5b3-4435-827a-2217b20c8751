package de.fellows.ems.panel.impl

import java.awt.geom.{ AffineTransform, Rectangle2D }
import java.awt.image.BufferedImage
import java.io.File
import javax.imageio.ImageIO

object RotateTest extends App {

  val rec = new Rectangle2D.Double(0, 0, 500, 200)

  Range(0, 180).foreach { i =>
    val rot = new AffineTransform()

    rot.scale(-1, 1)
    rot.translate(rec.getHeight, 0)
    rot.rotate(Math.toRadians(-i.doubleValue), rec.getMinX, rec.getMinY)
    val helper = rot.createTransformedShape(rec)

    val trans2 = new AffineTransform()

    trans2.translate(-helper.getBounds2D.getX, -helper.getBounds2D.getY)
    trans2.concatenate(rot)

    val rotated = trans2.createTransformedShape(rec)

    val bImg2 = new BufferedImage(2000, 2000, BufferedImage.TYPE_INT_RGB)
    val cg2   = bImg2.createGraphics
    cg2.fill(rotated)
    println(i)
    ImageIO.write(bImg2, "png", new File(f"/tmp/rotate/rotate$i%05d.png"))
  }

  val bImg = new BufferedImage(2000, 2000, BufferedImage.TYPE_INT_RGB)
  val cg   = bImg.createGraphics
  cg.fill(rec)

  ImageIO.write(bImg, "png", new File("/tmp/rotate/normal.png"))

}
