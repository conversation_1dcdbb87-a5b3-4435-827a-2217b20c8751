package de.fellows.ems.layerstack.api

import de.fellows.utils.JsonFormats
import play.api.libs.json.Json.MacroOptions
import play.api.libs.json.{Format, Json, JsonConfiguration}

import java.time.Instant

object Streams {

  sealed trait LayerstackStreamMessage

  case class LayerStackSelected(stack: LayerStacksAPI, user: Boolean)

  case class LayerStackMessage(time: Instant, stacks: Option[LayerStacksAPI]) extends LayerstackStreamMessage

  case class LayerStackChangedMessage(time: Instant, stack: Option[LayerStackAPI]) extends LayerstackStreamMessage

  case class LayerStackDefinitionChanged(stack: LayerstackDefinition)

  object LayerStackMessage {
    implicit val format: Format[LayerStackMessage] = Json.format[LayerStackMessage]
  }

  object LayerStackChangedMessage {
    implicit val format: Format[LayerStackChangedMessage] = Json.format[LayerStackChangedMessage]
  }

  object LayerStackSelected {
    implicit val format: Format[LayerStackSelected] = Json.format[LayerStackSelected]
  }

  object LayerStackDefinitionChanged {
    implicit val format: Format[LayerStackDefinitionChanged] = Json.format[LayerStackDefinitionChanged]
  }

  object LayerstackStreamMessage {
    implicit val cfg: JsonConfiguration.Aux[MacroOptions] =
      JsonFormats.JsonSealedTraitConfig
    implicit val format: Format[LayerstackStreamMessage] = Json.format[LayerstackStreamMessage]
  }
}
