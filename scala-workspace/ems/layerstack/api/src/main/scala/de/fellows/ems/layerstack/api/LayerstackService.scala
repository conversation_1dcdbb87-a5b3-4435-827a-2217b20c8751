// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.ems.layerstack.api

import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.ems.layerstack.api.Streams.{
  LayerStackDefinitionChanged,
  LayerStackMessage,
  LayerStackSelected,
  LayerstackStreamMessage
}
import de.fellows.ems.layerstack.api.sequence.{
  SequencedLayerstack,
  SequencedLayerstackAPI,
  SequencedLayerstackDefinitionAPI
}
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.collaboration.TimelineEvent
import de.fellows.utils.communication.{BinaryMessageSerializer, ServiceExceptionSerializer}
import de.fellows.utils.meta.MetaInfo
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.{OpenAPIDefinition, Operation}
import play.api.libs.json.{Format, Json}

import java.util.UUID

case class SequenceUpdate(material: Option[String], meta: Option[MetaInfo])

object SequenceUpdate {
  implicit val format: Format[SequenceUpdate] = Json.format[SequenceUpdate]
}

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate LayerStack API"
  )
)
@Tag(name = LayerstackService.API_DEFINITIONS, description = "Definitions")
@Tag(name = LayerstackService.API_LAYERSTACKS, description = "Layerstacks")
@Tag(name = LayerstackService.API_LIBRARIES, description = "Libraries")
@Tag(name = LayerstackService.API_MATERIALS, description = "Materials")
@Tag(name = LayerstackService.API_METRICS, description = "Metrics")
trait LayerstackService extends Service with StackrateServiceAPI {
  val subPath    = "ems/layerstack"
  val basePath   = s"/api/$subPath"
  val basePathv2 = s"/api/$subPath/v2"

  val internalBasePath   = s"/internal/$subPath/:team"
  val internalBasePathv2 = s"/internal/$subPath/v2/:team"

  @StackrateApi
  @Tag(name = LayerstackService.API_MATERIALS)
  def getMaterials(
      page: Option[Int],
      pagesize: Option[Int],
      filter: Option[String],
      flat: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[Material]]

  @StackrateApi
  @Tag(name = LayerstackService.API_MATERIALS)
  def createMaterial(library: Option[String]): ServiceCall[Seq[Material], Seq[Material]]

  @StackrateApi
  @Tag(name = LayerstackService.API_MATERIALS)
  def getMaterial(id: String): ServiceCall[NotUsed, Material]

  @StackrateApi
  @Tag(name = LayerstackService.API_MATERIALS)
  def deleteMaterial(id: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = LayerstackService.API_MATERIALS)
  def updateMaterial(id: String): ServiceCall[Material, Material]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def getLibraries(
      page: Option[Int],
      pagesize: Option[Int],
      filter: Option[String],
      flat: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[Library]]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def createLibrary: ServiceCall[Library, Library]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def getLibraryTypes: ServiceCall[NotUsed, Seq[(String, Int)]]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def getLibraryCount: ServiceCall[NotUsed, BigDecimal]

  @StackrateApi
  @Tag(name = LayerstackService.API_METRICS)
  def getMaterialTypes: ServiceCall[NotUsed, Seq[(String, Int)]]

  @StackrateApi
  @Tag(name = LayerstackService.API_METRICS)
  def getMaterialCount(`type`: Option[String]): ServiceCall[NotUsed, Int]

  @StackrateApi
  @Tag(name = LayerstackService.API_METRICS)
  def getMaterialCountProperty(property: String): ServiceCall[NotUsed, Seq[(String, Int)]]

  @StackrateApi
  @Tag(name = LayerstackService.API_METRICS)
  def getLibrarySuppliers: ServiceCall[NotUsed, Seq[(String, Int)]]

  @StackrateApi
  @Tag(name = LayerstackService.API_METRICS)
  def getMaterialSuppliers: ServiceCall[NotUsed, Seq[(String, Int)]]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def getLibrary(name: String): ServiceCall[NotUsed, Library]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def updateLibrary(name: String): ServiceCall[Library, Library]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def deleteLibrary(name: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def setLibraryMaterials(name: String): ServiceCall[Seq[String], Library]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def getLibraryMaterials(
      name: String,
      flat: Option[Boolean],
      filter: Option[String]
  ): ServiceCall[NotUsed, Seq[Material]]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def addMaterialToLib(name: String, material: String): ServiceCall[NotUsed, Library]

  @StackrateApi
  @Tag(name = LayerstackService.API_LIBRARIES)
  def removeMaterialFromLib(name: String, material: String, delete: Option[Boolean]): ServiceCall[NotUsed, Library]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def getLayerstacks(
      page: Option[Int],
      pagesize: Option[Int],
      filter: Option[String],
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[LayerstackDefinitionAPI]]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def listLayerstacks(
      page: Option[Int],
      pagesize: Option[Int]
  ): ServiceCall[NotUsed, Seq[LayerstackDefinitionDescription]]

  def _getStandardLayerstacks(team: String): ServiceCall[NotUsed, Seq[LayerstackDefinition]]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def createLayerstack: ServiceCall[LayerstackDefinitionAPI, LayerstackDefinitionAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def getLayerstack(name: String, resolve: Option[Boolean]): ServiceCall[NotUsed, LayerstackDefinitionAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def getLayerstackPrice(name: String): ServiceCall[NotUsed, LayerstackPrices]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def updateLayerstack(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[LayerstackDefinitionAPI, LayerstackDefinitionAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def deleteLayerstack(name: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def cloneLayerstack(name: String): ServiceCall[String, LayerstackDefinitionAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def convertLayerstack(name: String, format: String): ServiceCall[NotUsed, Array[Byte]]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def convertLayerstacks(
      page: Option[Int],
      pagesize: Option[Int],
      format: Option[String]
  ): ServiceCall[NotUsed, Array[Byte]]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def printLayerstack(name: String, template: Option[String]): ServiceCall[NotUsed, Array[Byte]]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def getSubLayerstacks(name: String, resolve: Option[Boolean]): ServiceCall[NotUsed, Seq[SubStackDefinition]]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def setSubLayerstacks(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[Seq[SubStackDefinition], LayerstackDefinitionAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def addSubLayerstack(name: String, resolve: Option[Boolean]): ServiceCall[SubStackDefinition, LayerstackDefinitionAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def getSubLayerstack(
      name: String,
      subname: String,
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, SubStackDefinition]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def setSubLayerstack(
      name: String,
      subname: String,
      resolve: Option[Boolean]
  ): ServiceCall[SubStackDefinition, LayerstackDefinitionAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_DEFINITIONS)
  def removeSubLayerstack(
      name: String,
      subname: String,
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, LayerstackDefinitionAPI]

  def _setPreview(team: String, name: String): ServiceCall[String, Done]

  @StackrateApi
  @Tag(name = LayerstackService.API_LAYERSTACKS)
  def matchLayerstack(assembly: UUID, version: UUID): ServiceCall[NotUsed, LayerStacksAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_LAYERSTACKS)
  def getLayerstackOptions(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[LayerstackDefinitionDescription]]
  def getLayerstackOptionsByVersion(version: UUID): ServiceCall[NotUsed, Seq[LayerstackDefinitionDescription]]

  @StackrateApi
  @Tag(name = LayerstackService.API_LAYERSTACKS)
  def selectLayerstack(assembly: UUID, version: UUID): ServiceCall[IDContainer, LayerStacksAPI]
  def selectLayerstackByVersion(version: UUID): ServiceCall[IDContainer, LayerStacksAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_LAYERSTACKS)
  def getPCBLayerstack(
      assembly: UUID,
      version: UUID,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, LayerStacksAPI]
  def getPCBLayerstackByVersion(
      version: UUID,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, LayerStacksAPI]

  def _getPCBLayerstack(
      team: String,
      layerstack: UUID,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, LayerStacks]

  def _matchLayerstack(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, LayerStacks]

  def _updateStack(team: String, layerstack: UUID): ServiceCall[LayerStack, Done]

  def streamLayerstacks(
      assembly: UUID,
      version: UUID,
      k: String,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, Source[LayerstackStreamMessage, NotUsed]]

  def layerstackSetTopic(): Topic[LayerStackSelected]

  def layerstackTimeline(): Topic[TimelineEvent]

  def layerstackDefinitionTopic(): Topic[LayerStackDefinitionChanged]

  // sequence definitions

  def getSequenceDefinitions(
      page: Option[Int],
      pagesize: Option[Int],
      filter: Option[String],
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[SequencedLayerstackDefinitionAPI]]

  def getSequenceDefinition(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, SequencedLayerstackDefinitionAPI]

  def getSequenceDefinitionPrice(name: String): ServiceCall[NotUsed, LayerstackPrices]

  def updateSequenceDefinition(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[SequencedLayerstackDefinitionAPI, SequencedLayerstackDefinitionAPI]

  def updateSequence(name: String, sequence: UUID): ServiceCall[SequenceUpdate, SequencedLayerstackDefinitionAPI]

  def deleteSequenceDefinition(name: String): ServiceCall[NotUsed, Done]

  def createSequenceDefinition(resolve: Option[Boolean])
      : ServiceCall[SequencedLayerstackDefinitionAPI, SequencedLayerstackDefinitionAPI]

  // sequences

  def matchLayerstackSequence(assembly: UUID, version: UUID): ServiceCall[NotUsed, SequencedLayerstackAPI]

  @StackrateApi
  @Tag(name = LayerstackService.API_SEQUENCES)
  @Operation(
    summary = "Stackup options for this PCB"
  )
  def getLayerstackSequenceOptions(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[SequencedLayerstackAPI]]

  def selectLayerstackSequence(assembly: UUID, version: UUID): ServiceCall[IDContainer, SequencedLayerstackAPI]

  def getPCBLayerstackSequence(
      assembly: UUID,
      version: UUID,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, SequencedLayerstackAPI]

  def streamLayerstackSequences(
      assembly: UUID,
      version: UUID,
      k: String,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, Source[LayerStackMessage, NotUsed]]

  def _updateStackSequence(team: String, assembly: UUID, version: UUID): ServiceCall[SequencedLayerstack, Done]

  def _getPCBLayerstackSequence(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, SequencedLayerstack]

  override def descriptor: Descriptor = {
    import Service._

    val DEFINITIONS = "definitions"
    val LAYERSTACKS = "layerstacks"
    val MATERIALS   = "materials"
    val LIBRARIES   = "libraries"
    val METRICS     = "metrics"

    withDocumentation(subPath)(
      named("layerstack")
        .withCalls(
          //  -------- //
          //  MATERIAL //
          //  -------- //
          restCall(Method.GET, s"$basePath/$MATERIALS?page&pagesize&filter&flat", getMaterials _),
          restCall(Method.POST, s"$basePath/$MATERIALS?library", createMaterial _),
          restCall(Method.GET, s"$basePath/$MATERIALS/:id", getMaterial _),
          restCall(Method.DELETE, s"$basePath/$MATERIALS/:id", deleteMaterial _),
          restCall(Method.PUT, s"$basePath/$MATERIALS/:id", updateMaterial _),

          //  --------- //
          //  LIBRARIES //
          //  --------- //
          restCall(Method.GET, s"$basePath/$LIBRARIES?page&pagesize&filter&flat", getLibraries _),
          restCall(Method.POST, s"$basePath/$LIBRARIES", createLibrary _),
          restCall(Method.GET, s"$basePath/$METRICS/libraries/types", getLibraryTypes _),
          restCall(Method.GET, s"$basePath/$METRICS/libraries/count", getLibraryCount _),
          restCall(Method.GET, s"$basePath/$METRICS/libraries/supplier", getLibrarySuppliers _),
          restCall(Method.GET, s"$basePath/$METRICS/materials/count?type", getMaterialCount _),
          restCall(Method.GET, s"$basePath/$METRICS/materials/countProperty?property", getMaterialCountProperty _),
          restCall(Method.GET, s"$basePath/$METRICS/materials/types", getMaterialTypes _),
          restCall(Method.GET, s"$basePath/$METRICS/materials/supplier", getMaterialSuppliers _),
          restCall(Method.GET, s"$basePath/$LIBRARIES/:name", getLibrary _),
          restCall(Method.PUT, s"$basePath/$LIBRARIES/:name", updateLibrary _),
          restCall(Method.DELETE, s"$basePath/$LIBRARIES/:name", deleteLibrary _),
          restCall(Method.PUT, s"$basePath/$LIBRARIES/:name/materials", setLibraryMaterials _),
          restCall(Method.GET, s"$basePath/$LIBRARIES/:name/materials?flat&filter", getLibraryMaterials _),
          restCall(Method.PUT, s"$basePath/$LIBRARIES/:name/materials/:material", addMaterialToLib _),
          restCall(Method.DELETE, s"$basePath/$LIBRARIES/:name/materials/:material?delete", removeMaterialFromLib _),

          //  ----------- //
          //  DEFINITIONS //
          //  ----------- //

          restCall(Method.GET, s"$basePath/$DEFINITIONS?page&pagesize&filter&resolve", getLayerstacks _),
          restCall(Method.GET, s"$basePath/$DEFINITIONS/list?page&pagesize", listLayerstacks _),
          restCall(
            Method.GET,
            s"$internalBasePath/$DEFINITIONS?page&pagesize&filter&resolve",
            _getStandardLayerstacks _
          ),
          restCall(Method.GET, s"$basePath/$DEFINITIONS/_convert?page&pagesize&format", convertLayerstacks _)
            .withResponseSerializer(new BinaryMessageSerializer()),
          restCall(Method.POST, s"$basePath/$DEFINITIONS", createLayerstack _),
          restCall(Method.GET, s"$basePath/$DEFINITIONS/:name?resolve", getLayerstack _),
          restCall(Method.GET, s"$basePath/$DEFINITIONS/:name/price", getLayerstackPrice _),
          restCall(Method.PUT, s"$basePath/$DEFINITIONS/:name?resolve", updateLayerstack _),
          restCall(Method.DELETE, s"$basePath/$DEFINITIONS/:name", deleteLayerstack _),
          restCall(Method.POST, s"$basePath/$DEFINITIONS/:name/clone", cloneLayerstack _),
          restCall(Method.GET, s"$basePath/$DEFINITIONS/:name/convert?format", convertLayerstack _)
            .withResponseSerializer(new BinaryMessageSerializer()),
          restCall(Method.GET, s"$basePath/$DEFINITIONS/:name/print?template", printLayerstack _)
            .withResponseSerializer(new BinaryMessageSerializer()),
          restCall(Method.GET, s"$basePath/$DEFINITIONS/:name/substacks?resolve", getSubLayerstacks _),
          restCall(Method.PUT, s"$basePath/$DEFINITIONS/:name/substacks?resolve", setSubLayerstacks _),
          restCall(Method.POST, s"$basePath/$DEFINITIONS/:name/substacks?resolve", addSubLayerstack _),
          restCall(Method.GET, s"$basePath/$DEFINITIONS/:name/substacks/:subname?resolve", getSubLayerstack _),
          restCall(Method.PUT, s"$basePath/$DEFINITIONS/:name/substacks/:subname?resolve", setSubLayerstack _),
          restCall(Method.DELETE, s"$basePath/$DEFINITIONS/:name/substacks/:subname?resolve", removeSubLayerstack _),
          restCall(Method.PUT, s"$internalBasePath/$DEFINITIONS/:name/preview", _setPreview _),

          // ----------- //
          // LAYERSTACKS //
          // ----------- //
          restCall(
            Method.PUT,
            s"$basePath/$LAYERSTACKS/assemblies/:assembly/versions/:version/match",
            matchLayerstack _
          ),
          restCall(
            Method.GET,
            s"$basePath/$LAYERSTACKS/assemblies/:assembly/versions/:version/match",
            getLayerstackOptions _
          ),
          restCall(
            Method.GET,
            s"$basePath/$LAYERSTACKS/versions/:version/match",
            getLayerstackOptionsByVersion _
          ),
          restCall(
            Method.PUT,
            s"$basePath/$LAYERSTACKS/assemblies/:assembly/versions/:version",
            selectLayerstack _
          ),
          restCall(
            Method.PUT,
            s"$basePath/$LAYERSTACKS/versions/:version",
            selectLayerstackByVersion _
          ),
          restCall(
            Method.GET,
            s"$basePath/$LAYERSTACKS/assemblies/:assembly/versions/:version?materials",
            getPCBLayerstack _
          ),
          restCall(
            Method.GET,
            s"$basePath/$LAYERSTACKS/versions/:version?materials",
            getPCBLayerstackByVersion _
          ),
          restCall(
            Method.GET,
            s"$basePath/$LAYERSTACKS/assemblies/:assembly/versions/:version/updates?k&materials",
            streamLayerstacks _
          ),
          restCall(
            Method.PUT,
            s"$internalBasePath/$LAYERSTACKS/assemblies/:assembly/versions/:version/match",
            _matchLayerstack _
          ),
          restCall(
            Method.PUT,
            s"$internalBasePath/$LAYERSTACKS/by-id/:layerstack",
            _updateStack _
          ),
          restCall(
            Method.GET,
            s"$internalBasePath/$LAYERSTACKS/by-id/:layerstack?materials",
            _getPCBLayerstack _
          ),

          // --------- //
          // Sequences //
          // --------- //

          restCall(Method.GET, s"$basePathv2/$DEFINITIONS?page&pagesize&filter&resolve", getSequenceDefinitions _),
          restCall(Method.GET, s"$basePathv2/$DEFINITIONS/:name?resolve", getSequenceDefinition _),
          restCall(Method.GET, s"$basePathv2/$DEFINITIONS/:name/price", getSequenceDefinitionPrice _),
          restCall(Method.PUT, s"$basePathv2/$DEFINITIONS/:name?resolve", updateSequenceDefinition _),
          restCall(Method.PUT, s"$basePathv2/$DEFINITIONS/:name/:sequence", updateSequence _),
          restCall(Method.DELETE, s"$basePathv2/$DEFINITIONS/:name", deleteSequenceDefinition _),
          restCall(Method.POST, s"$basePathv2/$DEFINITIONS?resolve", createSequenceDefinition _),
          restCall(
            Method.PUT,
            s"$basePathv2/$LAYERSTACKS/assemblies/:assembly/versions/:version/match",
            matchLayerstackSequence _
          ),
          restCall(
            Method.PUT,
            s"$basePathv2/$LAYERSTACKS/assemblies/:assembly/versions/:version",
            selectLayerstackSequence _
          ),
          restCall(
            Method.GET,
            s"$basePathv2/$LAYERSTACKS/assemblies/:assembly/versions/:version",
            getLayerstackSequenceOptions _
          ),
          restCall(
            Method.GET,
            s"$basePathv2/$LAYERSTACKS/assemblies/:assembly/versions/:version?materials",
            getPCBLayerstackSequence _
          ),
          restCall(
            Method.GET,
            s"$basePathv2/$LAYERSTACKS/assemblies/:assembly/versions/:version/updates?k&materials",
            streamLayerstackSequences _
          ),
          restCall(
            Method.PUT,
            s"$internalBasePathv2/$LAYERSTACKS/assemblies/:assembly/versions/:version",
            _updateStackSequence _
          ),
          restCall(
            Method.GET,
            s"$internalBasePathv2/$LAYERSTACKS/assemblies/:assembly/versions/:version",
            _getPCBLayerstackSequence _
          )
        )
        .withTopics(
          topic(LayerstackService.LAYERSTACK_SET, layerstackSetTopic()),
          topic(LayerstackService.TIMELINE, layerstackTimeline()),
          topic(LayerstackService.LAYERSTACK_DEFINITION, layerstackDefinitionTopic())
        )
        .withAcls(
          ServiceAcl(pathRegex = Some(s"/files/$subPath/.*")),
          ServiceAcl(pathRegex = Some(s"$basePath/.*"))
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )
  }
}

object LayerstackService {
  val V = s"v1.6"

  val LAYERSTACK_SET        = s"domain.layerstack.set-$V"
  val TIMELINE              = s"domain.layerstack.timeline-$V"
  val LAYERSTACK_DEFINITION = s"domain.layerstack.definition.changed-$V"

  final val API_DEFINITIONS = "Definitions"
  final val API_MATERIALS   = "Materials"
  final val API_LIBRARIES   = "Libraries"
  final val API_METRICS     = "Metrics"

  final val API_LAYERSTACKS = "Layerstacks"
  final val API_SEQUENCES   = "Layerstack Sequences"
}
//
