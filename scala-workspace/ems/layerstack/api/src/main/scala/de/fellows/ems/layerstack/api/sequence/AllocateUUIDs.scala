package de.fellows.ems.layerstack.api.sequence

import java.util.UUID

class AllocateUUIDs extends ApiSequenceProcessor {
  override def process(la: SequencedLayerstackDefinitionAPI): SequencedLayerstackDefinitionAPI =
    la.copy(
      id = Some(UUID.randomUUID()),
      topSequence = la.topSequence.transform {
        case x: MaterialSequence => x.copy(internalId = Some(UUID.randomUUID()))
        case x: LayerStackNode   => x.copy(internalId = Some(UUID.randomUUID()))
      },
      children =
        la.children.map(_.map(_.transform(
          {
            case x: MaterialSequence => Some(x.copy(internalId = Some(UUID.randomUUID())))
            case x: LayerStackNode   => Some(x.copy(internalId = Some(UUID.randomUUID())))
          },
          seqdef => Some(seqdef.copy(id = UUID.randomUUID()))
        )))
    )

}

class InternalAllocateUUIDs extends SequenceProcessor[SequencedLayerstackDefinition] {
  override def process(la: SequencedLayerstackDefinition): SequencedLayerstackDefinition =
    la.copy(
      id = UUID.randomUUID(),
      topSequence = la.topSequence.transform {
        case x: MaterialSequence => x.copy(internalId = Some(UUID.randomUUID()))
        case x: LayerStackNode   => x.copy(internalId = Some(UUID.randomUUID()))
      },
      children =
        la.children.map(_.transform(
          {
            case x: MaterialSequence => Some(x.copy(internalId = Some(UUID.randomUUID())))
            case x: LayerStackNode   => Some(x.copy(internalId = Some(UUID.randomUUID())))
          },
          seqdef => Some(seqdef.copy(id = UUID.randomUUID()))
        ))
    )

}
