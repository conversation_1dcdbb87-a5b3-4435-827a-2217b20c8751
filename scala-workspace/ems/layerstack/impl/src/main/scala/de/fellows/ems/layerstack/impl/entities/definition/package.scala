package de.fellows.ems.layerstack.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag}
import de.fellows.ems.layerstack.api.{LayerstackDefinition, SubStackDefinition}
import de.fellows.utils.FilePath
import de.fellows.utils.entities.CollaborativeEventInfo
import de.fellows.utils.meta._
import play.api.libs.json.{Format, Json}

import java.util.UUID

package object definition {

  sealed trait LayerstackDefinitionCommand

  sealed trait LayerstackDefinitionEvent extends AggregateEvent[LayerstackDefinitionEvent] {
    override def aggregateTag = LayerstackDefinitionEvent.Tag
  }

  object LayerstackDefinitionEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[LayerstackDefinitionEvent](NumShards)
  }

  case class CreateLayerstackDefinition(ls: LayerstackDefinition, info: CollaborativeEventInfo)
      extends LayerstackDefinitionCommand with ReplyType[LayerstackDefinition]

  case class SetLayerstackDefinitionMeta(meta: MetaInfo, info: CollaborativeEventInfo)
      extends LayerstackDefinitionCommand with ReplyType[LayerstackDefinition]

  case class DeleteLayerstackDefinition(team: String, name: String, info: CollaborativeEventInfo)
      extends LayerstackDefinitionCommand with ReplyType[Done]

  case class GetLayerstackDefinition(team: String) extends LayerstackDefinitionCommand
      with ReplyType[LayerstackDefinitionResponse]

  case class SetLayerstackDefinitionPrice(
      team: String,
      name: String,
      price: Option[BigDecimal],
      info: CollaborativeEventInfo
  ) extends LayerstackDefinitionCommand with ReplyType[LayerstackDefinition]

  case class SetLayerstackDefinitionImage(image: FilePath, info: CollaborativeEventInfo)
      extends LayerstackDefinitionCommand with ReplyType[LayerstackDefinition]

  case class SetLayerstackDefinitionName(team: String, name: String, newName: String, info: CollaborativeEventInfo)
      extends LayerstackDefinitionCommand with ReplyType[LayerstackDefinition]

  case class SetSubStacks(team: String, name: String, subStacks: Seq[SubStackDefinition], info: CollaborativeEventInfo)
      extends LayerstackDefinitionCommand with ReplyType[LayerstackDefinition]

  case class AddSubStack(team: String, name: String, subStack: SubStackDefinition, info: CollaborativeEventInfo)
      extends LayerstackDefinitionCommand with ReplyType[LayerstackDefinition]

  case class RemoveSubStack(team: String, name: String, subStack: String, info: CollaborativeEventInfo)
      extends LayerstackDefinitionCommand with ReplyType[LayerstackDefinition]

  case class ChangeSubStack(
      team: String,
      name: String,
      subStackName: String,
      subStack: SubStackDefinition,
      info: CollaborativeEventInfo
  ) extends LayerstackDefinitionCommand with ReplyType[LayerstackDefinition]

  case class LayerstackDefinitionCreated(ls: LayerstackDefinition, info: CollaborativeEventInfo)
      extends LayerstackDefinitionEvent

  case class LayerstackDefinitionMetaChanged(
      ls: LayerstackDefinition,
      meta: MetaInfo,
      oldMeta: MetaInfo,
      info: CollaborativeEventInfo
  ) extends LayerstackDefinitionEvent

  case class LayerstackDefinitionNameChanged(ls: LayerstackDefinition, oldName: String, info: CollaborativeEventInfo)
      extends LayerstackDefinitionEvent

  case class LayerstackDefinitionImageChanged(ls: LayerstackDefinition, image: Option[FilePath])
      extends LayerstackDefinitionEvent

  case class LayerstackDefinitionChanged(updated: LayerstackDefinition, info: CollaborativeEventInfo)
      extends LayerstackDefinitionEvent

  case class LayerstackDefinitionDeleted(id: UUID, team: String, name: String, info: CollaborativeEventInfo)
      extends LayerstackDefinitionEvent

  case class SubLayerstackDefinitionChanged(
      team: String,
      name: String,
      subStackName: String,
      subStack: SubStackDefinition,
      stack: LayerstackDefinition,
      info: CollaborativeEventInfo
  ) extends LayerstackDefinitionEvent

  case class LayerstackDefinitionResponse(response: Option[LayerstackDefinition])

  object LayerstackDefinitionResponse {
    implicit val format: Format[LayerstackDefinitionResponse] = Json.format[LayerstackDefinitionResponse]
  }

  object CreateLayerstackDefinition {
    implicit val format: Format[CreateLayerstackDefinition] = Json.format[CreateLayerstackDefinition]
  }

  object DeleteLayerstackDefinition {
    implicit val format: Format[DeleteLayerstackDefinition] = Json.format[DeleteLayerstackDefinition]
  }

  object GetLayerstackDefinition {
    implicit val format: Format[GetLayerstackDefinition] = Json.format[GetLayerstackDefinition]
  }

  object SetLayerstackDefinitionPrice {
    implicit val format: Format[SetLayerstackDefinitionPrice] = Json.format[SetLayerstackDefinitionPrice]
  }

  object SetLayerstackDefinitionName {
    implicit val format: Format[SetLayerstackDefinitionName] = Json.format[SetLayerstackDefinitionName]
  }

  object SetLayerstackDefinitionImage {
    implicit val format: Format[SetLayerstackDefinitionImage] = Json.format[SetLayerstackDefinitionImage]
  }

  object SetSubStacks {
    implicit val format: Format[SetSubStacks] = Json.format[SetSubStacks]
  }

  object AddSubStack {
    implicit val format: Format[AddSubStack] = Json.format[AddSubStack]
  }

  object RemoveSubStack {
    implicit val format: Format[RemoveSubStack] = Json.format[RemoveSubStack]
  }

  object ChangeSubStack {
    implicit val format: Format[ChangeSubStack] = Json.format[ChangeSubStack]
  }

  object LayerstackDefinitionCreated {
    implicit val format: Format[LayerstackDefinitionCreated] = Json.format[LayerstackDefinitionCreated]
  }

  object LayerstackDefinitionNameChanged {
    implicit val format: Format[LayerstackDefinitionNameChanged] = Json.format[LayerstackDefinitionNameChanged]
  }

  object LayerstackDefinitionImageChanged {
    implicit val format: Format[LayerstackDefinitionImageChanged] = Json.format[LayerstackDefinitionImageChanged]
  }

  object LayerstackDefinitionChanged {
    implicit val format: Format[LayerstackDefinitionChanged] = Json.format[LayerstackDefinitionChanged]
  }

  object LayerstackDefinitionDeleted {
    implicit val format: Format[LayerstackDefinitionDeleted] = Json.format[LayerstackDefinitionDeleted]
  }

  object SubLayerstackDefinitionChanged {
    implicit val format: Format[SubLayerstackDefinitionChanged] = Json.format[SubLayerstackDefinitionChanged]
  }

  object SetLayerstackDefinitionMeta {
    implicit val format: Format[SetLayerstackDefinitionMeta] = Json.format[SetLayerstackDefinitionMeta]
  }

  object LayerstackDefinitionMetaChanged {
    implicit val format: Format[LayerstackDefinitionMetaChanged] = Json.format[LayerstackDefinitionMetaChanged]
  }

}
