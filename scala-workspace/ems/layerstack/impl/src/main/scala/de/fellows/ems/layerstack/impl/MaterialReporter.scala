package de.fellows.ems.layerstack.impl

import de.fellows.ems.layerstack.api

import scala.concurrent.{ExecutionContext, Future}

trait MaterialReporter {

  /** Get Material by ID
    */
  def getMaterial(team: String, id: String)(implicit exc: ExecutionContext): Future[Option[api.Material]]

  /** Get Material by ID using the index for performance
    */
  def getIndexedMaterial(team: String, id: String)(implicit
      exc: ExecutionContext
  ): Future[Option[api.Material]]

  /** Get All Materials
    */
  def getMaterials(
      team: String,
      page: Int,
      pagesize: Int,
      filter: Option[String],
      flat: Option[Boolean]
  )(implicit
      exc: ExecutionContext
  ): Future[Seq[api.Material]]
}
