package de.fellows.ems.layerstack.impl

import akka.Done
import akka.stream.IOResult
import akka.stream.scaladsl.{FileIO, Sink}
import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.typesafe.config.ConfigFactory
import de.fellows.app.security.SecurityBodyParser
import de.fellows.ems.layerstack.api
import de.fellows.ems.layerstack.api.{Library, MaterialProperties}
import de.fellows.ems.layerstack.impl.entities.definition.{
  GetLayerstackDefinition,
  LayerstackDefinitionEntity,
  SetLayerstackDefinitionImage
}
import de.fellows.ems.layerstack.impl.entities.library.{LibraryEntity, SetLibrary, SetMaterials}
import de.fellows.ems.layerstack.impl.entities.material.{CreateMaterial, MaterialEntity}
import de.fellows.ems.layerstack.impl.read.library.LibraryRepository
import de.fellows.ems.layerstack.impl.standard.StandardLayerstackComponentsRepository
import de.fellows.ems.layerstack.impl.utils.convert.ipc2581.IPC2581Reader
import de.fellows.ems.layerstack.impl.utils.convert.{CSVLibraryConverter, Converter, MLBXConverter}
import de.fellows.ems.renderer.api.LayerstackRendererRequest
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.CollaborativeEventInfo
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta._
import de.fellows.utils.playutils.FileHelper
import de.fellows.utils.security.{AuthenticationServiceComposition, FileTokenContent, GenericTokenContent}
import de.fellows.utils.{FilePath, ImageUtils, PathUtils}
import play.api.http.FileMimeTypes
import play.api.libs.Files.TemporaryFile
import play.api.libs.json.{JsString, Json}
import play.api.libs.streams.Accumulator
import play.api.mvc.MultipartFormData.FilePart
import play.api.mvc._
import play.api.routing.Router
import play.api.routing.sird._
import play.core.parsers.Multipart.{FileInfo, FilePartHandler}

import java.io.File
import java.nio.file.Path
import java.util.UUID
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success}

class LayerstackFileService(
    action: DefaultActionBuilder,
    parser: PlayBodyParsers,
    registry: PersistentEntityRegistry,
    libraries: LibraryRepository,
    mime: FileMimeTypes,
    layerstackAccess: LayerStackComponentReporter,
    exCtx: ExecutionContext
)(implicit sd: ServiceDefinition) extends StackrateLogging {
  implicit val mimes: FileMimeTypes = mime
  implicit val x: ExecutionContext  = exCtx

  lazy val conf              = ConfigFactory.load()
  private val filesize: Long = 2 * 1024 * **********

  private val path_base    = "images"
  private val service_base = "layerstack"

  private var app: LayerstackServiceApp = _

  val basePath: String =
    conf.getString("fellows.storage.service")

  def withApp(app: LayerstackServiceApp): LayerstackFileService = {
    this.app = app
    this
  }

  def handleValidFile(
      team: String,
      targetFile: FilePath,
      partName: String,
      filename: String,
      contentType: Option[String]
  ): Future[Accumulator[ByteString, FilePart[FilePath]]] = {
    targetFile.createParentDir()
    val sink: Sink[ByteString, Future[IOResult]] = FileIO.toPath(new File(targetFile.toPath).toPath)
    val acc: Accumulator[ByteString, IOResult]   = Accumulator(sink)
    Future.successful {
      acc.map {
        case akka.stream.IOResult(_, _) =>
          FilePart(partName, filename, contentType, targetFile)
      }
    }
  }

  def imageHandler(token: GenericTokenContent, team: String, layerstack: UUID): FilePartHandler[FilePath] = {
    case FileInfo(partName, fileName, contentType, s) =>
      contentType match {
        case _ =>
          Await.result(
            registry.refFor[LayerstackDefinitionEntity](layerstack.toString).ask(GetLayerstackDefinition(team))
              .map(_.response)
              .flatMap { ls =>
                if (ls.isEmpty) {
                  throw new TransportException(TransportErrorCode.NotFound, "Layerstack not found")
                }
                val path = FilePath(basePath, team, layerstack.toString, path_base, None, fileName)
                handleValidFile(token.getTeam, path, partName, fileName, contentType)
              },
            1 minute
          )
      }
  }

  def libFromMats(mats: Seq[api.Material], name: String): Library = {
    val matTypes = mats.flatMap(_.materialType).distinct
    val libType =
      if (matTypes.length == 1) {
        matTypes.headOption
      } else {
        None
      }

    val supplier = mats.flatMap(p =>
      p.meta.flatMap(_.properties.get(MaterialProperties.Supplier).map(_.asInstanceOf[StringProperty].value))
    ).distinct

    val libSupp =
      if (supplier.length == 1) {
        supplier.headOption
      } else {
        None
      }

    Library(
      team = None,
      name = name,
      id = None,
      supplier = libSupp,
      libraryType = libType,
      materials = None
    )
  }

  import de.fellows.utils.UUIDUtils._

  def convertCSV(team: String, libraryName: Option[String], supplier: Option[String]): Handler = {
    var tkncnt: Option[GenericTokenContent] = None

    action.async(
      SecurityBodyParser(token => s"library:${team}:${team}:*:*:write") { token =>
        tkncnt = Some(token)
        parser.multipartFormData(filesize)
      }
    ) {
      request =>
        val files = request.body.files
        val token = tkncnt.get

        _convert(
          tkncnt.get,
          team,
          f =>
            libraryName.getOrElse({
              val fname = f.filename
              if (fname.contains('.')) {
                fname.splitAt(fname.lastIndexOf('.'))._1
              } else {
                fname
              }
            }),
          files,
          token,
          p => new CSVLibraryConverter(p.toFile)
        )

    }
  }

  private def _convert(
      tokenContent: GenericTokenContent,
      team: String,
      libraryName: FilePart[TemporaryFile] => String,
      files: Seq[FilePart[TemporaryFile]],
      token: GenericTokenContent,
      converter: Path => Converter
  ): Future[Result] =
    Future.sequence(files.map { file =>
      val mats = converter(file.ref.toPath).convert()

      val lib = libFromMats(mats, libraryName(file))

      libraries.getLibraryId(team, lib.name).flatMap {
        case Some(_) => Future.successful(None)
        case None =>
          val libraryId = UUID.randomUUID()
          val newLib = lib.copy(
            team = Some(team),
            id = Some(libraryId)
          )

          val libraryEntity = registry.refFor[LibraryEntity](libraryId.toString)
          for {
            lib <- libraryEntity.ask(SetLibrary(newLib, info = CollaborativeEventInfo(tokenContent)))
            mat <- Future.sequence(mats.map { mat =>
              val id = UUID.randomUUID().short()
              registry.refFor[MaterialEntity](id).ask(CreateMaterial(
                mat.copy(
                  team = Some(team),
                  id = Some(id)
                ),
                CollaborativeEventInfo(token)
              ))
            })

            add <- libraryEntity.ask(SetMaterials(libraryId, mat.map(_.id.get), info = CollaborativeEventInfo(token)))
          } yield {
            file.ref.delete()
            Some(lib)
          }

      }

    }).map(_.flatten).map(libs => Results.Ok(Json.stringify(Json.toJson(libs))))

  def convertMLBX(team: String): Handler = {
    var tkncnt: Option[GenericTokenContent] = None

    action.async(
      SecurityBodyParser(token => s"library:${team}:${team}:*:*:write") { token =>
        tkncnt = Some(token)
        parser.multipartFormData(filesize)
      }
    ) {

      request =>
        val files = request.body.files
        val token = tkncnt.get

        val libraryName: FilePart[TemporaryFile] => String = { file =>
          if (file.key != null && file.key.trim != "") {
            file.key
          } else {
            file.filename.substring(0, file.filename.lastIndexOf('.')).replace("_", " ")
          }
        }

        _convert(tkncnt.get, team, libraryName, files, token, p => new MLBXConverter(p))
    }
  }

  def convertIPC2581(team: String): Handler = {
    var tkncnt: Option[GenericTokenContent] = None

    action.async(
      SecurityBodyParser(token => s"layerstackdefinition:${team}:${team}:*:*:write") { token =>
        tkncnt = Some(token)
        parser.multipartFormData(filesize)
      }
    ) {

      request =>
        val files = request.body.files
        val token = tkncnt.get

        val defs = files.map { f =>
          val name =
            if (f.key != null && f.key.trim != "") {
              f.key
            } else {
              val i = f.filename.lastIndexOf(".")
              if (i > 0) {
                f.filename.substring(0, i)
              } else {
                f.filename
              }
            }
          new IPC2581Reader(name).convert(f.ref.toPath)
        }

        Future.sequence(defs.map { d =>
          LayerstackServiceImpl.createLayerstackDef(token, team, d.id, d, layerstackAccess, registry)
        }).map { mi =>
          Results.Ok(Json.stringify(Json.toJson(mi)))
        }.recover {
          case e => Results.BadRequest(e.getMessage)
        }

      // .getOrElse(Future.successful(Results.BadRequest("Failed to convert stack")))
    }
  }

  def uploadLayerstackImage(layerstack: String): Handler = {
    val custId                              = UUID.fromString(layerstack)
    var tkncnt: Option[GenericTokenContent] = None

    action(
      SecurityBodyParser(token =>
        s"$layerstack:${
            token.getTeam
          }:${
            token.getTeam
          }:${
            layerstack
          }:image:write"
      ) { token =>
        tkncnt = Some(token)
        parser.multipartFormData(imageHandler(token, token.getTeam, custId), filesize)
      }
    ) {

      request =>
        val files = request.body.files
        //        val files = request.body.files.map(_.contentType)

        val entity = registry.refFor[LayerstackDefinitionEntity](layerstack)
        val res = Future.sequence(files.map(f =>
          entity.ask(SetLayerstackDefinitionImage(f.ref, CollaborativeEventInfo(tkncnt.get)))
        ))
        res.map(_ => Done)

        val j = Json.obj(
          "file" -> JsString(files.head.ref.toApi)
        )
        Results.Ok(Json.stringify(j))
    }
  }

  def downloadLayerstackDefinitionImage(customer: String, relPath: String): Handler = {
    val resource = s"${customer}"
    download(resource, relPath)
  }

  def downloadStandardLayerstackImage(team: String, layerstack: String): Handler =
    action.async(parser.anyContent) {
      request =>
        StandardLayerstackComponentsRepository.getLayerstackByNameOrId(team, layerstack, layerstackAccess)
          .flatMap {
            case Failure(exception) => Future.successful(Results.NotFound(s"Layerstack not found"))
            case Success(layerstackDefinition) =>
              val path = StandardLayerstackComponentsRepository.getLayerstackImage(layerstackDefinition)

              path.findExisting() match {
                case Some(value) => Future.successful(FileHelper.deliverFile(value.toFile, layerstack, request.headers))
                case None =>
                  this.app.renderer._renderLayerstack().invoke(LayerstackRendererRequest(
                    layerstackDefinition,
                    path
                  )).map { f =>
                    FileHelper.deliverFile(f.findExisting().get.toFile, layerstack, request.headers)
                  }
              }
          }
    }

  def download(reqResource: String, relPath: String): Handler =
    action(parser.anyContent) {
      request =>
        request.queryString.get("k") match {
          case Some(k) if k.length == 1 =>
            AuthenticationServiceComposition.decodeFileToken(k.head) match {
              case Success(FileTokenContent(team, claims, true)) =>
                val original = FilePath(basePath, team, reqResource, s"$path_base", None, relPath)

                val q = request.queryString.get("quality") match {
                  case Some(q) =>
                    if (
                      PathUtils.probeContentType(original.toJavaPath).toOption.flatten.getOrElse("").contains("svg")
                    ) {
                      ""
                    } else {
                      q.head
                    }
                  case None => ""
                }
                val fp = FilePath(basePath, team, reqResource, s"$path_base/$q", None, relPath)

                if (
                  claims.exists(c =>
                    Seq(service_base).contains(c.service)
                      && original.matchesApiPath(c.path)
                      && original.matchesResource(c.resource)
                  )
                  && team == original.team
                ) {

                  val file = fp.toJavaPath.toFile

                  if (!file.exists() || original.toJavaFile.lastModified() > file.lastModified()) {
                    ImageUtils.createImage(fp, original, relPath, q)
                  }

                  FileHelper.deliverFile(file, relPath, request.headers)

                } else {
                  Results.Forbidden(s"Permission Denied")
                }

              case _ => Results.Forbidden(s"Permission Denied")
            }

          case _ => Results.Forbidden("Invalid Key")
        }
    }

  // @formatter:off
  def router: Router = {
    println("create route")
    Router.from {
      case POST(p"/files/ems/layerstack/teams/$team/libraries/convert/csv" ? q_o"library=$library" & q_o"supplier=$supplier") =>
        convertCSV(team, library, supplier)

      case POST(p"/files/ems/layerstack/teams/$team/libraries/convert/mlbx") =>
        convertMLBX(team)

      case POST(p"/files/ems/layerstack/teams/$team/definitions/ipc2581") =>
        convertIPC2581(team)

      case POST(p"/files/ems/layerstack/teams/$team/layerstacks/$layerstack/images") =>
        uploadLayerstackImage(layerstack)

      case GET(p"/files/ems/layerstack/teams/$team/layerstacks/$layerstack/images/$path") =>
        downloadLayerstackDefinitionImage(layerstack, path)

      case GET(p"/files/ems/layerstack/teams/$team/layerstacks/$layerstack/standardimage/preview.svg") =>
        downloadStandardLayerstackImage(team, layerstack)

    }
  }

  // @formatter:on
}
