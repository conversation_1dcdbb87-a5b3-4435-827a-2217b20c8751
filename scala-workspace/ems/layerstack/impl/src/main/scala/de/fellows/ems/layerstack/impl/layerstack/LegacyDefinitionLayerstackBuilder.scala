package de.fellows.ems.layerstack.impl.layerstack

import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assemby.api.Assembly
import de.fellows.ems.layerstack.api
import de.fellows.ems.layerstack.api.sequence.dsl._
import de.fellows.ems.layerstack.api.sequence.{LayerStackNode, LayerstackSequence, MaterialSequence, SequenceFiles, SequenceInserter, SequencedLayerstack, SequencedLayerstackDefinition}
import de.fellows.ems.layerstack.api.{LayerstackDefinition, MaterialProperties, MaterialTypes, Material => ApiMaterial}
import de.fellows.ems.layerstack.impl.entities.material.{GetMaterial, MaterialEntity}
import de.fellows.ems.pcb.model.{GerberFile, LayerConstants, PCBVersion}
import de.fellows.utils.FutureUtils
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta.{MetaInfo, Property}

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

/** Creates a layerstack using a given layerstack definition for use in a specific pcb
  *
  * @param definition
  * @param pcb
  * @param assembly
  * @param ec
  */
class LegacyDefinitionLayerstackBuilder(
    definition: LayerstackDefinition,
    pcb: PCBVersion,
    assembly: Assembly,
    ereg: PersistentEntityRegistry
)(implicit ec: ExecutionContext) extends StackrateLogging {

  def createTopSequence(definition: LayerstackDefinition): Future[LayerstackSequence] = {
    val topdef = definition.stacks.getOrElse(Seq()).head
    createSequence(definition.team.get, topdef)
  }

  def createChildren(definition: LayerstackDefinition): Future[Seq[SequencedLayerstackDefinition]] =
    Future.sequence(definition.stacks.getOrElse(Seq()).tail
      .map(ss => createSequence(definition.team.get, ss)))
      .map(_.map(s =>
        SequencedLayerstackDefinition(
          team = definition.team.get,
          id = UUID.randomUUID(),
          name = definition.name,
          topSequence = s,
          children = Seq(),
          sequenceLinks = Seq(),
          preview = None,
          metaInfo = MetaInfo()
        )
      ))

  private def createSequence(team: String, topdef: api.SubStackDefinition): Future[LayerstackSequence] =
    Future.sequence(topdef.layers.getOrElse(Seq()).zipWithIndex.map { ld =>
      FutureUtils.option(ld._1.materialRef.map(getMaterial)).map(_.flatten)
        .map { matop =>
          val m = (ld._1.material.flatMap(_.meta).getOrElse(MetaInfo()) ++ matop.flatMap(_.meta).getOrElse(
            MetaInfo()
          ) ++ ld._1.meta.getOrElse(MetaInfo()))

          val mat =
            Material(MaterialDescription(ld._1.materialRef.getOrElse("unknown"))) named s"Layer ${ld._2 + 1}" layer
              ld._1.layerType.getOrElse(MaterialTypes.UNKNOWN) meta m

          m.get[Property](MaterialProperties.Description) match {
            case Some(value) => mat described value.getValue.toString
            case None        => mat
          }
        }
    })
      .map { layers =>
        Sequence(
          layers: _*
        ) joined Processed
      }

  def getMaterial(id: String): Future[Option[ApiMaterial]] =
    ereg.refFor[MaterialEntity](id)
      .ask(GetMaterial(id))
      .map(m => Some(m))
      .recover(x => None)

  def insertDrills(ls: SequencedLayerstack, files: Seq[GerberFile]): Future[SequencedLayerstack] = {
    val drillfiles = files.filter(f => LayerConstants.DRILLS.contains(f.fType.fileType))

    var s        = ls
    val fbuilder = Seq.newBuilder[SequenceFiles]
    drillfiles.foreach { gf =>
      try {
        val node = Sequence() named gf.name joined (Machined meta "type" is gf.fType.fileType as "drill")
        val id   = node.internalId
        s = new SequenceInserter(node).byIndex(gf.fType.from, gf.fType.to).process(s).asInstanceOf[SequencedLayerstack]
        fbuilder.addOne(SequenceFiles(id.get, Seq(gf.id)))
      } catch {
        case e: Throwable =>
          logger.error(e.getMessage, e)
          Future.successful(ls)
      }
    }

    Future.successful(s.copy(
      files = s.files ++ fbuilder.result()
    ))

  }

  def build(): Future[SequencedLayerstack] =
    (for {
      // create the file-to-sequence links
      //      files <- matchFiles()

      top      <- createTopSequence(definition)
      children <- createChildren(definition)
    } yield {
      val ls = SequencedLayerstack(
        team = definition.team.get,
        id = UUID.randomUUID(),
        name = definition.name,
        topSequence = top,
        children = children,
        sequenceLinks = Seq(),
        metaInfo = definition.metaInfo,
        files = Seq(),
        preview = None,
        definition = Some(definition.id)
      )

      for {
        files     <- LayerstackBuilder.matchFiles(ls.iterator(), ereg, pcb.files)
        processed <- new MaterialSeqMetaResolver(ereg).process(ls)
        processed <- insertDrills(processed, pcb.files)
      } yield processed.copy(files = processed.files ++ files)
    }).flatten

}

/** Adds Material Meta info to a layerstackdefinition
  */
class MaterialSeqMetaResolver(ereg: PersistentEntityRegistry) {
  def process(d: SequencedLayerstack)(implicit ec: ExecutionContext): Future[SequencedLayerstack] =
    for {
      ts <-
        d.topSequence.asyncTransform {
          case x: MaterialSequence => LayerstackBuilder.resolveMaterial(x, ereg).map(_.getOrElse(x))
          case x                   => Future.successful(x)
        }
      ch <-
        Future.sequence(d.children.map(_.asyncTransform {
          case x: MaterialSequence =>
            LayerstackBuilder.resolveMaterial(x, ereg)
          case x: LayerStackNode => Future.successful(None)
        }))
    } yield d.copy(
      topSequence = ts,
      children = ch
    )
}
