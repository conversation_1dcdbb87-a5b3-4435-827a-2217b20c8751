package de.fellows.ems.layerstack.impl

import akka.Done
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.{
  AssemblyLifecycleStageName,
  AssemblyService,
  AssemblySharedMessage,
  AssemblyUtils,
  FileMatchingMessage,
  SharedAssembly,
  SharedAssemblyInfo
}
import de.fellows.ems.layerstack.api
import de.fellows.ems.pcb.api.PCBService
import de.fellows.utils.TopicUtils
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.streams.ValidMessage
import de.fellows.utils.telemetry.KamonUtils

import scala.concurrent.{ExecutionContext, Future}

class AssemblyListener(assemblyService: AssemblyService, pcbService: PCBService)(implicit exc: ExecutionContext)
    extends StackrateLogging {
  val started = System.currentTimeMillis()

  var app: LayerstackServiceApp = _

  def withApp(app: LayerstackServiceApp) = {
    this.app = app
    this
  }

  TopicUtils.subscribeLatest(assemblyService.assemblyTopic(), started, 1) { e =>
    e.payload match {
      case ValidMessage(FileMatchingMessage(assRef, files, locked, lifecycles)) if locked =>
        KamonUtils.safe {
          unsafeMatch(assRef).map(_ => Done)
        }.map(_ => Done)
    }
  }

  TopicUtils.subscribeLatest(assemblyService.shareTopic(), started, 1) { e =>
    e.payload match {
      case ValidMessage(AssemblySharedMessage(sharedAssembly)) =>
        KamonUtils.safe {
          logger.info(s"match layerstack for shared assembly ${sharedAssembly.id}")
          unsafeMatchShare(sharedAssembly)
        }.map(_ => Done)

    }

  }

  private def unsafeMatchShare(sharedAssembly: SharedAssembly) =
    // TODO share lifecycle
    (for {
      assembly <- assemblyService._getAssembly(sharedAssembly.ref.team, sharedAssembly.ref.id).invoke()
      version <- assemblyService._getVersion(
        sharedAssembly.ref.team,
        sharedAssembly.ref.id,
        sharedAssembly.ref.version
      ).invoke()
      pcbv <- pcbService._getPCBVersion(
        sharedAssembly.ref.team,
        sharedAssembly.ref.id,
        sharedAssembly.ref.version
      ).invoke()
    } yield app.srv._doSelectLayerstackForShare(
      SharedAssemblyInfo(
        sharedAssembly.team,
        sharedAssembly.id,
        sharedAssembly.ref.version,
        sharedAssembly.created
      ),
      assembly.assembly,
      version,
      pcbv,
      TimelineCommand.system
    )).flatten

  private def unsafeMatch(assRef: AssemblyReference): Future[api.LayerStacks] =
    app.srv.doMatchLayerstack(assRef.team, assRef.id, assRef.version, identity, TimelineCommand.system, true)
}
