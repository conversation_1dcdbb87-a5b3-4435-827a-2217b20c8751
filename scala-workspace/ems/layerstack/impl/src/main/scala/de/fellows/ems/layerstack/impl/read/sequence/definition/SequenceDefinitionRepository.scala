package de.fellows.ems.layerstack.impl.read.sequence.definition

import akka.stream.Materializer
import akka.stream.scaladsl.Sink
import com.datastax.driver.core.SimpleStatement
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import de.fellows.utils.communication.ServiceDefinition
import play.api.Logging

import java.util.UUID
import scala.concurrent.{ ExecutionContext, Future }

class SequenceDefinitionRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) extends Logging {

  def getStacksByCopperCount(team: String, copper: Int): Future[Seq[UUID]] =
    session.selectAll(
      // language=SQL
      "SELECT * FROM sequenceDefinitionsByCopperCount WHERE team = ? and count = ?",
      team,
      copper.asInstanceOf[Integer]
    )
      .map(rws => rws.map(_.getUUID("id")))

  def getStackIDByName(team: String, name: String): Future[Option[(UUID, String)]] =
    session.selectOne("SELECT * FROM sequencedefinitions WHERE team = ? AND name = ?", team, name)
      .map(_.map(r => (r.getUUID("id"), r.getString("name"))))

  def getAllStackIDs(team: String, page: Option[Int], pagesize: Option[Int]): Future[Seq[(UUID, String)]] = {
    val ps = pagesize.getOrElse(100)
    val p  = page.getOrElse(1) - 1

    val statement = new SimpleStatement(s"SELECT * FROM sequencedefinitions WHERE team = ? LIMIT ?", team, ps * (p + 1))
    statement.setFetchSize(ps)

    val source = session.select(
      statement
    )

    source.drop(p * ps).map(r => (r.getUUID("id"), r.getString("name"))).runWith(Sink.seq)
  }

}
