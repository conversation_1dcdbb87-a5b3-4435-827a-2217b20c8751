// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.ems.layerstack.impl

import akka.actor.ActorSystem
import akka.persistence.query.Offset
import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.datastax.driver.core.utils.UUIDs
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{ResponseHeader, TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.InvalidCommandException
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRef, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.assembly.commons.{AssemblyReference, SharedAssemblyReference}
import de.fellows.app.assemby.api.{
  Assembly,
  AssemblyLifecycleStageName,
  AssemblyService,
  AssemblyUtils,
  SharedAssemblyInfo,
  Version
}
import de.fellows.app.security.AccessControlServiceComposition.{authorizedString, authorizedStringWithToken}
import de.fellows.app.security.CombinedTokenAccessServiceComposition.auth
import de.fellows.app.user.api.TemplateAPI.RenderRequest
import de.fellows.app.user.api.UserService
import de.fellows.ems.layerstack.api
import de.fellows.ems.layerstack.api.Streams.{
  LayerStackChangedMessage,
  LayerStackDefinitionChanged,
  LayerStackMessage,
  LayerStackSelected,
  LayerstackStreamMessage
}
import de.fellows.ems.layerstack.api._
import de.fellows.ems.layerstack.api.sequence.{
  AllocateUUIDs,
  MaterialSequence,
  SequencedLayerstack,
  SequencedLayerstackAPI,
  SequencedLayerstackDefinition,
  SequencedLayerstackDefinitionAPI
}
import de.fellows.ems.layerstack.impl.entities.definition._
import de.fellows.ems.layerstack.impl.entities.library._
import de.fellows.ems.layerstack.impl.entities.material.{
  CreateMaterial,
  DeleteMaterial,
  GetMaterial,
  MaterialEntity,
  SetMaterial
}
import de.fellows.ems.layerstack.impl.entities.pcb._
import de.fellows.ems.layerstack.impl.entities.sequences
import de.fellows.ems.layerstack.impl.entities.sequences.{
  GetLayerstackSequenceDefinition,
  LayerstackSequenceDefinitionEntity,
  SetLayerstackSequenceDefinition,
  SetSequenceMaterial,
  SetSequenceMeta
}
import de.fellows.ems.layerstack.impl.layerstack.LayerStackMatcher
import de.fellows.ems.layerstack.impl.read.layerstack.LayerstackRepository
import de.fellows.ems.layerstack.impl.read.library.LibraryRepository
import de.fellows.ems.layerstack.impl.read.material.MaterialRepository
import de.fellows.ems.layerstack.impl.read.sequence.definition.SequenceDefinitionRepository
import de.fellows.ems.layerstack.impl.standard.StandardLayerstackComponentsRepository
import de.fellows.ems.layerstack.impl.utils.RequestFilter
import de.fellows.ems.layerstack.impl.utils.convert.ipc2581.IPC258Writer
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.model.DFM.Properties.DFM
import de.fellows.ems.pcb.model.{Density, PCBVersion}
import de.fellows.utils.FutureUtils._
import de.fellows.utils.UUIDUtils._
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.collaboration.{TimelineCommand, TimelineEvent, TimelineUser}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.CollaborativeEventInfo
import de.fellows.utils.internal.FileReader.withResource
import de.fellows.utils.meta._
import de.fellows.utils.security.{Auth0Token, AuthenticationServiceComposition, GenericTokenContent, TokenContent}
import de.fellows.utils.{FilePath, FutureUtils, UUIDUtils}
import org.apache.commons.compress.archivers.zip.{ZipArchiveEntry, ZipArchiveOutputStream}
import play.api.Logging
import play.api.libs.json.Json
import play.mvc.Http.HeaderNames

import java.io.ByteArrayOutputStream
import java.nio.file.{Files, Paths, StandardCopyOption}
import java.time.Instant
import java.util.UUID
import scala.collection.{immutable, mutable}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.xml.PrettyPrinter

class LayerstackServiceImpl(
    ereg: PersistentEntityRegistry,
    sdefs: SequenceDefinitionRepository,
    materials: MaterialRepository,
    libraries: LibraryRepository,
    stacks: LayerstackRepository,
    layerstackAccess: LayerStackComponentReporter,
    user: UserService,
    pcb: PCBService,
    ass: AssemblyService,
    system: ActorSystem
)(implicit ec: ExecutionContext, sd: ServiceDefinition) extends LayerstackService with Logging
    with StackrateAPIImpl {
  implicit val actorSystem: ActorSystem = system

  val fsBasePath: String =
    config.getString("fellows.storage.base")
  val fsServicePath: String =
    config.getString("fellows.storage.service")

  def _getMaterial(mats: Seq[String]): Future[Seq[Material]] =
    Future.sequence(mats.map(s => ereg.refFor[MaterialEntity](s).ask(GetMaterial(s))))

  override def getMaterials(
      page: Option[Int],
      pagesize: Option[Int],
      filter: Option[String],
      flat: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[api.Material]] =
    authorizedString(token =>
      s"material:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        val ps = pagesize.getOrElse(100)
        val p  = page.getOrElse(1) - 1

        layerstackAccess.getMaterials(token.team, p, ps, filter, flat)

      }
    }

  override def createMaterial(library: Option[String]): ServiceCall[Seq[api.Material], Seq[api.Material]] =
    authorizedString(token =>
      s"material:${token.team}:${token.team}:*:*:write"
    ) { (token, _) =>
      ServerServiceCall { mats =>
        Future.sequence(mats.map { mat =>
          val id = UUID.randomUUID().short()
          ereg.refFor[MaterialEntity](id).ask(CreateMaterial(
            mat.copy(
              team = Some(token.team),
              id = Some(id)
            ),
            CollaborativeEventInfo(token)
          ))
        }).map { mats =>
          library.map { lib =>
            (UUIDUtils.fromString(lib) match {
              case Some(id) => Future.successful(Some(id))
              case None     => libraries.getLibraryId(token.team, lib)
            }).flatMap {
              case Some(lid) => _addMaterialToLib(token, lid, mats).map(_ => mats)
              case None      => Future.successful(mats)
            }
          } match {
            case _ => mats
          }
        }
      }
    }

  override def deleteMaterial(id: String): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"material:${token.team}:${token.team}:$id:*:delete"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _deleteMaterial(id, token)
      }
    }

  val DEFAULT_PAGE_SIZE = 100

  private def _deleteMaterial(id: String, token: TokenContent) = {
    val matEntity = ereg.refFor[MaterialEntity](id)
    (for {
      libs <- libraries.getLibraryIDs(token.team, 0, DEFAULT_PAGE_SIZE, None).flatMap(ids =>
        Future.sequence(ids.map { id =>
          ereg.refFor[LibraryEntity](id.toString).ask(GetLibrary(id))
        })
      )
      _ <- Future.sequence(libs.map { lib =>
        val mats = lib.materials.toSeq.flatten
        if (mats.contains(id)) {
          val libEntity = ereg.refFor[LibraryEntity](lib.id.get.toString)
          libEntity.ask(SetMaterials(lib.id.get, mats.filter(_ != id), info = CollaborativeEventInfo(token)))
            .map(_ => Done)
        } else {
          Future.successful(Done)
        }
      })
      _ <- matEntity.ask(DeleteMaterial(id, CollaborativeEventInfo(token)))
    } yield Done).recover {
      case e: InvalidCommandException =>
        throw new TransportException(TransportErrorCode.NotFound, e)
    }
  }

  override def getMaterial(id: String): ServiceCall[NotUsed, api.Material] =
    authorizedString(token =>
      s"material:${token.team}:${token.team}:$id:*:write"
    ) { (token, _) =>
      ServerServiceCall { mat =>
        layerstackAccess.getMaterial(token.team, id).map(
          _.getOrElse(throw new TransportException(
            TransportErrorCode.NotFound,
            "Material not found"
          ))
        )
          .recover {
            case e: InvalidCommandException =>
              throw new TransportException(TransportErrorCode.NotFound, e)
          }

      }
    }

  override def updateMaterial(id: String): ServiceCall[api.Material, api.Material] =
    authorizedString(token =>
      s"material:${token.team}:${token.team}:$id:*:write"
    ) { (token, _) =>
      ServerServiceCall { mat =>
        ereg.refFor[MaterialEntity](id).ask(SetMaterial(mat, CollaborativeEventInfo(token)))
          .recover {
            case e: InvalidCommandException =>
              throw new TransportException(TransportErrorCode.NotFound, "Material not found")
          }
      }
    }

  override def getLibraries(
      page: Option[Int],
      pagesize: Option[Int],
      filter: Option[String],
      flat: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[api.Library]] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { mat =>
        val ps = pagesize.getOrElse(100)
        val p  = page.getOrElse(1) - 1

        layerstackAccess.getLibraries(token.team, p, ps, filter, flat)

      }
    }

  override def getLibraryTypes: ServiceCall[NotUsed, Seq[(String, Int)]] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { lib =>
        layerstackAccess.getLibraryTypes(token.team)

      }
    }

  override def getMaterialTypes: ServiceCall[NotUsed, Seq[(String, Int)]] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { lib =>
        layerstackAccess.getMaterialTypes(token.team)
      }
    }

  override def getLibraryCount: ServiceCall[NotUsed, BigDecimal] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { lib =>
        layerstackAccess.countLibraries(token.team)

      }
    }

  override def getMaterialCount(`type`: Option[String]): ServiceCall[NotUsed, Int] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { lib =>
        layerstackAccess.countMaterials(token.team, `type`)

      }
    }

  override def getMaterialCountProperty(property: String): ServiceCall[NotUsed, Seq[(String, Int)]] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { lib =>
        layerstackAccess.countMaterialsByProperty(token.team, property)

      }
    }

  override def getLibrarySuppliers: ServiceCall[NotUsed, Seq[(String, Int)]] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { lib =>
        layerstackAccess.getLibrarySuppliers(token.team)

      }
    }

  override def getMaterialSuppliers: ServiceCall[NotUsed, Seq[(String, Int)]] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { lib =>
        layerstackAccess.getMaterialSuppliers(token.team)

      }
    }

  override def createLibrary: ServiceCall[api.Library, api.Library] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:*:*:write"
    ) { (token, _) =>
      ServerServiceCall { lib =>
        val name = lib.name

        layerstackAccess.getLibraryId(token.team, name).flatMap {
          case Some(_) => throw new TransportException(TransportErrorCode.PolicyViolation, "Library exists")
          case None =>
            val id = UUID.randomUUID()
            ereg.refFor[LibraryEntity](id.toString).ask(SetLibrary(
              lib.copy(
                team = Some(token.team),
                id = Some(id)
              ),
              info = CollaborativeEventInfo(token)
            ))
        }
      }
    }

  override def getLibrary(name: String): ServiceCall[NotUsed, api.Library] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:${name}:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        layerstackAccess.getLibraryId(token.team, name)
          .flatMap { uuid =>
            layerstackAccess.getLibrary(
              token.team,
              uuid.getOrElse(
                throw new TransportException(TransportErrorCode.NotFound, s"Library '${name}' not found")
              )
            )
          }

      }
    }

  def isAllowedInLibrary(lib: api.Library, material: Material): Boolean = {
    val t1 = lib.libraryType.map(_.toLowerCase)
    val t2 = material.materialType.map(_.toLowerCase)
    val r  = lib.libraryType.isEmpty || t1 == t2
    logger.info(s"check material ${material} in lib ${lib.copy(materials = None)}:  ${t1} == ${t2} $r")
    r
  }

  override def updateLibrary(name: String): ServiceCall[api.Library, api.Library] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:${name}:*:read"
    ) { (token, _) =>
      ServerServiceCall { lib =>
        libraries.getLibraryId(token.team, name).flatMap {
          case Some(id) =>
            _getLibraryMaterials(id.toString, token.team, None).flatMap { mats =>
              if (!mats.forall(isAllowedInLibrary(lib, _))) {
                throw new TransportException(
                  TransportErrorCode.PolicyViolation,
                  "Library contains Materials that are not compatible with this type"
                )
              } else {
                ereg.refFor[LibraryEntity](id.toString).ask(SetLibrary(
                  lib.copy(id = Some(id)),
                  info = CollaborativeEventInfo(token)
                ))
              }

            }

          case None => throw new TransportException(TransportErrorCode.NotFound, "Library does not exists")
        }
      }
    }

  override def deleteLibrary(name: String): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:${name}:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        libraries.getLibraryId(token.team, name).flatMap {
          case Some(id) =>
            for {
              materials <- _getLibraryMaterials(name, token.team, None).flatMap { materials =>
                Future.sequence(materials.map { mat =>
                  mat.id match {
                    case Some(id) => _deleteMaterial(id, token)
                    case _        => Future.successful(Done)
                  }
                })
              }
              library <- ereg.refFor[LibraryEntity](id.toString).ask(DeleteLibrary(
                id = id,
                info = CollaborativeEventInfo(token)
              ))
            } yield library

          case None => throw new TransportException(TransportErrorCode.NotFound, "Library does not exists")
        }
      }
    }

  override def setLibraryMaterials(name: String): ServiceCall[Seq[String], api.Library] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:${name}:materials:write"
    ) { (token, _) =>
      ServerServiceCall { mats =>
        libraries.getLibraryId(token.team, name).flatMap {
          case Some(id) =>
            val libe = ereg.refFor[LibraryEntity](id.toString)

            libe.ask(GetLibrary(id)).flatMap { lib =>
              this._getMaterial(mats).flatMap { mats =>
                if (!mats.forall(this.isAllowedInLibrary(lib, _))) {
                  throw new TransportException(
                    TransportErrorCode.PolicyViolation,
                    "Some Materials are not allowed for this library type"
                  )
                } else {
                  libe.ask(SetMaterials(id, mats.toSeq.map(_.name.get), info = CollaborativeEventInfo(token)))
                }
              }
            }
              .recover {
                case e: InvalidCommandException => throw new TransportException(TransportErrorCode.NotFound, e)
              }

          case None => throw new TransportException(TransportErrorCode.NotFound, "Library does not exists")
        }
      }
    }

  override def getLibraryMaterials(
      name: String,
      flat: Option[Boolean],
      filter: Option[String]
  ): ServiceCall[NotUsed, Seq[Material]] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:${name}:materials:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _getLibraryMaterials(name, token.team, filter)
      }
    }

  private def _getLibraryMaterials(
      libraryName: String,
      team: String,
      filter: Option[String]
  ): Future[Seq[Material]] =
    layerstackAccess.getLibraryId(team, libraryName).flatMap {
      case Some(libraryId) =>
        layerstackAccess.getLibrary(team, libraryId).flatMap { lib =>
          (lib.materials.map(s =>
            s.map { matId =>
              val mats = layerstackAccess.getIndexedMaterial(team, matId)

              mats.map {
                case Some(mat) =>
                  if (MaterialRepository.materialFilter(mat, filter.map(RequestFilter.apply))) {
                    Some(mat)
                  } else {
                    None
                  }
                case None => None
              }

            }
          ).map(s => Future.sequence(s.toSeq)) match {
            case Some(f) => f.map(Some(_))
            case None    => Future.successful(None)
          }).map {
            case Some(x) => x.flatten
            case None    => Seq()
          }
        }

      case None => throw new TransportException(TransportErrorCode.NotFound, "Library does not exists")
    }

  override def addMaterialToLib(name: String, material: String): ServiceCall[NotUsed, api.Library] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:${name}:materials:write"
    ) { (token, _) =>
      ServerServiceCall { mats =>
        _addMaterialToLib(name, token, material)
      }
    }

  private def _addMaterialToLib(libraryName: String, token: TokenContent, material: String): Future[Library] =
    libraries.getLibraryId(token.team, libraryName).flatMap {
      case Some(libraryId) =>
        materials.getMaterial(token.team, material).flatMap {
          case None    => throw new TransportException(TransportErrorCode.NotFound, "Material not found")
          case Some(x) => _addMaterialToLib(token, libraryId, Seq(x))
        }

      case None => throw new TransportException(TransportErrorCode.NotFound, "Library does not exists")
    }

  private def _addMaterialToLib(token: TokenContent, libraryId: UUID, x: Seq[Material]): Future[Library] = {
    val libEntity = ereg.refFor[LibraryEntity](libraryId.toString)
    libEntity.ask(GetLibrary(libraryId)).flatMap { lib =>
      val mats = lib.materials.getOrElse(Seq())
      if (x.forall(m => isAllowedInLibrary(lib, m))) {
        if (x.exists(m => mats.contains(m.id.get))) {
          throw new TransportException(TransportErrorCode.PolicyViolation, "Material is already part of this library")
        } else {
          libEntity.ask(SetMaterials(libraryId, mats.toSeq ++ x.map(_.id.get), info = CollaborativeEventInfo(token)))
        }
      } else {
        throw new TransportException(TransportErrorCode.PolicyViolation, "Materialtype is not allowed in this Library")
      }
    }
  }

  override def removeMaterialFromLib(
      name: String,
      material: String,
      delete: Option[Boolean]
  ): ServiceCall[NotUsed, api.Library] =
    authorizedString(token =>
      s"library:${token.team}:${token.team}:${name}:materials:write"
    ) { (token, _) =>
      ServerServiceCall { mats =>
        libraries.getLibraryId(token.team, name).flatMap {
          case Some(libraryId) =>
            materials.getMaterial(token.team, material).flatMap {
              case None => throw new TransportException(TransportErrorCode.NotFound, "Material not found")
              case Some(x) =>
                val libEntity = ereg.refFor[LibraryEntity](libraryId.toString)
                val matEntity = ereg.refFor[MaterialEntity](x.id.get)
                for {
                  lib <- libEntity.ask(GetLibrary(libraryId)).flatMap { lib =>
                    val mats = lib.materials.getOrElse(Seq())
                    if (!mats.contains(material)) {
                      throw new TransportException(
                        TransportErrorCode.PolicyViolation,
                        "Material is not part of this library"
                      )
                    } else {
                      libEntity.ask(SetMaterials(
                        libraryId,
                        mats.toSeq.filter(_ != material),
                        info = CollaborativeEventInfo(token)
                      ))
                    }
                  }

                  _ <-
                    (if (delete.getOrElse(true)) {
                       matEntity.ask(DeleteMaterial(x.id.get, CollaborativeEventInfo(token)))
                     } else {
                       Future.successful(Done)
                     })
                } yield lib
            }

          case None => throw new TransportException(TransportErrorCode.NotFound, "Library does not exists")
        }
      }
    }

  def resolveMaterials(team: String, ls: SubStackDefinition, resolve: Option[Boolean]): Future[SubStackDefinition] =
    resolve match {
      case None | Some(false) => Future.successful(ls)
      case Some(true) =>
        ls.layers.map(_.map { ldef =>
          ldef.materialRef match {
            case Some(mid) =>
              layerstackAccess.getIndexedMaterial(team, mid)
                .map {
                  case Some(mat) => ldef.copy(material = Some(mat))
                  case None =>
                    ldef // throw new TransportException(TransportErrorCode.NotFound, s"Material $mid not found")
                }
            case None => Future.successful(ldef)
          }
        }).map(fs => Future.sequence(fs)) match {
          case Some(x) => x.map(ldefs => ls.copy(layers = Some(ldefs)))
          case None    => Future.successful(ls)
        }
    }

  def resolveMaterials(ls: LayerstackDefinition, resolve: Option[Boolean]): Future[LayerstackDefinition] =
    resolve match {
      case None | Some(false) => Future.successful(ls)
      case Some(true) =>
        option(ls.stacks.map(_.map(resolveMaterials(ls.team.get, _, resolve))).map(x => Future.sequence(x.toSeq))).map(
          resolvedSubStack =>
            ls.copy(stacks = resolvedSubStack)
        )
      //        ls.copy(stacks = ls.stacks.map(_.map(ss => ss.copy(layerMaterials = mats))))
    }

  def defaultDensity(ld: LayerDefinition, outer: Boolean, i: Int): BigDecimal =
    if (outer) {
      30
    } else {
      60
    }

  def getDensities(ss: SubStackDefinition): (Map[(UUID, Int), Density], Map[UUID, Seq[(String, Property)]]) = {
    val layers     = ss.layers.getOrElse(Seq())
    val extraMetas = mutable.Map[UUID, Seq[(String, Property)]]()
    (
      layers.zipWithIndex.flatMap { _ld =>
        val ld       = _ld._1
        val topLayer = _ld._2 == 0
        val botLayer = _ld._2 == ss.layers.size - 1

        val material = ld.material.get
        if (material.copperCount() == 1) {
          val d: Option[DecimalProperty] = ld.meta.flatMap(_ \ LayerProperties.CUDensity)

          val given = d.map(_.value)
          val dens =
            if (given.isDefined) {
              given.get
            } else {
              val x = defaultDensity(ld, topLayer || botLayer, 0)
              extraMetas.put(
                ld.id.get,
                extraMetas.getOrElse(ld.id.get, Seq()) :+ DecimalProperty.e(LayerProperties.CUDensity, x)
              )
              x
            }

          Map((ld.id.get, 0) -> Density(dens.doubleValue, 0))
        } else if (material.copperCount() == 2) {
          val upper: Option[DecimalProperty] =
            ld.meta.flatMap(_ \ LayerProperties.UpperCUDensity).orElse(ld.meta.flatMap(_ \ LayerProperties.CUDensity))
          val lower: Option[DecimalProperty] =
            ld.meta.flatMap(_ \ LayerProperties.LowerCUDensity).orElse(ld.meta.flatMap(_ \ LayerProperties.CUDensity))

          val givenUpper = upper.map(_.value)
          val upperVal =
            if (givenUpper.isDefined) {
              givenUpper.get
            } else {
              val x = defaultDensity(ld, topLayer, 0)
              extraMetas.put(
                ld.id.get,
                extraMetas.getOrElse(ld.id.get, Seq()) :+ DecimalProperty.e(LayerProperties.UpperCUDensity, x)
              )
              x
            }

          val givenLower = lower.map(_.value)
          val lowerVal =
            if (givenLower.isDefined) {
              givenLower.get
            } else {
              val x = defaultDensity(ld, botLayer, 1)
              extraMetas.put(
                ld.id.get,
                extraMetas.getOrElse(ld.id.get, Seq()) :+ DecimalProperty.e(LayerProperties.LowerCUDensity, x)
              )
              x
            }

          Seq(
            (ld.id.get, 0) -> Density(upperVal.doubleValue, 0),
            (ld.id.get, 1) -> Density(lowerVal.doubleValue, 0)
          )
        } else {
          Seq()
        }
      }.toMap,
      extraMetas.toMap
    )
  }

  def pressLayerStack(ldef: LayerstackDefinition): Future[Option[(MetaInfo, SubStackDefinition)]] =
    resolveMaterials(ldef, Some(true)).map { ld =>
      ld.stacks.flatMap(_.headOption).map { ss =>
        val (densities, densityMetas) = getDensities(ss)
        val layerMetas                = LayerStackPress.press(densities)(ss)

        val metaMap =
          layerMetas.map(x => x._1 -> MetaInfo(x._2.toMap)) ++ densityMetas.map(x => x._1 -> MetaInfo(x._2.toMap))

        var updated = ss.copy(
          layers = ss.layers.map(_.map {
            case ld if metaMap.contains(ld.id.get) =>
              ld.copy(
                meta = Some(ld.meta.getOrElse(MetaInfo()) ++ metaMap(ld.id.get))
              )
            case l => l
          })
        )

        val thickness = LayerStackPress.thickness2(ss.layers.get, metaMap)
        logger.warn(s"pressed layer: $thickness, $metaMap -> ${Json.toJson(updated)}")
        (MetaInfo(Map(DecimalProperty.e(LayerstackDFM.PRESSED_THICKNESS, thickness))), updated)
      }
    }

  def resolveMaterials(team: String, ls: LayerStacks, resolve: Option[Boolean]): Future[LayerStacks] =
    for {
      sel <- FutureUtils.option(ls.selected.map(s => resolveMaterials(team, s, resolve)))
    } yield LayerStacks(sel)

  def resolveMaterials(team: String, ls: LayerStack, resolve: Option[Boolean]): Future[LayerStack] =
    resolve match {
      case None | Some(false) => Future.successful(ls)
      case Some(true) =>
        Future.sequence(ls.stacks.map { s =>
          Future.sequence(s.layers.map(resolveMaterials(team, _))).map { layers =>
            s.copy(layers = layers)
          }
        }).map { ss =>
          ls.copy(stacks = ss)
        }
    }

  def resolveMaterials(team: String, ls: Layer): Future[Layer] =
    if (ls.definition.materialRef.isDefined) {
      FutureUtils.option(ls.definition.materialRef.map(mid =>
        layerstackAccess
          .getIndexedMaterial(team, mid)
      ))
        .map(_.flatten).map(mat => ls.copy(definition = ls.definition.copy(material = mat)))
    } else {
      Future.successful(ls)
    }

  override def _getStandardLayerstacks(team: String): ServerServiceCall[NotUsed, Seq[LayerstackDefinition]] =
    ServerServiceCall { _ =>
      StandardLayerstackComponentsRepository.getTeamLayerstacks(team, this.layerstackAccess)
    }

  override def listLayerstacks(
      page: Option[Int],
      pagesize: Option[Int]
  ): ServiceCall[NotUsed, Seq[LayerstackDefinitionDescription]] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        layerstackAccess.listLayerstacks(token.getTeam)
      }
    }

  override def getLayerstacks(
      page: Option[Int],
      pagesize: Option[Int],
      filter: Option[String],
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[api.LayerstackDefinitionAPI]] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        doGetLayerstacks(page, pagesize, resolve, token).map(_.map(_.toApi))
      }
    }

  private def doGetLayerstacks(
      page: Option[Int],
      pagesize: Option[Int],
      resolve: Option[Boolean],
      token: TokenContent
  ): Future[Seq[LayerstackDefinition]] =
    layerstackAccess.getAllStackIDs(token.team, page, pagesize).flatMap { x =>
      Future.sequence(x.map { stackInfo =>
        layerstackAccess.getLayerstack(token.team, stackInfo._1)
          .flatMap(ls =>
            FutureUtils.option({
              ls.map { _ls =>
                resolveMaterials(_ls, resolve)
              }
            })
          )
      })
    }.map(_.flatten)

  override def createLayerstack: ServiceCall[api.LayerstackDefinitionAPI, api.LayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:*:*:write"
    ) { (token, _) =>
      ServerServiceCall { ld =>
        if (ld.name.isEmpty) {
          throw new TransportException(TransportErrorCode.PolicyViolation, "Layerstackdefinition needs a name")
        }
        val id       = UUID.randomUUID()
        val ldWithID = ld.copy(id = Some(id))
        LayerstackServiceImpl.createLayerstackDef(token, token.team, id, ldWithID.toInternal, layerstackAccess, ereg)
      }
    }

  override def getLayerstack(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, api.LayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _doGetLayerstack(name, token, resolve).map(_.toApi)
      }
    }

  override def getLayerstackPrice(name: String): ServiceCall[NotUsed, api.LayerstackPrices] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _doGetLayerstack(name, token, Some(true)).map { ls =>
          ls.calculatePrice
        }
      }
    }

  private def _doGetLayerstack(
      name: String,
      token: TokenContent,
      resolve: Option[Boolean]
  ): Future[LayerstackDefinition] = {
    val fr = UUIDUtils.fromString(name) match {
      case Some(id) => Future.successful(Some(id))
      case None =>
        layerstackAccess.getStackIDByName(token.team, name).map(
          _.map(_._1)
        )
    }

    fr.flatMap {
      case Some(x) =>
        layerstackAccess.getLayerstack(token.team, x)
          .flatMap(ls =>
            FutureUtils.option({
              ls.map(_ls =>
                resolveMaterials(_ls, resolve)
              )
            })
          )
          .map(_.getOrElse(throw new TransportException(
            TransportErrorCode.PolicyViolation,
            "No Layerstack with this name found"
          )))
      case None =>
        throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
    }
  }

  override def updateLayerstack(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[api.LayerstackDefinitionAPI, api.LayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:write"
    ) { (token, _) =>
      ServerServiceCall { la =>
        val l = la.toInternal
        stacks.getStackIDByName(token.team, name).flatMap {
          case Some(x) =>
            val entity = ereg.refFor[LayerstackDefinitionEntity](x._1.toString)
            (for {
              _ <- stacks.getStackIDByName(token.team, l.name).flatMap { newId =>
                if (newId.isDefined && x._1 != newId.get._1) {
                  throw new TransportException(
                    TransportErrorCode.PolicyViolation,
                    s"A Layerstack with name ${l.name} exists already"
                  )
                } else {
                  Future.successful(Done)
                }
              }
              sanitized   <- LayerstackServiceImpl.sanitize(token.team, l, layerstackAccess)
              copperCount <- LayerstackServiceImpl.createMeta(token.team, l, layerstackAccess)
              a <-
                entity.ask(SetLayerstackDefinitionName(token.team, x._2, sanitized.name, CollaborativeEventInfo(token)))

              b <- option(sanitized.price.map(price =>
                entity.ask(SetLayerstackDefinitionPrice(token.team, x._2, Some(price), CollaborativeEventInfo(token)))
              ))
              c <- option(sanitized.stacks.map(ls =>
                entity.ask(SetSubStacks(token.team, x._2, ls, CollaborativeEventInfo(token)))
              ))
              press <- option(c.map(ss => pressLayerStack(ss))).map(_.flatten)
              sss <- option(press.map(_._2).map(ss =>
                entity.ask(ChangeSubStack(token.team, x._2, ss.name, ss, CollaborativeEventInfo(token)))
              ))
              smi <- entity.ask(SetLayerstackDefinitionMeta(
                c.map(_.metaInfo).getOrElse(MetaInfo()) ++ l.metaInfo ++ copperCount ++ press.map(_._1).getOrElse(
                  MetaInfo()
                ),
                CollaborativeEventInfo(token)
              ))
              stack <- entity.ask(GetLayerstackDefinition(token.team)).map(_.response)
            } yield stack).flatMap { ls =>
              FutureUtils.option(ls.map { _ls =>
                resolveMaterials(_ls, resolve).map(_.toApi)
              })
            }.map(_.getOrElse(throw new TransportException(
              TransportErrorCode.PolicyViolation,
              "No Layerstack with this name found"
            )))
          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  val definitionTemplateCategory = "layerstackdefinition"

  override def printLayerstack(name: String, template: Option[String]): ServiceCall[NotUsed, Array[Byte]] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:read"
    ) { (token, _) =>
      ServerServiceCall { (_, _) =>
        val res = _doGetLayerstack(name, token, Some(true)).flatMap { ld =>
          val variable = ld

          user._renderTemplate(token.team, definitionTemplateCategory, template).invoke(RenderRequest(
            resources = Seq(
              ld.image.map(_.toPath)
            ).flatten,
            variables = Map(
              "layerstack" -> Json.toJson(variable)
            )
          ))
        }

        import de.fellows.utils.ServiceCallUtils._
        res.map(inlinePDF)
      }
    }

  override def convertLayerstack(name: String, format: String): ServiceCall[NotUsed, Array[Byte]] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:read"
    ) { (token, _) =>
      ServerServiceCall { (_, _) =>
        format match {
          case "ipc2581" =>
            _doGetLayerstack(name, token, Some(true)).map { ld =>
              val res: Array[Byte] = _doConvertToIPC(ld)

              (
                ResponseHeader.Ok
                  .addHeader(HeaderNames.CONTENT_DISPOSITION, s"""attachment; filename="${name}.xml""""),
                res
              )

            }
          case x => throw new TransportException(TransportErrorCode.BadRequest, s"Unsupported Format ${x}")
        }

      }
    }

  override def convertLayerstacks(
      page: Option[Int],
      pagesize: Option[Int],
      format: Option[String]
  ): ServiceCall[NotUsed, Array[Byte]] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { (_, _) =>
        format match {
          case Some("ipc2581") | None =>
            doGetLayerstacks(page, pagesize, Some(true), token).map { f =>
              withResource(new ByteArrayOutputStream()) { bos =>
                val zipOutput = new ZipArchiveOutputStream(bos)
                var idx       = 1

                f.foreach { ld =>
                  val by    = _doConvertToIPC(ld)
                  val entry = new ZipArchiveEntry(s"Layerstack ${idx} ${ld.name}.xml")
                  entry.setSize(by.length)

                  zipOutput.putArchiveEntry(entry)
                  zipOutput.write(by)
                  zipOutput.closeArchiveEntry()

                  idx += 1
                }

                zipOutput.close()
                bos.toByteArray
              }

            }.map { arr =>
              (
                ResponseHeader.Ok
                  .addHeader(HeaderNames.CONTENT_DISPOSITION, s"""attachment; filename="layerstacks.zip""""),
                arr
              )
            }

          case x => throw new TransportException(TransportErrorCode.BadRequest, s"Unsupported Format ${x}")
        }

      }
    }

  private def _doConvertToIPC(ld: LayerstackDefinition) = {
    val xml     = new IPC258Writer(ld).convert
    val printer = new PrettyPrinter(80, 2)
    val res =
      ("""<?xml version="1.0" encoding="utf-8"?>""" + System.lineSeparator() + printer.format(xml)).getBytes()
    res
  }

  override def cloneLayerstack(name: String): ServiceCall[String, LayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:write"
    ) { (token, _) =>
      ServerServiceCall { l =>
        for {
          id <- (UUIDUtils.fromString(name) match {
            case Some(i) => Future.successful(Some((i, name)))
            case None =>
              layerstackAccess.getStackIDByName(token.team, name)
          }).map(_.getOrElse(throw new TransportException(
            TransportErrorCode.PolicyViolation,
            "No Layerstack with this name found"
          )))

          base <-
            layerstackAccess.getLayerstack(token.team, id._1)
              .map(_.getOrElse(throw new TransportException(
                TransportErrorCode.PolicyViolation,
                "Layerstack Definition is not available"
              )))

          clone <- {
            val id       = UUID.randomUUID()
            val newStack = base.copy(id = id, name = s"${l}")
            LayerstackServiceImpl.createLayerstackDef(token, token.team, id, newStack, layerstackAccess, ereg)
          }
        } yield clone
      }
    }

  override def deleteLayerstack(name: String): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:write"
    ) { (token, _) =>
      ServerServiceCall { l =>
        val id = UUIDUtils.fromString(name) match {
          case Some(i) => Future.successful(Some((i, name)))
          case None    => stacks.getStackIDByName(token.team, name)
        }

        id.flatMap {
          case Some(x) =>
            val entity = ereg.refFor[LayerstackDefinitionEntity](x._1.toString)
            entity.ask(DeleteLayerstackDefinition(token.team, x._2, CollaborativeEventInfo(token)))
          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  override def getSubLayerstacks(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[api.SubStackDefinition]] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:read"
    ) { (token, _) =>
      ServerServiceCall { l =>
        layerstackAccess.getStackIDByName(token.team, name).flatMap {
          case Some(x) =>
            layerstackAccess.getLayerstack(token.team, x._1)
              .map(_.map(_.stacks.getOrElse(Seq()))).flatMap { lss =>
                Future.sequence(lss.getOrElse(Seq()).map(ls => resolveMaterials(token.team, ls, resolve)))
              }
          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  override def addSubLayerstack(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[api.SubStackDefinition, api.LayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:write"
    ) { (token, _) =>
      ServerServiceCall { l =>
        stacks.getStackIDByName(token.team, name).flatMap {
          case Some(x) =>
            val entity = ereg.refFor[LayerstackDefinitionEntity](x._1.toString)

            entity.ask(GetLayerstackDefinition(token.team))
              .map(_.response)
              .flatMap(_ls =>
                _ls.map { ls =>
                  val contains = ls.stacks.getOrElse(Seq()).exists(_.name == l.name)
                  if (!contains) {
                    (for {
                      //                  _ <- collectMetaData(token.team, l)
                      sanitized <- LayerstackServiceImpl.sanitize(token.team, l, layerstackAccess)
                      add       <- entity.ask(AddSubStack(token.team, name, sanitized, CollaborativeEventInfo(token)))
                      props     <- LayerstackServiceImpl.createMeta(token.team, add, layerstackAccess)
                      smi <-
                        entity.ask(SetLayerstackDefinitionMeta(add.metaInfo ++ props, CollaborativeEventInfo(token)))
                    } yield smi.toApi).recover {
                      case e: InvalidCommandException => throw new TransportException(TransportErrorCode.NotFound, e)
                    }
                  } else {
                    throw new TransportException(
                      TransportErrorCode.PolicyViolation,
                      "Layerstack contains a SubStack with this name"
                    )
                  }
                }
                  .getOrElse(throw new TransportException(
                    TransportErrorCode.PolicyViolation,
                    "Layerstack contains a SubStack with this name"
                  ))
              )

          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  override def setSubLayerstacks(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[Seq[api.SubStackDefinition], api.LayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:write"
    ) { (token, _) =>
      ServerServiceCall { lyr =>
        val names = lyr.map(_.name)
        if (names.distinct.size != names.size) {
          throw new TransportException(TransportErrorCode.PolicyViolation, "Substacks need distinct names")
        }

        stacks.getStackIDByName(token.team, name).flatMap {
          case Some(x) =>
            Future.sequence(lyr.map { ss =>
              checkMaterials(token.team, ss)
            }).flatMap { b =>
              if (!b.contains(false)) {
                val entity = ereg.refFor[LayerstackDefinitionEntity](x._1.toString)
                (for {
                  l   <- Future.sequence(lyr.map(x => LayerstackServiceImpl.sanitize(token.team, x, layerstackAccess)))
                  sss <- entity.ask(SetSubStacks(token.team, x._2, l.toSeq, CollaborativeEventInfo(token)))
                  copper <- LayerstackServiceImpl.createMeta(token.team, sss, layerstackAccess)
                  smi <- entity.ask(SetLayerstackDefinitionMeta(sss.metaInfo ++ copper, CollaborativeEventInfo(token)))

                  resolvedMats <- resolveMaterials(smi, resolve)

                } yield resolvedMats.toApi).recover {
                  case e: InvalidCommandException => throw new TransportException(TransportErrorCode.NotFound, e)
                }

                //                .flatMap(ls => {
                //                  resolveMaterials(ls, resolve).map(_.toApi)
                //                })
              } else {
                throw new TransportException(TransportErrorCode.PolicyViolation, "Material not found")
              }
            }

          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  override def getSubLayerstack(
      name: String,
      subname: String,
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, api.SubStackDefinition] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        layerstackAccess.getStackIDByName(token.team, name).flatMap {
          case Some(x) =>
            layerstackAccess.getLayerstack(token.team, x._1)
              .map { stack =>
                stack.map { _stack =>
                  _stack.stacks.flatMap(_.find(_.name == subname))
                }
              }.map(_.flatten).flatMap {
                case Some(x) => resolveMaterials(token.team, x, resolve)
                case None => throw new TransportException(TransportErrorCode.NotFound, s"SubStack ${subname} not found")
              }
          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  override def setSubLayerstack(
      name: String,
      subname: String,
      resolve: Option[Boolean]
  ): ServiceCall[api.SubStackDefinition, api.LayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:write"
    ) { (token, _) =>
      ServerServiceCall { l =>
        checkMaterials(token.team, l)

        stacks.getStackIDByName(token.team, name).flatMap {
          case Some(x) =>
            val entity = ereg.refFor[LayerstackDefinitionEntity](x._1.toString)
            (for {
              lyr      <- LayerstackServiceImpl.sanitize(token.team, l, layerstackAccess)
              s        <- entity.ask(ChangeSubStack(token.team, name, subname, lyr, CollaborativeEventInfo(token)))
              copper   <- LayerstackServiceImpl.createMeta(token.team, s, layerstackAccess)
              smi      <- entity.ask(SetLayerstackDefinitionMeta(s.metaInfo ++ copper, CollaborativeEventInfo(token)))
              resolved <- resolveMaterials(smi, resolve)
            } yield resolved.toApi).recover {
              case e: InvalidCommandException => throw new TransportException(TransportErrorCode.NotFound, e)
            }

          //            (entity.ask(ChangeSubStack(token.team, name, subname, l, CollaborativeEventInfo(token)))
          //              .recover{
          //                case e: InvalidCommandException => throw new TransportException(TransportErrorCode.NotFound, e)
          //              }).flatMap(ls => {
          //              resolveMaterials(ls, resolve).map(_.toApi)
          //            })
          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, s"No Layerstack with this name found")
        }
      }
    }

  private def checkMaterials(team: String, l: SubStackDefinition): Future[Boolean] = {
    val mats = l.layers
      .map(ld =>
        ld.flatMap { ld =>
          ld.materialRef
        }
      ).getOrElse(Seq())

    materials.getMaterials(team, mats).map { m =>
      mats.forall(m.flatMap(_.id).contains)
    }
  }

  override def removeSubLayerstack(
      name: String,
      subname: String,
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, api.LayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:write"
    ) { (token, _) =>
      ServerServiceCall { l =>
        stacks.getStackIDByName(token.team, name).flatMap {
          case Some(x) =>
            val entity = ereg.refFor[LayerstackDefinitionEntity](x._1.toString)
            (for {
              rem      <- entity.ask(RemoveSubStack(token.team, name, subname, CollaborativeEventInfo(token)))
              copper   <- LayerstackServiceImpl.createMeta(token.team, rem, layerstackAccess)
              smi      <- entity.ask(SetLayerstackDefinitionMeta(rem.metaInfo ++ copper, CollaborativeEventInfo(token)))
              resolved <- resolveMaterials(smi, resolve)
            } yield resolved.toApi).recover {
              case e: InvalidCommandException => throw new TransportException(TransportErrorCode.NotFound, e)
            }

          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  override def _setPreview(team: String, name: String): ServiceCall[String, Done] =
    ServerServiceCall { origpath =>
      stacks.getStackIDByName(team, name).flatMap {
        case Some(x) =>
          val ent  = ereg.refFor[LayerstackDefinitionEntity](x._1.toString)
          val path = FilePath(fsServicePath, team, x._1.toString, "images", None, "generated_preview.svg")
          // FilePath(basePath, team, layerstack.toString, path_base, fileName)
          val fullOrigPath = Paths.get(fsBasePath).resolve(origpath)
          path.toJavaFile.getParentFile.mkdirs()
          Files.move(fullOrigPath, path.toJavaPath, StandardCopyOption.REPLACE_EXISTING)

          ent.ask(SetLayerstackDefinitionImage(path, CollaborativeEventInfo(TimelineUser.systemUserId.toString)))
            .map(_ => Done)
        case None => Future.successful(Done)
      }
    }

  def _doMatchLayerstack[T](
      team: String,
      assemblyID: UUID,
      versionID: UUID,
      conv: LayerStacks => T,
      tcmd: TimelineCommand
  ): ServerServiceCall[NotUsed, T] =
    ServerServiceCall { _ =>
      doMatchLayerstack(team, assemblyID, versionID, conv, tcmd, true)
    }

  def _doSelectLayerstackForShare(
      share: SharedAssemblyInfo,
      assembly: Assembly,
      version: Version,
      pcbv: PCBVersion,
      tcmd: TimelineCommand
  ): Future[LayerStacks] =
    new LayerStackMatcher(share.team, assembly, version, pcbv, layerstackAccess).findLayerstacks().flatMap { ls =>
      val entity = ereg.refFor[LayerStackEntity](share.id.toString)

      val newStacks = LayerStacks(
        selected = ls
      )

      _doSelectLayerstack(entity, share.team, assembly.id, version.id, newStacks, newStacks.selected, tcmd, false)
    }

  def doMatchLayerstack[T](
      team: String,
      assemblyID: UUID,
      versionID: UUID,
      conv: LayerStacks => T,
      tcmd: TimelineCommand,
      matchForShares: Boolean
  ): Future[T] =
    AssemblyUtils.lifecycle(
      AssemblyReference(team, assemblyID, None, versionID),
      ass,
      AssemblyLifecycleStageName.LayerStack
    ) {
      (for {
        assemblyWithShares <- ass._getAssembly(team, assemblyID).invoke()
        pcbv               <- pcb._getPCBVersion(team, assemblyID, versionID).invoke()
      } yield {
        val assembly = assemblyWithShares.assembly
        val version = assembly.currentVersion.getOrElse(throw new TransportException(
          TransportErrorCode.NotFound,
          s"Assembly $assembly has no version"
        ))
        val layerstackForMainTeam =
          new LayerStackMatcher(team, assembly, version, pcbv, layerstackAccess).findLayerstacks().flatMap { ls =>
            val entity = ereg.refFor[LayerStackEntity](versionID.toString)

            val newStacks = LayerStacks(
              selected = ls
            )

            _doSelectLayerstack(
              entity = entity,
              team = team,
              assembly = assemblyID,
              version = versionID,
              ls = newStacks,
              select = newStacks.selected,
              tcmd = tcmd,
              updateMetaData = true
            ).map(
              conv
            )

          }

        val layerstacksForShares =
          if (matchForShares) {
            Future.sequence(assemblyWithShares.shares.map { share =>
              _doSelectLayerstackForShare(
                share = share,
                assembly = assembly,
                version = version,
                pcbv = pcbv,
                tcmd = tcmd
              )
            }).map(_.map(conv))
          } else {
            Future.successful(Seq())
          }

        for {
          main <- layerstackForMainTeam
          _    <- layerstacksForShares
        } yield main
      }).flatten
    }

  override def _matchLayerstack(
      team: String,
      assemblyID: UUID,
      versionID: UUID
  ): ServerServiceCall[NotUsed, api.LayerStacks] =
    _doMatchLayerstack(team, assemblyID, versionID, identity, TimelineCommand.system)

  def _doUpdateLayerstack(
      team: String,
      layerstack: UUID,
      tcmd: TimelineCommand
  ): ServerServiceCall[api.LayerStack, Done] =
    ServerServiceCall { update =>
      ereg.refFor[LayerStackEntity](layerstack.toString).ask(GetLayerStacks(team, layerstack)).flatMap { lss =>
        if (lss.selected.map(_.definition.id).contains(update.definition.id)) {
          val ls = lss.selected.get
          val updated = ls.copy(
            stacks = ls.stacks.map { ss =>
              val subUpdate = update.stacks.find(_.definition.name == ss.definition.name)
              ss.copy(
                metaInfo =
                  Some(ss.metaInfo.getOrElse(MetaInfo()) ++ subUpdate.flatMap(_.metaInfo).getOrElse(MetaInfo())),
                layers = ss.layers.map(l =>
                  l.copy(
                    metaInfo = Some(l.metaInfo.getOrElse(MetaInfo()) ++ subUpdate.flatMap(
                      _.layers.find(_.definition.id == l.definition.id)
                    ).flatMap(_.metaInfo).getOrElse(MetaInfo()))
                  )
                )
              )
            }
          )
          ereg.refFor[LayerStackEntity](layerstack.toString).ask(SetLayerStackMeta(team, layerstack, updated, tcmd))
            .map(_ => Done)
        } else {
          Future.successful(Done)
        }
      }
    }

  override def _updateStack(team: String, layerstack: UUID): ServerServiceCall[api.LayerStack, Done] =
    _doUpdateLayerstack(team, layerstack, TimelineCommand.system)

  override def getPCBLayerstack(
      assembly: UUID,
      version: UUID,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, LayerStacksAPI] =
    doGetPcbLayerstack(version, materials)

  override def getPCBLayerstackByVersion(
      version: UUID,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, LayerStacksAPI] = doGetPcbLayerstack(version, materials)

  private def doGetPcbLayerstack(version: UUID, materials: Option[Boolean]): ServiceCall[NotUsed, LayerStacksAPI] =
    auth {
      // If the auth token comes from lumiquote, we need a different permission check.
      // layerstackusage permission is not part of auth0 token permissions
      case _: Auth0Token => "view:pcb"
      case t             => s"layerstackusage:${t.getTeam}:${t.getTeam}:$version:*:read"
    } { (token, _) =>
      val team = token.getTeam
      ServerServiceCall { _ =>
        ereg.refFor[LayerStackEntity](version.toString).ask(GetLayerStacks(team, version)).flatMap { ls =>
          resolveMaterials(team, ls, materials).map(_.toApi)
        }.recover {
          case e: InvalidCommandException =>
            LayerStacksAPI(None)
        }
      }
    }

  def relevantForAssembly(event: LayerStackSet, assembly: UUID, version: UUID): Boolean =
    event.stack.selected
      .exists { ls =>
        ls.assembly match {
          case AssemblyReference(team, refId, gid, refVersion) =>
            refId == assembly && refVersion == version
          case SharedAssemblyReference(team, id, sharedAssembly) =>
            sharedAssembly.id == assembly && sharedAssembly.version == version
        }
      }

  def relevantForAssembly(event: LayerStackChanged, assembly: UUID, version: UUID): Boolean =
    event.stack.assembly match {
      case AssemblyReference(team, refId, gid, refVersion) =>
        refId == assembly && refVersion == version
      case SharedAssemblyReference(team, id, sharedAssembly) =>
        sharedAssembly.id == assembly && sharedAssembly.version == version
    }

  override def streamLayerstacks(
      assembly: UUID,
      version: UUID,
      k: String,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, Source[LayerstackStreamMessage, NotUsed]] = {
    import scala.concurrent.duration._
    val b = AuthenticationServiceComposition.decodeTokenWithResponse(k)
    authorizedStringWithToken(b)(token => s"pcb:${token.team}:${token.team}:$version:stack:read") {
      (token, b) =>
        ServerServiceCall { s =>
          val ticks = Source.tick(30 seconds, 30 seconds, LayerStackMessage(Instant.now(), None))
          // resolveMaterials(token.team, ls, materials)
          val nowOffset = Offset.timeBasedUUID(UUIDs.timeBased())
          val sources = LayerStackEvent.Tag.allTags.map(tag =>
            ereg.eventStream(tag, nowOffset)
              .collect {
                case EventStreamElement(str, event: LayerStackSet, offset)
                    if relevantForAssembly(event, assembly, version) =>
                  Await.result(
                    resolveMaterials(token.team, event.stack, materials).map { ls =>
                      LayerStackMessage(Instant.now(), Some((ls.toApi)))
                    },
                    20 seconds
                  )

                case EventStreamElement(str, event: LayerStackChanged, offset)
                    if relevantForAssembly(event, assembly, version) =>
                  LayerStackChangedMessage(Instant.now(), Some(event.stack.toApi))
              }
          )

          val src = sources.reduce((a, b) => a.merge(b))
          Future.successful(src.merge(ticks))

        }
    }
  }

  override def _getPCBLayerstack(
      team: String,
      layerstack: UUID,
      materials: Option[Boolean]
  ): ServerServiceCall[NotUsed, api.LayerStacks] =
    ServerServiceCall { _ =>
      ereg.refFor[LayerStackEntity](layerstack.toString).ask(GetLayerStacks(team, layerstack)).flatMap { ls =>
        resolveMaterials(team, ls, materials)
      }.recover {
        case e: InvalidCommandException =>
          throw new TransportException(TransportErrorCode.NotFound, "LayerStack not found")
      }
    }

  def _doSelectLayerstack(
      entity: PersistentEntityRef[LayerStackCommand],
      team: String,
      assembly: UUID,
      version: UUID,
      ls: LayerStacks,
      select: Option[LayerStack],
      tcmd: TimelineCommand,
      updateMetaData: Boolean
  ): Future[LayerStacks] = {

    val upls = select match {
      case Some(value) => ls.copy(selected = Some(value))
      case None        => ls
    }

    for {
      ls <- entity.ask(SetLayerStacks(team, version, upls, true, tcmd))
      _ <-
        if (updateMetaData) {
          val additionalMeta = upls.selected.flatMap { sls =>
            val m1 = sls.definition.metaInfo
            val m2 = sls.stacks.flatMap(_.metaInfo).reduceOption(_ ++ _).getOrElse(MetaInfo())

            val res = m1 ++ m2
            if (res.properties.isEmpty) {
              None
            } else {
              Some(res)
            }
          }

          logger.info(s"update pcb with LS info ${additionalMeta}")
          additionalMeta.map { props =>
            val more = MetaInfo(Seq(
              (props \ [DecimalProperty] MaterialProperties.CuCount).map(x => Property.of(DFM.LAYERCOUNT, x.value).e)
            ).flatten.toMap)

            pcb._setMetaInfoProperty(team, assembly, version).invoke((props ++ more).properties.values.toSeq)
          }
            .getOrElse(Future.successful(Done))
        } else {
          Future.successful(Done)
        }
    } yield ls
  }

  override def selectLayerstack(assembly: UUID, version: UUID): ServiceCall[IDContainer, api.LayerStacksAPI] =
    authorizedString(token =>
      s"layerstackusage:${token.team}:${token.team}:$version:*:write"
    ) { (token, _) =>
      ServerServiceCall { selection =>
        _doSelectLayerstack(assembly, version, token, selection)
      }
    }

  override def selectLayerstackByVersion(version: UUID): ServiceCall[IDContainer, LayerStacksAPI] =
    auth {
      case _: Auth0Token => "edit:pcb"
      case t             => s"layerstackusage:${t.getTeam}:${t.getTeam}:$version:*:write"
    } { (token, _) =>
      ServerServiceCall { selection =>
        ass._getAssemblyByVersion(token.getTeam, version).invoke().flatMap { assembly =>
          _doSelectLayerstack(assembly.id, version, token, selection)
        }
      }
    }

  private def _doSelectLayerstack(
      assembly: UUID,
      version: UUID,
      token: GenericTokenContent,
      selection: IDContainer
  ): Future[LayerStacksAPI] = {
    val tcmd                                           = TimelineCommand.of(token)
    val entity: PersistentEntityRef[LayerStackCommand] = ereg.refFor[LayerStackEntity](version.toString)

    val assemblyRef = AssemblyReference(token.getTeam, assembly, None, version)
    AssemblyUtils.lifecycle(
      assemblyRef,
      ass,
      AssemblyLifecycleStageName.LayerStack
    ) {

      for {
        ldefOpt <- layerstackAccess.getLayerstack(token.getTeam, selection.id)
        pcbvOpt <- FutureUtils.option(ldefOpt.map(_ => pcb._getPCBVersion(token.getTeam, assembly, version).invoke()))

        set <- (ldefOpt, pcbvOpt) match {
          case (Some(ldef), Some(pcbv)) =>
            val newMatch = LayerStackMatcher.createLayerStack(ldef, assemblyRef, pcbv.files)
            val ls = LayerStacks(
              selected = newMatch
            )
            _doSelectLayerstack(entity, token.getTeam, assembly, version, ls, ls.selected, tcmd, false).map(_.toApi)
          case _ =>
            throw new TransportException(
              TransportErrorCode.BadRequest,
              "Selected Layerstack is not part of the matched options"
            )
        }
      } yield set
    }
  }

  override def matchLayerstack(assembly: UUID, version: UUID): ServiceCall[NotUsed, api.LayerStacksAPI] =
    authorizedString(token => s"layerstackusage:${token.team}:${token.team}:$version:*:write") { (token, _) =>
      _doMatchLayerstack(token.team, assembly, version, _.toApi, TimelineCommand.of(token))
    }
  override def getLayerstackOptions(
      assemblyId: UUID,
      versionId: UUID
  ): ServiceCall[NotUsed, Seq[LayerstackDefinitionDescription]] =
    authorizedString(token => s"layerstackusage:${token.team}:${token.team}:$versionId:*:read") { (token, _) =>
      ServerServiceCall { _ =>
        (for {
          (assembly, version) <- ass._getAssembly(token.getTeam, assemblyId).invoke().map(a =>
            a.assembly -> a.assembly.currentVersion.getOrElse(throw new TransportException(
              TransportErrorCode.NotFound,
              s"Assembly $assemblyId has no version"
            ))
          )

          pcbv <- pcb._getPCBVersion(token.getTeam, assemblyId, versionId).invoke()

          options <-
            new LayerStackMatcher(token.getTeam, assembly, version, pcbv, layerstackAccess).findApplicableDefinitions
        } yield options)
      }
    }

  override def getLayerstackOptionsByVersion(versionId: UUID)
      : ServiceCall[NotUsed, Seq[LayerstackDefinitionDescription]] =
    auth {
      case _: Auth0Token => s"edit:pcb"
      case t             => s"layerstackusage:${t.getTeam}:${t.getTeam}:$versionId:*:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        (for {
          (assembly, version) <- ass._getAssemblyByVersion(token.getTeam, versionId).invoke()
            .map(a =>
              a -> a.currentVersion.getOrElse(throw new TransportException(
                TransportErrorCode.NotFound,
                s"Version $versionId has no version"
              ))
            )

          pcbv <- pcb._getPCBVersion(token.getTeam, assembly.id, versionId).invoke()

          options <-
            new LayerStackMatcher(token.getTeam, assembly, version, pcbv, layerstackAccess).findApplicableDefinitions
        } yield options)
      }
    }

  override def layerstackDefinitionTopic(): Topic[Streams.LayerStackDefinitionChanged] =
    TopicProducer.taggedStreamWithOffset(LayerstackDefinitionEvent.Tag) { (tag, offset) =>
      ereg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: LayerstackDefinitionCreated, _) =>
          immutable.Seq((LayerStackDefinitionChanged(ev.ls), x.offset))
        case x @ EventStreamElement(_, ev: LayerstackDefinitionChanged, _) =>
          immutable.Seq((LayerStackDefinitionChanged(ev.updated), x.offset))
        case x @ EventStreamElement(_, ev: LayerstackDefinitionMetaChanged, _) =>
          immutable.Seq((LayerStackDefinitionChanged(ev.ls), x.offset))
        case x @ EventStreamElement(_, ev: SubLayerstackDefinitionChanged, _) =>
          immutable.Seq((LayerStackDefinitionChanged(ev.stack), x.offset))
        case x @ EventStreamElement(_, ev: SubLayerstackDefinitionChanged, _) =>
          immutable.Seq((LayerStackDefinitionChanged(ev.stack), x.offset))
        case _ => Nil
      }
    }

  override def layerstackSetTopic(): Topic[Streams.LayerStackSelected] =
    TopicProducer.taggedStreamWithOffset(LayerStackEvent.Tag) { (tag, offset) =>
      ereg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: LayerStackSet, _) =>
          immutable.Seq((LayerStackSelected((ev.stack.toApi), ev.user), x.offset))
        case x =>
          immutable.Seq()

      }
    }

  override def layerstackTimeline(): Topic[TimelineEvent] =
    TopicProducer.taggedStreamWithOffset(LayerStackEvent.Tag) { (tag, offset) =>
      ereg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: LayerStackTimelineChanged, _) => immutable.Seq((ev.stack, x.offset))
        case _                                                           => Nil
      }
    }

  override def getSequenceDefinitions(
      page: Option[Int],
      pagesize: Option[Int],
      filter: Option[String],
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[SequencedLayerstackDefinitionAPI]] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        doGetLayerstackDefinitions(page, pagesize, resolve, token)
          .flatMap(x =>
            Future.sequence(x.map { sdef =>
              resolveMaterials(sdef, resolve).map { mats =>
                sdef.toApi(mats)
              }
            })
          )
      }
    }

  private def doGetLayerstackDefinitions(
      page: Option[Int],
      pagesize: Option[Int],
      resolve: Option[Boolean],
      token: TokenContent
  ): Future[Seq[SequencedLayerstackDefinition]] =
    sdefs.getAllStackIDs(token.team, page, pagesize).flatMap { x =>
      Future.sequence(x.map { stackInfo =>
        ereg.refFor[LayerstackSequenceDefinitionEntity](stackInfo._1.toString).ask(GetLayerstackSequenceDefinition(
          token.team,
          stackInfo._1
        ))
          .map(_.response)
      })
    }.map(_.flatten)

  def resolveMaterials(ls: SequencedLayerstackDefinition, resolve: Option[Boolean]): Future[Map[String, Material]] =
    resolve match {
      case None | Some(false) => Future.successful(Map())
      case Some(true) =>
        (ls.asyncMap {
          case x: MaterialSequence =>
            materials.getMaterial(ls.team, x.material).map(_.toSeq)
          case _ => Future.successful(Seq())
        }).map(m => m.groupBy(_.id.get).map(x => x._1 -> x._2.head))
    }

  override def getSequenceDefinition(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[NotUsed, SequencedLayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _doGetLayerstackSequence(name, token, resolve).flatMap(sdef =>
          resolveMaterials(sdef, resolve).map(mats => sdef.toApi(mats))
        )
      }
    }

  private def _doGetLayerstackSequence(
      name: String,
      token: TokenContent,
      resolve: Option[Boolean]
  ): Future[SequencedLayerstackDefinition] = {
    val fr = UUIDUtils.fromString(name) match {
      case Some(id) => Future.successful(Some(id))
      case None     => sdefs.getStackIDByName(token.team, name).map(_.map(_._1))
    }

    fr.flatMap {
      case Some(x) =>
        ereg.refFor[LayerstackSequenceDefinitionEntity](x.toString).ask(GetLayerstackSequenceDefinition(
          token.team,
          x.c
        ))
          .map(_.response)
          .map(_.getOrElse(throw new TransportException(
            TransportErrorCode.PolicyViolation,
            "No Layerstack with this name found"
          )))
      case None =>
        throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
    }
  }

  override def getSequenceDefinitionPrice(name: String): ServiceCall[NotUsed, LayerstackPrices] = ???

  override def updateSequenceDefinition(
      name: String,
      resolve: Option[Boolean]
  ): ServiceCall[SequencedLayerstackDefinitionAPI, SequencedLayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:write"
    ) { (token, _) =>
      ServerServiceCall { la =>
        val fr = UUIDUtils.fromString(name) match {
          case Some(id) => Future.successful(Some(id))
          case None     => sdefs.getStackIDByName(token.team, name).map(_.map(_._1))
        }

        fr.flatMap {
          case Some(lsid) =>
            _doUpsertLayerstackSequence(token, la, lsid, resolve)
          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  private def _doUpsertLayerstackSequence(
      token: TokenContent,
      la: SequencedLayerstackDefinitionAPI,
      lsid: UUID,
      resolve: Option[Boolean]
  ) = {
    val entity = ereg.refFor[LayerstackSequenceDefinitionEntity](lsid.toString)

    val definition = la.toInternal(Some(token.team))
    for {
      copperCount <- definition.asyncMap {
        case x: MaterialSequence => materials.getMaterial(token.team, x.material)
            .map(m =>
              m.map { mat =>
                mat.meta.flatMap(_.get[DecimalProperty](MaterialProperties.CuCount).map(_.value.intValue))
                  .getOrElse(mat.copperCount())
              }.toSeq
            )
        case y => Future.successful(Seq(0))
      }.map(_.sum)

      d <- entity.ask(SetLayerstackSequenceDefinition(
        definition.copy(metaInfo = definition.metaInfo + DecimalProperty(MaterialProperties.CuCount, copperCount)),
        TimelineCommand.of(token)
      ))

      wm <- resolveMaterials(d, resolve)

    } yield d.toApi(wm)

    //    .flatMap(sdef => resolveMaterials(sdef, resolve).map(mats => sdef.toApi(mats)))
  }

  override def updateSequence(
      name: String,
      sequence: UUID
  ): ServiceCall[SequenceUpdate, SequencedLayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:sequence:write"
    ) { (token, _) =>
      ServerServiceCall { la =>
        val fr = UUIDUtils.fromString(name) match {
          case Some(id) => Future.successful(Some(id))
          case None     => sdefs.getStackIDByName(token.team, name).map(_.map(_._1))
        }

        fr.flatMap {
          case Some(lsid) =>
            val entity = ereg.refFor[LayerstackSequenceDefinitionEntity](lsid.toString)

            val tcmd = TimelineCommand.of(token)
            Future.sequence(Seq(
              la.meta.map { meta =>
                entity.ask(SetSequenceMeta(token.team, lsid, sequence, meta, tcmd))
              },
              la.material.map { mat =>
                entity.ask(SetSequenceMaterial(token.team, lsid, sequence, mat, tcmd))
              }
            ).flatten)
              .flatMap(_ => _doGetLayerstackSequence(lsid.toString, token, None).map(_.toApi()))

          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  override def deleteSequenceDefinition(name: String): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:$name:*:write"
    ) { (token, _) =>
      ServerServiceCall { l =>
        val fr = UUIDUtils.fromString(name) match {
          case Some(id) => Future.successful(Some(id))
          case None     => sdefs.getStackIDByName(token.team, name).map(_.map(_._1))
        }

        fr.flatMap {
          case Some(lsid) =>
            val entity = ereg.refFor[LayerstackSequenceDefinitionEntity](lsid.toString)
            entity.ask(sequences.DeleteLayerstackDefinition(token.team, lsid, TimelineCommand.of(token)))
          case None =>
            throw new TransportException(TransportErrorCode.PolicyViolation, "No Layerstack with this name found")
        }
      }
    }

  override def createSequenceDefinition(resolve: Option[Boolean])
      : ServiceCall[SequencedLayerstackDefinitionAPI, SequencedLayerstackDefinitionAPI] =
    authorizedString(token =>
      s"layerstackdefinition:${token.team}:${token.team}:*:*:create"
    ) { (token, _) =>
      ServerServiceCall { la =>
        val withIDs = new AllocateUUIDs().process(la)
        _doUpsertLayerstackSequence(token, withIDs, withIDs.id.get, resolve)
      }
    }

  override def matchLayerstackSequence(assembly: UUID, version: UUID): ServiceCall[NotUsed, SequencedLayerstackAPI] =
    authorizedString(token => s"layerstackusage:${token.team}:${token.team}:$version:*:write") { (token, _) =>
      ServerServiceCall { _ =>
        ???
      }
    }

  override def getLayerstackSequenceOptions(
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Seq[SequencedLayerstackAPI]] =
    authorizedString(token => s"layerstackusage:${token.team}:${token.team}:$version:*:read") { (token, _) =>
      ServerServiceCall { _ =>
        ???
      }
    }

  override def selectLayerstackSequence(
      assembly: UUID,
      version: UUID
  ): ServiceCall[IDContainer, SequencedLayerstackAPI] = ???

  override def getPCBLayerstackSequence(
      assembly: UUID,
      version: UUID,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, SequencedLayerstackAPI] = ???

  override def streamLayerstackSequences(
      assembly: UUID,
      version: UUID,
      k: String,
      materials: Option[Boolean]
  ): ServiceCall[NotUsed, Source[LayerStackMessage, NotUsed]] = ???

  override def _updateStackSequence(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[SequencedLayerstack, Done] = ???

  override def _getPCBLayerstackSequence(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, SequencedLayerstack] = ???
}

object LayerstackServiceImpl {
  def createLayerstackDef(
      token: GenericTokenContent,
      team: String,
      id: UUID,
      ld: LayerstackDefinition,
      reporter: LayerStackComponentReporter,
      ereg: PersistentEntityRegistry
  )(implicit ec: ExecutionContext, sd: ServiceDefinition): Future[LayerstackDefinitionAPI] =
    reporter.getStackIDByName(team, ld.name).flatMap {
      case Some(x) => throw new TransportException(
          TransportErrorCode.BadRequest,
          s"A Layerstack with name ${ld.name} exists already"
        )
      case None =>
        val newStack = sanitize(team, ld, reporter)

        val entity     = ereg.refFor[LayerstackDefinitionEntity](id.toString)
        val collabInfo = CollaborativeEventInfo(token)

        for {
          stack     <- newStack
          extraMeta <- createMeta(team, stack, reporter)

          cls <- entity.ask(CreateLayerstackDefinition(
            stack.copy(
              id = id,
              team = Some(team)
            ),
            collabInfo
          ))
          smi <- entity.ask(SetLayerstackDefinitionMeta(cls.metaInfo ++ extraMeta, collabInfo))
        } yield smi.toApi
    }

  def sanitize(team: String, ls: LayerstackDefinition, reporter: MaterialReporter)(implicit
      ec: ExecutionContext
  ): Future[LayerstackDefinition] =
    ls.stacks.map(ss => Future.sequence(ss.map(l => sanitize(team, l, reporter)))) match {
      case Some(s) => s.map(ss => ls.copy(stacks = Some(ss)))
      case None    => Future.successful(ls)
    }

  //    ls.copy(stacks = )))

  def sanitize(team: String, ssd: SubStackDefinition, reporter: MaterialReporter)(implicit
      ec: ExecutionContext
  ): Future[SubStackDefinition] =
    option(ssd.layers.map(ls => Future.sequence(ls.map(sanitize(team, _, reporter))))).map(x => ssd.copy(layers = x))

  def sanitize(team: String, ls: LayerDefinition, reporter: MaterialReporter)(implicit
      ec: ExecutionContext
  ): Future[LayerDefinition] =
    for {
      mat <- {
        if (ls.materialRef.isDefined) {
          option(ls.materialRef.map(mid => reporter.getMaterial(team, mid))).map(_.flatten)
        } else {
          Future.successful(ls.material)
        }
      }
    } yield {
      val t = mat.flatMap(x => x.materialType).orElse(ls.layerType).getOrElse(throw new TransportException(
        TransportErrorCode.NotFound,
        "Material not found"
      ))
      ls.copy(
        id = Some(UUID.randomUUID()),
        layerType = Some(t),
        meta = Some(
          MetaInfo(
            ls.meta.map(_.lowerCaseProperties).getOrElse(Map()) ++
              Map(
                DecimalProperty.e(MaterialProperties.CuCount, countCopper(mat))
              )
          )
        )
      )
    }

  def createMeta(team: String, ls: LayerstackDefinition, reporter: MaterialReporter)(implicit
      ec: ExecutionContext
  ): Future[MetaInfo] =
    for {
      props <- collectMetaData(team, ls, reporter)
    } yield MetaInfo(props)

  def collectMetaData(team: String, stck: LayerstackDefinition, reporter: MaterialReporter)(implicit
      ec: ExecutionContext
  ): Future[Map[String, Property]] = {

    val fmats = Future.sequence(stck.stacks.map(ssdo =>
      ssdo.map { ssd =>
        FutureUtils.option(ssd.layers.map { lo =>
          Future.sequence(lo.map(ld =>
            FutureUtils.option(ld.materialRef.map(mid => reporter.getMaterial(team, mid))).map(_.flatten)
          ))
        })
      }
    ).getOrElse(Seq())).map(_.flatMap {
      case Some(x) => Seq(x)
      case None    => Seq()
    }).map(_.flatten).map(_.flatMap {
      case Some(x) => Seq(x)
      case None    => Seq()
    }).map(l => l.distinctBy(_.id).groupBy(_.id.get))

    for {
      copperCount <- fmats.map { mats =>
        stck.stacks.map(so => so.map(ssd => countCopper(team, ssd, mats)).maxBy(_.value)).getOrElse(DecimalProperty(
          MaterialProperties.CuCount,
          0
        ))
      }

      height <- fmats.map { mats =>
        stck.stacks.map {
          so =>
            val res: Seq[Double] = so.map(ssd => calculateHeight(team, ssd, mats))
            res.max
        }
          .getOrElse(0.0)
      }
    } yield Map(
      copperCount.name -> copperCount,
      DecimalProperty.e(LayerStackProperties.TotalHeight, BigDecimal(height))
    )

  }

  def countCopper(mat: Option[Material]): Int =
    mat.map(_.copperCount()).getOrElse(0)

  def countCopper(team: String, ssd: SubStackDefinition, mats: Map[String, Seq[Material]]): DecimalProperty = {

    val count = ssd.layers.map(layersOption =>
      layersOption.map {
        ld =>
          ld.materialRef match {
            case Some(ref) =>
              mats.get(ref) match {
                case Some(m) if m.nonEmpty => m.head.copperCount()
                case _ => throw new TransportException(TransportErrorCode.NotFound, s"Material $ref not found")
              }

            case None => ld.material.map(_.copperCount()).getOrElse(0)
          }
      }
        .sum
    ).getOrElse(0)

    DecimalProperty(MaterialProperties.CuCount, count)
  }

  def calculateHeight(team: String, ssd: SubStackDefinition, mats: Map[String, Seq[Material]]): Double =
    ssd.layers.map { x =>
      val res: Seq[Double] = x.map(ld =>
        ld.materialRef match {
          case Some(ref) => mats.get(ref) match {
              case Some(m) if m.nonEmpty => m.head.height()
              case _ => throw new TransportException(TransportErrorCode.NotFound, s"Material $ref not found")
            }

          case None => ld.material.map(_.height()).getOrElse(0)
        }

      //          .map(mid => mats.get(mid) match {
      //          case Some(m) if m.nonEmpty => m.head.height()
      //          case _ => throw new TransportException(TransportErrorCode.NotFound, s"Material $mid not found")
      //        }).getOrElse(0)
      )

      res.sum
    }
      .getOrElse(0)

}
