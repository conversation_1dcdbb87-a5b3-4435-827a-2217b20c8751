package de.fellows.ems.layerstack

import de.fellows.app.assembly.commons.{AbstractAssemblyReference, AssemblyReference}
import de.fellows.ems.pcb.model.{GerberFile, LayerFile}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.meta.{DecimalProperty, MetaInfo, Property, StringProperty}
import de.fellows.utils.{FilePath, UUIDUtils}
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID

package object api {

  case class IDContainer(id: UUID)

  case class LayerStacks(selected: Option[LayerStack]) {
    def toApi(implicit sd: ServiceDefinition): LayerStacksAPI =
      LayerStacksAPI(
        selected = selected.map(_.toApi)
      )
  }

  case class LayerStacksAPI(selected: Option[LayerStackAPI])

  case class LayerStackAPI(
      assembly: AbstractAssemblyReference,
      definition: LayerstackDefinitionAPI,
      stacks: Seq[SubStackAPI],
      unmatchedFiles: Seq[LayerFile]
  )

  case class SubStackAPI(definition: SubStackDefinition, layers: Seq[LayerAPI], metaInfo: MetaInfo)

  case class LayerAPI(definition: LayerDefinition, files: Option[Seq[LayerFile]], metaInfo: MetaInfo)

  case class LayerStack(
      assembly: AbstractAssemblyReference,
      definition: LayerstackDefinition,
      stacks: Seq[SubStack],
      unmatchedFiles: Seq[GerberFile]
  ) {
    def toApi(implicit sd: ServiceDefinition): LayerStackAPI =
      LayerStackAPI(
        assembly,
        definition.toApi,
        stacks.map(_.toApi),
        unmatchedFiles.map(_.toLayer)
      )

    def collectMaterials(filter: Material => Boolean): Seq[Material] =
      stacks.flatMap(ss => ss.layers).flatMap(_.definition.material).filter(filter)

    def calculatePrice: LayerstackPrices = {
      val value = collectMaterials(_ => true)
      LayerStack.sumPrices(value)

    }
  }

  case class SubStack(definition: SubStackDefinition, layers: Seq[Layer], metaInfo: Option[MetaInfo]) {
    def toApi(implicit sd: ServiceDefinition): SubStackAPI =
      SubStackAPI(
        definition,
        layers.map(_.toApi),
        metaInfo.getOrElse(MetaInfo())
      )
  }

  case class Layer(definition: LayerDefinition, files: Option[Seq[GerberFile]], metaInfo: Option[MetaInfo]) {
    def toApi(implicit sd: ServiceDefinition): LayerAPI =
      LayerAPI(
        definition,
        files.map(_.map(_.toLayer)),
        metaInfo.getOrElse(MetaInfo())
      )
  }

  case class Filter(f: Map[String, String])

  case class Material(
      team: Option[String],
      id: Option[String],
      name: Option[String],
      meta: Option[MetaInfo],
      materialType: Option[String]
  ) {
    def copperCount(): Int =
      meta.map { mi =>
        val cu = mi.lowerCaseProperties.get(MaterialProperties.CuThickness)
        cu match {
          case Some(thickness: DecimalProperty) if thickness.value > 0 => 1
          case _ =>
            Seq(
              mi.lowerCaseProperties.get(MaterialProperties.LowerCuThickness),
              mi.lowerCaseProperties.get(MaterialProperties.UpperCuThickness)
            ).flatten
              .map {
                case p: DecimalProperty if p.value > 0 => 1
                case _                                 => 0
              }
              .sum
        }
      }.getOrElse(0)

    def height(): Double =
      materialType.getOrElse("") match {
        case MaterialTypes.FOIL       => getDouble(MaterialProperties.CuThickness, 0)
        case MaterialTypes.PREPREG    => getDouble(MaterialProperties.BaseThickness, 0)
        case MaterialTypes.SOLDERMASK => getDouble(MaterialProperties.MaskThickness, 0)
        case MaterialTypes.COVERLAY   => getDouble(MaterialProperties.BaseThickness, 0)
        case MaterialTypes.FLEXCORE | MaterialTypes.CORE =>
          getDouble(MaterialProperties.UpperCuThickness, 0) +
            getDouble(MaterialProperties.LowerCuThickness, 0) +
            getDouble(MaterialProperties.BaseThickness, 0)

        case _ => Math.max(
            getDouble(MaterialProperties.BaseThickness, 0),
            getDouble(MaterialProperties.CuThickness, 0)
          )
      }

    private def getDouble(name: String, default: Double): Double = {
      meta.flatMap(_.properties.get(name)).flatMap {
        case dec:DecimalProperty => Some(dec.value.doubleValue)
        case p: StringProperty => p.value.toDoubleOption
      }.getOrElse(default)
    }
  }

  /** A library of materials
    * @param team
    *   the team owning the library
    * @param name
    *   the name of the library
    * @param id
    *   the id
    * @param supplier
    *   the supplier name
    * @param libraryType
    *   the material types in the library
    * @param materials
    *   the list of materials. depending on the `flat` property in various calls, this can be empty for efficiency.
    */
  case class Library(
      team: Option[String],
      name: String,
      id: Option[UUID],
      supplier: Option[String],
      libraryType: Option[String],
      materials: Option[Seq[String]]
  )

  case class LayerstackDefinitionDescription(
      team: String,
      id: UUID,
      name: String,
      copperCount: Int,
      stackType: String,
      metaInfo: MetaInfo,
      image: Option[String],
      price: api.LayerstackPrices
  )

  case class LayerstackDefinitionAPI(
      id: Option[UUID],
      team: Option[String],
      name: Option[String],
      stacks: Option[Seq[SubStackDefinition]],
      price: Option[BigDecimal],
      metaInfo: Option[MetaInfo],
      creation: Option[Instant],
      image: Option[String],
      default: Option[Boolean]
  ) {
    def toInternal: LayerstackDefinition =
      LayerstackDefinition(
        id.get,
        team,
        name.get,
        stacks,
        price,
        metaInfo.getOrElse(MetaInfo()),
        creation,
        None
      )
  }

  case class LayerstackDefinition(
      id: UUID,
      team: Option[String],
      name: String,
      stacks: Option[Seq[SubStackDefinition]],
      price: Option[BigDecimal],
      metaInfo: MetaInfo,
      creation: Option[Instant],
      image: Option[FilePath] = None
  ) {
    def toApi(implicit sd: ServiceDefinition): LayerstackDefinitionAPI =
      LayerstackDefinitionAPI(
        Some(id),
        team,
        Some(name),
        stacks,
        price,
        Some(metaInfo),
        creation,
        image.map(_.toApi),
        None
      )

    def countCopper: Int =
      this.stacks.map(so =>
        so.flatMap { s =>
          s.layers.map(lo =>
            lo.flatMap { l =>
              l.meta.map(meta =>
                meta.properties.get(MaterialProperties.CuCount) match {
                  case Some(x: DecimalProperty) => x.value.intValue
                  case _                        => 0
                }
              )
            }.sum // sum copper layers in this substack
          )
        }.max        // use the largest substack
      ).getOrElse(0) // no layers found => 0 copper layers

    def collectMaterials(filter: Material => Boolean): Seq[Material] =
      stacks.getOrElse(Seq()).flatMap(ss => ss.layers.getOrElse(Seq())).flatMap(_.material).filter(filter)

    def calculatePrice: LayerstackPrices = {
      val value = collectMaterials(_ => true)

      LayerStack.sumPrices(value)
    }

  }

  case class SubStackDefinition(
      name: String,
      layers: Option[Seq[LayerDefinition]],
      stackType: Option[String],
      drills: Option[Seq[DrillSupport]],
      offset: Option[Int]
  )

  type MaterialRef = String

  case class LayerDefinition(
      id: Option[UUID],
      layerType: Option[String],
      meta: Option[MetaInfo],
      materialRef: Option[MaterialRef], // id of the referenced material
      material: Option[Material]        // the actual material (only used for API communication)
  )

  case class DrillSupport(from: Int, to: Int, col: Option[Int] = Some(0), drillType: String)

  case class LayerstackPrices(unitPrice: Option[BigDecimal], areaPrice: Option[BigDecimal])

  object LayerstackPrices {
    implicit val format: Format[LayerstackPrices] = Json.format[LayerstackPrices]
  }

  object Filter {
    implicit val format: Format[Filter] = Json.format[Filter]
  }

  object Material {
    implicit val format: Format[Material] = Json.format[Material]
  }

  object Library {
    implicit val format: Format[Library] = Json.format[Library]

    def toLibrary(m: Map[String, String]): Library =
      Library(
        team = None,
        name = m("name"),
        id = m.get("id").map(sid => UUIDUtils.ofShort(sid)),
        supplier = m.get("supplier"),
        libraryType = m.get("libraryType"),
        materials = None
      )
  }

  object DrillSupport {
    implicit val format: Format[DrillSupport] = Json.format[DrillSupport]
  }

  object LayerDefinition {
    implicit val format: Format[LayerDefinition] = Json.format[LayerDefinition]
  }

  object SubStackDefinition {
    implicit val format: Format[SubStackDefinition] = Json.format[SubStackDefinition]
  }

  object LayerstackDefinition {
    implicit val format: Format[LayerstackDefinition] = Json.format[LayerstackDefinition]
  }

  object LayerstackDefinitionDescription {
    implicit val format: Format[LayerstackDefinitionDescription] = Json.format[LayerstackDefinitionDescription]
  }

  object LayerstackDefinitionAPI {
    implicit val format: Format[LayerstackDefinitionAPI] = Json.format[LayerstackDefinitionAPI]
  }

  object Layer {
    implicit val format: Format[Layer] = Json.format[Layer]
  }

  object SubStack {
    implicit val format: Format[SubStack] = Json.format[SubStack]
  }

  object LayerStack {
    implicit val format: Format[LayerStack] = Json.format[LayerStack]

    def sumPrices(value: Seq[Material]): LayerstackPrices = {

      val areaPrices =
        value.flatMap(_.meta.flatMap(x => x.get[Property](MaterialProperties.AreaPrice).flatMap(_.decimalValue)))
      val unitPrices =
        value.flatMap(_.meta.flatMap(x => x.get[Property](MaterialProperties.UnitPrice).flatMap(_.decimalValue)))

      LayerstackPrices(
        if (unitPrices.isEmpty) {
          None
        } else {
          Some(unitPrices.sum)
        },
        if (areaPrices.isEmpty) {
          None
        } else {
          Some(areaPrices.sum)
        }
      )
    }
  }

  object LayerStacks {
    implicit val format: Format[LayerStacks] = Json.format[LayerStacks]
  }

  object LayerAPI {
    implicit val format: Format[LayerAPI] = Json.format[LayerAPI]
  }

  object SubStackAPI {
    implicit val format: Format[SubStackAPI] = Json.format[SubStackAPI]
  }

  object LayerStackAPI {
    implicit val format: Format[LayerStackAPI] = Json.format[LayerStackAPI]
  }

  object LayerStacksAPI {
    implicit val format: Format[LayerStacksAPI] = Json.format[LayerStacksAPI]
  }

  object IDContainer {
    implicit val format: Format[IDContainer] = Json.format[IDContainer]
  }

}
