package de.fellows.ems.analysis.impl.entity

import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEvent, AggregateEventShards, AggregateEventTag }
import de.fellows.ems.analysis.api.Analysis

package object request {

  sealed trait RequestCommand

  sealed trait RequestEvent extends AggregateEvent[RequestEvent] {
    override def aggregateTag: AggregateEventShards[RequestEvent] = RequestEvent.Tag
  }

  object RequestEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[RequestEvent](NumShards)
  }

  case class CreateRequest(id: String) extends RequestCommand with ReplyType[Analysis]

  case class UpdateState(id: String, states: Map[String, String]) extends RequestCommand with ReplyType[Analysis]

  case class GetRequest(id: String) extends RequestCommand with ReplyType[Analysis]

  case class RequestCreated(analysis: Analysis) extends RequestEvent

  case class RequestStateChanged(analysis: Analysis) extends RequestEvent

}
