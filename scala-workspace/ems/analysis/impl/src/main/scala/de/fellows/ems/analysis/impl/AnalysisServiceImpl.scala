package de.fellows.ems.analysis.impl

import akka.NotUsed
import akka.actor.ActorSystem
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.assemby.api.{AssemblyCreation, AssemblyService, InternalAssemblyCreation}
import de.fellows.app.security.AccessControlServiceComposition.authorizedString
import de.fellows.ems.analysis.api
import de.fellows.ems.analysis.api.{AnalysisResult, AnalysisService, Features, StateUpdate}
import de.fellows.ems.analysis.impl.entity.request.{CreateRequest, GetRequest, RequestEntity, UpdateState}
import de.fellows.ems.dfm.api.DFMService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.utils.UUIDUtils
import de.fellows.utils.UUIDUtils._
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.meta._

import scala.concurrent.ExecutionContext

class AnalysisServiceImpl(
    ass: AssemblyService,
    pcb: PCBService,
    layerstackService: LayerstackService,
    dfmService: DFMService,
    as: ActorSystem,
    preg: PersistentEntityRegistry
)(implicit val c: ExecutionContext) extends AnalysisService with StackrateAPIImpl {
  implicit val ias: ActorSystem = as

  override def setAnalysisStatus(id: String): ServiceCall[StateUpdate, api.Analysis] =
    authorizedString { token =>
      s"analysis:${token.team}:${token.userId}:$id:*:update"
    } { (token, _) =>
      ServerServiceCall { su =>
        preg.refFor[RequestEntity](id).ask(UpdateState(id, su.states))
      }
    }

  def getAnalysisResult(id: String): ServerServiceCall[NotUsed, AnalysisResult] =
    authorizedString { token =>
      s"analysis:${token.team}:${token.userId}:$id:*:read"
    } { (token, _) =>
      ServerServiceCall { su =>
        preg.refFor[RequestEntity](id).ask(GetRequest(id)).flatMap { analysis =>
          for {
            assembly   <- ass._getAssembly(token.team, UUIDUtils.ofShort(id)).invoke().map(_.assembly)
            pcbVersion <- pcb._getPCBVersion(token.team, assembly.id, assembly.currentVersion.map(_.id).get).invoke()
            layerstack <- layerstackService._getPCBLayerstack(
              token.team,
              assembly.currentVersion.map(_.id).get,
              Some(false)
            ).invoke()
          } yield AnalysisResult(
            id = analysis.id,
            states = analysis.states,
            metaInfo = assembly.features.find(_ == Features.META).map(_ =>
              MetaInfo(pcbVersion.meta.map(_.properties).getOrElse(Map()) ++ layerstack.selected.map(
                _.definition.metaInfo.properties
              ).getOrElse(Map()))
            ),
            dfm = None
          )
        }
      }
    }

  override def requestAnalysis(): ServiceCall[api.AnalysisRequest, api.Analysis] =
    authorizedString { token =>
      s"analysis:${token.team}:${token.userId}:*:*:create"
    } { (token, _) =>
      val tcmd = TimelineCommand.of(token)
      ServerServiceCall { req =>
        val creation = InternalAssemblyCreation(
          assembly = AssemblyCreation(
            name = Some(req.name),
            gid = None,
            customer = None,
            description = None,
            project = None,
            mail = None
          ),
          features = Some(Seq(Features.META)),
          timelineCommand = Some(tcmd),
          withoutFiles = false
        )
        ass._createNewAssembly(token.userId.toString, token.team).invoke(creation).flatMap { ass =>
          val id = ass.id.short()
          preg.refFor[RequestEntity](id).ask(CreateRequest(id))
        }
      }
    }
}
