package de.fellows.ems.analysis.impl.entity.request

import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import de.fellows.ems.analysis.api.Analysis
import de.fellows.utils.common.EntityException

class RequestEntity extends PersistentEntity {
  override type Command = RequestCommand
  override type Event   = RequestEvent
  override type State   = Option[Analysis]

  override def initialState: Option[Analysis] = None

  def existing(a: Analysis) =
    Actions()
      .onReadOnlyCommand[GetRequest, Analysis] {
        case (x: GetRequest, ctx, s) =>
          ctx.reply(a)
      }
      .onReadOnlyCommand[CreateRequest, Analysis] {
        case (x: CreateRequest, ctx, s) =>
          ctx.commandFailed(EntityException("Request exist", TransportErrorCode.BadRequest.http))
      }
      .onCommand[UpdateState, Analysis] {
        case (x: UpdateState, ctx, s) =>
          val analysis = a.copy(states = a.states ++ x.states)
          ctx.thenPersist(RequestStateChanged(analysis))(_ => ctx.reply(analysis))
      }

  def missing() =
    Actions()
      .onReadOnlyCommand[GetRequest, Analysis] {
        case (x: GetRequest, ctx, s) =>
          ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Request does not exist"))
      }
      .onCommand[CreateRequest, Analysis] {
        case (x: CreateRequest, ctx, s) =>
          val analysis = Analysis(x.id, Map())
          ctx.thenPersist(RequestCreated(analysis))(_ => ctx.reply(analysis))
      }
      .onReadOnlyCommand[UpdateState, Analysis] {
        case (x: UpdateState, ctx, s) =>
          ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Request does not exist"))
      }

  override def behavior: Behavior = {
    case Some(x) => existing(x)
    case None    => missing()
  }

}
