package de.fellows.ems.dfm.impl.entity

import com.lightbend.lagom.scaladsl.playjson._
import de.fellows.ems.dfm.api.{ Violation, Violations }
import de.fellows.ems.dfm.impl.entity.violation._
import de.fellows.ems.renderer.api.FileReference

object JsonSerializerRegistry extends JsonSerializerRegistry {
  override def serializers =
    List(
      JsonSerializer[ViolationsResponse],
      JsonSerializer[Violation],
      JsonSerializer[Violations],
      JsonSerializer[SetViolations],
      JsonSerializer[StartAnalysis],
      JsonSerializer[StopAnalysis],
      JsonSerializer[GetViolations.type],
      JsonSerializer[DeleteViolation],
      JsonSerializer[SetViolationStates],
      JsonSerializer[ViolationsReset],
      JsonSerializer[ViolationElementAdded],
      JsonSerializer[ViolationAdded],
      JsonSerializer[ViolationDeleted],
      JsonSerializer[ViolationChanged],
      JsonSerializer[ViolationStatusChanged],
      <PERSON>son<PERSON>erializer[AnalysisChanged],
      JsonSerializer[DFMTimelineChanged]
    )

  override def migrations: Map[String, JsonMigration] =
    Map(
      JsonMigrations
        .renamed(
          fromClassName = "de.fellows.ems.dfm.api.FileReference",
          inVersion = 2,
          toClass = classOf[FileReference]
        )
    )
}
