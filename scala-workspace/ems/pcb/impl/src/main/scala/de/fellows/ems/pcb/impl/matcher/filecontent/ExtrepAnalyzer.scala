package de.fellows.ems.pcb.impl.matcher.filecontent

import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.impl.matcher.{FileMatch, FileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition

import java.nio.file.Files
import scala.jdk.CollectionConverters._

/** Reads a `*.EXTREP` file, which is an export report generated by some CAD Tools. Each line contains a file extension
  * and its filetype. For instance:
  *
  * {{{
  *
  *   *.gtl     Copper Top
  * }}}
  */
class ExtrepAnalyzer(implicit serviceDefinition: ServiceDefinition) extends FileMatcher(false) {
  override def id: String = "extrep"

  val service = serviceDefinition.name

  val confidence = VeryHighConfidence

  override def mime: Option[Seq[String]] = Some(Seq(null))

  val linePattern = "^\\.([A-Za-z0-9]+)\\s*(.*)$".r

  override def matchFile(
      filename: String,
      file: FilePath,
      mime: Option[String],
      allFiles: Seq[FilePath]
  ): Map[FilePath, Seq[FileMatch]] =
    if (filename.toLowerCase.endsWith(".extrep")) {
      val extensions = getMatchOptionsForExtensions(file)

      // only keep extensions that are at most once in the list.
      // Sometimes projects have an extrep file, but re-use extensions, which makes the extrep rather useless.
      // In this case, we ignore all extensions that have duplicates.
      val extensionMap: Map[String, Seq[MatchOption]] =
        extensions
          .filter { e =>
            extensions.count(_._1 == e._1) == 1
          }
          .toMap

      convertToFileMatch(allFiles, extensionMap)
    } else {
      Map()
    }

  private def convertToFileMatch(allFiles: Seq[FilePath], extensionMap: Map[String, Seq[MatchOption]]) =
    allFiles.flatMap { fp =>
      val fname = fp.filename.toLowerCase
      extensionMap.find(x => fname.endsWith(s".${x._1}"))
        .map(_._2)
        .map { opts =>
          fp ->
            opts.map { opt =>
              createMatch(
                opt.fileType,
                cat = opt match {
                  case _: GerberOption     => LayerConstants.Categories.gerber
                  case _: MechanicalOption => LayerConstants.Categories.mechanical
                },
                conf = Some(this.confidence + opt.confidenceModifier.getOrElse(0))
              )
            }
        }
    }.toMap

  private def getMatchOptionsForExtensions(file: FilePath) =
    Files.readAllLines(file.toJavaPath).asScala
      .flatMap { line =>
        linePattern.findFirstMatchIn(line) match {
          case None => None
          case Some(value) =>
            val b = value.group(1)

            val c = value.group(2)

            Keywords.getMatchOption(c).map(o => b.toLowerCase -> o)
        }
      }
}

object ExtrepAnalyzer {}
