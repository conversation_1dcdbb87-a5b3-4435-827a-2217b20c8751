package de.fellows.ems.pcb.impl.queue

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.ems.pcb.api.{FileMessage, PCBService, PCBStreamEvent}
import de.fellows.ems.pcb.impl.entity.pcb.{FileSet, FileTypesChanged, FileTypesSet, FilesSet, PCBEvent}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.ems.renderer.api.RendererUtils
import de.fellows.ems.renderer.api.job.{ConvertFileJobEntry, RenderBoardJobEntry, RenderFileJobEntry}
import de.fellows.utils.internal.{FileLifecycleStageName, LifecycleDeadline, LifecycleStageStatus, StageStatusName}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.RedisLog
import de.fellows.utils.redislog.jobs.{JobBuilder, JobType, QueuedJob}
import de.fellows.utils.streams.{EmptyMessage, StreamMessage, ValidMessage}
import redis.clients.jedis.CommandObject

import scala.concurrent.duration.{Deadline, DurationInt}
import scala.concurrent.{ExecutionContext, Future}
import com.datastax.driver.core.utils.UUIDs
import de.fellows.utils.redislog.CutOff
import com.typesafe.config.Config
import de.fellows.utils.TimeBasedUUIDUtils

class PCBEventQueue(
    readSide: RedisLog,
    jobBuilder: JobBuilder,
    assemblyService: AssemblyService,
    pcbservice: PCBService,
    cassandraSession: CassandraSession,
    config: Config
)(implicit
    val ctx: ExecutionContext
) extends ReadSideProcessor[PCBEvent] with StackrateLogging {

  def defaultDeadline: Deadline = LifecycleDeadline.in(10 minutes)

  def initLifecycles(fileName: String, assRef: AssemblyReference): Future[Done] =
    Future.sequence(Seq(
      initLifecycle(fileName, FileLifecycleStageName.Render, assRef),
      initLifecycle(fileName, FileLifecycleStageName.FileAnalysis, assRef)
    )).map(_ => Done)

  def initLifecycle(fileName: String, lcName: FileLifecycleStageName, assRef: AssemblyReference): Future[Done] = {
    val deadline = defaultDeadline
    setAsyncLifecycle(
      assemblyService,
      Seq(),
      assRef,
      fileName,
      lcName,
      StageStatusName.Waiting,
      System.currentTimeMillis(),
      Some(deadline)
    )
  }

  def setAsyncLifecycle(
      assService: AssemblyService,
      msgs: Seq[String],
      assRef: AssemblyReference,
      fileName: String,
      lcName: FileLifecycleStageName,
      statusName: StageStatusName,
      time: Long,
      deadline: Option[Deadline]
  ): Future[Done] =
    assService._updateFileLifecycle(
      assRef.team,
      assRef.id,
      Some(assRef.version),
      fileName,
      lcName.value,
      Some(time)
    ).invoke(
      LifecycleStageStatus(
        statusName,
        msgs,
        None,
        None,
        deadline
      )
    )

  def prepareStatements(): Future[Done] = Future.successful(Done)

  private def addToQueue(e: StreamMessage[PCBStreamEvent]): Future[Seq[CommandObject[_]]] =
    e match {
      case ValidMessage(FileMessage(ass, allFiles)) =>
        pcbservice._getPCBVersion(ass.team, ass.id, ass.version).invoke().flatMap { pcbv =>
          val files = allFiles
            .filter(f => RendererUtils.isRenderableMimeType(f.fType.mimeType))
            // In order to prevent duplicate work from happening, we filter out drill files that
            // have already been processed by the initial rendering process.
            //
            // We need this because the reconciliation process triggers a file type change (drill ranges)
            // which in turn triggers this logic. If we process drill files again, nothing will happen to
            // the results, but we will eventually trigger a reconciliation, analysis and dfm
            .filter(gf =>
              gf.fType.fileType match {
                case LayerConstants.DRILL | LayerConstants.PH_DRILL | LayerConstants.NPH_DRILL =>
                  // If the drills are already persisted, then there's no point in adding this file to the queue.
                  val isUnreconciledHolePersisted = pcbv.unreconciledHoles.exists(_.file == gf.id)
                  !isUnreconciledHolePersisted

                case _ => true
              }
            )

          val MIMES          = Seq(LayerConstants.NATIVE_EAGLE, LayerConstants.NATIVE_KICAD)
          val filesToConvert = allFiles.filter(file => MIMES.contains(file.fType.fileType))

          logger.info(s"[REDIS] adding to queue: ${files.map(_.name)}")

          Future.sequence(
            files.map(f => initLifecycles(f.name, ass)) ++
              filesToConvert.map(file => initLifecycle(file.name, FileLifecycleStageName.Render, ass))
          ).flatMap { _ =>
            val renderFileJobs = files.map { file =>
              val (jobId, commands) = jobBuilder.buildCreateJobCommands(
                jobDescription = RenderFileJobEntry(
                  ass = ass,
                  file = file,
                  allFileJobs = None
                ),
                queueName = "rendering",
                jobIdOption = None
              )

              (jobId, commands)
            }
            val (renderJobIds, renderJobCommands) = renderFileJobs.unzip

            val convertFileJobs = filesToConvert.map { file =>
              jobBuilder.buildCreateJobCommands(
                jobDescription = ConvertFileJobEntry(
                  ass = ass,
                  file = file
                ),
                queueName = "rendering",
                jobIdOption = None
              )
            }

            val (convertJobIds, convertJobCommands) = convertFileJobs.unzip

            val jobDependencies =
              renderJobIds.map(QueuedJob(_, JobType.JobTypeRender, "rendering")) ++
                convertJobIds.map(QueuedJob(_, JobType.JobTypeConvertNative, "rendering"))

            val (_, boardJobCommands) = jobBuilder.buildCreateJobCommands(
              jobDescription = RenderBoardJobEntry(
                assembly = ass,
                dependencies = jobDependencies
              ),
              queueName = "rendering",
              jobIdOption = None
            )

            Future.successful(renderJobCommands.flatten ++ convertJobCommands.flatten ++ boardJobCommands)
          }
        }

      case ValidMessage(_) => Future.successful(Seq())
      case EmptyMessage()  => Future.successful(Seq())
    }

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[PCBEvent] = {
    val processorID = "pcbqueue-v1.0"
    val cutOff      = TimeBasedUUIDUtils.getCuttOff(introducedAt = 1717167864000L, config)
    readSide.builder[PCBEvent](processorID, cutOff)
      .setEventHandler[FileSet](e => addToQueue(e.event.toStreamEvent))
      .setEventHandler[FilesSet](e => addToQueue(e.event.toStreamEvent))
      .setEventHandler[FileTypesSet](e => addToQueue(e.event.toStreamEvent))
      .setEventHandler[FileTypesChanged](e => addToQueue(e.event.toStreamEvent))
      .setOffsetStore(new CassandraOffsetStore(processorID, cassandraSession))
      .build()
  }

  override def aggregateTags: Set[AggregateEventTag[PCBEvent]] = PCBEvent.Tag.allTags
}
