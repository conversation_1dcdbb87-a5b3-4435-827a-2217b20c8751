//package de.fellows.ems.pcb.impl.layerstack
//
//import de.fellows.ems.pcb.model.GerberFile
//import de.fellows.ems.pcb.model.LayerConstants._
//
//object Strategies {
//
//  case class LayerStack(stack: Seq[Layer])
//
//  def check(files: Seq[GerberFile], counts: Map[String, (Int, Int)])(block: => Option[LayerStack])(implicit name: String) = {
//
//    val layers = files.groupBy(_.fType.fileType).map((x: (String, Seq[GerberFile])) => x._1 -> x._2.size)
//    if (counts.forall(e => {
//      layers.get(e._1) match {
//        case Some(v) => v >= e._2._1 && v <= e._2._2
//        case None => e._2._1 == 0
//      }
//    })) {
//      block
//    } else {
//      println(s"Layerstack strategy ${name} not applicable ")
//      None
//    }
//  }
//
//  def collectFiles(files: Seq[GerberFile], filter: Seq[String]) = {
//    files.filter(p => filter.contains(p.fType.fileType)).sortBy(gf => gf.fType.index.getOrElse(0)).map(toLayer)
//  }
//
//
//  private val CORE = Layer(material = Some("core"))
//  private val DIELECTRIC = Layer(material = Some("dielectric"))
//
//  def default2layer: Seq[GerberFile] => Option[LayerStack] = { files =>
//    implicit val f = files
//    implicit val name = "Default 2 Layer"
//
//    check(files, Map(
//      PASTE_TOP -> (0, 1),
//      SILKSCREEN_TOP -> (0, 1),
//      SOLDERMASK_TOP -> (0, 1),
//      COPPER_TOP -> (1, 1),
//      COPPER_MID -> (0, 0),
//      PLANE_MID -> (0, 0),
//      COPPER_BOTTOM -> (1, 1),
//      SOLDERMASK_BOTTOM -> (0, 1),
//      SILKSCREEN_BOTTOM -> (0, 1),
//      PASTE_BOTTOM -> (0, 1),
//    )){
//
//      Some(LayerStack(Seq(
//        toLayer(PASTE_TOP, thickness = Some(10)),
//        toLayer(SILKSCREEN_TOP, thickness = Some(10)),
//        toLayer(SOLDERMASK_TOP, thickness = Some(10)),
//        toLayer(COPPER_TOP, thickness = Some(35)),
//        Some(CORE.copy(thickness = Some(1480))),
//        toLayer(COPPER_BOTTOM, thickness = Some(35)),
//        toLayer(SOLDERMASK_BOTTOM, thickness = Some(10)),
//        toLayer(SILKSCREEN_BOTTOM, thickness = Some(10)),
//        toLayer(PASTE_BOTTOM, thickness = Some(10)),
//      ).flatten))
//    }
//  }
//
//
//  def default4layer: Seq[GerberFile] => Option[LayerStack] = { files =>
//    implicit val f = files
//    implicit val name = "Default 4 Layer"
//    check(files, Map(
//      PASTE_TOP -> (0, 1),
//      SILKSCREEN_TOP -> (0, 1),
//      SOLDERMASK_TOP -> (0, 1),
//      COPPER_TOP -> (1, 1),
//      //      COPPER_MID -> (2, 2),
//      COPPER_BOTTOM -> (1, 1),
//      SOLDERMASK_BOTTOM -> (0, 1),
//      SILKSCREEN_BOTTOM -> (0, 1),
//      PASTE_BOTTOM -> (0, 1),
//    )){
//      collectFiles(files, Seq(COPPER_MID, PLANE_MID)) match {
//        case x if x.size == 2 =>
//          Some(LayerStack(Seq(
//            toLayer(PASTE_TOP, thickness = Some(10)),
//            toLayer(SILKSCREEN_TOP, thickness = Some(10)),
//            toLayer(SOLDERMASK_TOP, thickness = Some(10)),
//            toLayer(COPPER_TOP, thickness = Some(35)),
//            Some(DIELECTRIC.copy(thickness = Some(288))),
//            Some(x(0).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(710))),
//            Some(x(1).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(288))),
//            toLayer(COPPER_BOTTOM, thickness = Some(35)),
//            toLayer(SOLDERMASK_BOTTOM, thickness = Some(10)),
//            toLayer(SILKSCREEN_BOTTOM, thickness = Some(10)),
//            toLayer(PASTE_BOTTOM, thickness = Some(10)),
//          ).flatten))
//
//        case _ => None
//      }
//
//
//    }
//  }
//
//  def default6layer: Seq[GerberFile] => Option[LayerStack] = { files =>
//    implicit val f = files
//    implicit val name = "Default 6 Layer"
//    check(files, Map(
//      PASTE_TOP -> (0, 1),
//      SILKSCREEN_TOP -> (0, 1),
//      SOLDERMASK_TOP -> (0, 1),
//      COPPER_TOP -> (1, 1),
//      //      COPPER_MID -> (4, 4),
//      COPPER_BOTTOM -> (1, 1),
//      SOLDERMASK_BOTTOM -> (0, 1),
//      SILKSCREEN_BOTTOM -> (0, 1),
//      PASTE_BOTTOM -> (0, 1),
//    )){
//
//      collectFiles(files, Seq(COPPER_MID, PLANE_MID)) match {
//        case x if x.size == 4 =>
//          Some(LayerStack(Seq(
//            toLayer(PASTE_TOP, thickness = Some(10)),
//            toLayer(SILKSCREEN_TOP, thickness = Some(10)),
//            toLayer(SOLDERMASK_TOP, thickness = Some(10)),
//            toLayer(COPPER_TOP, thickness = Some(35)),
//            Some(DIELECTRIC.copy(thickness = Some(123))),
//            Some(x(0).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(410))),
//            Some(x(1).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(195))),
//            Some(x(2).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(410))),
//            Some(x(3).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(123))),
//            toLayer(COPPER_BOTTOM, thickness = Some(35)),
//            toLayer(SOLDERMASK_BOTTOM, thickness = Some(10)),
//            toLayer(SILKSCREEN_BOTTOM, thickness = Some(10)),
//            toLayer(PASTE_BOTTOM, thickness = Some(10)),
//          ).flatten))
//
//        case _ =>
//          None
//      }
//
//    }
//  }
//
//  def default8layer: Seq[GerberFile] => Option[LayerStack] = { files =>
//    implicit val f = files
//    implicit val name = "Default 8 Layer"
//    check(files, Map(
//      PASTE_TOP -> (0, 1),
//      SILKSCREEN_TOP -> (0, 1),
//      SOLDERMASK_TOP -> (0, 1),
//      COPPER_TOP -> (1, 1),
//      //      COPPER_MID -> (6, 6),
//      COPPER_BOTTOM -> (1, 1),
//      SOLDERMASK_BOTTOM -> (0, 1),
//      SILKSCREEN_BOTTOM -> (0, 1),
//      PASTE_BOTTOM -> (0, 1),
//    )){
//      collectFiles(files, Seq(COPPER_MID, PLANE_MID)) match {
//        case x if x.size == 6 =>
//          Some(LayerStack(Seq(
//            toLayer(PASTE_TOP, thickness = Some(10)),
//            toLayer(SILKSCREEN_TOP, thickness = Some(10)),
//            toLayer(SOLDERMASK_TOP, thickness = Some(10)),
//            toLayer(COPPER_TOP, thickness = Some(35)),
//            Some(DIELECTRIC.copy(thickness = Some(123))),
//            Some(x(0).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(250))),
//            Some(x(1).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(105))),
//            Some(x(2).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(250))),
//            Some(x(3).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(105))),
//            Some(x(4).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(250))),
//            Some(x(5).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(123))),
//            toLayer(COPPER_BOTTOM, thickness = Some(35)),
//            toLayer(SOLDERMASK_BOTTOM, thickness = Some(10)),
//            toLayer(SILKSCREEN_BOTTOM, thickness = Some(10)),
//            toLayer(PASTE_BOTTOM, thickness = Some(10)),
//          ).flatten))
//
//        case _ => None
//      }
//    }
//  }
//
//
//  def default10layer: Seq[GerberFile] => Option[LayerStack] = { files =>
//    implicit val f = files
//    implicit val name = "Default 10 Layer"
//    check(files, Map(
//      PASTE_TOP -> (0, 1),
//      SILKSCREEN_TOP -> (0, 1),
//      SOLDERMASK_TOP -> (0, 1),
//      COPPER_TOP -> (1, 1),
//      //      COPPER_MID -> (8, 8),
//      COPPER_BOTTOM -> (1, 1),
//      SOLDERMASK_BOTTOM -> (0, 1),
//      SILKSCREEN_BOTTOM -> (0, 1),
//      PASTE_BOTTOM -> (0, 1),
//    )){
//      collectFiles(files, Seq(COPPER_MID, PLANE_MID)) match {
//        case x if x.size == 8 =>
//          Some(LayerStack(Seq(
//            toLayer(PASTE_TOP, thickness = Some(10)),
//            toLayer(SILKSCREEN_TOP, thickness = Some(10)),
//            toLayer(SOLDERMASK_TOP, thickness = Some(10)),
//            toLayer(COPPER_TOP, thickness = Some(35)),
//            Some(DIELECTRIC.copy(thickness = Some(165))),
//            Some(x(0).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(100))),
//            Some(x(1).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(110))),
//            Some(x(2).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(100))),
//            Some(x(3).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(110))),
//            Some(x(4).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(100))),
//            Some(x(5).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(110))),
//            Some(x(6).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(100))),
//            Some(x(7).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(165))),
//            toLayer(COPPER_BOTTOM, thickness = Some(35)),
//            toLayer(SOLDERMASK_BOTTOM, thickness = Some(10)),
//            toLayer(SILKSCREEN_BOTTOM, thickness = Some(10)),
//            toLayer(PASTE_BOTTOM, thickness = Some(10)),
//          ).flatten))
//        case _ => None
//      }
//    }
//  }
//
//  def default12layer: Seq[GerberFile] => Option[LayerStack] = { files =>
//    implicit val f = files
//    implicit val name = "Default 12 Layer"
//    check(files, Map(
//      PASTE_TOP -> (0, 1),
//      SILKSCREEN_TOP -> (0, 1),
//      SOLDERMASK_TOP -> (0, 1),
//      COPPER_TOP -> (1, 1),
//      //      COPPER_MID -> (8, 8),
//      COPPER_BOTTOM -> (1, 1),
//      SOLDERMASK_BOTTOM -> (0, 1),
//      SILKSCREEN_BOTTOM -> (0, 1),
//      PASTE_BOTTOM -> (0, 1),
//    )){
//      collectFiles(files, Seq(COPPER_MID, PLANE_MID)) match {
//        case x if x.size == 10 =>
//          Some(LayerStack(Seq(
//            toLayer(PASTE_TOP, thickness = Some(10)),
//            toLayer(SILKSCREEN_TOP, thickness = Some(10)),
//            toLayer(SOLDERMASK_TOP, thickness = Some(10)),
//            toLayer(COPPER_TOP, thickness = Some(35)),
//            Some(DIELECTRIC.copy(thickness = Some(110))),
//            Some(x(0).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(110))),
//            Some(x(1).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(110))),
//            Some(x(2).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(110))),
//            Some(x(3).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(110))),
//            Some(x(4).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(110))),
//            Some(x(5).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(110))),
//            Some(x(6).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(110))),
//            Some(x(7).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(110))),
//            Some(x(8).copy(thickness = Some(35))),
//            Some(CORE.copy(thickness = Some(100))),
//            Some(x(9).copy(thickness = Some(35))),
//            Some(DIELECTRIC.copy(thickness = Some(110))),
//            toLayer(COPPER_BOTTOM, thickness = Some(35)),
//            toLayer(SOLDERMASK_BOTTOM, thickness = Some(10)),
//            toLayer(SILKSCREEN_BOTTOM, thickness = Some(10)),
//            toLayer(PASTE_BOTTOM, thickness = Some(10)),
//          ).flatten))
//        case _ => None
//      }
//    }
//  }
//
//  def isInverted(file: GerberFile): Boolean = {
//    val invertedTypes = Seq(PLANE_MID, SOLDERMASK_TOP, SOLDERMASK_BOTTOM)
//    invertedTypes.contains(file.fType.fileType)
//  }
//
//  def toLayer(gf: GerberFile): Layer = {
//    Layer(
//      file = Some(gf.id),
//      material = None,
//      inverted = isInverted(gf)
//
//    )
//  }
//
//  def toLayer(fType: String, thickness: Option[BigDecimal] = None)(implicit files: Seq[GerberFile]): Option[Layer] = {
//    files.find(f => f.fType.fileType == fType).map(toLayer).map(_.copy(thickness = thickness))
//  }
//}
