package de.fellows.ems.pcb.impl.entity.pcb

import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import de.fellows.utils.JsonFormats.singletonFormat
import de.fellows.utils.communication.ServiceError
import play.api.libs.json.Format

object PCBVersionChanged
    extends ServiceError(1, "PCB Version Cannot change", transportError = TransportErrorCode.NotAcceptable) {
  @transient implicit val format: Format[PCBVersionChanged.type] = singletonFormat(PCBVersionChanged)
}

object FileNotFound extends ServiceError(1, "File Does Not Exist", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[FileNotFound.type] = singletonFormat(FileNotFound)
}

object MetaNotFound extends ServiceError(3, "Metainfo Does Not Exist", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[MetaNotFound.type] = singletonFormat(MetaNotFound)
}

object DFMInProgress
    extends ServiceError(4, "DFM Analysis is in progress", transportError = TransportErrorCode.PolicyViolation) {
  @transient implicit val format: Format[DFMInProgress.type] = singletonFormat(DFMInProgress)
}

object NoDFMInProgress
    extends ServiceError(4, "No DFM Analysis is in progress", transportError = TransportErrorCode.PolicyViolation) {
  @transient implicit val format: Format[NoDFMInProgress.type] = singletonFormat(NoDFMInProgress)
}
