// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.ems.pcb.impl

import akka.actor.ActorSystem
import akka.persistence.query.Offset
import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.datastax.driver.core.utils.UUIDs
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{
  Forbidden,
  MessageProtocol,
  ResponseHeader,
  TransportErrorCode,
  TransportException
}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntity, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.{PlayServiceCall, ServerServiceCall}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.AssemblyLifecycleStageName.SpecificationRender
import de.fellows.app.assemby.api.{
  Assembly,
  AssemblyCreation,
  AssemblyDescription,
  AssemblyLifecycleStage,
  AssemblyLifecycleStageName,
  AssemblyService,
  AssemblyUpdate,
  AssemblyUtils,
  CloneParameters,
  InternalAssemblyCreation,
  InternalAssemblyUpdate,
  InternalFile,
  VersionDescription
}
import de.fellows.app.security.AccessControlServiceComposition.{authorizedString, authorizedStringWithToken}
import de.fellows.app.security.CombinedTokenAccessServiceComposition.auth
import de.fellows.app.security.SecurityBodyParser
import de.fellows.app.user.api.TemplateAPI.RenderRequest
import de.fellows.app.user.api.UserService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBApi.{CollectedPCB, SetPreviews}
import de.fellows.ems.pcb.api.PCBV2Api.{
  DuplicatePcb,
  MatchablePcbProperty,
  PCBV2,
  PCBV2File,
  PropertyMatch,
  SimilarFileWithScore
}
import de.fellows.ems.pcb.api._
import de.fellows.ems.pcb.api.specification.units.LengthUnit.Millimeter
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.{
  PCBPreviews,
  PCBV2BasicBoardProperties,
  PCBV2Specification,
  PCBV2SpecificationCapabilities,
  PCBV2SpecificationStatus,
  PCBV2SpecificationStatusUpdateRequest,
  PCBV2Update
}
import de.fellows.ems.pcb.impl.entity.pcb
import de.fellows.ems.pcb.impl.entity.pcb._
import de.fellows.ems.pcb.impl.entity.specification.SpecificationCommands._
import de.fellows.ems.pcb.impl.entity.specification.SpecificationEvents._
import de.fellows.ems.pcb.impl.entity.specification.{SpecificationCommands, SpecificationEntity}
import de.fellows.ems.pcb.impl.svix.SvixEvents.PCBSpecificationChanged
import de.fellows.ems.pcb.model
import de.fellows.ems.pcb.model.DFM.Properties
import de.fellows.ems.pcb.model.DFM.Properties.Settings
import de.fellows.ems.pcb.model._
import de.fellows.ems.renderer.api.RendererMetaInfo
import de.fellows.ems.renderer.api.job.RenderSpecificationJobEntry
import de.fellows.utils.apidoc.{StackrateAPIImpl, StackrateApiExtension}
import de.fellows.utils.collaboration.{TimelineCommand, TimelineEvent}
import de.fellows.utils.communication.TransportErrors._
import de.fellows.utils.communication.{ServiceDefinition, ServiceError, ServiceException, TransportExceptionHelper}
import de.fellows.utils.entities.CollaborativeEventInfo
import de.fellows.utils.internal.{DuplicateResults, File, LifecycleStageStatus}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta._
import de.fellows.utils.playutils.FileHelper
import de.fellows.utils.redislog.jobs.JobBuilder
import de.fellows.utils.security.{
  Auth0Token,
  AuthenticationServiceComposition,
  FileSecurityClaim,
  FileTokenContent,
  GenericTokenContent,
  JwtTokenUtil,
  TokenContent,
  TokenResponse
}
import de.fellows.utils.streams.StreamMessage
import de.fellows.utils.svix.SvixHelper
import de.fellows.utils.{FilePath, FutureUtils, SerializedCompressedFile, UUIDUtils}
import play.api.Logging
import play.api.http.FileMimeTypes
import play.api.libs.json.Json
import play.api.mvc.{DefaultActionBuilder, EssentialAction, PlayBodyParsers, Result, Results}

import java.io
import java.nio.file.Files
import java.time.{Instant, ZoneId, ZonedDateTime}
import java.util.{Base64, UUID}
import scala.collection.immutable
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.reflect.ClassTag
import de.fellows.utils.UUIDUtils.UUIDImprovements
import de.fellows.luminovo.client.customparts.alerts.CustomPartsAlertsService
import de.fellows.luminovo.client.customparts.alerts.model.UpsertAlertRequest
import de.fellows.luminovo.client.customparts.alerts.model.GeneratedOutline
import java.awt.geom.Rectangle2D
import de.fellows.luminovo.client.customparts.alerts.model.Active
import de.fellows.luminovo.client.customparts.alerts.model.Resolved
import de.fellows.luminovo.client.customparts.alerts.CustomPartsAlerts
import scala.util.Success
import scala.util.Failure
import de.fellows.luminovo.client.customparts.alerts.model.Pcb
import de.fellows.luminovo.client.customparts.alerts.model.InconclusiveDimensions
import de.fellows.luminovo.client.customparts.alerts.model.CustomPartAlertStatus
import de.fellows.luminovo.client.customparts.alerts.model.NoOutlineFile
import de.fellows.luminovo.client.customparts.alerts.CustomPartReference
import de.fellows.luminovo.client.customparts.alerts.CustomPartLumiquoteReference

@StackrateApiExtension(
  classes = Array(classOf[PCBServiceFileUploadAPI])
)
class PCBServiceImpl(
    system: ActorSystem,
    eReg: PersistentEntityRegistry,
    action: DefaultActionBuilder,
    parser: PlayBodyParsers,
    mime: FileMimeTypes,
    pcbRep: PCBRepository,
    specRepo: SpecificationRepository,
    assService: AssemblyService,
    layerstackService: LayerstackService,
    panelService: PanelService,
    userService: UserService,
    jobBuilder: JobBuilder,
    alerts: CustomPartsAlerts
)(implicit ctx: ExecutionContext, sd: ServiceDefinition)
    extends PCBService with StackrateLogging
    with StackrateAPIImpl {
  implicit val actorSystem: ActorSystem = system
  implicit val m: FileMimeTypes         = mime

  private def refFor[T <: PersistentEntity: ClassTag](id: UUID) = eReg.refFor[T](id.toString)

  override def setMeta(assembly: UUID, version: UUID): ServiceCall[MetaInfo, MetaInfo] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:meta:write"
    ) { (token, _) =>
      val allowed = RendererMetaInfo.WriteableInfo.ALL
      val tcmd    = TimelineCommand.of(token)
      ServerServiceCall { updates =>
        updates.properties.foreach { e =>
          if (!allowed.contains(e._1)) {
            throw new TransportException(TransportErrorCode.NotAcceptable, s"Property ${e._1} can not be set")
          }
        }
        _doSetMetaInfoProperties(
          token.team,
          version,
          updates.properties.values.toSeq,
          Some(token.userId),
          true,
          tcmd
        ).map(
          _.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "not found"))
        )
      }
    }

  override def removeMeta(assembly: UUID, version: UUID): ServiceCall[Seq[String], MetaInfo] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:meta:write"
    ) { (token, _) =>
      val tcmd = TimelineCommand.of(token)
      ServerServiceCall { updates =>
        _doRemoveMetaInfoProperties(token.team, version, updates, Some(token.userId), tcmd).map(
          _.getOrElse(TransportErrorCode.NotFound ! "not found")
        )
      }
    }

  override def setFileMetaInfoProperty(assembly: UUID, version: UUID, file: UUID): ServiceCall[MetaInfo, MetaInfo] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:meta:write"
    ) { (token, _) =>
      ServerServiceCall { props =>
        _doSetFileMetaInfoProperties(token.team, version, file, props.properties.values.toSeq, Some(token.userId)).map(
          _.getOrElse(TransportErrorCode.NotFound ! "not found")
        )
      }
    }

  override def removeFileMetaInfoProperty(
      assembly: UUID,
      version: UUID,
      file: UUID
  ): ServiceCall[Seq[String], MetaInfo] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:meta:write"
    ) { (token, _) =>
      ServerServiceCall { props =>
        _doRemoveFileMetaInfoProperties(token.team, version, file, props, Some(token.userId)).map(
          _.getOrElse(TransportErrorCode.NotFound ! "not found")
        )
      }
    }

  def resolveVersion(assembly: String, token: TokenContent): UUID =
    // TODO ST-640
    Await.result(
      assService._findAssembly(token.team, assembly).invoke(NotUsed).map(_.currentVersion.map(_.id).getOrElse(
        throw new TransportException(TransportErrorCode.NotFound, "not found")
      )),
      10 minutes
    )

  override def getLatestMeta(id: String): ServiceCall[NotUsed, MetaInfo] = {
    var assembly: Option[UUID] = None

    authorizedString { token =>
      val vassembly = resolveVersion(id, token)
      assembly = Some(vassembly)
      s"pcb:${token.team}:${token.team}:${vassembly.version}:*:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        _getMeta(token.team, assembly.get)
      }
    }

  }

  override def getMeta(assembly: UUID, version: UUID): ServiceCall[NotUsed, MetaInfo] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:stack:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _getMeta(token.team, version)
      }
    }

  private def _getMeta(team: String, version: UUID) =
    refFor[PCBEntity](version).ask(GetVersion(team, version)).map { pcbv =>
      pcbv.meta.getOrElse(MetaInfo())
    }

  private def _getOutline(team: String, version: UUID): Future[Option[Outline]] =
    refFor[PCBEntity](version).ask(GetVersion(team, version))
      .map { v =>
        v.outline
      }

  private def _getOutlineCandidates(team: String, version: UUID): Future[(Seq[Outline], Option[Outline])] =
    refFor[PCBEntity](version).ask(GetVersion(team, version))
      .map { v =>
        (v.outlineCandidates, v.outline)
      }

  override def setOutline(assembly: UUID, version: UUID): ServiceCall[model.SetOutline, Done] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:outline:write"
    ) { (token, _) =>
      ServerServiceCall { so =>
        _doSetOutline(token.team, assembly, version, so, TimelineCommand.of(token))
      }
    }

  private def toApi(o: Outline, withGraphic: Option[Boolean]): APIOutline =
    withGraphic match {
      case Some(true) =>
        GraphicUtils.readFile(o.path) match {
          case Some(value) => o.toApi(value)
          case None        => o.toApi
        }
      case _ => o.toApi
    }

  override def getSharedOutline(share: UUID, withGraphic: Option[Boolean]): ServiceCall[NotUsed, APIOutline] =
    auth {
      case _: Auth0Token => "view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$share:outline:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        for {
          validatedShare <- this.assService._getAssemblyShare(token.getTeam, share).invoke()
          outline <- _getOutline(token.getTeam, validatedShare.share.ref.version).map(_.map { o =>
            toApi(o, withGraphic)
          }
            .getOrElse(throw new TransportException(TransportErrorCode.NotFound, "No Outline found")))
        } yield outline
      }
    }

  override def getOutline(
      assembly: UUID,
      version: UUID,
      withGraphic: Option[Boolean]
  ): ServiceCall[NotUsed, APIOutline] =
    auth {
      // If the auth token comes from lumiquote, we need a different permission check.
      // pcb permission is not part of auth0 token permissions
      case _: Auth0Token => "view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$version:outline:read"
    } { (token, _) =>
      ServerServiceCall { so =>
        _getOutline(token.getTeam, version).map(_.map { o =>
          toApi(o, withGraphic)

        }
          .getOrElse(throw new TransportException(TransportErrorCode.NotFound, "No Outline found")))
      }
    }

  override def _getOutline(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Outline] =
    ServerServiceCall { so =>
      _getOutline(team, version).map(_.getOrElse(throw new TransportException(
        TransportErrorCode.NotFound,
        "No Outline found"
      )))
    }

  override def getOutlineCandidate(
      assembly: UUID,
      version: UUID,
      candidate: UUID,
      withGraphic: Option[Boolean]
  ): ServiceCall[NotUsed, APIOutline] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:outline:read"
    ) { (token, _) =>
      ServerServiceCall { so =>
        _doGetOutlineCandidate(token.getTeam, version, candidate, withGraphic)
      }
    }

  override def getOutlineCandidateBySpecification(
      assembly: UUID,
      version: UUID,
      specification: String
  ): ServiceCall[NotUsed, Graphic] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:outline:read"
    ) { (token, _) =>
      ServerServiceCall { so =>
        _doGetOutlineCandidateBySpecification(token.getTeam, assembly, version, specification)
      }
    }

  private def _doGetOutlineCandidateBySpecification(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: String
  ): Future[Graphic] =
    for {
      spec  <- _doGetPCBV2Specification(team, version, specification)
      pcbv2 <- _doGetPCBV2(team, version)
    } yield {
      val s      = Seq(spec.settings, pcbv2.properties)
      val height = s.flatMap(_.board.basic.boardHeight).headOption
      val width  = s.flatMap(_.board.basic.boardWidth).headOption

      val scale = 100
      (height, width) match {
        case (Some(_h), Some(_w)) =>
          val h = _h.to(Millimeter).doubleValue * scale
          val w = _w.to(Millimeter).doubleValue * scale

          val ol =
            new Rectangle2D.Double(0, 0, w, h)
          // svg path for a rectangle
          val path = s"M 0 0 H $w V $h H 0 Z"

          val dims = Dimension.of(ol)
          Graphic(
            viewbox = dims,
            format = Format(
              unit = "mm",
              resolution = 0.0001,
              dimension = Some(dims),
              scaling = Some(scale),
              gerberscale = None
            ),
            count = 1,
            paths = Seq(Seq(GraphicElement(
              path = Some(path),
              tag = None,
              use = None,
              attributes = None
            ))),
            defs = Seq()
          )

        case _ => throw new TransportException(TransportErrorCode.NotFound, "No Outline found")
      }
    }

  private def _doGetOutlineCandidate(
      team: String,
      version: UUID,
      candidate: UUID,
      withGraphic: Option[Boolean]
  ): Future[APIOutline] =
    _getOutlineCandidates(team, version).map(cs =>
      (candidate match {
        case UUIDUtils.nil =>
          // backwards compatibility: may have nil-ids for the current outline
          cs._2.map(toApi(_, withGraphic))
        case validId =>
          cs._1.find(_.id == validId).map { o =>
            toApi(o, withGraphic)
          }
      })
        .getOrElse(throw new TransportException(TransportErrorCode.NotFound, "No Outline found"))
    )

  override def getSharedOutlineCandidate(
      share: UUID,
      candidate: UUID,
      withGraphic: Option[Boolean]
  ): ServiceCall[NotUsed, APIOutline] =
    auth {
      // If the auth token comes from lumiquote, we need a different permission check.
      // pcb permission is not part of auth0 token permissions
      case _: Auth0Token => "view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$share:outline:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        this.assService._getAssemblyShare(token.getTeam, share).invoke().flatMap { assembly =>
          _doGetOutlineCandidate(assembly.share.ref.team, assembly.share.ref.version, candidate, withGraphic)
        }
      }

    }

  override def getSharedOutlineCandidates(
      share: UUID,
      withGraphic: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[APIOutline]] =
    auth {
      // If the auth token comes from lumiquote, we need a different permission check.
      // pcb permission is not part of auth0 token permissions
      case _: Auth0Token => "view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$share:outline:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        this.assService._getAssemblyShare(token.getTeam, share).invoke().flatMap { validatedShare =>
          _getOutlineCandidates(validatedShare.share.ref.team, validatedShare.share.ref.version).map(_._1.map { o =>
            toApi(o, withGraphic)
          })
        }
      }
    }

  override def getOutlineCandidates(
      assembly: UUID,
      version: UUID,
      withGraphic: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[APIOutline]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:outline:read"
    ) { (token, _) =>
      ServerServiceCall { so =>
        _getOutlineCandidates(token.getTeam, version).map(_._1.map { o =>
          toApi(o, withGraphic)
        })
      }
    }

  override def _setOutline(team: String, assembly: UUID, version: UUID): ServerServiceCall[model.SetOutline, Done] =
    ServerServiceCall { so =>
      _doSetOutline(team, assembly, version, so, TimelineCommand.system)
    }

  private def setAlertForOutline(team: String, version: UUID, ol: Outline): Future[Done] =
    _doGetPCBV2WithAssembly(team, version).flatMap {
      case (pcbv2, assembly) if assembly.externalReference.isDefined =>
        val stateForGeneratedOutline =
          if (ol.file.isEmpty) { Active }
          else { Resolved }

        val stateForNoOutlineFile: CustomPartAlertStatus =
          if (ol.file.isEmpty) {
            Resolved
          } else {
            pcbv2.files.getOrElse(Seq.empty).find(_.id == ol.file.get).map { f =>
              if (f.fileType.category != LayerConstants.Categories.mechanical) {
                Active
              } else {
                Resolved
              }
            }.getOrElse(Resolved)
          }

        val lqReference = assembly.externalReference.map { lq =>
          CustomPartLumiquoteReference(
            rfqId = lq.rfqId,
            assemblyId = lq.assemblyId
          )
        }
        val customPartReference = CustomPartReference(team, version, lqReference)

        for {
          _ <- alerts.upsert(customPartReference, Pcb(NoOutlineFile), stateForNoOutlineFile)
          _ <- alerts.upsert(customPartReference, Pcb(GeneratedOutline), stateForGeneratedOutline)
        } yield Done

      case _ => Future.successful(Done)
    }

  override def _selectBestOutlineCandidate(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServerServiceCall[NotUsed, Outline] =
    ServerServiceCall { so =>
      val entity = refFor[PCBEntity](version)

      for {
        v        <- entity.ask(GetVersion(team, version))
        selected <- Future.successful(v.outlineCandidates.sortBy(_.score).lastOption)
        s <- FutureUtils.option(selected.map { ol =>
          for {

            ol <- _doSetOutline(team, assembly, version, SetOutline(ol.id, Some(false)), TimelineCommand.system)
          } yield ol
        })
      } yield selected.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "No outline found"))
    }

  override def _setOutlineCandidates(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServerServiceCall[Seq[Outline], Done] =
    ServerServiceCall { so =>
      val ref        = AssemblyReference(team, assembly, None, version)
      val versionent = refFor[PCBEntity](version)
      versionent.ask(SetOutlineCandidates(team, ref, so, TimelineCommand.system))
    }

  override def _addOutlineCandidates(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServerServiceCall[Seq[Outline], Done] =
    ServerServiceCall { so =>
      val ref        = AssemblyReference(team, assembly, None, version)
      val versionent = refFor[PCBEntity](version)
      versionent.ask(AddOutlineCandidates(team, ref, so, TimelineCommand.system))
    }

  private def _doSetOutline(
      team: String,
      assembly: UUID,
      version: UUID,
      so: SetOutline,
      tcmd: TimelineCommand
  ): Future[Done.type] = {
    val ref        = AssemblyReference(team, assembly, None, version)
    val versionent = refFor[PCBEntity](version)
    versionent.ask(GetVersion(team, version)).flatMap(_.outlineCandidates.find(_.id == so.candidate) match {
      case Some(f) =>
        for {

          ol <- versionent.ask(pcb.SetOutlineCommand(team, ref, so.candidate, so.userChoice.getOrElse(true), tcmd))
          _ <-
            versionent.ask(pcb.SetMetaInfoProperty(team, ref, ol.metaInfo.properties.values.toSeq, None, tcmd)).map(
              mi => Some(mi)
            )
          _ <- setAlertForOutline(team, version, f)

        } yield {
          createSpecificationRenderJob(
            team = team,
            assembly = assembly,
            lifecycle = SpecificationRender
          )
          Done
        }
      case None => throw new ServiceException(new ServiceError(404, s"Outline Candidate ${so.candidate} not found"))
    })
  }

  override def updateLayer(assembly: UUID, version: UUID, file: UUID): ServiceCall[LayerFileUpdate, LayerFile] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:stack:read"
    ) { (token, _) =>
      ServerServiceCall { lfu: LayerFileUpdate =>
        val tcmd = TimelineCommand.of(token)

        refFor[PCBEntity](version).ask(UpdateFile(token.team, file, lfu, tcmd)).map { v =>
          v.toLayer
        }
      }
    }

  def generateEtag(paths: Iterable[FilePath]) =
    paths.map(_.toJavaFile.lastModified()).maxOption.map { newest =>
      val time = ZonedDateTime.ofInstant(Instant.ofEpochMilli(newest), ZoneId.systemDefault())
      val etag = s"${time}.${paths.size}"
      Base64.getEncoder.encodeToString(etag.getBytes)
    }

  override def _getLayer(
      team: String,
      assembly: UUID,
      version: UUID,
      file: UUID
  ): ServiceCall[NotUsed, Map[String, Graphic]] =
    ServerServiceCall { _ =>
      val rndrs = pcbRep.getRendersFor(file)
      rndrs.map { m =>
        (m.map { (x: (String, FilePath)) =>
          x._1 -> GraphicUtils.readFile(x._2)
        }.collect {
          case (k, Some(v)) => k -> v
        })
      }
    }

  override def getLayer(assembly: UUID, version: UUID, file: UUID): ServiceCall[NotUsed, Map[String, Graphic]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:stack:read"
    ) { (_, _) =>
      ServerServiceCall { (x, y) =>
        val nonematch = x.headers.map(x => (x._1.toLowerCase, x._2)).toMap.get("if-none-match")
        val rndrs     = pcbRep.getRendersFor(file)

        val resp = rndrs.map { m =>
          val etag = generateEtag(m.values)
          println(s"tag check: $etag == $nonematch = ${etag.forall(nonematch.contains)}")
          if (etag.exists(nonematch.contains)) {
            (ResponseHeader(304, MessageProtocol.empty, Seq()), Map[String, Graphic]())
          } else {
            val hdr = (etag match {
              case Some(tag) => ResponseHeader.Ok
                  .withHeader("ETAG", tag)
                  .withHeader("E-TAG", tag)
              case None => ResponseHeader.Ok
            })

            (
              hdr,
              m.map { (x: (String, FilePath)) =>
                x._1 -> GraphicUtils.readFile(x._2)
              }.collect {
                case (k, Some(v)) => k -> v
              }
            )
          }
        }

        resp.map { x =>
          x
        }
      }
    }

  override def _getPCB(team: String, assembly: UUID, version: UUID): ServerServiceCall[NotUsed, PCB] =
    ServerServiceCall { _ =>
      _doGetPCB(team, version)
    }

  private def _doGetPCB(team: String, version: UUID): Future[PCB] =
    _doGetPCBVersion(team, version).map { v =>
      v.assembly match {
        case None => throw new TransportException(TransportErrorCode.NotFound, "PCB not found")
        case Some(ass) =>
          toApi(v, ass)
      }
    }

  private def _doGetPCBVersion(team: String, version: UUID): Future[PCBVersion] =
    refFor[PCBEntity](version).ask(GetVersion(team, version))

  override def _getPCBVersion(team: String, assembly: UUID, version: UUID): ServerServiceCall[NotUsed, PCBVersion] =
    ServerServiceCall { _ =>
      _doGetPCBVersion(team, version)
    }

  override def _setDrillsSets(team: String, assembly: UUID, version: UUID): ServiceCall[Seq[DrillSet], Done] =
    ServerServiceCall { dss =>
      refFor[PCBEntity](version).ask(AddDrillSets(team, version, dss)).map(_ => Done)
    }

  override def _addDrills(team: String, assembly: UUID, version: UUID): ServiceCall[InternalAddDrills, Done] =
    ServerServiceCall { x =>
      val ref = refFor[PCBEntity](version)
      ref.ask(AddDrills(team, x.assRef, x.reconciledDrills))
    }

  override def _setNetList(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[SerializedCompressedFile[NetList], Done] =
    ServerServiceCall { x =>
      val ref = refFor[PCBEntity](version)
      ref.ask(SetNetList(team, assembly, version, x))
    }

  override def _removeDrills(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Done] =
    ServerServiceCall { x =>
      val ref = refFor[PCBEntity](version)
      logger.info(s"removing all drills for ${assembly}/${version}")
      ref.ask(RemoveDrills(team, assembly, version))
    }

  def _addUnreconciledDrills(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[InternalAddUnreconciledDrills, Done] =
    ServerServiceCall { x =>
      val ref = refFor[PCBEntity](version)
      ref.ask(AddUnreconciledDrills(team, x.assRef, x.f, x.drills))
    }

  private def toApi(v: PCBVersion, ass: AssemblyReference) =
    model.PCB(
      ass.id,
      ass.version,
      v.outline.map(_.toApi),
      v.holes.map(_.holes.toApi),
      v.files.map(_.toLayer),
      v.meta,
      v.specifications,
      v.defaultSpecification,
      v.drillSets,
      v.netlist.flatMap(_.read().toOption)
    )

  override def updatePCB(assembly: UUID, version: UUID): ServiceCall[PCBUpdate, PCB] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:*:write"
    ) { (token, _) =>
      ServerServiceCall { update =>
        val tcmd = TimelineCommand.of(token)
        refFor[PCBEntity](version).ask(UpdatePCB(token.team, update, tcmd)).flatMap { pcb =>
          if (update.defaultSpecification.isDefined) {
            val spec = update.defaultSpecification.get
            eReg.refFor[SpecificationEntity](spec.toString).ask(GetSpecification(token.team, spec)).flatMap { s =>
              if (s.preview.isDefined) {
                assService._setPreview(token.team, assembly, Some(version)).invoke(s.preview.get).map(_ =>
                  toApi(pcb, pcb.assembly.get)
                )
              } else {
                Future.successful(toApi(pcb, pcb.assembly.get))
              }
            }
          } else {
            Future.successful(toApi(pcb, pcb.assembly.get))
          }
        }
      }
    }

  override def getFullPCB(assembly: UUID, version: UUID): ServiceCall[NotUsed, CollectedPCB] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        for {
          _assembly <- assService._getAssembly(token.team, assembly).invoke()
          _pcb      <- _doGetPCB(token.team, version)
          _stack <- layerstackService._getPCBLayerstack(token.team, version, Some(true)).invoke().map(
            Option.apply
          ).recover(_ => None)
          _panel <-
            panelService._getCustomerPanels(token.team, assembly, version).invoke().map(Option.apply).recover(_ => None)
        } yield CollectedPCB(
          assembly = _assembly.assembly,
          pcb = _pcb,
          layerstack = _stack.flatMap(_.selected.map(_.toApi)),
          panels = _panel.map(_.map(_.toApi))
        )
      }
    }

  override def getPCB(assembly: UUID, version: UUID): ServiceCall[NotUsed, PCB] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:*:read"
    ) { (token, _) =>
      _getPCB(token.team, assembly, version)
    }

  override def getSharedPCB(share: UUID): ServiceCall[NotUsed, PCB] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$share:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        for {
          a   <- assService._getAssemblyShare(token.team, share).invoke()
          pcb <- _doGetPCB(a.assembly.team, a.share.ref.version)
        } yield pcb
      }
    }

  def getSharedPCBMetaData(share: UUID): ServiceCall[NotUsed, MetaInfo] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$share:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        for {
          a   <- assService._getAssemblyShare(token.team, share).invoke()
          pcb <- _doGetPCB(a.assembly.team, a.share.ref.version)
        } yield pcb.metaInfo.getOrElse(MetaInfo())
      }
    }

  override def getDrills(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[HoleList]] =
    auth {
      case _: Auth0Token => "view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$version:outline:read"
    } { (token, _) =>
      _getDrills(token.getTeam, assembly, version)
    }

  override def _getDrills(team: String, assembly: UUID, version: UUID) =
    ServerServiceCall { _ =>
      refFor[PCBEntity](version).ask(GetVersion(team, version)).map { v =>
        v.reconciledHoles.flatMap(_.holes.read().toOption)
      }
    }

  override def streamPreviews(
      assembly: UUID,
      version: UUID,
      k: String
  ): ServiceCall[NotUsed, Source[PCBStreamMessage, NotUsed]] = {
    val b = AuthenticationServiceComposition.decodeTokenWithResponse(k)

    authorizedStringWithToken(b)(token => s"pcb:${token.team}:${token.team}:$version:specification:read") {
      (_, _) =>
        ServerServiceCall { _ =>
          val ticks = Source.tick(30 seconds, 30 seconds, PCBStreamMessage(t = "ping", m = Ping(Instant.now()), None))

          val nowOffset = Offset.timeBasedUUID(UUIDs.timeBased())
          val sources =
            SpecificationEvent.Tag.allTags.map(tag =>
              eReg.eventStream(tag, nowOffset)
                .collect {
                  case EventStreamElement(_, event: SpecificationPreviewChanged, _)
                      if event.spec.assembly.version == version =>
                    PCBStreamMessage(
                      "specpreview",
                      PreviewChangedMessage(event.spec.id, event.preview.toApi),
                      ref = Some(event.spec.assembly)
                    )
                }
            )

          val src = sources.reduce((a, b) => a.merge(b))
          Future.successful(src.merge(ticks))

        }
    }
  }

  override def getSpecifications(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[PCBSpecificationApi]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:specification:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        refFor[PCBEntity](version).ask(GetVersion(token.team, version)).flatMap { pcb =>
          Future.sequence(pcb.specifications.map(sid =>
            refFor[SpecificationEntity](sid).ask(GetSpecification(token.team, sid))
          )).map(_.map(_.toApi))
        }
      }
    }

  override def getSharedSpecifications(share: UUID): ServiceCall[NotUsed, Seq[PCBSpecificationApi]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$share:specification:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        for {
          a     <- assService._getAssemblyShare(token.team, share).invoke()
          specs <- _doGetSpecifications(a.assembly.team, a.share.ref.version)
        } yield specs.map(_.toApi)
      }
    }

  override def getSharedSpecification(share: UUID, specification: UUID): ServiceCall[NotUsed, PCBSpecificationApi] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$share:specification:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        for {
          a    <- assService._getAssemblyShare(token.team, share).invoke()
          spec <- _doGetSpecification(a.assembly.team, a.share.ref.version, specification)
        } yield spec
      }
    }

  override def _getSpecifications(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Seq[PCBSpecification]] =
    ServerServiceCall { _ =>
      _doGetSpecifications(team, version)
    }

  private def _doGetSpecifications(team: String, version: UUID) =
    refFor[PCBEntity](version).ask(GetVersion(team, version)).flatMap { pcb =>
      Future.sequence(pcb.specifications.map(sid =>
        refFor[SpecificationEntity](sid).ask(GetSpecification(team, sid))
      ))
    }

  override def findSpecification(
      assembly: UUID,
      version: UUID,
      alias: String
  ): ServiceCall[NotUsed, PCBSpecificationApi] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:specification:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        println(s"spec: $specRepo")
        specRepo.getSpecificationIDsByAlias(token.team, assembly, version, alias).flatMap { ids =>
          if (ids.nonEmpty) {
            refFor[SpecificationEntity](ids.head).ask(GetSpecification(token.team, ids.head)).map(_.toApi)
          } else {
            Future.failed(new TransportException(TransportErrorCode.NotFound, "Specification not found"))
          }
        }
      }
    }

  override def _getSpecification(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[NotUsed, Seq[PCBSpecification]] =
    ServerServiceCall { _ =>
      refFor[PCBEntity](version).ask(GetVersion(team, version)).flatMap { pcb =>
        pcb.specifications.find(_ == specification) match {
          case None    => Future.successful(Seq())
          case Some(s) => refFor[SpecificationEntity](s).ask(GetSpecification(team, s)).map(Seq(_))
        }
      }
    }

  override def _getMergedMetaInfo(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: Option[UUID]
  ): ServiceCall[NotUsed, MetaInfo] =
    ServerServiceCall { _ =>
      refFor[PCBEntity](version).ask(GetVersion(team, version)).flatMap { pcb =>
        val specId = specification match {
          case Some(id) => pcb.specifications.find(_ == id)
          case None     => pcb.specifications.headOption
        }

        specId.map { id =>
          refFor[SpecificationEntity](id).ask(GetSpecification(team, id)).map { spec =>
            pcb.meta.getOrElse(MetaInfo()) ++ spec.getMergedMetaInfo()
          }
        }.getOrElse(Future.successful(MetaInfo()))

      }
    }

  override def getSpecification(
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[NotUsed, PCBSpecificationApi] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:specification:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _doGetSpecification(token.team, version, specification)
      }
    }

  private def _doGetSpecification(team: String, version: UUID, specification: UUID): Future[PCBSpecificationApi] =
    refFor[PCBEntity](version).ask(GetVersion(team, version)).flatMap { pcb =>
      pcb.specifications.find(_ == specification) match {
        case None    => throw new TransportException(TransportErrorCode.NotFound, "Specification not found")
        case Some(s) => refFor[SpecificationEntity](s).ask(GetSpecification(team, s)).map(_.toApi)
      }
    }

  override def createSpecification(
      assembly: UUID,
      version: UUID
  ): ServiceCall[PCBApi.PCBSpecificationCreation, PCBSpecificationApi] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:specification:write"
    ) { (token, _) =>
      ServerServiceCall { creation =>
        val tcmd = TimelineCommand.of(token).copy(params =
          Map(
            "name" -> creation.alias
          )
        )

        val assEntity = refFor[PCBEntity](version)
        println(s"get spec $version")
        assEntity.ask(GetVersion(token.team, version)).flatMap(v =>
          v.assembly match {
            case None => throw new TransportException(TransportErrorCode.NotFound, "PCB not found")
            case Some(assref) =>
              (creation.template.map(tmpl =>
                refFor[SpecificationEntity](tmpl).ask(GetSpecification(token.team, tmpl))
              ) match {
                case Some(f) => f
                case None =>
                  Future.successful(
                    PCBSpecification.defaultTemplate(assref, "default")
                  )
              }).flatMap { template =>
                val newUUID = UUID.randomUUID()
                val spec = PCBSpecification(
                  assembly = assref,
                  id = newUUID,
                  alias = creation.alias,
                  dfm = template.dfm,
                  settings = template.settings,
                  user = template.user,
                  base = None,
                  preview = None,
                  previewRear = None,
                  changes = None
                )

                refFor[SpecificationEntity](spec.id).ask(CreateSpecification(token.team, assref, spec, tcmd)).flatMap(
                  s =>
                    assEntity.ask(AddSpecification(token.team, s.id, false, tcmd)).map(_ => spec).map(_.toApi)
                )
              }
          }
        )
      }
    }

  override def deleteSpecification(assembly: UUID, version: UUID, specification: UUID): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:specification:write"
    ) { (token, _) =>
      ServerServiceCall { creation =>
        val tcmd      = TimelineCommand.of(token)
        val assEntity = refFor[PCBEntity](version)
        println(s"get spec $version")

        assEntity.ask(GetVersion(token.team, version)).flatMap(v =>
          v.assembly match {
            case None => throw new TransportException(TransportErrorCode.NotFound, "PCB not found")
            case Some(_) =>
              refFor[SpecificationEntity](specification).ask(SpecificationCommands.RemoveSpecification(
                token.team,
                specification,
                tcmd
              )).flatMap(s =>
                assEntity.ask(pcb.RemoveSpecification(token.team, specification, tcmd)).map(_ => Done)
              )
          }
        )
      }
    }

  override def _setSpecificationPreview(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[SetPreviews, PCBSpecificationApi] =
    ServerServiceCall { prev =>
      doSetSpecificationPreview(team, assembly, version, specification, prev).map(_.toApi)
    }

  private def doSetSpecificationPreview(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: UUID,
      prev: SetPreviews
  ): Future[PCBSpecification] = {
    val spec = refFor[SpecificationEntity](specification)
    val pcbentity =
      refFor[PCBEntity](version)

    if (prev.front.forall(_.findExisting().isDefined) && prev.rear.forall(_.findExisting().isDefined)) {

      val pf = prev.front.map(_.stamped(false))
      val pr = prev.rear.map(_.stamped(false))

      (for {
        oldSpec <- spec.ask(GetSpecification(team, specification))
        pcb     <- pcbentity.ask(GetVersion(team, version))

        // set both previews
        _ <- pr.map(rfp =>
          spec.ask(SetSpecificationPreview(team, specification, rfp, Some(true)))
        ).getOrElse(
          Future.successful(Done)
        )
        _ <- pf.map(rfp =>
          spec.ask(SetSpecificationPreview(team, specification, rfp, Some(false)))
        ).getOrElse(
          Future.successful(Done)
        )

        // get the specification for returnal
        s <- spec.ask(GetSpecification(team, specification))

        // set the assembly preview image if needed
        _ <-
          if (pcb.defaultSpecification.contains(specification)) {
            FutureUtils.option(pf.map { _pf =>
              assService._setPreview(team, assembly, Some(version)).invoke(_pf)
            })
          } else {
            Future.successful(Done)
          }

      } yield s).recover {
        case e: Throwable =>
          e.printStackTrace()
          throw e
      }

    } else {
      spec.ask(GetSpecification(team, specification))
    }
  }

  override def _setFileMetaInfoProperty(
      team: String,
      assembly: UUID,
      version: UUID,
      file: UUID
  ): ServiceCall[Seq[Property], Done] =
    ServerServiceCall { props =>
      _doSetFileMetaInfoProperties(team, version, file, props, None).map(_ => Done)
    }

  override def _removeFileMetaInfoProperty(
      team: String,
      assembly: UUID,
      version: UUID,
      file: UUID
  ): ServiceCall[Seq[String], Done] =
    ServerServiceCall { props =>
      _doRemoveFileMetaInfoProperties(team, version, file, props, None).map(_ => Done)
    }

  override def _addLayerFiles(team: String, assembly: UUID, version: UUID): ServiceCall[Seq[File], Seq[GerberFile]] =
    ServerServiceCall { files =>
      val aref = AssemblyReference(
        team,
        assembly,
        None,
        version
      )
      val gerberfiles = files.map(f =>
        GerberFile(
          id = f.id,
          name = f.name,
          path = f.path,
          fType = f.fType,
          format = None,
          inverted = AssemblyListener.inverted(f.fType),
          metaInfo = None,
          hash = f.hash
        )
      )
      refFor[PCBEntity](version).ask(
        SetFiles(
          team,
          aref,
          gerberfiles
        )
      ).map(_ => gerberfiles)
    }

  override def _setMetaInfoProperty(
      team: String,
      assembly: UUID,
      version: UUID,
      overwrite: Option[Boolean]
  ): ServiceCall[Seq[Property], Done] =
    ServerServiceCall { props =>
      _doSetMetaInfoProperties(team, version, props, None, overwrite.getOrElse(true), TimelineCommand.system).map(_ =>
        Done
      )
    }

  override def _removeMetaInfoProperty(team: String, assembly: UUID, version: UUID): ServiceCall[Seq[String], Done] =
    ServerServiceCall { props =>
      _doRemoveMetaInfoProperties(team, version, props, None, TimelineCommand.system).map(_ => Done)
    }

  private def _doSetFileMetaInfoProperties(
      team: String,
      version: UUID,
      file: UUID,
      props: Seq[Property],
      user: Option[UUID]
  ): Future[Option[MetaInfo]] = {
    val vent = refFor[PCBEntity](version)
    vent.ask(GetVersion(team, version)).flatMap { v =>
      v.assembly match {
        case Some(assRef) => vent.ask(SetFileMetaInfoProperty(team, assRef, file, props, user)).map(mi => Some(mi))
        case None         => Future.successful(None)
      }
    }
  }

  private def _doRemoveFileMetaInfoProperties(
      team: String,
      version: UUID,
      file: UUID,
      props: Seq[String],
      user: Option[UUID]
  ): Future[Option[MetaInfo]] = {
    val vent = refFor[PCBEntity](version)
    vent.ask(GetVersion(team, version)).flatMap { v =>
      v.assembly match {
        case Some(assRef) => vent.ask(RemoveFileMetaInfoProperty(team, assRef, file, props, user)).map(mi => Some(mi))
        case None         => Future.successful(None)
      }
    }
  }

  private def _doSetMetaInfoProperties(
      team: String,
      version: UUID,
      props: Seq[Property],
      user: Option[UUID],
      overwrite: Boolean,
      tcmd: TimelineCommand
  ): Future[Option[MetaInfo]] = {
    val vent = refFor[PCBEntity](version)
    vent.ask(GetVersion(team, version)).flatMap { v =>
      v.assembly match {
        case Some(assRef) =>
          val filteredProps = props.filter { p =>
            // either overwrite is true, or the property is not yet set
            overwrite || v.meta.forall(m => !m.properties.contains(p.name))
          }

          vent.ask(SetMetaInfoProperty(team, assRef, filteredProps, user, tcmd)).map(mi => Some(mi))
        case None => Future.successful(None)
      }
    }
  }

  private def _doRemoveMetaInfoProperties(
      team: String,
      version: UUID,
      props: Seq[String],
      user: Option[UUID],
      tcmd: TimelineCommand
  ): Future[Option[MetaInfo]] = {
    val vent = refFor[PCBEntity](version)
    vent.ask(GetVersion(team, version)).flatMap { v =>
      v.assembly match {
        case Some(assRef) => vent.ask(RemoveMetaInfoProperty(team, assRef, props, user, tcmd)).map(mi => Some(mi))
        case None         => Future.successful(None)
      }
    }
  }

  private[impl] def isVisualProperty(
      u: PCBApi.PCBSpecificationUpdate,
      original: PCBSpecification,
      pcbMetaInfo: MetaInfo
  ): Boolean = {

    def isDifferent(prop: String): Boolean = {
      val before = original.settings.get[Property](prop).orElse(pcbMetaInfo.get(prop)).map(_.getValue)
      val after  = u.settings.flatMap(_.find(_.name == prop)).map(_.getValue)
      before != after
    }

    Seq(
      isDifferent(DFM.Properties.DFM.WIDTH),
      isDifferent(DFM.Properties.DFM.HEIGHT),
      isDifferent(DFM.Properties.DFM.LAYERCOUNT),
      isDifferent(Settings.COLOR),
      isDifferent(Settings.SOLDERMASK_SIDES),
      isDifferent(Settings.SILKSCREEN_COLOR),
      isDifferent(Settings.SILKSCREEN_SIDES),
      isDifferent(Settings.FINISH)
    ).contains(true)
  }

  override def changeSpecification(
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[PCBApi.PCBSpecificationUpdate, PCBSpecificationApi] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:specification:write"
    ) { (token, _) =>
      ServerServiceCall { update =>
        val spec = refFor[SpecificationEntity](specification)
        val tcmd = TimelineCommand.of(token)
        val team = token.getTeam

        update.dfm.foreach(_.map(_.name).foreach(x =>
          if (!Properties.DFM.ALL.contains(x)) {
            throw new TransportException(TransportErrorCode.PolicyViolation, s"Illegal DFM Property $x")
          }
        ))
        update.settings.foreach(_.map(_.name).foreach(x =>
          if (!Properties.Settings.ALL.contains(x)) {
            throw new TransportException(TransportErrorCode.PolicyViolation, s"Illegal PCB Setting $x")
          }
        ))

        (for {
          pcb      <- _doGetPCBVersion(team, version)
          original <- spec.ask(GetSpecification(team, specification))

          pcbProperties = pcb.meta.getOrElse(MetaInfo.empty)

          isVisualUpdate <- Future.successful(isVisualProperty(update, original, pcbProperties))

          _ <-
            if (isVisualUpdate) {
              assService._updateVersionLifecycle(
                team,
                assembly,
                Some(version),
                SpecificationRender.value,
                Some(System.currentTimeMillis())
              )
                .invoke(LifecycleStageStatus.emptyWaiting)
            } else {
              Future.successful(Done)
            }

          _ <-
            update.dfm.map(dfm =>
              spec.ask(SetDFMProperties(token.team, specification, dfm, tcmd))
            ) match {
              case Some(f) => f.map(Some(_))
              case None    => Future.successful(None)
            }
          _ <-
            update.settings.map(s =>
              spec.ask(SetSettingProperties(token.team, specification, Some(s), None, tcmd))
            ) match {
              case Some(f) => f.map(Some(_))
              case None    => Future.successful(None)
            }
          _ <-
            update.user.map(s =>
              spec.ask(SetUserProperties(token.team, specification, s, tcmd))
            ) match {
              case Some(f) => f.map(Some(_))
              case None    => Future.successful(None)
            }

          found <- eReg.refFor[PCBEntity](version.toString).ask(GetVersion(token.team, version))

          _ <- {
            val names = (update.dfm.toSeq.flatMap(_.map(_.name)) ++ update.settings.toSeq.flatMap(
              _.map(_.name)
            ) ++ update.user.toSeq.flatMap(_.map(_.name))).toSet.mkString(",")
            spec.ask(
              SaveSpecification(
                team = token.team,
                id = specification,
                base = found.meta,
                info = Some(CollaborativeEventInfo(token)),
                tcmd = tcmd.copy(params = Map("properties" -> names))
              )
            ).map { spec =>
              if (isVisualUpdate) {
                createSpecificationRenderJob(
                  team = token.team,
                  assembly = assembly,
                  lifecycle = SpecificationRender
                )
              }
              Done
            }
          }
          updatedSpec <- spec.ask(GetSpecification(token.team, specification))

          _ <- Future.successful(SvixHelper.sendInternal(
            PCBSpecificationChanged.EVENT,
            PCBSpecificationChanged(
              token.team,
              specification,
              version,
              PCBV2Layer.hashString(PCBV2Layer.to(updatedSpec).settings) // convert to pcbv2 for forwards compatibility
            )
          ))
        } yield updatedSpec.toApi)

      }
    }

  /** Starts a call to the renderer to render the specification, and then save the previews to the pcb
    */
  private def createSpecificationRenderJob(
      team: String,
      assembly: UUID,
      lifecycle: AssemblyLifecycleStageName
  ): Unit =
    jobBuilder.createJob(
      jobDescription = RenderSpecificationJobEntry(
        team = team,
        assembly = assembly,
        lifecycle = Some(lifecycle)
      ),
      queueName = "rendering",
      jobIdOption = None
    )

  override def streamAll(k: String): ServiceCall[NotUsed, Source[PCBStreamMessage, NotUsed]] = {
    val b = AuthenticationServiceComposition.decodeTokenWithResponse(k)

    authorizedStringWithToken(b)(token => s"pcb:${token.team}:${token.team}:*:stack:read") {
      (_, _) =>
        ServerServiceCall { _ =>
          val ticks = Source.tick(30 seconds, 30 seconds, PCBStreamMessage(t = "ping", m = Ping(Instant.now()), None))

          val nowOffset = Offset.timeBasedUUID(UUIDs.timeBased())
          val sources   = pcbEvents(b.team, None, nowOffset)

          val src = sources.reduce((a, b) => a.merge(b))
          Future.successful(src.merge(ticks))

        }
    }
  }

  override def streamVersion(
      assembly: UUID,
      version: UUID,
      k: String
  ): ServiceCall[NotUsed, Source[PCBStreamMessage, NotUsed]] =
    auth {
      case _: Auth0Token => "view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$version:stack:read"
    } { (t, _) =>
      ServerServiceCall { _ =>
        val ticks = Source.tick(30 seconds, 30 seconds, PCBStreamMessage(t = "ping", m = Ping(Instant.now()), None))

        val nowOffset = Offset.timeBasedUUID(UUIDs.timeBased())
        val sources   = pcbEvents(t.getTeam, Some(version), nowOffset)

        val src = sources.reduce((a, b) => a.merge(b))
        Future.successful(src.merge(ticks))

      }
    }

  private def pcbEvents(
      team: String,
      version: Option[UUID],
      nowOffset: Offset
  ) = {
    def filter(event: AssemblyReference) =
      event.team == team && (version.isEmpty || version.contains(event.version))

    PCBEvent.Tag.allTags.map { tag =>
      eReg
        .eventStream(tag, nowOffset)
        .collect {
          case EventStreamElement(_, event: FileDimensionSet, _) if filter(event.assRef) =>
            PCBStreamMessage("format", FileFormatChanged(PCBEventProcessor.toApi(event.f)), Some(event.assRef))
          case EventStreamElement(_, event: OutlineSet, _) if filter(event.assRef) =>
            PCBStreamMessage("outline", OutlineSetMsg(PCBEventProcessor.toApi(event.f)), Some(event.assRef))

          case EventStreamElement(_, event: OutlineCandidatesSet, _) if filter(event.assRef) =>
            PCBStreamMessage(
              "outline_candidates_set",
              OutlineCandidatesMsg(event.candidates.map(_.toApi)),
              Some(event.assRef)
            )
          case EventStreamElement(_, event: OutlineCandidateSet, _) if filter(event.assRef) =>
            PCBStreamMessage("outline_candidate_set", OutlineCandidateMsg(event.candidate.toApi), Some(event.assRef))
          case EventStreamElement(_, event: DrillsAdded, _) if filter(event.assRef) =>
            PCBStreamMessage("drill", DrillsAddedMsg(event.f.id, event.holes.toApi), Some(event.assRef))
          case EventStreamElement(_, event: FileSet, _) if filter(event.assRef) =>
            PCBStreamMessage("file", FileSetMsg(event.f.toLayer), Some(event.assRef))
          case EventStreamElement(_, event: AnalysisStateChanged, _) if filter(event.assRef) =>
            PCBStreamMessage(
              "analysis",
              AnalysisMsg(Some(event.status), event.meta.getOrElse(MetaInfo())),
              Some(event.assRef)
            )
          case EventStreamElement(_, event: MetaInfoSet, _) if filter(event.assRef) =>
            PCBStreamMessage("analysis", AnalysisMsg(None, event.f), Some(event.assRef))
        }
    }
  }

  private def pcbEventsV2(
      team: String,
      version: Option[UUID],
      nowOffset: Offset
  ): Source[PCBV2StreamMessage, NotUsed] = {
    def filter(event: AssemblyReference): Boolean =
      event.team == team && (version.isEmpty || version.contains(event.version))

    val sources = PCBEvent.Tag.allTags.toSeq.map { tag =>
      eReg
        .eventStream(tag, nowOffset)
        .collect {
          case EventStreamElement(_, event: AnalysisStateChanged, _) if filter(event.assRef) =>
            PCBV2StreamMessage(
              "analysis",
              AnalysisMsgV2(Some(event.status), PCBV2Layer.to(event.meta.getOrElse(MetaInfo()))),
              Some(event.assRef)
            )
        }
    }

    sources.reduce((a, b) => a.merge(b))
  }

  override def pcbEvents(): Topic[StreamMessage[PCBStreamEvent]] =
    TopicProducer.taggedStreamWithOffset(PCBEvent.Tag) { (tag, offset) =>
      eReg.eventStream(tag, offset).mapConcat { x =>
        immutable.Seq((x.event.toStreamEvent, x.offset))
      }
    }

  override def specificationTimeline(): Topic[TimelineEvent] =
    TopicProducer.taggedStreamWithOffset(SpecificationEvent.Tag) { (tag, offset) =>
      logger.info(s"[EVStream] start spec timeline with tag $tag from offset ${offset}")
      eReg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: SpecificationTimelineChanged, _) =>
          immutable.Seq((ev.evt, x.offset))
        case _ => Nil
      }
    }

  override def pcbTimeline(): Topic[TimelineEvent] =
    TopicProducer.taggedStreamWithOffset(PCBEvent.Tag) { (tag, offset) =>
      logger.info(s"[EVStream] start pcb timeline with tag $tag from offset ${offset}")
      eReg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: PCBTimelineChanged, _) =>
          immutable.Seq((ev.evt, x.offset))
        case _ => Nil
      }
    }

  override def specificationSavedTopic(): Topic[SpecificationSavedMessage] =
    TopicProducer.taggedStreamWithOffset(SpecificationEvent.Tag) { (tag, offset) =>
      logger.info(s"[EVStream] start specificationSavedTopic with tag $tag from offset ${offset}")
      eReg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: SpecificationSaved, _) =>
          immutable.Seq((SpecificationSavedMessage(Some(ev.spec)), x.offset))
        case x =>
          immutable.Seq((SpecificationSavedMessage(None), x.offset))
      }
    }

  val definitionTemplateCategory = "specification"

  def printSpecification(
      assembly: UUID,
      version: UUID,
      specification: UUID,
      template: Option[String]
  ): ServiceCall[NotUsed, Array[Byte]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:specification:read"
    ) { (token, _) =>
      println(s"specification: ${specification} assembly: ${assembly} version: ${version} template: ${template}")
      ServerServiceCall { (_, _) =>
        val res =
          for {
            spec     <- _getSpecification(token.team, assembly, version, specification).invoke().map(_.head)
            assembly <- assService._getAssembly(token.team, spec.assembly.id).invoke()
            pcb      <- _getPCBVersion(token.team, spec.assembly.id, spec.assembly.version).invoke()
            layerstack <- layerstackService._getPCBLayerstack(
              token.team,
              spec.assembly.version,
              Some(true)
            ).invoke()
              .map(Some(_))
              .recover {
                case e => None
              }
            customerPanels <- panelService._getCustomerPanels(token.team, spec.assembly.id, version).invoke()
          } yield userService._renderTemplate(token.team, definitionTemplateCategory, template).invoke({
            val variable = Map(
              "specification"  -> Json.toJson(spec),
              "assembly"       -> Json.toJson(assembly),
              "pcb"            -> Json.toJson(pcb),
              "layerstack"     -> Json.toJson(layerstack),
              "customerPanels" -> Json.toJson(customerPanels)
            )

            val filepaths = Seq.newBuilder[String]
            spec.preview.map(f => filepaths.addOne(f.toPath))
            spec.previewRear.map(f => filepaths.addOne(f.toPath))
            layerstack.flatMap(_.selected).flatMap(_.definition.image).foreach(fp => filepaths.addOne(fp.toPath))
            customerPanels.flatMap(_.preview).foreach(fp => filepaths.addOne(fp.toPath))

            val body = RenderRequest(
              resources = filepaths.result(),
              variables = variable
            )
            body
          })

        import de.fellows.utils.ServiceCallUtils._
        res.flatten.map(inlinePDF)
      }
    }

  /** V2
    */
  override def createPCBV2(): ServiceCall[PCBV2Api.PCBV2Creation, PCBV2Api.PCBV2] =
    auth(_ => "create:pcb") { (t, _) =>
      ServerServiceCall { c =>
        val tcmd         = TimelineCommand.of(t)
        val withoutFiles = c.withoutFiles.getOrElse(false)
        for {
          ass <- assService._createNewAssembly(t.getUserId, t.getTeam).invoke(InternalAssemblyCreation(
            assembly = AssemblyCreation(
              name = c.name,
              description = c.description,
              customer = c.customer,
              contact = c.contact,
              orderId = c.orderId,
              externalReference = c.externalReference
            ),
            features = Some(PCBFeatures.FEATURES),
            timelineCommand = Some(tcmd),
            withoutFiles = withoutFiles
          ))

          version = ass.currentVersion.getOrElse(
            throw new TransportException(TransportErrorCode.NotFound, "No version found in created assembly")
          )

          pcb <- AssemblyListener.doCreatePCBForAssembly(
            eReg,
            VersionDescription(
              assembly = AssemblyDescription(
                team = t.getTeam,
                id = ass.id,
                agid = ass.gid,
                name = ass.name,
                features = ass.features
              ),
              version = version,
              previous = None
            )
          )
        } yield PCBV2Layer.to(ass, pcb._1, pcb._2.toSeq)
      }
    }

  override def clonePCBV2(pcb: UUID): ServiceCall[PCBV2Api.PCBV2Clone, PCBV2Api.PCBV2Id] =
    auth(_ => "create:pcb") { (t, _) =>
      ServerServiceCall { c =>
        for {
          version <- refFor[PCBEntity](pcb).ask(GetVersion(t.getTeam, pcb))

          assemblyId =
            version
              .assembly
              .getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Assembly not found"))
              .id

          ass <- assService._cloneAssembly().invoke(
            CloneParameters(
              source = assemblyId,
              targetTeam = t.getTeam,
              targetName = Some(c.name),
              externalReference = c.externalReference
            )
          )

        } yield {
          val clonedPcbId = ass
            .currentVersion
            .getOrElse(
              throw new TransportException(TransportErrorCode.NotFound, "Version not found in cloned assembly")
            )
            .id

          PCBV2Api.PCBV2Id(clonedPcbId)
        }
      }
    }

  override def updatePCBV2(pcb: UUID): ServiceCall[PCBV2Api.PCBV2Update, PCBV2Api.PCBV2] =
    auth(token =>
      s"create:pcb"
    ) { (t, _) =>
      ServerServiceCall { c =>
        val tcmd = TimelineCommand.of(t)
        val update = AssemblyUpdate(
          name = c.name,
          description = c.description,
          customer = c.customer,
          status = None,
          assignee = None
        )

        val iup = InternalAssemblyUpdate(
          update,
          tcmd
        )

        for {
          ref             <- getAssemblyReference(t.getTeam, pcb)
          updatedAssembly <- assService._updateAssembly(t.getTeam, ref.id).invoke(iup)
          version <- refFor[PCBEntity](updatedAssembly.currentVersion.map(_.id).get).ask(GetVersion(t.getTeam, pcb))
          specs   <- _doGetSpecifications(updatedAssembly.team, version)

        } yield PCBV2Layer.to(updatedAssembly, version, specs)
      }
    }

  override def _findPCBV2(team: String, orderId: String): ServerServiceCall[NotUsed, Seq[PCBV2Api.PCBV2]] =
    ServerServiceCall { _ =>
      assService._findAssembliesByOrderId(team, orderId).invoke()
        .flatMap(assemblies =>
          Future.sequence(assemblies.map { assembly =>
            refFor[PCBEntity](assembly.currentVersion.map(_.id).get).ask(GetVersion(
              assembly.team,
              assembly.currentVersion.map(_.id).get
            )).flatMap { version =>
              _doGetSpecifications(assembly.team, version)
                .map { specs =>
                  PCBV2Layer.to(assembly, version, specs)
                }
            }
          })
        )
    }

  override def findPCBV2(orderId: String): ServiceCall[NotUsed, Seq[PCBV2Api.PCBV2]] =
    auth(_ => "view:pcb") { (t, _) =>
      _findPCBV2(t.getTeam, orderId)
    }

  override def _getPCBV2(team: String, pcb: UUID): ServerServiceCall[NotUsed, PCBV2Api.PCBV2] =
    ServerServiceCall { _ =>
      _doGetPCBV2(team, pcb)
    }

  override def _getSharedPCBV2(team: String, share: UUID): ServerServiceCall[NotUsed, PCBV2Api.PCBV2] =
    ServerServiceCall { _ =>
      this.assService._getAssemblyShare(team, share).invoke().flatMap { assemblyWithShare =>
        _doGetPCBV2(assemblyWithShare.share.ref.team, assemblyWithShare.share.ref.version)
      }
    }

  override def getPCBV2Duplicates(version: UUID): ServiceCall[NotUsed, Seq[DuplicatePcb]] =
    authorizedString(token =>
      s"pcb:${token.team}:${token.team}:$version:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        doGetPossibleDuplicates(token.getTeam, version)
      }
    }

  override def getWebSocketToken(pcb: UUID): ServiceCall[NotUsed, TokenResponse] =
    auth {
      case _: Auth0Token => "view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:stack:read"
    } { (t, _) =>
      ServerServiceCall { _ =>
        val token = JwtTokenUtil.generateFileToken(
          FileTokenContent(t.getTeam, Seq(FileSecurityClaim("pcb", pcb.toString, "all"))),
          Some(System.currentTimeMillis() / 1000 + 20)
        )
        Future.successful(TokenResponse(token))
      }
    }

  override def streamVersionV2(
      pcb: UUID,
      k: Option[String]
  ): ServiceCall[NotUsed, Source[PCBV2StreamMessage, NotUsed]] =
    ServerServiceCall { _ =>
      k.flatMap(t => AuthenticationServiceComposition.decodeFileToken(t).toOption) match {
        case Some(token) if token.claims.contains(FileSecurityClaim("pcb", pcb.toString, "all")) =>
          val team = token.getTeam

          assService._getAssemblyIdByVersion(team, pcb).invoke().map { assemblyId =>
            val nowOffset = Offset.timeBasedUUID(UUIDs.timeBased())
            val pcbSource = pcbEventsV2(team, Some(pcb), nowOffset)

            val pingTicks =
              Source.tick(
                30.seconds,
                30.seconds,
                PCBV2StreamMessage(t = "ping", m = PingV2(Instant.now()), None)
              )

            val assemblySource =
              Source
                .futureSource(
                  assService._streamAssemblyV2(team, assemblyId, Some(pcb)).invoke()
                )
                .mapConcat(s => PCBV2StreamMessage.fromAssemblyStreamMessage(s))

            pcbSource
              .merge(pingTicks)
              .merge(assemblySource)
          }

        case _ => throw Forbidden(s"Invalid token")
      }
    }

  private def retrieveAssemblyAndPCB(team: String, pcb: UUID): Future[(Assembly, PCBVersion)] =
    for {
      version <- refFor[PCBEntity](pcb).ask(GetVersion(team, pcb))
      assemblyId =
        version
          .assembly
          .getOrElse {
            throw TransportExceptionHelper(
              TransportErrorCode.NotFound,
              "Assembly not found",
              s"version.assembly was not defined for pcb ${pcb} in team ${team}"
            )
          }
          .id
      assemblyWithShares <- assService._getAssembly(team, assemblyId).invoke()
      assembly = assemblyWithShares.assembly
    } yield (assembly, version)

  private def _doGetPCBV2WithAssembly(team: String, pcb: UUID): Future[(PCBV2Api.PCBV2, Assembly)] =
    doGetPCB(team, pcb).map {
      case (assembly, version, specs) => (PCBV2Layer.to(assembly, version, specs), assembly)
    }

  /** Retrieve a PCB V2 from the database
    */
  private def _doGetPCBV2(team: String, pcb: UUID): Future[PCBV2Api.PCBV2] =
    doGetPCB(team, pcb).map {
      case (assembly, version, specs) => PCBV2Layer.to(assembly, version, specs)
    }

  private def doGetPCB(team: String, pcb: UUID): Future[(Assembly, PCBVersion, Seq[PCBSpecification])] =
    for {
      (assembly, version) <- retrieveAssemblyAndPCB(team, pcb)
      specs               <- _doGetSpecifications(assembly.team, version)
    } yield (assembly, version, specs)

  override def getPCBV2(pcb: UUID): ServiceCall[NotUsed, PCBV2Api.PCBV2] =
    auth {
      case _: Auth0Token => s"view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:*:read"
    } { (t, _) =>
      _getPCBV2(t.getTeam, pcb)
    }

  override def getSharedPCBV2(share: UUID): ServiceCall[NotUsed, PCBV2Api.PCBV2] =
    auth {
      case _: Auth0Token => s"view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$share:*:read"
    } { (t, _) =>
      ServerServiceCall { _ =>
        this.assService._getAssemblyShare(t.getTeam, share).invoke().flatMap { a =>
          _doGetPCBV2(a.share.ref.team, a.share.ref.version)
        }
      }
    }

  override def deletePCBV2(pcb: UUID): ServiceCall[NotUsed, Done] =
    auth {
      case _: Auth0Token => s"edit:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:*:write"
    } { (t, _) =>
      ServerServiceCall { _ =>
        for {
          assembly <- assService._getAssemblyByVersion(t.getTeam, pcb).invoke()
          d <- assService._deleteAssembly(
            team = t.getTeam,
            assembly = assembly.id,
            keepFiles = Some(true),
            forceDelete = Some(true)
          ).invoke()
        } yield d
      }
    }

  override def getPCBV2File(pcb: UUID, file: String): ServiceCall[NotUsed, NotUsed] =
    PlayServiceCall { _ =>
      EssentialAction { header =>
        var tkn: GenericTokenContent = null
        val ac = action.async(SecurityBodyParser(_ =>
          s"view:pcb"
        ) {
          t =>
            tkn = t
            parser.anyContent
        }) { _ =>
          val r = assService._getAssemblyFilesByVersion(tkn.getTeam, pcb).invoke()
            .map { files =>
              files.find(
                _.file.name == file
              ) match {
                case Some(value) =>
                  Results.Ok.sendFile(
                    value.path.toJavaFile,
                    fileName = f =>
                      Some(value.file.name)
                  ).withHeaders(
                    ("X-File-Name"                   -> value.file.name),
                    ("Access-Control-Expose-Headers" -> "Content-Disposition")
                  )
                case None => Results.NotFound
              }

            }

          r
        }

        ac(header)
      }
    }

  override def getSharedPCBV2Files(share: UUID): ServiceCall[NotUsed, NotUsed] =
    PlayServiceCall { _ =>
      EssentialAction { header =>
        var tkn: GenericTokenContent = null
        val ac = action.async(SecurityBodyParser.applyOr {
          case x: TokenContent => Seq(
              s"pcb:${x.getTeam}:${x.getTeam}:$share:*:read"
            )
          case x: Auth0Token       => Seq(s"view:pcb")
          case x: FileTokenContent => Seq()
        } {
          t =>
            tkn = t
            parser.anyContent
        }) { _ =>
          assService._getAssemblyShare(tkn.getTeam, share).invoke().flatMap { assembly =>
            _doGetPCBV2Files(
              team = assembly.share.ref.team,
              pcb = assembly.share.ref.version,
              assembly = assembly.assembly
            )
          }
        }

        ac(header)
      }
    }

  override def getPCBV2Files(pcb: UUID): ServiceCall[NotUsed, NotUsed] =
    PlayServiceCall { _ =>
      EssentialAction { header =>
        var tkn: GenericTokenContent = null
        val ac = action.async(SecurityBodyParser.applyOr {
          case x: TokenContent     => Seq(s"pcb-api:${x.getTeam}:${x.getTeam}:$pcb:files:read")
          case x: Auth0Token       => Seq(s"view:pcb")
          case x: FileTokenContent => Seq(s"pcb/${pcb.toString}/all")
        } {
          t =>
            tkn = t
            parser.anyContent
        }) { _ =>
          assService._getAssemblyByVersion(tkn.getTeam, pcb).invoke().flatMap { assembly =>
            _doGetPCBV2Files(
              team = tkn.getTeam,
              pcb = pcb,
              assembly = assembly
            )
          }
        }

        ac(header)
      }
    }

  private def _doGetPCBV2Files(team: String, pcb: UUID, assembly: Assembly): Future[Result] =
    assService
      ._getAssemblyOriginalFilesByVersion(team, pcb)
      .invoke()
      .flatMap { files =>
        if (files.length == 1 && files.head.originalName.toLowerCase.endsWith(".zip")) {
          logger.info("deliver the original zip file")

          Future.successful(files.head.path.toJavaFile, (_: io.File) => ())
        } else if (files.isEmpty) {
          assService._getAssemblyFilesByVersion(team, pcb).invoke().map { internalFiles =>
            // if there is no original file, use the processed files. mostly used for backwards compatibility
            logger.info("no original files available, deliver the processed files")
            logger.info(s"files ${internalFiles.map(f => (f.file.name, f.path)).mkString("\n")}")

            // TODO: shouldn't these be deleted?
            (createPCBZipFile(assembly, internalFiles.map(_.path)), (_: io.File) => ())
          }
        } else {
          logger.info("deliver a package of the original files")
          logger.info(s"files ${files.mkString("\n")}")

          Future.successful(createPCBZipFile(assembly, files.map(_.path)), (f: io.File) => f.delete())
        }
      }
      .map {
        case (file, onClose) =>
          val fileName = file.getName
          Results.Ok.sendFile(
            file,
            fileName = _ =>
              Some(fileName),
            onClose = () => onClose(file)
          ).withHeaders(
            ("X-File-Name"                   -> fileName),
            ("Access-Control-Expose-Headers" -> "Content-Disposition")
          )
      }

  private def createPCBZipFile(ass: Assembly, files: Seq[FilePath]): io.File = {
    val gid = ass.gid.replaceAll("\\W+", "")
    val f   = Files.createTempDirectory("assemblyZip").resolve(s"pcb-$gid.zip").toFile
    FileHelper.zip(
      target = f,
      value = files,
      nameGenerator = file => s"$gid/${file.filename}"
    )
    f
  }

  private def _doGetSpecifications(team: String, version: PCBVersion): Future[Seq[PCBSpecification]] =
    Future.sequence(version.specifications.map(spec =>
      refFor[SpecificationEntity](spec).ask(GetSpecification(team, spec))
    ))

  override def _getPCBV2Lifecycle(
      team: String,
      pcb: UUID,
      lifecycle: String
  ): ServerServiceCall[NotUsed, AssemblyLifecycleStage] =
    ServerServiceCall { _ =>
      for {
        assembly <- assService._getAssemblyByVersion(team, pcb).invoke()
      } yield assembly.currentVersion.toSeq.flatMap(_.lifecycles).find(_.name == lifecycle)
        .getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Lifecycle not found"))
    }

  override def getPCBV2Lifecycle(pcb: UUID, lifecycle: String): ServiceCall[NotUsed, AssemblyLifecycleStage] =
    auth {
      case _: Auth0Token => s"view:pcb"
      case token         => s"pcb:${token.getTeam}:${token.getTeam}:$pcb:*:read"
    } { (t, _) =>
      _getPCBV2Lifecycle(t.getTeam, pcb, lifecycle)
    }

  override def _getPCBV2Lifecycles(team: String, pcb: UUID): ServerServiceCall[NotUsed, Seq[AssemblyLifecycleStage]] =
    ServerServiceCall { _ =>
      for {
        assembly <- assService._getAssemblyByVersion(team, pcb).invoke()
      } yield assembly.currentVersion.toSeq.flatMap(_.lifecycles)
    }

  override def getPCBV2Lifecycles(pcb: UUID): ServiceCall[NotUsed, Seq[AssemblyLifecycleStage]] =
    auth {
      case _: Auth0Token => s"view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:*:read"
    } { (t, _) =>
      _getPCBV2Lifecycles(t.getTeam, pcb)
    }

  private def getAssemblyReference(team: String, pcb: UUID): Future[AssemblyReference] =
    this
      .assService
      ._getAssemblyByVersion(team, pcb)
      .invoke()
      .map(ass => mkAssemblyReference(ass, pcb))

  private def mkAssemblyReference(assembly: Assembly, pcb: UUID): AssemblyReference =
    AssemblyReference(
      assembly.team,
      assembly.id,
      Some(assembly.gid),
      pcb
    )

  override def _getPCBV2Specification(
      team: String,
      pcb: UUID,
      specification: String
  ): ServerServiceCall[NotUsed, PCBV2Specification] =
    ServerServiceCall { _ =>
      _doGetPCBV2Specification(team, pcb, specification)
    }

  override def _getSharedPCBV2Specification(
      team: String,
      share: UUID,
      specification: String
  ): ServerServiceCall[NotUsed, PCBV2Specification] =
    ServerServiceCall { _ =>
      this.assService._getAssemblyShare(team, share).invoke().flatMap { assemblyWithShare =>
        _doGetPCBV2Specification(assemblyWithShare.share.ref.team, assemblyWithShare.share.ref.version, specification)
      }
    }

  private def _doGetPCBV2Specification(team: String, pcb: UUID, specification: String): Future[PCBV2Specification] =
    getV2Specifications(team, pcb) map { specs =>
      val id   = UUIDUtils.fromString(specification).map(_.toString).getOrElse(specification)
      val spec = specs.find(x => x.name == id || x.id.toString == id)

      spec.getOrElse(throw new TransportException(
        TransportErrorCode.NotFound,
        "Specification not found"
      ))
    }

  override def getPCBV2Specification(pcb: UUID, specification: String): ServiceCall[NotUsed, PCBV2Specification] =
    auth {
      case _: Auth0Token => s"view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:specification:read"
    } { (t, _) =>
      _getPCBV2Specification(t.getTeam, pcb, specification)
    }

  override def getSharedPCBV2Specification(
      share: UUID,
      specification: String
  ): ServiceCall[NotUsed, PCBV2Specification] =
    auth {
      case _: Auth0Token => s"view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$share:specification:read"
    } { (t, _) =>
      ServerServiceCall { _ =>
        assService._getAssemblyShare(t.getTeam, share).invoke().flatMap { assemblyWithShare =>
          _doGetPCBV2Specification(assemblyWithShare.share.ref.team, assemblyWithShare.share.ref.version, specification)
        }
      }
    }

  override def getPCBV2SpecificationPreviews(pcb: UUID, specification: String): ServiceCall[NotUsed, PCBPreviews] =
    auth {
      case _: Auth0Token => s"view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:specification:read"
    } { (t, _) =>
      ServerServiceCall { _ =>
        _doGetPCBV2Specification(t.getTeam, pcb, specification)
          .map(_.previews)
      }
    }

  override def _getPCBV2Specifications(team: String, pcb: UUID): ServerServiceCall[NotUsed, Seq[PCBV2Specification]] =
    ServerServiceCall { _ =>
      getV2Specifications(team, pcb)
    }

  override def _getSharedPCBV2Specifications(
      team: String,
      share: UUID
  ): ServerServiceCall[NotUsed, Seq[PCBV2Specification]] =
    ServerServiceCall { _ =>
      this.assService._getAssemblyShare(team, share).invoke().flatMap { assemblyWithShare =>
        getV2Specifications(assemblyWithShare.share.ref.team, assemblyWithShare.share.ref.version)
      }
    }

  override def getPCBV2Specifications(pcb: UUID): ServiceCall[NotUsed, Seq[PCBV2Specification]] =
    auth {
      case _: Auth0Token => s"view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:specification:read"
    } { (t, _) =>
      _getPCBV2Specifications(t.getTeam, pcb)
    }

  override def getSharedPCBV2Specifications(share: UUID): ServiceCall[NotUsed, Seq[PCBV2Specification]] =
    auth {
      case _: Auth0Token => s"view:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$share:specification:read"
    } { (t, _) =>
      ServerServiceCall { _ =>
        assService._getAssemblyShare(t.getTeam, share).invoke().flatMap { assemblyWithShare =>
          getV2Specifications(assemblyWithShare.share.ref.team, assemblyWithShare.share.ref.version)
        }
      }
    }

  override def createPCBV2Specification(pcb: UUID): ServiceCall[PCBV2Specification, PCBV2Specification] = {
    import de.fellows.ems.pcb.api.specification.units.UnitConversions._
    auth {
      case _: Auth0Token => s"edit:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:*:write"
    } { (t, _) =>
      ServerServiceCall { spec =>
        val entity     = refFor[PCBEntity](pcb)
        val specId     = UUID.randomUUID()
        val specentity = refFor[SpecificationEntity](specId)
        val tcmd       = TimelineCommand.of(t)

        for {
          assRef <- getAssemblyReference(t.getTeam, pcb)
          specification = PCBV2Layer.from(assRef, spec, layerCount = None)
          spec <- specentity.ask(CreateSpecification(t.getTeam, assRef, specification, tcmd))
          _    <- entity.ask(AddSpecification(assRef.team, spec.id, true, tcmd))

        } yield PCBV2Layer.to(spec)
      }
    }
  }

  private[impl] def propertyChanged(
      u: PCBV2Update,
      spec: PCBV2Specification,
      pcbBasicProperties: PCBV2BasicBoardProperties,
      properties: Seq[PCBV2BasicBoardProperties => Option[Any]]
  ): Boolean = {
    def isDifferent(prop: PCBV2BasicBoardProperties => Option[Any]): Boolean = {
      val before = prop(spec.settings.board.basic).orElse(prop(pcbBasicProperties))
      val after  = u.settings.flatMap(x => prop(x.board.basic))

      before != after
    }
    properties.map(isDifferent).contains(true)
  }

  private[impl] def isVisualProperty(
      u: PCBV2Update,
      spec: PCBV2Specification,
      pcbBasicProperties: PCBV2BasicBoardProperties
  ): Boolean =
    propertyChanged(
      u,
      spec,
      pcbBasicProperties,
      Seq(
        _.soldermaskColor,
        _.soldermaskSide,
        _.silkscreenColor,
        _.silkscreenSide,
        _.surfaceFinish
      )
    )

  override def updatePCBV2SpecificationStatus(
      pcb: UUID,
      specification: UUID
  ): ServiceCall[PCBV2SpecificationStatusUpdateRequest, PCBV2Specification] =
    auth {
      case _: Auth0Token => s"edit:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:specification:write"
    } { (token, _) =>
      ServerServiceCall { request =>
        val entity = refFor[SpecificationEntity](specification)
        val tcmd   = TimelineCommand.of(token)

        for {
          spec <- entity.ask(
            SetSpecificationStatus(
              team = token.getTeam,
              id = specification,
              status = PCBV2SpecificationStatus.toModel(request.status),
              info = CollaborativeEventInfo(token),
              tcmd = tcmd
            )
          )
          specv2 <- Future.successful(PCBV2Layer.to(spec))
        } yield specv2
      }
    }

  override def updatePCBV2Specification(
      pcb: UUID,
      specification: UUID,
      removeUndefined: Option[Boolean]
  ): ServiceCall[PCBV2Update, PCBV2Specification] = {
    import de.fellows.ems.pcb.api.specification.units.UnitConversions._
    auth {
      case _: Auth0Token => s"edit:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:specification:write"
    } { (token, _) =>
      ServerServiceCall { sup: PCBV2Update =>
        val entity             = refFor[SpecificationEntity](specification)
        val tcmd               = TimelineCommand.of(token)
        val canRemoveUndefined = removeUndefined.getOrElse(false)

        for {
          (assembly, oldPcb, _) <- doGetPCB(token.getTeam, pcb)
          original              <- entity.ask(GetSpecification(token.getTeam, specification))

          originalV2       = PCBV2Layer.to(original)
          (update, delete) = PCBServiceImpl.createUpdate(sup, oldPcb, original, canRemoveUndefined)

          specUpdate = SetSettingProperties(
            team = token.getTeam,
            id = specification,
            props = Some(update),
            remove = Some(delete),
            tcmd = tcmd
          )

          _ = logger.info(s"updating spec ${specUpdate}")

          spec <- entity.ask(specUpdate)

          pcbBasicProperties =
            oldPcb.meta.map(PCBV2Layer.pcbBasicProperties).getOrElse(PCBV2BasicBoardProperties.empty)

          isVisualUpdate = isVisualProperty(sup, originalV2, pcbBasicProperties)

          specv2 = PCBV2Layer.to(spec)

          _ <-
            if (isVisualUpdate) {
              assService._updateVersionLifecycle(
                token.getTeam,
                spec.assembly.id,
                Some(spec.assembly.version),
                SpecificationRender.value,
                Some(System.currentTimeMillis())
              )
                .invoke(LifecycleStageStatus.emptyWaiting)
            } else {
              Future.successful(Done)
            }

          save <- entity.ask(
            SaveSpecification(
              team = token.getTeam,
              id = specification,
              base = spec.base,
              info = Some(CollaborativeEventInfo(token)),
              tcmd = tcmd
            )
          )

          _ = {
            if (isVisualUpdate) {
              createSpecificationRenderJob(
                team = token.getTeam,
                assembly = spec.assembly.id,
                lifecycle = SpecificationRender
              )
            } else {
              ()
            }
          }

          _ <- {
            if (propertyChanged(sup, originalV2, pcbBasicProperties, Seq(_.boardHeight, _.boardWidth))) {
              panelService._reCutCustomerPanels(token.getTeam, spec.assembly.id, spec.assembly.version).invoke()
            } else {
              Future.successful(Done)
            }
          }

          _ <- {
            if (propertyChanged(sup, originalV2, pcbBasicProperties, Seq(_.boardHeight, _.boardWidth))) {
              updateDimensionsAlert(token.getTeam, assembly, spec.assembly.version, pcbBasicProperties, specv2)
            } else {
              Future.successful(Done)
            }
          }

          pcb <-
            refFor[PCBEntity](spec.assembly.version).ask(GetVersion(token.getTeam, spec.assembly.version))

          versionId =
            pcb.assembly.map(_.version).getOrElse(throw new IllegalStateException("No versionId found"))

          _ <- Future.successful {
            SvixHelper.sendInternal(
              PCBSpecificationChanged.EVENT,
              PCBSpecificationChanged(
                token.getTeam,
                specification,
                versionId,
                PCBV2Layer.hashString(specv2.settings)
              )
            )
          }

        } yield {
          logger.info(s"updating spec ${specUpdate} -> ${spec}")
          specv2
        }
      }
    }
  }

  def updateDimensionsAlert(
      team: String,
      assembly: Assembly,
      version: UUID,
      basic: PCBV2BasicBoardProperties,
      spec: PCBV2Specification
  ): Future[Done] = {
    logger.info(
      s"updating dimensions alert ${basic.boardHeight} != ${spec.settings.board.basic.boardHeight} || ${basic.boardWidth} != ${spec.settings.board.basic.boardWidth}"
    )

    val specifiedHeight = spec.settings.board.basic.boardHeight
    val specifiedWidth  = spec.settings.board.basic.boardWidth

    val baseHeight = basic.boardHeight
    val baseWidth  = basic.boardWidth

    val status =
      if (
        (specifiedHeight.isDefined && specifiedHeight != baseHeight) ||
        (specifiedWidth.isDefined && specifiedWidth != baseWidth)
      ) {
        Active
      } else {
        Resolved
      }

    val lqReference = assembly.externalReference.map { lq =>
      CustomPartLumiquoteReference(
        rfqId = lq.rfqId,
        assemblyId = lq.assemblyId
      )
    }
    val customPartReference = CustomPartReference(team, version, lqReference)

    alerts.upsert(customPartReference, Pcb(InconclusiveDimensions), status).map {
      case Success(result) =>
        logger.info(s"upserted dimensions alert ${result}")
        Done
      case Failure(e) =>
        logger.error(s"failed to upsert dimensions alert ${e}")
        Done
    }

  }

  override def deletePCBV2Specification(pcb: UUID, specification: UUID): ServiceCall[NotUsed, Done] = {
    import de.fellows.ems.pcb.impl.entity.{pcb => pcbpkg}
    auth {
      case _: Auth0Token => s"edit:pcb"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:specification:write"
    } { (t, _) =>
      ServerServiceCall { s =>
        val specEntity = refFor[SpecificationEntity](specification)
        val pcbEntity  = refFor[PCBEntity](pcb)
        val tcmd       = TimelineCommand.of(t)

        for {
          _ <- specEntity.ask(SpecificationCommands.RemoveSpecification(
            t.getTeam,
            specification,
            tcmd
          ))

          _ <- pcbEntity.ask(pcbpkg.RemoveSpecification(t.getTeam, specification, tcmd))

        } yield Done
      }
    }
  }

  override def getAllCapabilities: ServiceCall[NotUsed, PCBV2SpecificationCapabilities] =
    auth {
      case _: Auth0Token => s"view:capabilities"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:*:capabilities:read"
    } { (t, _) =>
      ServerServiceCall { s =>
        Future.successful(PCBV2Layer.defaultCapabilities())
      }
    }

  override def getCapabilitiesForShare(share: UUID): ServiceCall[NotUsed, PCBV2SpecificationCapabilities] =
    auth {
      case _: Auth0Token => s"view:capabilities"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$share:capabilities:read"
    } { (t, _) =>
      ServerServiceCall { _ =>
        assService._getAssemblyShare(t.getTeam, share).invoke().flatMap { share =>
          _doGetCapabilities(share.share.ref.team, share.share.ref.version)
        }
      }
    }

  override def getCapabilities(pcb: UUID): ServiceCall[NotUsed, PCBV2SpecificationCapabilities] =
    auth {
      case _: Auth0Token => s"view:capabilities"
      case t             => s"pcb:${t.getTeam}:${t.getTeam}:$pcb:capabilities:read"
    } { (t, _) =>
      ServerServiceCall { _ =>
        _doGetCapabilities(t.getTeam, pcb)
      }
    }

  private def _doGetCapabilities(team: String, pcb: UUID): Future[PCBV2SpecificationCapabilities] =
    _doGetPCBV2(team, pcb).map { pcb =>
      val layerstackType = pcb.specifications.headOption.flatMap(_.settings.layerStack.layerstackType).orElse(
        pcb.properties.layerStack.layerstackType
      )
      val layerCount = pcb.specifications.headOption.flatMap(_.settings.layerStack.layercount).orElse(
        pcb.properties.layerStack.layercount
      ).map(_.intValue)
      val surfaceFinish = pcb.specifications.headOption.flatMap(_.settings.board.basic.surfaceFinish).orElse(
        pcb.properties.board.basic.surfaceFinish
      )
      PCBV2Layer.defaultCapabilities(layerstackType, layerCount, surfaceFinish)
    }

  /** Returns V2 Specifications for a given PCB
    *
    * @param team
    * Team ID
    * @param pcb
    * PCB ID
    */
  private def getV2Specifications(team: String, pcb: UUID): Future[Seq[PCBV2Specification]] =
    for {
      version <- refFor[PCBEntity](pcb).ask(GetVersion(team, pcb))
      replies <- Future.sequence(version.specifications.map(spec =>
        refFor[SpecificationEntity](spec).ask(GetSpecification(team, spec))
      ))
    } yield replies.map(PCBV2Layer.to)

  private def doGetPossibleDuplicates(team: String, pcbId: UUID) =
    for {
      _ <- Future.unit

      (assembly, pcb, specs) <- doGetPCB(team, pcbId)

      pcbv2  = PCBV2Layer.to(assembly, pcb, specs)
      hashes = pcb.files.flatMap(_.hash)

      fileDuplicates             <- pcbRep.getDuplicateFiles(team, pcbId, hashes)
      possiblePropertyDuplicates <- pcbRep.getTeamProjectMetas(team, pcbId)

      groupedDuplicateCandidates = collectDuplicatePcbCandidates(pcbv2, fileDuplicates, possiblePropertyDuplicates)

      duplicatePcbCandidates <- Future.traverse(groupedDuplicateCandidates.keys) { version =>
        doGetPCB(team, version).map {
          case (assembly, pcbv, value) => (version, assembly, pcbv, value)
        }
      }
    } yield {
      val duplicatePcbs = duplicatePcbCandidates
        .flatMap { candidatePcb =>
          groupedDuplicateCandidates
            .get(candidatePcb._1)
            .map { pcbMatchCandidate =>
              computeDuplicateScore(pcbv2, candidatePcb, pcbMatchCandidate)
            }
        }
        .filter(_.totalScore > 0.1)
        .toSeq

      duplicatePcbs.sortBy(_.totalScore).reverse
    }

  private def collectDuplicatePcbCandidates(
      pcb: PCBV2,
      fileDuplicates: Seq[(UUID, UUID)],
      propertyDuplicates: Seq[SimpleMetaInfo]
  ): Map[UUID, DuplicatePcbCandidate] = {
    val groupedFileDuplicates  = fileDuplicates.groupMap(_._1)(_._2)
    val groupedPropertyMatches = collectDuplicateProperties(pcb, propertyDuplicates)
    val versionIds             = groupedFileDuplicates.keySet ++ groupedPropertyMatches.keySet

    versionIds
      .toSeq
      .map { versionId =>
        val fileDuplicates     = groupedFileDuplicates.getOrElse(versionId, Seq.empty)
        val propertyDuplicates = groupedPropertyMatches.getOrElse(versionId, Seq.empty)

        val candidate = DuplicatePcbCandidate(
          version = versionId,
          fileMatches = fileDuplicates.toSet,
          propertyMatches = propertyDuplicates
        )

        (versionId, candidate)
      }
      .toMap
  }

  private def collectDuplicateProperties(
      pcb: PCBV2,
      propertyDuplicates: Seq[SimpleMetaInfo]
  ): Map[UUID, Seq[PropertyMatch]] =
    propertyDuplicates
      .view
      .map(s => (s.versionId, computePropertyMatches(pcb, s)))
      .filter { case (_, matches) => matches.nonEmpty }
      .toMap

  // Check if two doubles are close enough to be considered equal
  private def isSimilar(left: Double, right: Double): Boolean =
    left >= right - 0.1 && left <= right + 0.1

  private def computePropertyMatches(
      pcb: PCBV2,
      simpleMetaInfo: SimpleMetaInfo
  ): Seq[PropertyMatch] = {
    val layerCountMatch = pcb.properties.layerStack.layercount.exists(_.intValue == simpleMetaInfo.layerCount)
    val widthMatch = pcb.properties.board.basic.boardWidth.exists { width =>
      isSimilar(width.to(Millimeter).doubleValue, simpleMetaInfo.boardWidth)
    }
    val heightMatch = pcb.properties.board.basic.boardHeight.exists { height =>
      isSimilar(height.to(Millimeter).doubleValue, simpleMetaInfo.boardHeight)
    }

    val matches =
      Seq(
        Option.when(layerCountMatch)(PropertyMatch(MatchablePcbProperty.LayerCount)),
        Option.when(widthMatch)(PropertyMatch(MatchablePcbProperty.BoardWidth)),
        Option.when(heightMatch)(PropertyMatch(MatchablePcbProperty.BoardHeight))
      ).flatten

    matches
  }

  private def computeDuplicateScore(
      originalPcb: PCBV2,
      candidatePcb: (UUID, Assembly, PCBVersion, Seq[PCBSpecification]),
      pcbMatchCandidate: DuplicatePcbCandidate
  ): DuplicatePcb = {
    val (_, assembly, pcbv, specs) = candidatePcb

    val pcbv2 = PCBV2Layer.to(assembly, pcbv, specs)
    val matchedFiles =
      pcbv
        .files
        .filter(f => pcbMatchCandidate.fileMatches.contains(f.id))
        .flatMap(f => pcbv2.files.flatMap(_.find(_.name == f.name)))

    val sum = MatchablePcbProperty.values.map(propertyWeight).sum + originalPcb.files.size * FILE_MATCH_WEIGHT

    val propertyMatchScore = pcbMatchCandidate.propertyMatches.map(p => propertyWeight(p).doubleValue / sum).sum
    val fileMatchScore     = (FILE_MATCH_WEIGHT * matchedFiles.size) / sum
    val totalScore         = propertyMatchScore + fileMatchScore

    DuplicatePcb(
      pcb = pcbv2,
      gid = assembly.gid,
      propertyMatches = pcbMatchCandidate.propertyMatches,
      propertyMatchScore = propertyMatchScore,
      exactFiles = matchedFiles,
      fileMatchScore = fileMatchScore,
      totalScore = totalScore
    )
  }

  private val FILE_MATCH_WEIGHT = 50d

  private def propertyWeight(propertyMatch: PropertyMatch): Int = propertyWeight(propertyMatch.property)
  private def propertyWeight(property: MatchablePcbProperty): Int = property match {
    case MatchablePcbProperty.LayerCount  => 5
    case MatchablePcbProperty.BoardWidth  => 20
    case MatchablePcbProperty.BoardHeight => 20
  }
}

object PCBServiceImpl extends Logging {
  val optionalProperties = Set(
    Settings.NUMBER_OF_PREPREGS,
    Settings.NUMBER_OF_LAMINATION_CYCLES
  )

  def createUpdate(
      input: PCBV2Update,
      oldPcb: PCBVersion,
      original: PCBSpecification,
      canRemoveUndefined: Boolean
  ): (Seq[Property], Seq[Property]) = {
    val properties = oldPcb.meta.map(PCBV2Layer.to)
    val settingUpdate =
      input
        .settings
        .getOrElse {
          throw new TransportException(TransportErrorCode.BadRequest, s"Missing update request")
        }

    val updateInfo = PCBV2Layer.from(settingUpdate, properties.flatMap(_.layerStack.layercount))
    val oldInfo    = oldPcb.meta.map(_.properties).getOrElse(Map.empty)

    val (delete, update) = updateInfo.properties.partition {
      case (key, value) => oldInfo.get(key).contains(value)
    }

    val toRemoveFromSpec =
      if (canRemoveUndefined) original.settings.properties.filter {
        case (key, _) => !updateInfo.properties.contains(key) && optionalProperties.contains(key)
      }
      else Seq.empty

    val toDelete = delete ++ toRemoveFromSpec

    logger.info(s"update properties ${update.toSeq} delete ${toDelete.toSeq}")

    (update.values.toSeq, toDelete.values.toSeq)
  }
}

final case class DuplicatePcbCandidate(
    version: UUID,
    fileMatches: Set[UUID],
    propertyMatches: Seq[PropertyMatch]
)

final case class DuplicatePcbScore(
    propertyMatches: Seq[PropertyMatch],
    propertyConfidence: Int,
    exactFiles: Seq[PCBV2File],
    totalConfidence: Int
)

//
