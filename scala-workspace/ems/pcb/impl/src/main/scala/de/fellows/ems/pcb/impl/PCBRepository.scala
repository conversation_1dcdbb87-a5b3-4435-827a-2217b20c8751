package de.fellows.ems.pcb.impl

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core._
import com.lightbend.lagom.scaladsl.persistence.ReadSideProcessor.ReadSideHandler
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.impl.entity.pcb._
import de.fellows.ems.pcb.model
import de.fellows.ems.pcb.model.DFM.Properties.DFM
import de.fellows.ems.pcb.model._
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.FilePath
import de.fellows.utils.meta._
import play.api.Logging
import play.api.libs.json.Json

import java.util.UUID
import scala.annotation.nowarn
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._

class PCBRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    materializer: Materializer
) {
  PCBEventProcessor.registerCodecs(session)

  def getRendersFor(file: UUID): Future[Map[String, FilePath]] =
    session.selectAll(
      """
        | SELECT * FROM renders WHERE file = ?
      """.stripMargin,
      file
    ).map(_.map { r =>
      r.getString("rType") -> r.get("path", classOf[FilePath])
    }.toMap)

  def getDuplicateFiles(team: String, version: UUID, hashes: Seq[String]): Future[Seq[(UUID, UUID)]] =
    session.selectAll(
      """
        | SELECT * FROM file_hashes WHERE team = ? AND hash IN ?
      """.stripMargin,
      team,
      hashes.asJava
    ).map { hashRows =>
      val builder = Vector.newBuilder[(UUID, UUID)]
      builder.sizeHint(hashRows.size)
      hashRows.foreach { row =>
        val rowVersion = row.getUUID("version")
        if (rowVersion != version) {
          builder.addOne((rowVersion, row.getUUID("file")))
        }
      }
      builder.result()
    }

  def getTeamProjectMetas(
      team: String,
      version: UUID
  ): Future[Seq[SimpleMetaInfo]] =
    session.selectAll(
      """
        | SELECT * FROM project_meta WHERE team = ?
      """.stripMargin,
      team
    ).map { rows =>
      val builder = Vector.newBuilder[SimpleMetaInfo]
      builder.sizeHint(rows.size)

      rows.foreach { row =>
        val rowVersion = row.getUUID("version")
        if (rowVersion != version) {
          builder += SimpleMetaInfo(
            versionId = rowVersion,
            boardWidth = row.getDouble("width"),
            boardHeight = row.getDouble("height"),
            layerCount = row.getInt("layerCount")
          )
        }
      }

      builder.result()
    }

}

private[impl] class PCBEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext
) extends ReadSideProcessor[PCBEvent] with Logging {
  PCBEventProcessor.registerCodecs(session)

  private var stmtSetFile: PreparedStatement       = _
  private var stmtDeleteFile: PreparedStatement    = _
  private var stmtSetFileFormat: PreparedStatement = _
  private var stmtSetRender: PreparedStatement     = _
//  var stmtSetHoles: PreparedStatement        = _
  private var stmtSetMetaInfo: PreparedStatement     = _
  private var stmtSetFileMetaInfo: PreparedStatement = _

  private var stmtSetFileHash: PreparedStatement    = _
  private var stmtSetProjectMeta: PreparedStatement = _

  // language=SQL
  private def createTables(): Future[Done] =
    for {

      _ <- PCBCodecHelper.loadTypes(session)

      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS files (
          |           version uuid,
          |           id uuid,
          |           name text,
          |           path frozen<filepath>,
          |           ftype frozen<filetype>,
          |           format frozen<format>,
          |           inverted boolean,
          |           PRIMARY KEY(version, id)
          |);
        """.stripMargin
      )

      _ <- session.executeCreateTable(
        """
            CREATE TABLE IF NOT EXISTS renders (
              file uuid,
              rType text,
              created timestamp,
              path filepath,

              PRIMARY KEY (file, rType)
            )
          """
      )

      _ <- session.executeCreateTable(
        """
            CREATE TABLE IF NOT EXISTS metainfo (
              version uuid,
              size frozen<format>,
              technology text,

              properties text,

              numericalProperties list<frozen<numProperty>>,
              listProperties list<frozen<listProperty>>,

              PRIMARY KEY (version)
            )
          """
      )

      // TODO: deleting file hashes?
      _ <- session.executeCreateTable(
        """
            CREATE TABLE IF NOT EXISTS file_hashes (
              team text,
              version uuid,
              file uuid,
              hash text,
              PRIMARY KEY ((team, hash), version, file)
            )
          """
      )

      _ <- session.executeCreateTable(
        """
            CREATE TABLE IF NOT EXISTS project_meta (
              team text,
              version uuid,
              layerCount int,
              width double,
              height double,
              PRIMARY KEY (team, version)
            )
          """
      )

      _ <- session.executeCreateTable(
        """
            CREATE TABLE IF NOT EXISTS filemetainfo (
              file uuid,
              size frozen<format>,
              technology text,

              properties text,

              numericalProperties list<frozen<numProperty>>,
              listProperties list<frozen<listProperty>>,

              PRIMARY KEY (file)
            )
          """
      )

    } yield {
      PCBEventProcessor.registerCodecs(session)

      Done
    }

  def prepareStatements(): Future[Done] =
    for {

      setFile <- session.prepare(
        """
          | UPDATE files SET name = ?, path = ?, ftype =?, inverted = ? WHERE version = ? AND id = ?
        """.stripMargin
      )
      deleteFile <- session.prepare(
        """
          | DELETE FROM files WHERE version = ? AND id = ?
        """.stripMargin
      )
      setFileFormat <- session.prepare(
        """
          | UPDATE files SET format = ? WHERE version = ? AND id = ?
        """.stripMargin
      )

      setRender <- session.prepare(
        """
          | UPDATE renders SET created = ?, path = ? WHERE file = ? AND rType = ?
        """.stripMargin
      )

      setMetaInfo <- session.prepare(
        """
          | UPDATE metainfo SET
          |    technology = ?,
          |    size = ?,
          |    properties = ?
          |
          | WHERE version = ?
        """.stripMargin
      )
      setFileMetaInfo <- session.prepare(
        """
          | UPDATE filemetainfo SET
          |    technology = ?,
          |    size = ?,
          |
          |    properties = ?
          |
          | WHERE file = ?
        """.stripMargin
      )

      setFileHash <- session.prepare(
        """
          |INSERT INTO file_hashes (team, hash, version, file)
          |VALUES (:team, :hash, :version, :file);
        """.stripMargin
      )

      setProjectMeta <- session.prepare(
        """
          | UPDATE project_meta SET
          |   layerCount = :layerCount,
          |   width = :width,
          |   height = :height
          | WHERE team = :team AND version = :version
        """.stripMargin
      )

      _ <- PCBEventProcessor.registerCodecs(session)
    } yield {

      stmtSetFile = setFile
      stmtDeleteFile = deleteFile
      stmtSetFileFormat = setFileFormat
      stmtSetRender = setRender
      stmtSetMetaInfo = setMetaInfo
      stmtSetFileMetaInfo = setFileMetaInfo

      stmtSetFileHash = setFileHash
      stmtSetProjectMeta = setProjectMeta

      Done
    }

  def updateFile(assRef: AssemblyReference, f: GerberFile): Future[List[BoundStatement]] = {

    println(s"update file $f")
    Future.successful(
      List(
        stmtSetFile.bind(
          f.name,
          f.path,
          f.fType,
          f.inverted.orNull,
          assRef.version,
          f.id
        )
      ) ++ (f.format match {

        case Some(frm) =>
          List(stmtSetFileFormat.bind(
            frm,
            assRef.version,
            f.id
          ))
        case None => List()
      }) ++
        (f.hash match {
          case Some(hash) =>
            List(
              stmtSetFileHash
                .bind()
                .setString("team", assRef.team)
                .setUUID("file", f.id)
                .setUUID("version", assRef.version)
                .setString("hash", hash)
            )
          case None => List()
        })
    )
  }

  private def updateFile(event: FileDimensionSet): Future[List[BoundStatement]] =
    updateFile(event.assRef, event.f)

  private def updateFile(@nowarn event: FileSet): Future[List[BoundStatement]] =
    updateFile(event.assRef, event.f)

  private def updateFile(event: FilesSet): Future[List[BoundStatement]] =
    Future.sequence(event.f.map(updateFile(event.assRef, _)))
      .map(_.toList.flatten)

  private def deleteFile(event: FileDeleted): Future[List[BoundStatement]] =
    Future.successful(List(stmtDeleteFile.bind(event.assRef.version, event.f.id)))

  private def addRender(event: RenderAdded): Future[immutable.Seq[BoundStatement]] =
    Future.successful(event.newRender.map { x =>
      stmtSetRender.bind(event.time, x._2, event.file.id, x._1)
    }.toList)

  private def setMetaInfo(event: MetaInfoSet): Future[immutable.Seq[BoundStatement]] = {
    val meta                            = event.f
    val layerCount: Option[BigDecimal]  = meta \? "layercount"
    val boardWidth: Option[BigDecimal]  = meta \? "boardwidth" orElse meta \? DFM.WIDTH
    val boardHeight: Option[BigDecimal] = meta \? "boardheight" orElse meta \? DFM.HEIGHT
    Future.successful(
      List(
        stmtSetMetaInfo.bind(
          "default",
          null,
          Json.toJson(event.f).toString(),
          event.assRef.version
        ),
        stmtSetProjectMeta.bind()
          .setString("team", event.assRef.team)
          .setUUID("version", event.assRef.version)
          .setInt("layerCount", layerCount.fold(0)(_.intValue))
          .setDouble("width", boardWidth.fold(0d)(_.doubleValue))
          .setDouble("height", boardHeight.fold(0d)(_.doubleValue))
      )
    )
  }

  private def setFileMetaInfo(event: FileMetaInfoSet): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(stmtSetFileMetaInfo.bind(
      "default",
      null,
      Json.toJson(event.f).toString(),
      event.assRef.version
    )))

  override def buildHandler(): ReadSideHandler[PCBEvent] = readSide.builder[PCBEvent]("pcbrepo-v1.6")
    .setGlobalPrepare(createTables)
    .setPrepare(_ => prepareStatements())
    .setEventHandler[FileSet](e => updateFile(e.event))
    .setEventHandler[FilesSet](e => updateFile(e.event))
    .setEventHandler[FileDeleted](e => deleteFile(e.event))
    .setEventHandler[FileDimensionSet](e => updateFile(e.event))
    .setEventHandler[RenderAdded](e => addRender(e.event))
    .setEventHandler[MetaInfoSet](e => setMetaInfo(e.event))
    .setEventHandler[FileMetaInfoSet](e => setFileMetaInfo(e.event))
    .build()

  override def aggregateTags: Set[AggregateEventTag[PCBEvent]] = PCBEvent.Tag.allTags

}

object PCBEventProcessor {

  def toApi(f: Format): model.Format =
    model.Format(
      f.unit,
      f.resolution,
      f.dimension,
      f.complexity,
      f.scaling,
      f.gerberscale
    )

  def toApi(f: GerberFile): LayerFile =
    model.LayerFile(
      Option(f.id),
      Some(f.name),
      None,
      f.format.map(toApi),
      Option(f.fType),
      f.metaInfo.getOrElse(MetaInfo()),
      f.inverted.getOrElse(false)
    )

  def registerCodecs(s: CassandraSession)(implicit ctx: ExecutionContext): Future[Seq[Session]] =
    s.underlying().flatMap { c =>
      implicit val ci: Session = c

      Future.sequence(Seq(
        PCBCodecHelper.registerPCBCodecs(s)
      ))
    }

}
