package de.fellows.ems.pcb.impl.entity

import com.lightbend.lagom.scaladsl.playjson.{JsonMigration, JsonMigrations, JsonSerializer, JsonSerializerRegistry}
import de.fellows.ems.pcb.impl.entity.pcb._
import de.fellows.ems.pcb.impl.entity.specification.SpecificationCommands.{RemoveSpecification => _, _}
import de.fellows.ems.pcb.impl.entity.specification.SpecificationEvents._
import de.fellows.ems.pcb.model.{SetOutline => _, _}
import de.fellows.utils.UUIDUtils
import de.fellows.utils.meta.MetaInfo
import play.api.libs.json.{JsObject, JsPath, JsString, JsValue, Json}

import java.util.UUID

object PCBServiceSerializerRegistry extends JsonSerializerRegistry {
  override def serializers = List(
    JsonSerializer[GerberFilesResponse],
    JsonSerializer[MetaInfoResponse],
    JsonSerializer[PCBVersion],
    JsonSerializer[PCBSpecification],
    JsonSerializer[DrillFile],
    JsonSerializer[Outline],
    JsonSerializer[Graphics],
    JsonSerializer[Dimension],
    JsonSerializer[BigPoint],
    JsonSerializer[MetaInfo],
    JsonSerializer[GerberFile],
    JsonSerializer[SetVersion],
    JsonSerializer[GetVersion],
    JsonSerializer[VersionSet],
    JsonSerializer[SetFileFormat],
    JsonSerializer[FileDimensionSet],
    JsonSerializer[SetOutlineCommand],
    JsonSerializer[OutlineSet],
    JsonSerializer[FailRender],
    JsonSerializer[SetFile],
    JsonSerializer[UpdateFile],
    JsonSerializer[UpdatePCB],
    JsonSerializer[SetFileTypes],
    JsonSerializer[SetFiles],
    JsonSerializer[FileSet],
    JsonSerializer[FilesSet],
    JsonSerializer[DeleteFile],
    JsonSerializer[FileDeleted],
    JsonSerializer[SetOutlineCommand],
    JsonSerializer[SetOutlineCandidates],
    JsonSerializer[AddOutlineCandidates],
    JsonSerializer[OutlineSet],
    JsonSerializer[OutlineCandidateSet],
    JsonSerializer[OutlineCandidatesSet],
    JsonSerializer[AddDrills],
    JsonSerializer[DrillsAdded],
    JsonSerializer[RemoveDrills],
    JsonSerializer[ReconciledDrillsRemoved],
    JsonSerializer[ReconciledDrillsAdded],
    JsonSerializer[RenderAdded],
    JsonSerializer[FinishFileMatching],
    JsonSerializer[FileTypesSet],
    JsonSerializer[FileTypesChanged],
    JsonSerializer[SetMetaInfoFormat],
    JsonSerializer[SetFileMetaInfoProperty],
    JsonSerializer[SetMetaInfoProperty],
    JsonSerializer[RemoveMetaInfoProperty],
    JsonSerializer[RemoveFileMetaInfoProperty],
    JsonSerializer[MetaInfoSet],
    JsonSerializer[FileMetaInfoSet],
    JsonSerializer[AnalysisStateChanged],
    JsonSerializer[CreateSpecification],
    JsonSerializer[SetDFMProperties],
    JsonSerializer[SetUserProperties],
    JsonSerializer[SetSettingProperties],
    JsonSerializer[RemoveSpecification],
    JsonSerializer[SpecificationCreated],
    JsonSerializer[DFMPropertySet],
    JsonSerializer[SettingPropertySet],
    JsonSerializer[UserPropertySet],
    JsonSerializer[SpecificationRemoved],
    JsonSerializer[AddSpecification],
    JsonSerializer[RemoveSpecification],
    JsonSerializer[SpecificationsChanged],
    JsonSerializer[SaveSpecification],
    JsonSerializer[SpecificationSaved],
    JsonSerializer[SetSpecificationPreview],
    JsonSerializer[SetSpecificationStatus],
    JsonSerializer[SpecificationStatusSet],
    JsonSerializer[GetSpecification],
    JsonSerializer[SpecificationPreviewChanged],
    JsonSerializer[DefaultSpecificationChanged],
    JsonSerializer[UpdatePCB],
    JsonSerializer[PCBUpdated],
    JsonSerializer[RenderStateChanged],
    JsonSerializer[AddDrillSets],
    JsonSerializer[DrillSetsAdded],
    JsonSerializer[PCBTimelineChanged],
    JsonSerializer[SpecificationTimelineChanged],
    JsonSerializer[AddUnreconciledDrills],
    JsonSerializer[UnreconciledDrillsAdded],
    JsonSerializer[UnreconciledDrillsSet],
    JsonSerializer[SetNetList],
    JsonSerializer[NetListSet],
    JsonSerializer[CloneFromPcbVersion],
    JsonSerializer[PcbVersionSet]
  )

  private def pcbUpdate(pcb: JsObject): JsObject = {
    val files         = (JsPath \ "files").read[Seq[GerberFile]].reads(pcb).get
    val legacyOutline = (JsPath \ "outline" \ "file").read[UUID].reads(pcb).get
    val userChoice    = (JsPath \ "outline" \ "userChoice").read[Boolean].reads(pcb).get
    val metaInfo      = (JsPath \ "outline" \ "metaInfo").read[MetaInfo].reads(pcb).getOrElse(MetaInfo())
    val score         = (JsPath \ "outline" \ "score").readNullable[BigDecimal].reads(pcb).get

    val path = files.find(_.id == legacyOutline).map(f =>
      f.path.copy(base = "outline", filename = s"${f.path.filename}.outline.json.gzip")
    )

    val ol = Outline(
      id = UUIDUtils.nil,
      file = Some(legacyOutline),
      path = path.get,
      userChoice = userChoice,
      metaInfo = metaInfo,
      score = score.getOrElse(0),
      density = Density(0, 0)
    )

    (pcb - "outline") + ("outline" -> Json.toJson(ol))
  }

  private val pcbMigration = new JsonMigration(currentVersion = 2) {
    override def transform(fromVersion: Int, json: JsObject): JsValue =
      if (fromVersion < 2) {
        pcbUpdate(json)
      } else {
        json
      }
  }

  private val pcbUpdatedMigration = new JsonMigration(3) {
    override def transform(fromVersion: Int, json: JsObject): JsObject =
      if (fromVersion < 2) {
        val dfmStatus = (JsPath \ "dfmStatus").read[JsString].reads(json).get
        json + ("analysisStatus" -> dfmStatus) - "dfmStatus"
      } else if (fromVersion < 3) {
        val pcb        = (JsPath \ "pcb").read[JsObject].reads(json).get
        val updatedPcb = pcbUpdate(pcb)
        (json - "pcb") + ("pcb" -> updatedPcb)
      } else {
        json
      }
  }

  override def migrations = Map[String, JsonMigration](
    classOf[PCBUpdated].getName -> pcbUpdatedMigration,
    classOf[PCBVersion].getName -> pcbMigration,
    JsonMigrations.renamed("de.fellows.ems.pcb.model.DFMStatus", inVersion = 2, toClass = classOf[MetaStatus])
  )
}
