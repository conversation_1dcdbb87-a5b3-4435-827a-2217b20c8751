package de.fellows.ems.pcb.impl.matcher
import de.fellows.ems.pcb.impl.matcher.Confidence.FullConfidence
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging
import de.luminovo.odb.odbpp.model.{ODBStructuredText, ODBUtils}
import play.api.libs.json.{Format, Json}

import java.nio.file.Path
import scala.util.Using

case class ODBLayerInfo(
    layerType: String,
    context: Option[String],
    polarity: Option[String],
    row: Option[Int],
    addType: Option[String],
    start: Option[String],
    end: Option[String]
)

object ODBLayerInfo {
  implicit val f: Format[ODBLayerInfo] = Json.format[ODBLayerInfo]
}

class ODBMatcher(allFiles: Seq[FilePath])(implicit serviceDefinition: ServiceDefinition) extends StackrateLogging {

  val typeMap = Map(
    // we only use tops for now, refine later
    "signal"       -> LayerConstants.COPPER_TOP,
    "power_ground" -> LayerConstants.PLANE_MID,
//    "dielectric" -> "",
    "mixed"        -> LayerConstants.COPPER_MID,
    "solder_mask"  -> LayerConstants.SOLDERMASK_TOP,
    "solder_paste" -> LayerConstants.PASTE_TOP,
    "silk_screen"  -> LayerConstants.SILKSCREEN_TOP,
    "drill"        -> LayerConstants.DRILL,
    "rout"         -> LayerConstants.DRILL,
    "document"     -> LayerConstants.MECHANICAL,
    "component"    -> LayerConstants.MECHANICAL // TODO add component type
//    "mask"             -> ,
//    "conductive_paste" ->
  )

  def id: String = "odb"

  def confidence: Int = FullConfidence

  def service: String = serviceDefinition.name

  def matchFiles(): Map[FilePath, Seq[FileMatch]] = {
    logger.info(s"matching ODB files: ${allFiles.map(_.filename).mkString(", ")}")

    val roots = allFiles.flatMap { fp =>
      ODBUtils.findODBRoot(fp.toJavaPath)
    }.distinct

    if (roots.size != 1) {
      throw new IllegalStateException(
        s"Illegal numer of ODB roots found: ${roots.mkString(", ")} -- ${allFiles.map(_.toJavaPath).mkString(", ")}"
      )
    }
    val root = roots.head

    logger.info(s"matching ODB root: $root")

    val matrix = root.resolve("matrix/matrix")

    val typemap: Map[String, ODBLayerInfo] = ODBMatcher.createTypeMap(matrix)

    val (featureFiles, restFiles) = allFiles
      .filter(_.toJavaFile.isDirectory)
      .partition { fp =>
      val path = fp.toJavaPath
      path.resolve("features").toFile.exists() || path.resolve("profile").toFile.exists()
    }

    val types =
      typemap.flatMap { x =>
        featureFiles.find(_.filename.toLowerCase == x._1.toLowerCase).map { fp =>
          fp -> x._2
        }
      }

    logger.info(s"found types\n${types.mkString("\n")}")

    val signals = matchSignals(types)

    val stepProfiles = root.resolve("steps").toFile.listFiles().map { step =>
      step.toPath.resolve("profile")
    }

    val profiles = allFiles.filter(f => stepProfiles.contains(f.toJavaPath)).map { fp =>
      fp -> createMatch(0, LayerConstants.OUTLINE)
        .copy(mimeType = LayerConstants.Mime.odblinerecord)
    }

    val r: Seq[(FilePath, FileMatch)] =
      (
        signals ++
          matchTopBottom(
            types,
            Set("solder_mask"),
            LayerConstants.SOLDERMASK_TOP,
            LayerConstants.SOLDERMASK_BOTTOM
          ) ++
          matchTopBottom(
            types,
            Set("solder_paste"),
            LayerConstants.PASTE_TOP,
            LayerConstants.PASTE_BOTTOM
          ) ++
          matchTopBottom(
            types,
            Set("silk_screen"),
            LayerConstants.SILKSCREEN_TOP,
            LayerConstants.SILKSCREEN_BOTTOM
          ) ++
          matchDrills(types, signals) ++
          matchGeneric(
            types,
            Set("document", "component", "mask", "dielectric", "conductive_paste"),
            LayerConstants.MECHANICAL
          ) ++
          profiles ++
          restFiles.map { fp =>
            fp -> createMatch(0, LayerConstants.UNKNOWN)
          }
      )

    logger.info(s"matched ${r}")
    r.map(x => x._1 -> Seq(x._2)).toMap
  }

  def matchTopBottom(
      types: Map[FilePath, ODBLayerInfo],
      odbTypesToMatch: Set[String],
      topType: String,
      botType: String
  ): Seq[(FilePath, FileMatch)] = {
    val layers = types.filter(x => odbTypesToMatch.contains(x._2.layerType)).toSeq.sortBy(_._2.row)
    if (layers.nonEmpty) {
      val top = layers.head
      Seq(top._1 -> createMatch(1, topType)) ++
        (if (layers.size > 1) {
           val bot = layers.last
           Seq(bot._1 -> createMatch(2, botType))
         } else {
           Seq()
         })
    } else {
      Seq()
    }

  }

  def matchGeneric(
      types: Map[FilePath, ODBLayerInfo],
      genericTypes: Set[String],
      matchedGenericType: String
  ): Seq[(FilePath, FileMatch)] = {
    val genericLayers = types.filter(x => genericTypes.contains(x._2.layerType)).toSeq.sortBy(_._2.row)

    genericLayers.zipWithIndex.map { x =>
      val (layer, index) = x
      layer._1 -> createMatch(index + 1, matchedGenericType)
    }
  }

  def matchDrills(
      types: Map[FilePath, ODBLayerInfo],
      signals: Seq[(FilePath, FileMatch)]
  ): Seq[(FilePath, FileMatch)] = {
    val drillTypes  = Set("drill", "rout")
    val drillLayers = types.filter(x => drillTypes.contains(x._2.layerType)).toSeq.sortBy(_._2.row)

    drillLayers.zipWithIndex.map { x =>
      val (layer, index) = x

      val startIndex =
        signals.zipWithIndex.find(l => layer._2.start.contains(l._1._1.filename.toLowerCase)).map(_._2 + 1)
      val endIndex =
        signals.zipWithIndex.find(l => layer._2.end.contains(l._1._1.filename.toLowerCase)).map(_._2 + 1)

      layer._1 -> createMatch(
        index + 1,
        LayerConstants.DRILL,
        from = startIndex,
        to = endIndex
      )
    }
  }

  private def matchSignals(types: Map[FilePath, ODBLayerInfo]): Seq[(FilePath, FileMatch)] = {
    val copperTypes = Set("signal", "power_ground", "mixed")
    val copperLayers =
      types.filter(x => copperTypes.contains(x._2.layerType) && !x._2.context.contains("misc")).toSeq.sortBy(_._2.row)

    if (copperLayers.nonEmpty) {
      val top = copperLayers.head
      Seq(top._1 ->
        createMatch(1, LayerConstants.COPPER_TOP)) ++
        (if (copperLayers.size > 1) {
           val bot = copperLayers.last
           val mid = copperLayers.drop(1).dropRight(1) // size is at least 2, so this is safe.

           mid.zipWithIndex.map { x =>
             val (layer, index) = x
             x._1._1 -> (layer._2.polarity.contains("negative") match {
               case true  => createMatch(index + 2, LayerConstants.PLANE_MID)
               case false => createMatch(index + 2, LayerConstants.COPPER_MID)
             })
           } :+
             bot._1 -> createMatch(mid.size + 2, LayerConstants.COPPER_BOTTOM)
         } else {
           Seq()
         })
    } else {
      Seq()
    }
  }

  private def createMatch(index: Int, mappedType: String, from: Option[Int] = None, to: Option[Int] = None) =
    FileMatch(
      confidence = confidence,
      matcher = id,
      service = Some(service),
      category = LayerConstants.Categories.odb,
      fileType = Some(mappedType),
      productionFile = Some(true),
      mimeType = LayerConstants.Mime.odblayer,
      index = Some(index), // 1 is top, first mid is 2. index is 0-based, so we add 2
      from = from,
      to = to
    )
}

object ODBMatcher {

  def createTypeMap(matrix: Path): Map[String, ODBLayerInfo] = {
    val parsedMatrix = Using.resource(ODBUtils.fromFile(matrix.toFile)) { matrixSource =>
      ODBStructuredText.parse(matrixSource.getLines())
    }

    val typemap =
      parsedMatrix.obj("LAYER")
        .flatMap { layer =>
          val t   = layer.assignment("TYPE").map(_.toLowerCase)
          val ctx = layer.assignment("CONTEXT").map(_.toLowerCase)
          val n   = layer.assignment("NAME").map(_.toLowerCase)
          val p   = layer.assignment("POLARITY").map(_.toLowerCase)
          val r   = layer.assignment("ROW").map(_.toInt)
          val at  = layer.assignment("ADD_TYPE").map(_.toLowerCase)

          val start = layer.assignment("START_NAME").map(_.toLowerCase)
          val end   = layer.assignment("END_NAME").map(_.toLowerCase)

          (n, t) match {
            case (Some(name), Some(typ)) =>
              Some(name -> ODBLayerInfo(typ.toLowerCase, ctx, p, r, at, start, end))

            case _ => None
          }

        }.toMap
    typemap
  }
}
