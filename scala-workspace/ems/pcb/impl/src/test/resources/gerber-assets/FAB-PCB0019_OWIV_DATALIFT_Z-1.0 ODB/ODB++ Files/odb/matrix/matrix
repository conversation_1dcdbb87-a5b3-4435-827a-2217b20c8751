
STEP {
    COL=1
    NAME=PCB
}


LAYER {
    ROW=1
    CONTEXT=BOARD
    TYPE=COMPONENT
    ID=1
    NAME=COMP_+_TOP
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=2
    CONTEXT=BOARD
    TYPE=SOLDER_PASTE
    ID=2
    NAME=TOP_PASTE
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=3
    CONTEXT=BOARD
    TYPE=SILK_SCREEN
    ID=3
    NAME=TOP_OVERLAY
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=4
    CONTEXT=BOARD
    TYPE=SOLDER_MASK
    ID=4
    NAME=TOP_SOLDER
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=5
    CONTEXT=BOARD
    TYPE=SIGNAL
    ID=5
    NAME=TOP
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=6
    CONTEXT=BOARD
    TYPE=DIELECTRIC
    ID=6
    NAME=DIELECTRIC_1
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
    DIELECTRIC_TYPE=PREPREG
    DIELECTRIC_NAME=FR-4
}

LAYER {
    ROW=7
    CONTEXT=BOARD
    TYPE=SIGNAL
    ID=7
    NAME=L2_GND
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=8
    CONTEXT=BOARD
    TYPE=DIELECTRIC
    ID=8
    NAME=DIELECTRIC2
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
    DIELECTRIC_TYPE=CORE
    DIELECTRIC_NAME=FR-4
}

LAYER {
    ROW=9
    CONTEXT=BOARD
    TYPE=SIGNAL
    ID=9
    NAME=L3_VCC
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=10
    CONTEXT=BOARD
    TYPE=DIELECTRIC
    ID=10
    NAME=DIELECTRIC3
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
    DIELECTRIC_TYPE=PREPREG
    DIELECTRIC_NAME=FR-4
}

LAYER {
    ROW=11
    CONTEXT=BOARD
    TYPE=SIGNAL
    ID=11
    NAME=L4_SIG
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=12
    CONTEXT=BOARD
    TYPE=DIELECTRIC
    ID=12
    NAME=DIELECTRIC4
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
    DIELECTRIC_TYPE=CORE
    DIELECTRIC_NAME=FR-4
}

LAYER {
    ROW=13
    CONTEXT=BOARD
    TYPE=SIGNAL
    ID=13
    NAME=L5_GND
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=14
    CONTEXT=BOARD
    TYPE=DIELECTRIC
    ID=14
    NAME=DIELECTRIC5
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
    DIELECTRIC_TYPE=PREPREG
    DIELECTRIC_NAME=FR-4
}

LAYER {
    ROW=15
    CONTEXT=BOARD
    TYPE=SIGNAL
    ID=15
    NAME=BOTTOM
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=16
    CONTEXT=BOARD
    TYPE=SOLDER_MASK
    ID=16
    NAME=BOTTOM_SOLDER
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=17
    CONTEXT=BOARD
    TYPE=SILK_SCREEN
    ID=17
    NAME=BOTTOM_OVERLAY
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=18
    CONTEXT=BOARD
    TYPE=SOLDER_PASTE
    ID=18
    NAME=BOTTOM_PASTE
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=19
    CONTEXT=BOARD
    TYPE=DRILL
    ID=19
    NAME=DRILL_NON-PLATED_BOTTOM-TOP
    POLARITY=POSITIVE
    START_NAME=TOP
    END_NAME=BOTTOM
    OLD_NAME=
}

LAYER {
    ROW=20
    CONTEXT=BOARD
    TYPE=DRILL
    ID=20
    NAME=DRILL_PLATED_BOTTOM-TOP
    POLARITY=POSITIVE
    START_NAME=TOP
    END_NAME=BOTTOM
    OLD_NAME=
}

LAYER {
    ROW=21
    CONTEXT=MISC
    TYPE=DOCUMENT
    ID=21
    NAME=DRILL_DRAWING_TOP-BOTTOM
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=22
    CONTEXT=MISC
    TYPE=DOCUMENT
    ID=22
    NAME=DRILL_GUIDE_TOP-BOTTOM
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=23
    CONTEXT=BOARD
    TYPE=COMPONENT
    ID=23
    NAME=COMP_+_BOT
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=24
    CONTEXT=MISC
    TYPE=DOCUMENT
    ID=24
    NAME=PCB_SHAPE
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=25
    CONTEXT=MISC
    TYPE=DOCUMENT
    ID=25
    NAME=PCB_FAB_NOTES
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=26
    CONTEXT=MISC
    TYPE=DOCUMENT
    ID=26
    NAME=PANEL_SHAPE
    POLARITY=POSITIVE
    START_NAME=
    END_NAME=
    OLD_NAME=
}

LAYER {
    ROW=27
    CONTEXT=BOARD
    TYPE=ROUT
    ID=27
    NAME=rout
    POLARITY=POSITIVE
    START_NAME=TOP
    END_NAME=BOTTOM
    OLD_NAME=
}

