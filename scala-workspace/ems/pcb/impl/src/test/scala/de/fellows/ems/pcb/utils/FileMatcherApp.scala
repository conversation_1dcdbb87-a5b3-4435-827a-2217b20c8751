package de.fellows.ems.pcb.utils

import de.fellows.ems.pcb.impl.matcher.DefaultFilesMatcher
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import play.api.libs.json.Json

object FileMatcherApp extends App {
  implicit val sd: ServiceDefinition = ServiceDefinition("test")

  private val path: FilePath = FilePath(args(0))
  println(s"match ${path.toPath}")
  val paths = if (path.toJavaFile.isDirectory) {
    path.toJavaFile.listFiles().map(f => FilePath(f.getPath)).toSeq
  } else {
    Seq(path)
  }
  val r = Json.prettyPrint(Json.toJson(DefaultFilesMatcher(paths).createMatches()))
  println(r)

}
