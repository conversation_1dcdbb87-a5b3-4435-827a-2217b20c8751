grammar Calc ;

expression
	: plusOrMinus ;

plusOrMinus
	: plusOrMinus MINUS multOrDiv	#Minus
	| plusOrMinus PLUS multOrDiv	#Plus
	| multOrDiv	 # ToMultOrDiv ;

multOrDiv
	: multOrDiv MULTI unary	# Multiplication
	| multOrDiv DIV unary	# Division
	| unary	# ToUnary ;

unary
	: PLUS unary 	#UnaryPlus
	| MINUS unary	#UnaryMinus
	| atom	#ToAtom ;

atom
	: num=NUMBER	#ConstantNum
	| varuse	#ToVarUse
	| LPAREN expression RPAREN #Braces ;

varuse
	: (VAR varname)	#VarUsage ;

varname
	: 	NUMBER+ ;

NUMBER
	: (DIGIT)*('.'(DIGIT)+)? ;

MULTI
	: ('x'|'X') ;

DIV
	: ('/') ;

PLUS
	: '+' ;

MINUS
	: '-' ;

VAR
	: '$' ;

LPAREN
	: '(' ;

RPAREN
	: ')' ;

WS
	: [ \r\n\t] + -> skip ;

fragment
DIGIT
	: '0'..'9' ;
