lexer grammar XNCLexer ;

M48 : 'M48';

M30 : 'M30';

G05: 'G05';
G01: 'G01';
G02: 'G02';
G03: 'G03';
G00: 'G00';
M15: 'M15';
M16: 'M16';

EOH : '%';

LZ : 'LZ';
TZ : 'TZ';

DOT : '.';
COMMA: ',';

T : 'T';
C : 'C';
X : 'X';
Y : 'Y';
A : 'A';
S : 'S';
F : 'F';
B : 'B';

COMMENT: ';'  -> pushMode(COMMENTMODE) ;

INCH: 'INCH';
METRIC : 'METRIC';

NEWLINE: ('\r\n'|'\n'|'\r');

DIGIT
	: '0'..'9' ;
POSDIGIT
	: '1'..'9' ;



mode COMMENTMODE;

COMMENTCONTENT: STRING ;

STRING : ~('\r' | '\n')+ ;

CLOSE
: ('\r\n'|'\n'|'\r') -> popMode ;

