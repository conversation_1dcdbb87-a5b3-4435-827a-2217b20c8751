// Generated from /home/<USER>/src/epibator/scala-workspace/ems/pcb/gerber-parser/src/main/antlr4/XNCParser.g4 by ANTLR 4.13.1
package de.fellows.ems.gerber.xnc.parser;
import org.antlr.v4.runtime.tree.ParseTreeListener;

/**
 * This interface defines a complete listener for a parse tree produced by
 * {@link XNCParser}.
 */
public interface XNCParserListener extends ParseTreeListener {
	/**
	 * Enter a parse tree produced by {@link XNCParser#xnc}.
	 * @param ctx the parse tree
	 */
	void enterXnc(XNCParser.XncContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#xnc}.
	 * @param ctx the parse tree
	 */
	void exitXnc(XNCParser.XncContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#header}.
	 * @param ctx the parse tree
	 */
	void enterHeader(XNCParser.HeaderContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#header}.
	 * @param ctx the parse tree
	 */
	void exitHeader(XNCParser.HeaderContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#toolTable}.
	 * @param ctx the parse tree
	 */
	void enterToolTable(XNCParser.ToolTableContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#toolTable}.
	 * @param ctx the parse tree
	 */
	void exitToolTable(XNCParser.ToolTableContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#body}.
	 * @param ctx the parse tree
	 */
	void enterBody(XNCParser.BodyContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#body}.
	 * @param ctx the parse tree
	 */
	void exitBody(XNCParser.BodyContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#drillSelection}.
	 * @param ctx the parse tree
	 */
	void enterDrillSelection(XNCParser.DrillSelectionContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#drillSelection}.
	 * @param ctx the parse tree
	 */
	void exitDrillSelection(XNCParser.DrillSelectionContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#routSelection}.
	 * @param ctx the parse tree
	 */
	void enterRoutSelection(XNCParser.RoutSelectionContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#routSelection}.
	 * @param ctx the parse tree
	 */
	void exitRoutSelection(XNCParser.RoutSelectionContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#rout}.
	 * @param ctx the parse tree
	 */
	void enterRout(XNCParser.RoutContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#rout}.
	 * @param ctx the parse tree
	 */
	void exitRout(XNCParser.RoutContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#comment}.
	 * @param ctx the parse tree
	 */
	void enterComment(XNCParser.CommentContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#comment}.
	 * @param ctx the parse tree
	 */
	void exitComment(XNCParser.CommentContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#startHeader}.
	 * @param ctx the parse tree
	 */
	void enterStartHeader(XNCParser.StartHeaderContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#startHeader}.
	 * @param ctx the parse tree
	 */
	void exitStartHeader(XNCParser.StartHeaderContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#setUnit}.
	 * @param ctx the parse tree
	 */
	void enterSetUnit(XNCParser.SetUnitContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#setUnit}.
	 * @param ctx the parse tree
	 */
	void exitSetUnit(XNCParser.SetUnitContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#toolDeclaration}.
	 * @param ctx the parse tree
	 */
	void enterToolDeclaration(XNCParser.ToolDeclarationContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#toolDeclaration}.
	 * @param ctx the parse tree
	 */
	void exitToolDeclaration(XNCParser.ToolDeclarationContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#endHeader}.
	 * @param ctx the parse tree
	 */
	void enterEndHeader(XNCParser.EndHeaderContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#endHeader}.
	 * @param ctx the parse tree
	 */
	void exitEndHeader(XNCParser.EndHeaderContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#drillMode}.
	 * @param ctx the parse tree
	 */
	void enterDrillMode(XNCParser.DrillModeContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#drillMode}.
	 * @param ctx the parse tree
	 */
	void exitDrillMode(XNCParser.DrillModeContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#setRoutMode}.
	 * @param ctx the parse tree
	 */
	void enterSetRoutMode(XNCParser.SetRoutModeContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#setRoutMode}.
	 * @param ctx the parse tree
	 */
	void exitSetRoutMode(XNCParser.SetRoutModeContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#selectTool}.
	 * @param ctx the parse tree
	 */
	void enterSelectTool(XNCParser.SelectToolContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#selectTool}.
	 * @param ctx the parse tree
	 */
	void exitSelectTool(XNCParser.SelectToolContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#drillHit}.
	 * @param ctx the parse tree
	 */
	void enterDrillHit(XNCParser.DrillHitContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#drillHit}.
	 * @param ctx the parse tree
	 */
	void exitDrillHit(XNCParser.DrillHitContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#toolDown}.
	 * @param ctx the parse tree
	 */
	void enterToolDown(XNCParser.ToolDownContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#toolDown}.
	 * @param ctx the parse tree
	 */
	void exitToolDown(XNCParser.ToolDownContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#toolUp}.
	 * @param ctx the parse tree
	 */
	void enterToolUp(XNCParser.ToolUpContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#toolUp}.
	 * @param ctx the parse tree
	 */
	void exitToolUp(XNCParser.ToolUpContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#linearRout}.
	 * @param ctx the parse tree
	 */
	void enterLinearRout(XNCParser.LinearRoutContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#linearRout}.
	 * @param ctx the parse tree
	 */
	void exitLinearRout(XNCParser.LinearRoutContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#cwRout}.
	 * @param ctx the parse tree
	 */
	void enterCwRout(XNCParser.CwRoutContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#cwRout}.
	 * @param ctx the parse tree
	 */
	void exitCwRout(XNCParser.CwRoutContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#ccwRout}.
	 * @param ctx the parse tree
	 */
	void enterCcwRout(XNCParser.CcwRoutContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#ccwRout}.
	 * @param ctx the parse tree
	 */
	void exitCcwRout(XNCParser.CcwRoutContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#toolNumber}.
	 * @param ctx the parse tree
	 */
	void enterToolNumber(XNCParser.ToolNumberContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#toolNumber}.
	 * @param ctx the parse tree
	 */
	void exitToolNumber(XNCParser.ToolNumberContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#holeDiameter}.
	 * @param ctx the parse tree
	 */
	void enterHoleDiameter(XNCParser.HoleDiameterContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#holeDiameter}.
	 * @param ctx the parse tree
	 */
	void exitHoleDiameter(XNCParser.HoleDiameterContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#decimal}.
	 * @param ctx the parse tree
	 */
	void enterDecimal(XNCParser.DecimalContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#decimal}.
	 * @param ctx the parse tree
	 */
	void exitDecimal(XNCParser.DecimalContext ctx);
	/**
	 * Enter a parse tree produced by {@link XNCParser#integer}.
	 * @param ctx the parse tree
	 */
	void enterInteger(XNCParser.IntegerContext ctx);
	/**
	 * Exit a parse tree produced by {@link XNCParser#integer}.
	 * @param ctx the parse tree
	 */
	void exitInteger(XNCParser.IntegerContext ctx);
}