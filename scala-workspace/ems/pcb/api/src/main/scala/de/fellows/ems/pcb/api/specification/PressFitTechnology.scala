package de.fellows.ems.pcb.api.specification

import de.fellows.utils.JsonFormats
import de.fellows.utils.JsonFormats.EnumResolver
import play.api.libs.json.Format

sealed abstract class PressFitTechnology(val value: String)

object PressFitTechnology {
  object MassivePressFit  extends PressFitTechnology("massive")
  object FlexiblePressFit extends PressFitTechnology("flexible")

  val ALL = Seq(
    MassivePressFit,
    FlexiblePressFit
  )

  implicit val res: EnumResolver[PressFitTechnology] = JsonFormats.resolver(ALL, _.value)
  implicit val f: Format[PressFitTechnology]         = JsonFormats.enumFormat[PressFitTechnology]
}
