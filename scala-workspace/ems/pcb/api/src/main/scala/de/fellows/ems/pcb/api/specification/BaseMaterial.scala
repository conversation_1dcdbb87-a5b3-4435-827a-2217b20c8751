package de.fellows.ems.pcb.api.specification

import de.fellows.utils.JsonFormats
import de.fellows.utils.JsonFormats.EnumResolver
import de.fellows.utils.apidoc.StackrateAPIObject
import io.swagger.v3.oas.annotations.media.Schema
import play.api.libs.json.Format

@StackrateAPIObject()
@Schema(`type` = "string", allowableValues = Array("FR-2", "FR-3", "FR-4", "CEM1", "CEM2", "CEM3", "polyimide"))
sealed abstract class BaseMaterial(val value: String) {
  override def toString: String = value
}



object BaseMaterial {
  object FR2 extends BaseMaterial("FR-2")
  object FR3 extends BaseMaterial("FR-3")
  object FR4 extends BaseMaterial("FR-4")
  object CEM1 extends BaseMaterial("CEM1")
  object CEM2 extends BaseMaterial("CEM2")
  object CEM3 extends BaseMaterial("CEM3")
  object Polyimide extends BaseMaterial("polyimide")

  val ALL = Seq(FR2, FR3, FR4, CEM1, CEM2, CEM3, Polyimide)

  implicit val res: EnumResolver[BaseMaterial] = JsonFormats.resolver(ALL, _.value)
  implicit val f: Format[BaseMaterial] = JsonFormats.enumFormat[BaseMaterial]
}
