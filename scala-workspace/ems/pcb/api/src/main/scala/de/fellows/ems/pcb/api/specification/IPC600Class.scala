package de.fellows.ems.pcb.api.specification

import enumeratum._
import de.fellows.utils.JsonFormats
import de.fellows.utils.JsonFormats.{BasicResolver, EnumResolver}
import de.fellows.utils.apidoc.StackrateAPIObject
import io.swagger.v3.oas.annotations.media.Schema
import play.api.libs.json.Format

@StackrateAPIObject()
@Schema(`type` = "string", allowableValues = Array("IPCNone", "IPC1", "IPC2", "IPC2+", "IPC3", "IPC3a"))
sealed abstract class IPC600Class(override val entryName: String) extends EnumEntry

object IPC600Class extends Enum[IPC600Class] with PlayJsonEnum[IPC600Class] {
  override def values: IndexedSeq[IPC600Class] = findValues

  case object IPCNone  extends IPC600Class("ipcnone")
  case object IPC1     extends IPC600Class("ipc1")
  case object IPC2     extends IPC600Class("ipc2")
  case object IPC2Plus extends IPC600Class("ipc2+")
  case object IPC3     extends IPC600Class("ipc3")
  case object IPC3a    extends IPC600Class("ipc3a")

  val legacyNames = new BasicResolver[IPC600Class](Map(
    IPCNone  -> Seq(""),
    IPC1     -> Seq("1"),
    IPC2     -> Seq("2"),
    IPC2Plus -> Seq("2+"),
    IPC3     -> Seq("3"),
    IPC3a    -> Seq("3a")
  ))

  val res: EnumResolver[IPC600Class] =
    JsonFormats.resolver[IPC600Class](values, _.entryName) ++ legacyNames
}
