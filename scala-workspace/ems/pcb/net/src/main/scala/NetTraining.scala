package de.fellows.ems.pcb.compiler

import java.io.{ File, FileFilter }
import java.util
import java.util.Random

object NetTraining {
  protected val seed             = 1337
  protected var rng              = new Random(seed)
  protected val maxPathsPerLabel = 0
  protected var numLabels        = 2
  protected var splitTrainTest   = 0.8

  var height           = 100
  var width            = 100
  var channels         = 3
  protected var epochs = 100

  val batchSize = 10

  def main(args: Array[String]): Unit = {
    //    StatusListenerConfigHelper
    val labelMaker = new ParentPathLabelGenerator
    val mainPath   = new File("/home/<USER>/Code/fellows/KI/outline/training")
    val fileSplit  = new FileSplit(mainPath, NativeImageLoader.ALLOWED_FORMATS, rng)

    val numExamples = Math.toIntExact(fileSplit.length())

    numLabels = fileSplit.getRootDir().listFiles(new FileFilter {
      override def accept(file: File): Boolean = file.isDirectory
    }).length;

    val pathFilter = new RandomPathFilter(rng);

    val inputSplit = fileSplit.sample(pathFilter, splitTrainTest, 1 - splitTrainTest)
    val trainData  = inputSplit(0)
    val testData   = inputSplit(1)

    println(s"training with ${trainData.length()}, testing with ${testData.length()} and $numLabels labels")
    println(s"examples ${numExamples}")

    /** Data Setup -> transformation
      *   - Transform = how to tranform images and generate large dataset to train on
      */
    val flipTransform1 = new FlipImageTransform(rng)
    val flipTransform2 = new FlipImageTransform(new Random(123))
    val warpTransform  = new WarpImageTransform(rng, 42)
    val shuffle        = false
    val pipeline: util.List[Pair[ImageTransform, java.lang.Double]] =
      util.Arrays.asList(new Pair(flipTransform1, 0.9), new Pair(flipTransform2, 0.8), new Pair(warpTransform, 0.5))

    val transform = new PipelineImageTransform(pipeline, shuffle)

    val scaler = new ImagePreProcessingScaler(0, 1)

    println("build model")

    val network = outlineModel

    val uiServer = UIServer.getInstance

    val statsStorage = new FileStatsStorage(new File(System.getProperty("java.io.tmpdir"), "ui-stats.dl4j"))
    uiServer.attach(statsStorage)

    val trainRR                    = new ImageRecordReader(height, width, channels, labelMaker)
    var trainIter: DataSetIterator = null

    println("train model")

    val testRR = new ImageRecordReader(height, width, channels, labelMaker)
    testRR.initialize(testData)
    val testIter = new RecordReaderDataSetIterator(testRR, batchSize, 1, numLabels)
    scaler.fit(testIter)
    testIter.setPreProcessor(scaler)

    // listeners
    network.setListeners(
      new StatsListener(statsStorage),
      new ScoreIterationListener(1),
      new EvaluativeListener(testIter, 1, InvocationType.EPOCH_END)
    )

    // Train without transformations
    trainRR.initialize(trainData, null)
    trainIter = new RecordReaderDataSetIterator(trainRR, batchSize, 1, numLabels)
    scaler.fit(trainIter)
    trainIter.setPreProcessor(scaler)
    network.fit(trainIter, epochs)

    // Train with transformations
    //    trainRR.initialize(trainData, transform)
    //    trainIter = new RecordReaderDataSetIterator(trainRR, batchSize, 1, numLabels)
    //    scaler.fit(trainIter)
    //    trainIter.setPreProcessor(scaler)
    //    network.fit(trainIter, epochs)

    println("Save model....")
    val basePath = "/tmp/model/"
    network.save(new File(basePath + "model.bin"))

  }

  def lenetModel: MultiLayerNetwork = {

    /** Revisde Lenet Model approach developed by ramgo2 achieves slightly above random Reference:
      * https://gist.github.com/ramgo2/833f12e92359a2da9e5c2fb6333351c5
      */

    val conf = new NeuralNetConfiguration.Builder()
      .seed(seed)
      .l2(0.005)
      .activation(Activation.RELU)
      .weightInit(WeightInit.XAVIER)
      .updater(new AdaDelta())
      .list()
      .layer(0, convInit("cnn1", channels, 50, Array[Int](5, 5), Array[Int](1, 1), Array[Int](0, 0), 0))
      .layer(1, maxPool("maxpool1", Array[Int](2, 2)))
      .layer(2, conv5x5("cnn2", 100, Array[Int](5, 5), Array[Int](1, 1), 0))
      .layer(3, maxPool("maxool2", Array[Int](2, 2)))
      .layer(4, new DenseLayer.Builder().nOut(500).build)
      .layer(
        5,
        new OutputLayer.Builder(LossFunctions.LossFunction.NEGATIVELOGLIKELIHOOD)
          .nOut(numLabels)
          .activation(Activation.SOFTMAX)
          .build()
      )
      .setInputType(InputType.convolutional(height, width, channels))
      .build()

    new MultiLayerNetwork(conf)
  }

  def outlineModel: MultiLayerNetwork = {

//    val conf3 = new NeuralNetConfiguration.Builder()
//      .seed(ThreadLocalRandom.current().nextLong())
//      .optimizationAlgo(OptimizationAlgorithm.STOCHASTIC_GRADIENT_DESCENT)
//      .iterations(1)
//      .learningRate(0.006)
//      .updater(Nesterovs(0.9))
//      .regularization(true)
//
//      .l2(1e-4).list()
//      .layer(0, DenseLayer.Builder().nIn(1000) // Number of input datapoints.
//        .nOut(1000) // Number of output datapoints.
//        .activation(ActivationReLU()) // Activation function.
//        .weightInit(WeightInit.XAVIER) // Weight initialization.
//        .build())
//      .layer(1, OutputLayer.Builder(LossFunctions.LossFunction.NEGATIVELOGLIKELIHOOD).nIn(1000).nOut(2).activation(ActivationSoftmax()).weightInit(WeightInit.XAVIER).build()).pretrain(false).backprop(true).build()
//    val model = MultiLayerNetwork(conf) model
//    .init() // define the answeres val cat = Nd4j.create(doubleArrayOf(1.0,0.0)) val dog = Nd4j.create(doubleArrayOf(0.0,1.0)) // catLabels and dogLabels hold the input labels from the training set in a list val trainingData = catLabels.map { DataSet(it, cat) } + dogLabels.map { DataSet(it, dog) } // don't show dogs and cats one after another Collections.shuffle(trainingData) // feed all data to the model model.fit(ListDataSetIterator(trainingData))

    val conf = new NeuralNetConfiguration.Builder()
      .seed(seed)
      .optimizationAlgo(OptimizationAlgorithm.STOCHASTIC_GRADIENT_DESCENT)
      .updater(new Adam())
      .l2(1e-4)
      .list()
      .layer(new DenseLayer.Builder()
        .nIn(30000)                    // Number of input datapoints.
        .nOut(1000)                    // Number of output datapoints.
        .activation(Activation.RELU)   // Activation function.
        .weightInit(WeightInit.XAVIER) // Weight initialization.
        .build())
      .layer(new OutputLayer.Builder(LossFunctions.LossFunction.NEGATIVELOGLIKELIHOOD)
        .nIn(1000)
        .nOut(2)
        .activation(Activation.SOFTMAX)
        .weightInit(WeightInit.XAVIER)
        .build())
      .setInputType(InputType.convolutional(height, width, channels))
      .build()

    new MultiLayerNetwork(conf)
  }

  def alexnetModel: MultiLayerNetwork = {

    /** AlexNet model interpretation based on the original paper ImageNet Classification with Deep Convolutional Neural
      * Networks and the imagenetExample code referenced.
      * http://papers.nips.cc/paper/4824-imagenet-classification-with-deep-convolutional-neural-networks.pdf
      */
    val nonZeroBias = 1
    val dropOut     = 0.5
    val conf =
      new NeuralNetConfiguration.Builder()
        .seed(seed)
        .weightInit(new NormalDistribution(0.0, 0.01))
        .activation(Activation.RELU)
        .updater(new Sgd(1e-3))
        .gradientNormalization(GradientNormalization.RenormalizeL2PerLayer)
        .l2(5 * 1e-4)
        .list()
        .layer(convInit("cnn1", channels, 96, Array[Int](11, 11), Array[Int](4, 4), Array[Int](3, 3), 0))
        .layer(new LocalResponseNormalization.Builder().name("lrn1").build).layer(maxPool("maxpool1", Array[Int](3, 3)))
        .layer(conv5x5("cnn2", 256, Array[Int](1, 1), Array[Int](2, 2), nonZeroBias))
        .layer(new LocalResponseNormalization.Builder().name("lrn2").build).layer(maxPool("maxpool2", Array[Int](3, 3)))
        .layer(conv3x3("cnn3", 384, 0)).layer(conv3x3("cnn4", 384, nonZeroBias)).layer(conv3x3(
          "cnn5",
          256,
          nonZeroBias
        ))
        .layer(maxPool("maxpool3", Array[Int](3, 3)))
        .layer(fullyConnected("ffn1", 4096, nonZeroBias, dropOut, new NormalDistribution(0, 0.005)))
        .layer(fullyConnected("ffn2", 4096, nonZeroBias, dropOut, new NormalDistribution(0, 0.005)))
        .layer(new OutputLayer.Builder(LossFunctions.LossFunction.NEGATIVELOGLIKELIHOOD)
          .name("output")
          .nOut(numLabels)
          .activation(Activation.SOFTMAX).build)
        .setInputType(InputType.convolutional(height, width, channels)).build

    new MultiLayerNetwork(conf)
  }

  private def maxPool(name: String, kernel: Array[Int]) =
    new SubsamplingLayer.Builder(kernel, Array[Int](2, 2)).name(name).build

  private def convInit(
      name: String,
      in: Int,
      out: Int,
      kernel: Array[Int],
      stride: Array[Int],
      pad: Array[Int],
      bias: Double
  ) = new ConvolutionLayer.Builder(kernel, stride, pad).name(name).nIn(in).nOut(out).biasInit(bias).build

  private def conv3x3(name: String, out: Int, bias: Double) = new ConvolutionLayer.Builder(
    Array[Int](3, 3),
    Array[Int](1, 1),
    Array[Int](1, 1)
  ).name(name).nOut(out).biasInit(bias).build

  private def conv5x5(name: String, out: Int, stride: Array[Int], pad: Array[Int], bias: Double) =
    new ConvolutionLayer.Builder(Array[Int](5, 5), stride, pad).name(name).nOut(out).biasInit(bias).build

  private def fullyConnected(name: String, out: Int, bias: Double, dropOut: Double, dist: NormalDistribution) =
    new DenseLayer.Builder().name(name).nOut(out).biasInit(bias).dropOut(dropOut).dist(dist).build
}
