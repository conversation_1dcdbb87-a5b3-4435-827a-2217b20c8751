package de.fellows.ems.pcb.model.codec

import com.datastax.driver.core.{ ProtocolVersion, TypeCodec, UDTValue, UserType }
import de.fellows.ems.pcb.model.{ BigPoint, Dimension }

import java.nio.ByteBuffer

class DimensionCodec(cdc: TypeCodec[UDTValue]) extends TypeCodec[Dimension](cdc.getCqlType, classOf[Dimension]) {

  def from(value: Dimension): UDTValue =
    if (value == null) null
    else
      cdc.getCqlType.asInstanceOf[UserType].newValue()
        .set("min", value.min, classOf[BigPoint])
        .set("max", value.max, classOf[BigPoint])

  def to(value: UDTValue): Dimension =
    if (value == null) {
      null
    } else {
      Dimension(
        min = value.get("min", classOf[BigPoint]),
        max = value.get("max", classOf[BigPoint])
      )
    }

  override def serialize(value: Dimension, protocolVersion: ProtocolVersion): ByteBuffer =
    cdc.serialize(from(value), protocolVersion)

  override def deserialize(bytes: ByteBuffer, protocolVersion: ProtocolVersion): Dimension =
    to(cdc.deserialize(bytes, protocolVersion))

  override def parse(value: String): Dimension =
    if (value == null || value.isEmpty) null
    else to(cdc.parse(value))

  override def format(value: Dimension): String =
    if (value == null) null
    else cdc.format(from(value))
}
