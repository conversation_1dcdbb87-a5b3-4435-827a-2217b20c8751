package de.fellows.ems.pcb.model.graphics

import de.fellows.utils.JsonFormats
import play.api.libs.json.Json.MacroOptions
import play.api.libs.json.{Format, Json, JsonConfiguration}

case class Macro(name: String, instructions: Seq[MacroInstruction]) {
  override def toString: String = {
    val lines = instructions.map {
      case VariableAssignment(variable, assignment) => s"$variable = $assignment"
      case PrimitiveUsage(primitive, modifier)      => s"flash $primitive $modifier"
    }

    s"Macro ${name}: \n\t" + lines.mkString("\n\t") + "\n\n"
  }
}

sealed trait MacroInstruction {}

case class VariableAssignment(variable: Int, assignment: String) extends MacroInstruction

case class PrimitiveUsage(primitive: Int, modifier: Seq[String]) extends MacroInstruction

object VariableAssignment {
  implicit val f: Format[VariableAssignment] = Json.format[VariableAssignment]
}

object PrimitiveUsage {
  implicit val f: Format[PrimitiveUsage] = Json.format[PrimitiveUsage]
}

object MacroInstruction {
  implicit val cfg: JsonConfiguration.Aux[MacroOptions] = JsonFormats.JsonSealedTraitConfig
  implicit val format: Format[MacroInstruction]         = Json.format[MacroInstruction]
}

object Macro {
  implicit val f: Format[Macro] = Json.format[Macro]
}
