package de.fellows.ems.pcb.model.graphics

import de.fellows.ems.pcb.model.BigPoint
import de.fellows.ems.pcb.model.graphics.Paths.FunctionalPathIterator
import de.fellows.ems.pcb.model.graphics.tree.Distance
import org.apache.batik.ext.awt.geom.ExtendedGeneralPath

import java.awt.Shape
import java.awt.geom._

object Geometry {
  case class ArcPositions(start: GPoint, startAngle: Double, end: GPoint, endAngle: Double, center: GPoint)
  case class ArcParameters(absSweep: Double, sweepFlag: Int, largeFlag: Int)

  /** get the area of a Rectangle
    */
  def area(r: Rectangle2D): Double =
    r.getWidth * r.getHeight

  /** Get the bounds of a shape.
    *
    * The [[Shape Shape.getBounds2D]] is not accurate enough for accurate measurements (especially for curves, where it
    * is quite a lot larger than necessary). This method interpolates multiple points on the curves, and gets the bounds
    * for those.
    *
    * This is more accurate, but also slower.
    */
  def bounds(shape: Shape): Option[Rectangle2D] =
    bounds(shape.getPathIterator(null))

  def distance(x1: Double, y1: Double, x2: Double, y2: Double): Double =
    Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2))

  /** calculate the bounding box of n arbitrary path
    *
    * @param numberOfPoints
    *   how many points to interpolate on a curve
    */
  def bounds(iter: PathIterator, numberOfPoints: Int = 100): Option[Rectangle2D] = {
    var minX: Double = Double.PositiveInfinity
    var minY: Double = Double.PositiveInfinity
    var maxX: Double = Double.NegativeInfinity
    var maxY: Double = Double.NegativeInfinity

    def extend(x: Double, y: Double): Unit = {
      minX = Math.min(minX, x)
      minY = Math.min(minY, y)
      maxX = Math.max(maxX, x)
      maxY = Math.max(maxY, y)
    }

    iter.foreach {
      case x: LineTo =>
        extend(x.fromX, x.fromY)
        extend(x.toX, x.toY)

      case x: QuadTo =>
        // interpolate points on the curve and add them to the bounds
        val (min, max) = x.boundingBox(numberOfPoints)
        extend(min.x, min.y)
        extend(max.x, max.y)

      case x: CubicTo =>
        // interpolate points on the curve and add them to the bounds
        val (min, max) = x.boundingBox(numberOfPoints)
        extend(min.x, min.y)
        extend(max.x, max.y)

      case x: Close =>
        extend(x.fromX, x.fromY)
        extend(x.toX, x.toY)

      case _ =>
    }

    val validMinX = !minX.isInfinity && !minX.isNaN
    val validMinY = !minY.isInfinity && !minY.isNaN
    val validMaxX = !maxX.isInfinity && !maxX.isNaN
    val validMaxY = !maxY.isInfinity && !maxY.isNaN

    Option.when(validMinX && validMinY && validMaxX && validMaxY) {
      new Rectangle2D.Double(minX, minY, maxX - minX, maxY - minY)
    }
  }

  def circle(center: BigPoint, diameter: BigDecimal): Ellipse2D.Double = {
    val start = center - (diameter / 2)
    new Ellipse2D.Double(start.x.doubleValue, start.y.doubleValue, diameter.doubleValue, diameter.doubleValue)
  }

  def thermal(
      center: GPoint,
      internalScale: Int,
      _innerDiameter: Double,
      _outerDiameter: Double,
      _gapSize: Double,
      _numSpokes: Int,
      _angle: Double,
      rounded: Boolean
  ): Area = {
    if (_numSpokes == 0) {
      return new Area()
    }
    val innerRadius = _innerDiameter / 2.0 * internalScale
    val outerRadius = _outerDiameter / 2.0 * internalScale
    val innerStart  = center tx innerRadius
    val outerStart  = center tx outerRadius
    val gapSize     = _gapSize * internalScale
    val width       = outerRadius - innerRadius

    val centerStart = innerStart tx (width / 2)

    val a = new Area()
    val angleOfGap =
      if (rounded) {
        Geometry.angleOfAcuteTriangleInRadians(innerRadius, innerRadius, gapSize + width / 2)._3 / 2
      } else {
        Geometry.angleOfAcuteTriangleInRadians(innerRadius, innerRadius, gapSize)._3 / 2
      }

    val anglePerSpoke = 360 / _numSpokes

    (0 to _numSpokes).foreach { i =>
      val rotatedInnerStart  = Geometry.rotate(innerStart, center, Math.toRadians(anglePerSpoke * i) + angleOfGap)
      val rotatedCenterStart = Geometry.rotate(centerStart, center, Math.toRadians(anglePerSpoke * i) + angleOfGap)
      val rotatedOuterStart  = Geometry.rotate(outerStart, center, Math.toRadians(anglePerSpoke * i) + angleOfGap)

      val rotatedInnerEnd =
        Geometry.rotate(innerStart, center, Math.toRadians(anglePerSpoke * (i + 1)) - angleOfGap)
      val rotatedOuterEnd =
        Geometry.rotate(outerStart, center, Math.toRadians(anglePerSpoke * (i + 1)) - angleOfGap)
      val rotatedCenterEnd =
        Geometry.rotate(centerStart, center, Math.toRadians(anglePerSpoke * (i + 1)) - angleOfGap)

      val p = new Path2D.Double()

      p.moveTo(rotatedInnerStart.x, rotatedInnerStart.y)
      p.append(
        Geometry.getArc(rotatedInnerStart, rotatedInnerEnd, center, innerRadius, false).getPathIterator(null),
        true
      )

      p.lineTo(rotatedOuterEnd.x, rotatedOuterEnd.y)
      p.append(
        Geometry.getArc(rotatedOuterEnd, rotatedOuterStart, center, outerRadius, true).getPathIterator(null),
        true
      )
      p.closePath()

      val spokeArea = new Area(p)
      if (rounded) {
        // add end caps
        spokeArea.add(new Area(Geometry.circle(rotatedCenterStart, width)))
        spokeArea.add(new Area(Geometry.circle(rotatedCenterEnd, width)))
      }
      a.add(spokeArea)
    }

    val scaledAngle = _angle
    a.transform(AffineTransform.getRotateInstance(Math.toRadians(scaledAngle)))
    a
  }

  def circle(center: GPoint, diameter: BigDecimal): Ellipse2D.Double =
    circle(BigPoint(center.x, center.y), diameter)

  def getArc(start: GPoint, end: GPoint, center: GPoint, radius: Double, clockwise: Boolean) = {
    val arc       = getArcPositions(start, end, center, clockwise)
    val arcParams = findArcParameters(arc)
    val arcPath = ExtendedGeneralPath.computeArc(
      start.x,
      start.y,
      radius,
      radius,
      0,
      arcParams.largeFlag == 1,
      arcParams.sweepFlag == 0,
      end.x,
      end.y
    )
    arcPath
  }

  def getArcPositions(from: GPoint, to: GPoint, center: GPoint, clockwise: Boolean): ArcPositions = {

    val startAngle = Math.atan2(from.y - center.y, from.x - center.x)
    val endAngle   = Math.atan2(to.y - center.y, to.x - center.x)

    val (adjustedStartAngle, adjustedEndAngle) = clockwise match {
      case true =>
        if (startAngle > endAngle) {
          (endAngle, startAngle)
        } else {
          (endAngle, startAngle + (Math.PI * 2))
        }
      case false =>
        if (endAngle > startAngle) {
          (endAngle, startAngle)
        } else {
          (endAngle + (Math.PI * 2), startAngle)
        }
    }

    ArcPositions(
      from,
      adjustedStartAngle,
      to,
      adjustedEndAngle,
      center
    )
  }

  def findArcParameters(arc: ArcPositions): ArcParameters = {
    val sweep    = -(arc.endAngle - arc.startAngle)
    val absSweep = Math.abs(sweep)

    val sweepFlag =
      if (sweep < 0) {
        1
      } else {
        0
      }

    val largeFlag =
      if (absSweep <= Math.PI) {
        0
      } else {
        1
      }
    ArcParameters(absSweep, sweepFlag, largeFlag)
  }

  /** get angle between points as radians
    *
    * @param a
    * @param b
    * @return
    */
  def angle(a: GPoint, b: GPoint): Double = {
    var div = a.magnitude * b.magnitude

    if (div.compare(0) <= 0) {
      div = 1
    }

    val dot = a dot b

    var result = dot / div
    if (result.abs >= 1) {
      result = 1
    }
    if (result.abs <= -1) {
      result = -1
    }
    val res = Math.acos(result.toDouble)
    if (res.isNaN) {
      throw new IllegalStateException(s"radians is NaN: $res ,$div, $dot, $result")
    }
    res
  }

  /** rotate a point around a center by the given radians
    */
  def rotate(point: GPoint, center: GPoint, radians: Double): GPoint = {
    val rotX = (Math.cos(radians) * (point.x - center.x)) - (Math.sin(radians) * (point.y - center.y)) + center.x
    val rotY = (Math.sin(radians) * (point.x - center.x)) - (Math.cos(radians) * (point.y - center.y)) + center.y

    GPoint(round(rotX), round(rotY))
  }

  def angleOfAcuteTriangleInRadians(a: Double, b: Double, c: Double) = {
    val aA = Math.acos((b * b + c * c - a * a) / (2 * b * c))
    val aB = Math.acos((a * a + c * c - b * b) / (2 * a * c))
    val aC = Math.acos((a * a + b * b - c * c) / (2 * a * b))

    (aA, aB, aC)
  }

  // round to 4 decimal places
  def round(d: Double): Double =
    Math.round(d * 10000) / 10000.0

  def calcRadiansOnArc(c: GPoint, p: GPoint): Double = {
    val th     = Math.atan2(p.getY - c.getY, p.getX - c.getX)
    val TWO_PI = Math.PI * 2.0

    th match {
      case x if x < 0      => x + TWO_PI
      case x if x > TWO_PI => x - TWO_PI
      case x               => x
    }

    th + TWO_PI
  }

  def calculateArcRadians(c: GPoint, from: GPoint, to: GPoint): (Double, Double) = {
    val PI_HALF   = Math.PI / 2.0
    val TWO_PI    = Math.PI * 2.0
    var fromTheta = Math.atan2(from.getY - c.getY, from.getX - c.getX)
    var toTheta   = Math.atan2(to.getY - c.getY, to.getX - c.getX)

    fromTheta = fromTheta match {
      case x if x < 0      => x + TWO_PI
      case x if x > TWO_PI => x - TWO_PI
      case x               => x
    }

    toTheta = toTheta match {
      case x if x < 0      => x + TWO_PI
      case x if x > TWO_PI => x - TWO_PI
      case x               => x
    }

    (fromTheta, toTheta)
  }

  /** Calculates the angle between 2 points.
    *
    * @param from
    * @param to
    * @param movingXUp
    * @return
    */
  def calculateRadians(from: GPoint, to: GPoint, movingXUp: Boolean): Double = {
    // draws a helper line parallel to the x axis, starting at 'from',
    val helper   = from + GPoint(0, 20)
    val curAngle = Math.toDegrees(Geometry.angle(to - from, helper - from))

    var radians = 0.0
    if (curAngle >= 90) {
      radians = -Math.toRadians(curAngle - 90.0)
    } else {
      radians = Math.toRadians(90.0 - curAngle)
    }

    if (movingXUp) {
      radians = -radians
    }

    if (radians.isNaN) {
      throw new IllegalStateException(s"radians is NaN: $curAngle")
    }

    radians
  }

  /** clamps the end of the distance to the radius. only if the clamped point is on the same side of the arc as the edge
    * point
    */
  def clampWithArc(
      distance: Distance,
      arcCenter: GPoint,
      arcEdge: GPoint,
      arcRadius: Double,
      arcPoints: Seq[GPoint]
  ): Option[Distance] = {
    val distance1 = Geometry.clampByOffset(distance, -arcRadius)

    val rotatedEdge  = arcPoints(0)
    val rotatedEdge2 = arcPoints(3)

    if (sideOfLine(rotatedEdge, rotatedEdge2, arcEdge) == sideOfLine(rotatedEdge, rotatedEdge2, distance1.end)) {
      Some(
        distance1
      )
    } else {
      if (pointToLine(distance1.end, rotatedEdge, rotatedEdge2).distance <= 0.01) {
        Some(
          distance1
        )
      } else {
        None
      }
    }
  }

  /** remove parts of the distance from the end
    */
  def clampByOffset(distance: Distance, offset: Double): Distance =
    clamp(distance, distance.distance + offset)

  /** clamp distance to the given length by removing/adding from the end
    *
    * @param distance
    * @param length
    * @return
    */
  def clamp(distance: Distance, length: Double): Distance = {
    val x1 = distance.start.getBX
    val y1 = distance.start.getBY
    val x2 = distance.end.getBX
    val y2 = distance.end.getBY

    val d1 = x2 - x1
    val d2 = y2 - y1
    val d =
      Math.sqrt((
        d1 * d1 +
          d2 * d2
      ))

    // https://math.stackexchange.com/questions/1314916/shorten-segment-to-certain-length
    def p(n: Double) = GPoint(((1 - (n / d)) * x1 + (n / d) * x2), ((1 - n / d) * y1 + (n / d) * y2))

    Distance(distance.start, p(length));
  }

  private def point(p0: GPoint, p1: GPoint, p2: GPoint, p3: GPoint) = {
    val x =
      pointOnCurve(p0.x, p1.x, p2.x, p3.x)

    val y =
      pointOnCurve(p0.y, p1.y, p2.y, p3.y)

    GPoint(x, y)
  }

  /** Center between two points
    */
  def center(a: GPoint, b: GPoint): GPoint =
    GPoint((a.x + b.x) / 2, (a.y + b.y) / 2)

  /** get the center of the circle described by the cubic curve */
  private def center(a: GPoint, b: GPoint, c: GPoint): Option[GPoint] = {

    val xDeltaA = b.getX - a.getX
    val yDeltaA = b.getY - a.getY

    val xDeltaB = c.getX - b.getX
    val yDeltaB = c.getY - b.getY

    if (xDeltaA == 0 || yDeltaA == 0 || xDeltaB == 0 || yDeltaB == 0) {
      None
    } else {
      val ma = yDeltaA / xDeltaA
      val mb = yDeltaB / xDeltaB

      val x =
        if (mb - ma == 0) {
          0
        } else {
          (
            (ma * mb * (a.y - c.y))
              + (mb * (a.x + b.x))
              - (ma * (b.x + c.x))
          ) / (2 * (mb - ma))
        }

      val y = (-1 * (x - (a.x + b.x) / 2) / ma) + ((a.y + b.y) / 2)
      Some(GPoint(x, y))
    }
  }

  private def pointOnCurve(p0: Double, p1: Double, p2: Double, p3: Double) = {
    val res = (Math.pow(0.5, 3) * p0) +
      (3 * 0.5 * Math.pow(0.5, 2) * p1) +
      (3 * Math.pow(0.5, 2) * 0.5 * p2) +
      (Math.pow(0.5, 3) * p3)

    res
  }

  case class AuxiliaryCurveParameters(center: GPoint, edgepoint: GPoint, radius: Double)

  /** convert Cubic curves to auxiliary points.
    *
    * @param pts
    * @return
    *   (center, edgepoint, radius)
    */
  def radius(pts: Seq[GPoint]): Option[AuxiliaryCurveParameters] = {
    val edgePoint = point(pts.head, pts(1), pts(2), pts(3))
    val cntr      = center(pts.head, edgePoint, pts(3))
    cntr.map(c => AuxiliaryCurveParameters(c, edgePoint, cntr.get.distance(edgePoint)))
  }

  /** Check whether the given point query is on the convex or concave side of the given arc (linep1, linep2)
    *
    * @param linep1
    *   start of the arc
    * @param linep2
    *   end of the arc
    * @param query
    *   the point
    * @param clockwise
    *   whether the arc is clockwise
    * @return
    *   true, if the point is on the convex side
    */
  def onConvexSide(linep1: GPoint, linep2: GPoint, query: GPoint, clockwise: Boolean): Boolean =
    ((query.x - linep1.x) * (linep2.y - linep1.y) - (query.y - linep1.y) * (linep2.x - linep1.x) > 0) == clockwise

  def sideOfLine(linep1: GPoint, linep2: GPoint, query: GPoint): Boolean =
    ((query.x - linep1.x) * (linep2.y - linep1.y) - (query.y - linep1.y) * (linep2.x - linep1.x) > 0)

  def lineToLine(start1: GPoint, end1: GPoint, start2: GPoint, end2: GPoint): Seq[Distance] =
    Seq(
      pointToLine(start1, start2, end2),
      pointToLine(end1, start2, end2),
      pointToLine(start2, start1, end1),
      pointToLine(end2, start1, end1)
    )

  def pointToLine(p: GPoint, p0: GPoint, p1: GPoint): Distance = {
    val v = p1 - p0
    val w = p - p0

    val c1 = w dot v
    val c2 = v dot v

    if (c1 <= 0) {
      Distance(p, p0)
    } else if (c2 <= c1) {
      Distance(p, p1)
    } else {
      val b  = c1 / c2
      val pb = p0 + (v * b)

      Distance(p, pb)
    }
  }

  /** calculate the factorial of a number
    */
  def factorial(n: Int): Int =
    n match {
      case x if x <= 1 => 1
      case x           => x * factorial(x - 1)
    }

  def surfaceAreaOfHole(diameter: BigDecimal, height: BigDecimal): BigDecimal = {
    val radius = diameter / 2
    2 * Math.PI * radius * height
  }
}
