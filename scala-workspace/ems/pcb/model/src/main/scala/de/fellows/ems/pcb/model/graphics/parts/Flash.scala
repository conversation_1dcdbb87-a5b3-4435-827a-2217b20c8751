package de.fellows.ems.pcb.model.graphics.parts

import de.fellows.ems.pcb.model.graphics._
import de.fellows.ems.pcb.model.graphics.tree.ElementId
import play.api.libs.json.{Format, Json}

import java.awt.Shape

case class FlashOrientation(degrees: Double, mirror: Boolean)

object FlashOrientation {
  implicit val format: Format[FlashOrientation] = Json.format[FlashOrientation]
}

case class Flash(
    aperture: ApertureDefinition,
    s: Shape,
    pol: Polarity,
    i: ElementId[Int],
    target: GPoint,
    a: Option[Map[String, Seq[String]]],
    orientation: Option[FlashOrientation]
) extends Graphic(pol, i, a) {

  override def shape: Shape = s

  override def intersects(other: Graphic): Boolean =
    this.index.x == other.index.x || (other match {
      case otherLine: Flash =>
        val otherArea = otherLine.shape
        doCollisionWithContour(other, otherArea)
      case otherLine: Line =>
        val otherArea = otherLine.shape
        doCollisionWithContour(other, otherArea)
      case _ =>
        super.intersects(other)
    })

  private def doCollisionWithContour(other: Graphic, otherArea: Shape) =
    if (Paths.countourIntersects(shape, otherArea)) {
      true
    } else {
      super.intersects(other)
    }

}
