package de.fellows.ems.pcb.model

import de.fellows.utils.internal.FileType
import de.fellows.utils.spi.{EagleFileTypeDetector, GerberFileTypeDetector, KicadFileTypeDetector}

object LayerConstants {
  val UNKNOWN       = "Unknown"
  val NATIVE_ALTIUM = "native-altium"
  val NATIVE_KICAD  = "native-kicad"
  val NATIVE_EAGLE  = "native-eagle"
  val STACK_UP      = "StackUp"

  val MECHANICAL = "Mechanical"

  val OUTLINE           = "Outline"
  val KEEP_OUT          = "KeepOut"
  val PASTE_TOP         = "PasteTop"
  val PASTE_BOTTOM      = "PasteBottom"
  val SILKSCREEN_TOP    = "SilkscreenTop"
  val SILKSCREEN_BOTTOM = "SilkscreenBottom"
  val SOLDERMASK_TOP    = "SoldermaskTop"
  val SOLDERMASK_BOTTOM = "SoldermaskBottom"
  val COPPER_TOP        = "CopperTop"
  val COPPER_MID        = "CopperMid"
  val PLANE_MID         = "PlaneMid"
  val COPPER_BOTTOM     = "CopperBottom"
  val ADHESIVE_TOP      = "AdhesiveTop"
  val ADHESIVE_BOTTOM   = "AdhesiveBottom"
  val PEELABLE_TOP      = "PeelableTop"
  val PEELABLE_BOTTOM   = "PeelableBottom"
  val DRILL             = "Drill"
  val NPH_DRILL         = "NPHDrill"
  val PH_DRILL          = "PHDrill"
  val DRILLSETS         = "DrillSets"
  val DRILL_PARAMETERS  = "DrillParameters"

  val LEGACY_GERBER = "LegacyGerber"

  val COPPER: Seq[String]         = Seq(COPPER_MID, COPPER_TOP, COPPER_BOTTOM, PLANE_MID)
  val SOLDERMASK: Seq[String]     = Seq(SOLDERMASK_TOP, SOLDERMASK_BOTTOM)
  val SILKSCREEN: Seq[String]     = Seq(SILKSCREEN_TOP, SILKSCREEN_BOTTOM)
  val DRILLS: Seq[String]         = Seq(DRILL, NPH_DRILL, PH_DRILL)
  val WITH_COLLISION: Seq[String] = COPPER

  def isCopper(f: GerberFile): Boolean        = isCopper(f.fType.fileType)
  def isCopper(fileType: String): Boolean     = COPPER.contains(fileType)
  def isSolderMask(f: GerberFile): Boolean    = isSolderMask(f.fType.fileType)
  def isSolderMask(fileType: String): Boolean = SOLDERMASK.contains(fileType)

  object Mime {
    val gerber = Some(GerberFileTypeDetector.MIME)
    val drill  = Some("text/drill")

    val odblayer      = Some("text/odb+layer")
    val odblinerecord = Some("text/odb+record")
    val odbstructured = Some("text/odb+structured")

    def isOdb(mime: String): Boolean = mime.startsWith("text/odb")

    val kicad  = Some(KicadFileTypeDetector.MIME)
    val eagle  = Some(EagleFileTypeDetector.MIME)
    val altium = Some("application/altium")
  }

  object Categories {
    val gerberName     = "gerber"
    val odbName        = FileType.CATEGORY_ODB
    val mechanicalName = "mechanical"
    val unknownName    = "unknown"
    val gerber         = Some(gerberName)
    val odb            = Some(odbName)
    val mechanical     = Some(mechanicalName)
    val unknown        = Some(unknownName)

    val validProductCategories = Set(gerberName, odbName)
  }

}

object RenderConstants {

  val PREVIEW_SVG = "preview_svg"
  val LAYER_SVG   = "layer_svg"
  val COPPER_SVG  = "copper_svg"

  val PREVIEW_JSON = "preview_json"
  val LAYER_JSON   = "layer_json"
  val COPPER_JSON  = "copper_json"

  val ALL_JSON: Seq[String] = Seq(
    PREVIEW_JSON,
    LAYER_JSON,
    COPPER_JSON
  )
  val ALL_SVG: Seq[String] = Seq(
    PREVIEW_SVG,
    LAYER_SVG,
    COPPER_SVG
  )
}
