package de.fellows.ems.pcb.model.graphics.parts

import de.fellows.ems.pcb.model.graphics.tree.ElementId
import de.fellows.ems.pcb.model.graphics.{Graphic, Polarity}

import java.awt.Shape

case class ComplexRegion(
    s: Shape,
    pol: Polarity,
    i: ElementId[Int],
    a: Option[Map[String, Seq[String]]],
    cleared: Option[Seq[Graphic]],
    uncleared: Option[Graphic]
) extends Graphic(pol, i, a) {

  override lazy val id: Option[String] = Some(i.x.toString)
  override def shape: Shape            = s

}
