package de.fellows.ems.pcb.model.graphics.ops

import de.fellows.ems.pcb.model.graphics.{ApertureDefinition, GPoint, Geometry, Rectangle}

object Interpolation {

  def getRectParams(bounds: GPoint, target: GPoint, scaling: Int) = {

    val xSize  = (bounds.x * scaling)
    val ySize  = (bounds.y * scaling)
    val x      = target.getBX - (xSize / 2)
    val y      = target.getBY - (ySize / 2)

    (xSize, ySize, x, y)
  }

  def createRectForC(from: GPoint, to: GPoint, radians: Double, dia: Double) = {
    val rad = dia / 2

    val p1 = Geometry.rotate(from + GPoint(0, rad), from, radians)
    val p2 = Geometry.rotate(from - GPoint(0, rad), from, radians)
    val p3 = Geometry.rotate(to - GPoint(0, rad), to, radians)
    val p4 = Geometry.rotate(to + GPoint(0, rad), to, radians)

    val rec = Rectangle(p1, p2, p3, p4)
    rec
  }

  def createRectForR(from: GPoint, to: GPoint, bounds: GPoint, movement: Movement, scaling: Int): Rectangle = {

    val (fxSize: Double, fySize: Double, fx: Double, fy: Double) = Interpolation.getRectParams(bounds, from, scaling)
    val (txSize: Double, tySize: Double, tx: Double, ty: Double) = Interpolation.getRectParams(bounds, to, scaling)

    val fromRec =
      Rectangle(GPoint(fx, fy), GPoint(fx + fxSize, fy), GPoint(fx + fxSize, fy + fySize), GPoint(fx, fy + fySize))

    val toRec =
      Rectangle(GPoint(tx, ty), GPoint(tx + txSize, ty), GPoint(tx + txSize, ty + tySize), GPoint(tx, ty + tySize))

    if (movement.moveXUp) {
      if (movement.moveYUp) {
        Rectangle(fromRec.p2, toRec.p2, toRec.p4, fromRec.p4)
      } else {
        Rectangle(fromRec.p1, toRec.p1, toRec.p3, fromRec.p3)
      }

    } else {
      if (movement.moveYUp) {
        Rectangle(fromRec.p3, toRec.p3, toRec.p1, fromRec.p1)
      } else {
        Rectangle(fromRec.p4, toRec.p4, toRec.p2, fromRec.p2)
      }
    }

  }

}

case class Movement(moveX: BigDecimal, moveY: BigDecimal) {

  def moveXUp: Boolean =
    moveX >= 0

  def moveYUp: Boolean =
    moveY >= 0

  override def toString: String =
    s"Movement(x=$moveXUp, y=$moveYUp)"
}

object Movement {
  def apply(from: GPoint, to: GPoint): Movement =
    new Movement(to.x - from.x, to.y - from.y)
}
