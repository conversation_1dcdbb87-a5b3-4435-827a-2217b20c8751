package de.fellows.ems.pcb.model.graphics.lookup

import akka.Done
import de.fellows.ems.pcb.model.graphics.{CollisionChecker, Intersectable}

import java.awt.geom.Rectangle2D

trait GraphicLookup[T <: Intersectable[T]] {
  def size(detailed: Boolean = false): Int

  def getComplexity: Int

  def pop(value: (T => Boolean)): Seq[T]

  def copy(): GraphicLookup[T]

  /** Retrieves and removes all elements that collide (exact) with the check
    *
    * @param check
    * @return
    */
  def popCollide(check: T): Seq[T]

  /** retrieve all possible neighbors of the given check
    *
    * @param check
    * @param margin
    * @return
    */
  def neighbor(check: T, margin: Double): Vector[T]

  /** Retrieves all elements that collide (exact) with the check
    *
    * @param check
    * @return
    */
  def collide(check: T): Seq[T]

  /** Retrieves all elements that collide (exact) with the check using the given collision checker
    *
    * @param check
    * @param envelope
    * @param pop
    * @param collisionChecker
    * @return
    */
  def collideWith<PERSON>hecker(
      check: T,
      envelope: Rectangle2D,
      pop: <PERSON><PERSON><PERSON>,
      collisionChecker: CollisionChecker[T, T]
  ): Seq[T]

  /** Inserts a new element
    *
    * @param query
    */
  def insert(query: T)

  /** Visit all elements of this lookup in no particular order
    *
    * @param walker
    */
  def collect[U](walker: LookupCollector[T, U]): Vector[U]

  def walk(walker: LookupWalker[T]): Unit

  /** retrieve the first element.
    *
    * @return
    */
  def head(): Option[T]

  //  def stream(fp: FilePath): Option[FilePath]

  /** applies all clearances in this lookupcontainer and recreates the contents
    *
    * @return
    */
  def processElements(processor: T => Option[T]): Done

  def finish()

}
