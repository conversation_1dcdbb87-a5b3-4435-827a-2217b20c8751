package de.fellows.ems.pcb.model.codec.properties

import com.datastax.driver.core.{ TypeCodec, UDTValue, UserType }
import de.fellows.ems.pcb.model.codec.AbstractCodec
import de.fellows.utils.meta._

import scala.jdk.CollectionConverters._

class ListPropertyCodec(cdc: TypeCodec[UDTValue]) extends AbstractCodec[ListProperty](cdc, classOf[ListProperty]) {
  override def toUDTValue(value: ListProperty): UDTValue =
    if (value == null) null
    else
      cdc.getCqlType.asInstanceOf[UserType].newValue()
        .setString("name", value.name)
        .setList("value", value.value.map(_.toString).asJava)

  override def toPoint(value: UDTValue): ListProperty =
    if (value == null) null
    else
      ListProperty(
        value.getString("name"),
        value.getList("value", classOf[String]).asScala.toSeq
      )
}
