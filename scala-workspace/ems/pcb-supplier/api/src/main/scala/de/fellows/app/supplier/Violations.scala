package de.fellows.app.supplier

import de.fellows.ems.pcb.model.BigPoint
import de.fellows.utils.JsonFormats
import play.api.libs.json.Json.MacroOptions
import play.api.libs.json.{Format, Json, JsonConfiguration}

object Violations {

  sealed trait DFMViolation {
    val location: Option[BigPoint] = None
    val severity: Int              = 1

  }

  case class StringViolation(
      override val location: Option[BigPoint] = None,
      override val severity: Int = 1,
      val value: String
  ) extends DFMViolation

  case class BooleanViolation(
      override val location: Option[BigPoint] = None,
      override val severity: Int = 1,
      val value: Boolean
  ) extends DFMViolation

  case class ListViolation(
      override val location: Option[BigPoint] = None,
      override val severity: Int = 1,
      val value: Seq[String]
  ) extends DFMViolation

  case class DecimalViolation(
      override val location: Option[BigPoint] = None,
      override val severity: Int = 1,
      val value: BigDecimal
  ) extends DFMViolation

  case class DFMViolations(
      technology: String,
      rules: Seq[DFMRuleViolations]
  )

  case class DFMRuleViolations(
      rule: Capability,
      violations: Seq[DFMViolation]
  )

  object DFMRuleViolations {
    implicit val f: Format[DFMRuleViolations] = Json.format[DFMRuleViolations]
  }

  object DFMViolations {
    implicit val f: Format[DFMViolations] = Json.format[DFMViolations]
  }

  object StringViolation {
    implicit val format: Format[StringViolation] = Json.format
  }

  object ListViolation {
    implicit val format: Format[ListViolation] = Json.format
  }

  object DecimalViolation {
    implicit val format: Format[DecimalViolation] = Json.format
  }

  object BooleanViolation {
    implicit val format: Format[BooleanViolation] = Json.format
  }

  object DFMViolation {

    implicit val cfg: JsonConfiguration.Aux[MacroOptions] = JsonFormats.JsonSealedTraitConfig
    implicit val format: Format[DFMViolation]             = Json.format

  }

}
