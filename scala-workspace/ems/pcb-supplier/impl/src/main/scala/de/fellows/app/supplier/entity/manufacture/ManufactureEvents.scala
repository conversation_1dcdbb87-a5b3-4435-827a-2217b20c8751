package de.fellows.app.supplier.entity.manufacture

import com.lightbend.lagom.scaladsl.persistence.{ AggregateEvent, AggregateEventShards, AggregateEventTag }
import de.fellows.app.supplier.SupplierManufacture
import de.fellows.ems.pcb.model.PCBSpecification
import play.api.libs.json
import play.api.libs.json.Json

import java.time.Instant

sealed trait ManufactureEvent extends AggregateEvent[ManufactureEvent] {
  override def aggregateTag: AggregateEventShards[ManufactureEvent] = ManufactureEvent.Tag
}

object ManufactureEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[ManufactureEvent](NumShards)
}

case class MatchingStarted(spec: PCBSpecification, time: Instant) extends ManufactureEvent

case class MatchingFinished(spec: PCBSpecification, time: Instant, result: Seq[SupplierManufacture])
    extends ManufactureEvent

//case class PriceListSet(assemblyReference: AssemblyReference, supplier: String, lists: Map[String, PriceList]) extends ManufactureEvent

object MatchingStarted {
  implicit val format: json.Format[MatchingStarted] = Json.format

}

object MatchingFinished {
  implicit val format: json.Format[MatchingFinished] = Json.format
}
