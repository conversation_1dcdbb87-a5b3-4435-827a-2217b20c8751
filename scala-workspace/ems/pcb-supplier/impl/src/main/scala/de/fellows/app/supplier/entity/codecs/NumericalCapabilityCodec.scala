package de.fellows.app.supplier.entity.codecs

import com.datastax.driver.core.{ TypeCodec, UDTValue, UserType }
import de.fellows.app.supplier.NumericalCapability
import de.fellows.ems.pcb.model.codec.AbstractCodec

class NumericalCapabilityCodec(cdc: TypeCodec[UDTValue])
    extends AbstractCodec[NumericalCapability](cdc, classOf[NumericalCapability]) {

  override def toUDTValue(value: NumericalCapability): UDTValue =
    if (value == null) null
    else
      cdc.getCqlType.asInstanceOf[UserType].newValue()
        .setString("name", value.name)
        .setDecimal("min", value.min.map(_.bigDecimal).orNull)
        .setDecimal("max", value.max.map(_.bigDecimal).orNull)

  override def toPoint(value: UDTValue): NumericalCapability =
    if (value == null) null
    else
      NumericalCapability(
        value.getString("name"),
        getBigDecimal(value, "min"),
        getBigDecimal(value, "max")
      )

}
