package de.fellows.app.supplier

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, EventStreamElement, ReadSideProcessor}
import de.fellows.app.supplier.entity.codecs.{
  BooleanCapabilityCodec,
  ListCapabilityCodec,
  NumericalCapabilityCodec,
  StringCapabilityCodec
}
import de.fellows.app.supplier.entity.supplier._
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.ems.pcb.model.codec.PCBCodecHelper.registerUDTCodec
import de.fellows.utils.communication.ServiceDefinition

import scala.jdk.CollectionConverters._
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}

case class TechnologyDTO(id: String)

class TechnologyRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {
  TechnologyRepository.registerCodecs(session)

  def getTechnologiesForTeam(team: String): Future[Seq[TechnologyDTO]] =
    session.selectAll("SELECT id FROM technologies WHERE team = ?", team).map(_.map { r =>
      TechnologyDTO(r.getString("id"))
    })
}

object TechnologyRepository {
  def registerCodecs(s: CassandraSession)(implicit ctx: ExecutionContext) =
    PCBCodecHelper.registerPCBCodecs(s).map { us =>
      implicit val ius = us

      registerUDTCodec("numericalCapability", tc => new NumericalCapabilityCodec(tc))
      registerUDTCodec("listCapability", tc => new ListCapabilityCodec(tc))
      registerUDTCodec("stringCapability", tc => new StringCapabilityCodec(tc))
      registerUDTCodec("booleanCapability", tc => new BooleanCapabilityCodec(tc))
    }
}

class TechnologyEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[PCBSupplierEvent] {
  var stmtSetTechnology: PreparedStatement      = _
  var stmtDeleteTechnologies: PreparedStatement = _

  def setTechnology(e: EventStreamElement[TechnologySet]): Future[immutable.Seq[BoundStatement]] = {
    val t = e.event.technology
    Future.successful(immutable.List(bind(e.event.id, t)))
  }

  private def delete(team: String, id: String) =
    Future.successful(List(
      stmtDeleteTechnologies.bind(
        team,
        id
      )
    ))

  private def bind(id: String, t: Technology) = {
    val numColl = t.capabilities.collect { case a: NumericalCapability => a } match {
      case x if x.isEmpty => null
      case x              => x.asJava
    }
    val stringsColl = t.capabilities.collect { case a: StringCapability => a } match {
      case x if x.isEmpty => null
      case x              => x.asJava
    }
    val listColl = t.capabilities.collect { case a: ListCapability => a } match {
      case x if x.isEmpty => null
      case x              => x.asJava
    }
    val boolColl = t.capabilities.collect { case a: BooleanCapability => a } match {
      case x if x.isEmpty => null
      case x              => x.asJava
    }

    stmtSetTechnology.bind(
      numColl,
      listColl,
      stringsColl,
      boolColl,
      t.name,
      t.priceCategory.map(_.category).getOrElse(-1),
      t.pcbSupplier.orNull,
      t.team.get,
      t.id.get
    )
  }

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[PCBSupplierEvent] =
    readSide.builder[PCBSupplierEvent]("supplierEventOffset-v1.2")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[TechnologySet](e => setTechnology(e))
      .setEventHandler[TechnologyDeleted](e => delete(e.event.team, e.event.id))
      .build()

  override def aggregateTags: Set[AggregateEventTag[PCBSupplierEvent]] = PCBSupplierEvent.Tag.allTags

  private def prepareStatements() =
    // language=SQL
    for {
      deleteTechnologies <- session.prepare(
        """
          | DELETE FROM technologies WHERE team = :team AND id = :id
        """.stripMargin
      )
      setTechnology <- session.prepare(
        """
          | UPDATE technologies SET
          |    numericals = ?,
          |    lists = ?,
          |    strings = ?,
          |    booleans = ?,
          |    name = ?,
          |    priceIndication = ?,
          |    supplierId = ?
          | WHERE team = ? AND id = ?
        """.stripMargin
      )

    } yield {
      TechnologyRepository.registerCodecs(session)

      stmtSetTechnology = setTechnology
      stmtDeleteTechnologies = deleteTechnologies
      Done
    }

  private def createTables() =
    // language=SQL
    for {
      _ <- PCBCodecHelper.loadTypes(session)

      _ <- session.executeCreateTable(
        """
          | CREATE TYPE IF NOT EXISTS numericalCapability (
          |   name text,
          |   min decimal,
          |   max decimal
          | )
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TYPE IF NOT EXISTS listCapability (
          |   name text,
          |   allowed list<text>,
          |   forbidden list<text>
          | )
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TYPE IF NOT EXISTS stringCapability (
          |   name text,
          |   allowed text,
          |   forbidden text
          | )
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TYPE IF NOT EXISTS booleanCapability (
          |   name text,
          |   allowed boolean
          | )
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS technologies (
          |            team text,
          |            name text,
          |            id text,
          |            supplierId uuid,
          |            priceIndication int,
          |            numericals list<frozen<numericalCapability>>,
          |            lists list<frozen<listCapability>>,
          |            strings list<frozen<stringCapability>>,
          |            booleans list<frozen<booleanCapability>>,
          |
          |            PRIMARY KEY (team, id)
          |);
        """.stripMargin
      )

    } yield {
      TechnologyRepository.registerCodecs(session)
      Done
    }

}
