package de.fellows.app.supplier.entity.manufacture

import akka.Done
import com.lightbend.lagom.scaladsl.api.transport.NotFound
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import de.fellows.app.supplier.{Manufacture, ManufactureMatch, MatchingState}

import java.time.Instant

class ManufactureEntity extends PersistentEntity {
  override type Command = ManufactureCommand
  override type Event   = ManufactureEvent
  override type State   = Manufacture

  override def initialState: Manufacture = Manufacture(None, Seq())

  def upsert(matches: Seq[ManufactureMatch], update: ManufactureMatch): Seq[ManufactureMatch] = {
    val idx = matches.indexWhere(_.spec.id == update.spec.id)
    if (idx < 0) {
      matches :+ update
    } else {
      matches.updated(idx, update)
    }
  }

  override def behavior: Behavior =
    Actions()
      .onCommand[StartMatching, Done] {
        case (x: StartMatching, ctx, _) =>
          ctx.thenPersist(
            MatchingStarted(x.spec, Instant.now())
          )(_ => ctx.reply(Done))
      }
      .onCommand[StopMatching, Done] {
        case (x: StopMatching, ctx, s) =>
          s.ref match {
            case Some(assref) if assref == x.spec.assembly =>
              ctx.thenPersist(
                MatchingFinished(x.spec, Instant.now(), x.suppliers)
              )(_ => ctx.reply(Done))
            case _ =>
              ctx.commandFailed(NotFound("no ref set"))
              ctx.done
          }

      }
      .onReadOnlyCommand[GetManufactureInfo, ManufactureResponse] {
        case (x: GetManufactureInfo, ctx, s) =>
          s.ref match {
            case Some(_) =>
              ctx.reply(ManufactureResponse(Some(s)))
            case None =>
              ctx.reply(ManufactureResponse(None))
          }

      }
      .onEvent {
        case (x: MatchingStarted, s) => s.copy(
            ref = Some(x.spec.assembly),
            matches = upsert(
              s.matches,
              ManufactureMatch(
                x.spec,
                MatchingState(
                  time = Some(x.time),
                  state = Manufacture.STATE_MATCHING
                ),
                suppliers = Seq()
              )
            )
          )

        case (x: MatchingFinished, s) =>
          s.copy(
            matches = upsert(
              s.matches,
              ManufactureMatch(
                x.spec,
                MatchingState(
                  time = Some(x.time),
                  state = Manufacture.STATE_READY
                ),
                suppliers = x.result
              )
            )
          )
      }

}
