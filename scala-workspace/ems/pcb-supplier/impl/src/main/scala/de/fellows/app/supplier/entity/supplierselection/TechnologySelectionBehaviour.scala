package de.fellows.app.supplier.entity.supplierselection

import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.supplier.TechnologySelection
import play.api.libs.json.{Format, Json}

import java.util.UUID

sealed trait TechnologySelectionCommand

sealed trait TechnologySelectionEvent extends AggregateEvent[TechnologySelectionEvent] {
  override def aggregateTag: AggregateEventShards[TechnologySelectionEvent] = TechnologySelectionEvent.Tag
}

object TechnologySelectionEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[TechnologySelectionEvent](NumShards)
}

case class SelectTechnology(assemblyReference: AssemblyReference, supplier: UUID, technology: Option[String])
    extends TechnologySelectionCommand with ReplyType[TechnologyResponse]

case class GetTechnologySelection(assemblyReference: AssemblyReference) extends TechnologySelectionCommand
    with ReplyType[TechnologyResponse]

case class TechnologySelected(
    supplier: Option[UUID],
    selection: TechnologySelection,
    oldSelection: Option[TechnologySelection]
) extends TechnologySelectionEvent

case class TechnologyResponse(response: Seq[TechnologySelection])

object TechnologyResponse {
  implicit val f: Format[TechnologyResponse] = Json.format[TechnologyResponse]
}

object SelectTechnology {
  implicit val f: Format[SelectTechnology] = Json.format[SelectTechnology]
}

object GetTechnologySelection {
  implicit val f: Format[GetTechnologySelection] = Json.format[GetTechnologySelection]
}

object TechnologySelected {
  implicit val f: Format[TechnologySelected] = Json.format[TechnologySelected]
}
